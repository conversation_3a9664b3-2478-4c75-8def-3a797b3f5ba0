using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using UnityEngine.EventSystems;
using System;

public class NGDecorationTile : NGDirectionCardBase
{
    public TMP_Text m_quantityText;
    public NGDecorationInfoManager.NGDecorationInfo m_info;
    int m_quantity;
    public string m_decoration;
    public override bool ScaleCardDownOnDrag { get { return true; } }

    override public void GiveReward()
    {
        base.GiveReward();
    
        Debug.Log($"{m_gift.m_name} Gift Recieved");
    }
    override public NGCardInfoHolder GetDescriptionText()
    {
        string description = "No description available!";
        string name = "No name available!";

        if (m_gift != null)
            name = m_gift.m_giftTitle;
        if (m_info != null)
            description = m_info.m_description;

        return new NGCardInfoHolder(name, description, Cost, 1, "Decoration");
    }
    override protected void Activate(NGBusinessGift _gift, MAParserSection _section, INGDecisionCardHolder _fromHolder)
    {
        base.Activate(_gift, _section, _fromHolder);
        m_decoration = _gift.m_cardPower;
        if (m_gift.m_power.IsNullOrWhiteSpace() == false)
        {
            var prams = ReactReflection.GetParameters(m_gift.m_power);
            if (prams == null || prams.Length <= 1)
            {
                Debug.LogError($"{m_gift.m_power} incorrect syntax");
                return;
            }
            m_decoration = prams[0];
        }
        
        if (m_decoration == "Random")
        {
            //int r = _gameFlow.GetDeterministicRand(_gift, NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos.Count-1);
            int r = UnityEngine.Random.Range(0,NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos.Count-1);
            m_decoration = NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos[r].m_prefabName;
        }
        m_info = NGDecorationInfoManager.GetInfo(m_decoration);

        if (m_info == null)
        {
            Debug.LogError("Decoration: " + m_decoration + " has no info.");
            //int r = _gameFlow.GetDeterministicRand(_gift, NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos.Count-1);
            int r = UnityEngine.Random.Range(0,NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos.Count-1);
            m_decoration = NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos[r].m_prefabName;
            m_info = NGDecorationInfoManager.GetInfo(m_decoration);
        }
        string path = m_info.m_spritePath.Substring(0, m_info.m_spritePath.Length - 4);
        m_image.sprite = SpriteAtlasMapLoader.LoadClonedSprite(path);
        //NGDecorationInfoManager.MakeSprite(m_info, ResearchRewardsUIController.s_rewardCaptureType, m_image);

        m_quantity = 1;
        if (_gift.m_quantity.IsZero() == false)
            m_quantity = (int)_gift.m_quantity;

        m_quantityText.gameObject.SetActive(m_quantity > 1);
        m_quantityText.text = m_quantity.ToString();

        if (m_info.m_displayName != "")
            m_title.text = m_info.m_displayName;
    }

#if OldBusinessFlow
    override protected void Activate(NGBusinessGift _gift, NGBusinessFlow _flow, INGDecisionCardHolder _fromHolder)
    {
        base.Activate(_gift, _flow, _fromHolder);
        m_decoration = _gift.m_cardPower;
        if (m_gift.m_power.IsNullOrWhiteSpace() == false)
        {
            var prams = ReactReflection.GetParameters(m_gift.m_power);
            if (prams == null || prams.Length <= 1)
            {
                Debug.LogError($"{m_gift.m_power} incorrect syntax");
                return;
            }
            m_decoration = prams[0];
        }
        
        if (m_decoration == "Random")
        {
            int r = _flow.GetDeterministicRand(_gift, NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos.Count-1);
            m_decoration = NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos[r].m_prefabName;
        }
        m_info = NGDecorationInfoManager.GetInfo(m_decoration);

        if (m_info == null)
        {
            Debug.LogError("Decoration: " + m_decoration + " has no info.");
            int r = _flow.GetDeterministicRand(_gift, NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos.Count-1);
            m_decoration = NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos[r].m_prefabName;
            m_info = NGDecorationInfoManager.GetInfo(m_decoration);
        }
        string path = m_info.m_spritePath.Substring(0, m_info.m_spritePath.Length - 4);
        m_image.sprite = SpriteAtlasMapLoader.LoadClonedSprite(path);
        //NGDecorationInfoManager.MakeSprite(m_info, ResearchRewardsUIController.s_rewardCaptureType, m_image);

        m_quantity = 1;
        if (_gift.m_quantity.IsZero() == false)
            m_quantity = (int)_gift.m_quantity;

        m_quantityText.gameObject.SetActive(m_quantity > 1);
        m_quantityText.text = m_quantity.ToString();

        if (m_info.m_displayName != "")
            m_title.text = m_info.m_displayName;
    }
#endif
    protected override bool IsPendingUserAction()
    {
     //   return PDMDebugDecoration.Me != null;
        return false;
    }

    protected override void OnCardDraggedBackOverHolder()
    {
        // if(PDMDebugDecoration.Me != null)
        //     PDMDebugDecoration.Me.ClickedClose();
    }

    override protected NGMovingObject DragActivate(PointerEventData _eventData)
    {
        InitBuildTool();
        return null;
    }

    public void ClosedDecorationMenu(int _usageCount)
    {        
        //Shows the card holder
        if(NGBusinessDecisionDialog.Me)
            NGBusinessDecisionDialog.Me.gameObject.SetActive(true);
        
        if (_usageCount == 0)
        {
            Destroy(gameObject);
            PayGiftCost();
            GiveReward();
        }
        else if(!m_dragging)
        {
            ActionCancelled();
        }
    }

    void InitBuildTool()
    {
        PDMDebugDecoration.Create(this, m_decoration, (int) m_gift.m_quantity, false, ClosedDecorationMenu);

        if(NGBusinessDecisionDialog.Me)
            NGBusinessDecisionDialog.Me.ToggleHiding(true);
    }
}
