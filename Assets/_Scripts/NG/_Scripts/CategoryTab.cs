using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

public class CategoryTab : MonoBehaviour
{
    public TMP_Text m_title;
    public ColorBlock m_buisinessCB;
    public ColorBlock m_buildingsCB;

    private int m_id;
    public void Init(string _t, int _id, int _num, int _numunlocked)
    {
        m_id = _id;
        m_title.text = _t + " " + _numunlocked + "/" + _num;
        if (_t == "Buildings")
            ChangeColours(m_buildingsCB);
        if (_t == "Business")
            ChangeColours(m_buisinessCB);
    }

    public void OnPressed()
    {
        // ResearchLabGUI mgr = GetComponentInParent<ResearchLabGUI>();
        // if (mgr != null)
        //     mgr.CategoryTabPressed(m_id);
    }

    private void ChangeColours(ColorBlock _cb)
    {
        Button_v0 button = GetComponent<Button_v0>();
        if (button != null)
        {
            _cb.colorMultiplier = 1.0f;
            button.colors = _cb;
        }
    }
}
