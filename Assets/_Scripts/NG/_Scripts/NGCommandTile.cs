using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGCommandTile : NGDirectionCardBase
{
    public Transform m_priceHolder; 
    
    protected override void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _cardHolder)
    {
        base.Activate(_gift, _maParserSection, _cardHolder);
        m_priceHolder.gameObject.SetActive(_gift.m_cardPrice > 0);
    }
    override public void GiveReward()
    {
        base.GiveReward();
        MAParserSupport.TryParse(m_gift.m_cardPower, out var power);
    }
}
