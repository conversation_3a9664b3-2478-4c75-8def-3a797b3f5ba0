using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class NGDirectionGiftInfo : MonoBehaviour
{
    public NGGiftIcon m_giftIconPrefab;

    NGBusinessDirection m_direction;
    public Transform m_giftHolder;

    public void ClickedCancel()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        DestroyMe();
    }

    public void ClickedAccept()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        m_direction.ClickedGiftAccept(m_direction);
        DestroyMe();
    }

    void DestroyMe()
    {
        Destroy(gameObject);
    }
    void Activate(NGBusinessDirection _direction)
    {
        m_direction = _direction;
        m_giftHolder.DestroyChildren();
        foreach(var gift in m_direction.m_gifts)
        {
            NGGiftIcon.Create(m_giftIconPrefab, m_giftHolder, gift);
        }

    }
    public static NGDirectionGiftInfo Create(NGBusinessDirection _direction)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_NGDirectionGiftInfoPrefab, NGBusinessDecisionManager.Me.m_NGDirectionDialogHolder);
        var bd = go.GetComponent<NGDirectionGiftInfo>();
        bd.Activate(_direction);
        return bd;
    }
}
