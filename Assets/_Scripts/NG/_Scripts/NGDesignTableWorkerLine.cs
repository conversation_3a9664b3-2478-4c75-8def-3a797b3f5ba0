using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class NGDesignTableWorkerLine : MonoBehaviour
{
    public TMP_Text m_workerCount;
    public TMP_Text m_workerTime;
    public TMP_Text m_animText;
    public Animator m_anim;

    public int m_numWorkers = 0;
    public float m_workerTimes = 0;
    public void SetValue(int _numWorkers, float _workerTimes)
    {
        m_workerCount.text = _numWorkers.ToString();
        m_workerTime.text = _workerTimes.ToHMSTimeString();
        if (_numWorkers != m_numWorkers || _workerTimes != m_workerTimes)
        {
            var value = (_workerTimes - m_workerTimes);
            if (value.IsZero() == false)
            {
                var plusMinus = (value < 0) ? " " : "+";
                m_animText.text = $"{plusMinus}{value}";
                m_anim.SetTrigger("FloatUp");
            }
        }

        m_numWorkers = _numWorkers;
        m_workerTimes = _workerTimes;
    }
    void Activate()
    {
        
    }

    public static NGDesignTableWorkerLine Create(NGDesignTableWorkerLine _prefab, Transform _holder)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var activate = go.GetComponent<NGDesignTableWorkerLine>();
        activate.Activate();
        return activate;
    }

}
