using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
#if HOVER
public class NGWarning : BaseClosableUIController
{
	[SerializeField] private TMP_Text m_title;
	[SerializeField] private TMP_Text m_contentText;
	[SerializeField] private TMP_Text m_cancelButtonText;
	[SerializeField] private TMP_Text m_confirmButtonText;
	private NGTradingHouseGUI m_tradingHouseGUI;

	public void Activate(NGTradingHouseGUI _tradingHouseGUI){
		m_tradingHouseGUI = _tradingHouseGUI;
	}
	public void ClickedConfirm(){
		// tell someone and continue
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);

		Close();
	}

	public void ClickedCancel(){
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
		Close();
	}

	public static NGWarning Create(NGTradingHouseGUI _tradingHouseGUI, Transform _parent){
		var go = Instantiate(_tradingHouseGUI.m_warningPrefab);
		go.transform.SetParent(_parent);
		var w = go.GetComponent<NGWarning>();
		w.Activate(_tradingHouseGUI);
		return w;
	}
}
#endif