using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class TutorialTownScene
{
    public string id;
    public bool m_debugChanged;
    public string m_name;
    public string m_description;
    public string m_picture;
    public string m_position;
    public string m_objective;
    
    public static List<TutorialTownScene> s_records = new();
    public static List<TutorialTownScene> GetList => s_records;
    public string DebugDisplayName => m_name;
    const string TUTORIAL_PREFAB_PATH = "_Prefabs/Tutorial/";

    public static bool PostImportARecord(TutorialTownScene _what)
    {
        if (_what.m_name.IsNullOrWhiteSpace())
        {
            Debug.LogError($"TutorialTownScene item has no name");
            return false;
        }
        
/*        var item = new TutorialMaster.TutorialData {
            m_name = _what.m_name,
            m_descriptions = new List<string>(_what.m_description.Split('|')),
            m_portraitObject = ResManager.Load<GameObject>(TUTORIAL_PREFAB_PATH + _what.m_picture),
        };
        var cell = _what.m_position.RemoveWhiteSpaceAndToLower();
        if (!TutorialMaster.s_tutorialStringToPosition.TryGetValue(cell, out var position))
        {
            var split = cell.Split(',');
            if (split.Length < 2) {
            } else if (split.Length < 2 || float.TryParse(split[0], System.Globalization.NumberStyles.Float, System.Globalization.CultureInfo.InvariantCulture, out position.x) == false) {
                Debug.LogWarning($"{_what.m_name} Illegal Value at Cell {_what.m_position}");
            } else if (float.TryParse(split[1], System.Globalization.NumberStyles.Float, System.Globalization.CultureInfo.InvariantCulture, out position.x) == false) {
                Debug.LogWarning($"{_what.m_name} Illegal Value at Cell {_what.m_position}");
            }
            cell = "raw";
        }
        item.m_positionType = cell;
        item.m_position = position;
        if (!string.IsNullOrEmpty(_what.m_objective)) {
            string[] array = _what.m_objective.Split('|');
            for (int n = 0; n < array.Length; n++) {
                var obj = new TutorialMaster.TutorialObjectiveRequirementData();
                var terms = array[n].Split (',');
                foreach (var term in terms) {
                    if (!int.TryParse (term, out obj.m_targetAmount)) {
                        obj.m_requiredString = term;
                    }
                }
                item.m_objectives.Add (obj);
            }
        }
        TutorialMaster.Me.AddData(item);*/
        return true;
    }
    public static List<TutorialTownScene> LoadInfo()
    {
        s_records = NGKnack.ImportKnackInto<TutorialTownScene>(PostImportARecord);
        return s_records;
    }
}
