using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGChoicesTile : NGDirectionCardBase
{
    override protected void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder)
    {
        base.Activate(_gift, _maParserSection, _fromHolder);
        if (m_gift.m_spritePath.IsNullOrWhiteSpace() == false)
            m_image.sprite = m_gift.GetSprite;
    }
    public override NGCardInfoHolder GetDescriptionText()
    {
        return new NGCardInfoHolder(m_gift.m_giftTitle, m_gift.m_description, Cost, 1, "ChoicesCharacter");
    }
    
    override public void GiveReward()
    {
        base.GiveReward();
    }
}
