#if CHOICES
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
public class NGTutorialMessagePopup : NGTutorialMessageBase
{
    public TMP_Text m_preBarMessage;
    public TMP_Text m_postBarMessage;
    public Image m_barFill;
    public TMP_Text m_barMin;
    public TMP_Text m_barMax;
    public TMP_Text m_barValue;
    public RectTransform m_barTextHolder;
    public RectTransform m_barHolder;
    public RectTransform m_buttonHolder;
    public string m_trackFuncString;
    public NGTutorial m_line;
    public enum MessageType
    {
        Bar,
        Dots,
    }

    public MessageType m_messageType;

    void Update()
    {
        UpdateTrackFunc();
    }

    void UpdateTrackFunc()
    {/*
        if (m_trackFuncString.IsNullOrWhiteSpace() == false)
        {
            m_trackResult = ReactReflection.DecodeLine(NGTutorialInterface.Me, m_trackFuncString) as NGTutorialInterface.TrackReturn;
            m_barFill.fillAmount = m_trackResult.ValuePercent;
            m_barMin.text = m_trackResult.m_minValue.ToString("F0");
            m_barMax.text = m_trackResult.m_maxValue.ToString("F0");
            m_barValue.text = m_trackResult.m_currentValue.ToString("F0");
            var bvrt = m_barValue.GetComponent<RectTransform>();
            var width = m_barHolder.rect.width;
            bvrt.anchoredPosition = new Vector2( width * m_trackResult.ValuePercent, bvrt.anchoredPosition.y);
        }*/
    }
    override protected void Activate(NGTutorial _line, string _preBarText, string _postBarText, string _trackFuncString, string _type, string _pose)
    {
        base.Activate(_line, _preBarText, _postBarText, _trackFuncString, _type, _pose);
        m_line = _line;
        m_preBarMessage.text = _preBarText;
        m_preBarMessage.gameObject.SetActive(_preBarText.IsNullOrWhiteSpace() == false);
        m_postBarMessage.text = _postBarText;
        m_postBarMessage.gameObject.SetActive(_postBarText.IsNullOrWhiteSpace() == false);
        m_trackFuncString = _trackFuncString;
        m_barHolder.gameObject.SetActive(m_trackFuncString.IsNullOrWhiteSpace() == false);
        m_buttonHolder.gameObject.SetActive(m_trackFuncString.IsNullOrWhiteSpace());
        UpdateTrackFunc();
    }

    override public void ClickedButton()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        m_buttonFinished = true;
    }

    override public bool IsFinished()
    {
        return false;
    }

 
  

}
#endif