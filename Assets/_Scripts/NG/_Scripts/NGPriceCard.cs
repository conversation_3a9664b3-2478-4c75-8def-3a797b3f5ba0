using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

using DesignTableSceneManager = DesignTableManager;

public class NGPriceCard : MonoBehaviour
{
    public TextMeshPro m_text;
    bool m_activated = false;
    // Start is called before the first frame update
    void Start()
    {
    }
    void Activate()
    {
        m_activated = true;
        if (DesignTableSceneManager.Me == null)
            gameObject.SetActive(false);
        switch (DesignTableSceneManager.Me.DesignMode)
        {
            case DesignTableManager.DESIGN_CATEGORY.AVATAR:
            case DesignTableManager.DESIGN_CATEGORY.TUTORIAL_PRODUCT:
                gameObject.SetActive(false);
                return;
            case DesignTableManager.DESIGN_CATEGORY.CIVIC:
            case DesignTableManager.DESIGN_CATEGORY.FACTORY:
            case DesignTableManager.DESIGN_CATEGORY.HOUSE:
            case DesignTableManager.DESIGN_CATEGORY.PACKAGING:
            case DesignTableManager.DESIGN_CATEGORY.PIP_UNLOCK:
            case DesignTableManager.DESIGN_CATEGORY.PRODUCT_SELECTION:
            case DesignTableManager.DESIGN_CATEGORY.PRODUCT:
            case DesignTableManager.DESIGN_CATEGORY.RESEARCH_LAB:
                break;
        }
    }
    // Update is called once per frame
    void Update()
    {
        if (DesignTableSceneManager.Me == null) return;
        if (DesignTableSceneManager.Me.IsInitialised == false) return;
        if (m_activated == false) Activate();
        switch (DesignTableSceneManager.Me.DesignMode)
        {
            case DesignTableManager.DESIGN_CATEGORY.CIVIC:
			case DesignTableManager.DESIGN_CATEGORY.HOUSE:
				var design = DesignTableManager.Me.GetDesignOnTable();
                //m_text.text = $"©{design.RefreshBuildingCost():F0}";
                break;
            case DesignTableManager.DESIGN_CATEGORY.FACTORY:
            case DesignTableManager.DESIGN_CATEGORY.PACKAGING:
            case DesignTableManager.DESIGN_CATEGORY.PIP_UNLOCK:
            case DesignTableManager.DESIGN_CATEGORY.PRODUCT_SELECTION:
            case DesignTableManager.DESIGN_CATEGORY.PRODUCT:
            case DesignTableManager.DESIGN_CATEGORY.RESEARCH_LAB:
                m_text.text = "";
                break;
        }
    }
}
