using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.EventSystems;

public class NGBlockTile : NGDirectionCardBase
{
    public List<NGBlockInfo> m_infos;
    public TMP_Text m_blockText;

    public void GetGiftSprite(NGBusinessGift _gift)
    {
        if (_gift == null) return;

        if (!string.IsNullOrEmpty(_gift.m_spritePath))
        {
            m_image.sprite = _gift.GetSprite;
        }
        else if (_gift.Type == NGBusinessGift.GiftType.Decoration)
        {
            var dec = NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos.Find((o => o.m_prefabName.Equals(_gift.m_cardPower)));
            string path = dec.m_spritePath.Substring(0, dec.m_spritePath.Length - 4);
            m_image.sprite = SpriteAtlasMapLoader.LoadClonedSprite(path);
        }
        else if (!string.IsNullOrEmpty(_gift.m_buildingDesign))
        {
            GameManager.Me.GetDesignSpriteAsync(_gift.m_buildingDesign, CaptureObjectImage.Use.Building, _s =>
            {
                if (m_image != null) // image has been destroyed since requesting
                    m_image.sprite = _s;
            });
        }
        else
        {
            Debug.LogError("The gift must have a sprite path or a design in knack");
        }
    }

    public override List<(string title, string info)> GetInfoStats()
    {
        List<(string title, string info)> info = new ();
        
        var blocks = GetBlocks();
        
        info.Add(($"<b>{blocks.Count} Blocks in Pack</b>",""));
        
        foreach(var block in blocks)
        {
            info.Add((block.m_displayName, block.m_description));
        }
        
        return info;
    }
    
    private List<NGBlockInfo> GetBlocks()
    {
        List<NGBlockInfo> blocks = new();
        var split = m_gift.m_cardPower.Split('|', ';', '\n');
        foreach (var b in split)
        {
            var item = NGBlockInfo.GetInfo(b);
            if (item != null)
            {
                blocks.Add(item);
            }
        }
        return blocks;
    }
    
    override public void GiveReward()
    {
        base.GiveReward();
        var quanity = (m_gift.m_quantity > 0) ? m_gift.m_quantity : 1;
        
        var blocks = GetBlocks();
        
        foreach(var block in blocks)
        {
            for(int i = 0; i < quanity; i++)
            {
                var itemDrawer = block.m_mADrawerInfos;
                var splitD = itemDrawer.Split('|', ';', '\n');
                if(splitD.Length > 1)
                {
                    itemDrawer = splitD[0];
                }

                if (itemDrawer.IsNullOrWhiteSpace()) continue;
                var infos = MADrawerInfo.GetInfosByDrawerName(itemDrawer);
                if (infos == null || infos.Count == 0) continue;
                foreach (var j in infos)
                {
                    GameManager.UnlockDraw(j, true);
                }

                if (block.m_starterPack == false)
                {
                    GameManager.AddUnlock(block.m_prefabName);
                }
            }
        }
    }
    /*public void GiveRewardOld()
    {
        base.GiveReward();
        var quanity = (m_gift.m_quantity > 0) ? m_gift.m_quantity : 1;
        var split = m_gift.m_cardPower.Split('|', ';', '\n');
        for (int i = 0; i < quanity; i++)
        {
            foreach (var b in split)
            {
                var item = ItemDetailsHelper.GetBuyItemByName(b);
                if (item != null)
                {
                    item.OnUnlock();
                    var itemIds = NGPlayer.GetUnlockedBuyItemIds(item);
                    NGPlayer.Me.AddUnlockedBuyItem(itemIds);
                    string unlocksString = "";
                    foreach (string uid in itemIds)
                        unlocksString += $"{uid}+{(GameManager.IsConsumableUnlock(uid) ? 1 : -1)}~";
                }
            }
        }
    }*/
    override public NGCardInfoHolder GetDescriptionText()
    {
        string description = "No description available!";
        string name = "No name available!";

        if (m_gift != null)
            name = m_gift.m_giftTitle;
        description = m_gift.m_description;

        return new NGCardInfoHolder(name, description, Cost, 1, "Decoration");
    }
    
    override protected void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder)
    {
        base.Activate(_gift, _maParserSection, _fromHolder);
        m_infos = new List<NGBlockInfo>();
        var names = "";
        
        var split = _gift.m_cardPower.Trim().Split('|', ';', '\n');
        foreach (var b in split)
        {
            var blockInfo = NGBlockInfo.GetInfo(b);
            if (blockInfo == null)
            {
                Debug.LogError("block: " + b + " has no info.");
                continue;
            }          
            m_infos.Add(blockInfo);
            names += $"1x{blockInfo.m_displayName}\n";
        }
        if(m_blockText != null)
            m_blockText.text = names.TrimEnd('\n');

        GetGiftSprite(_gift);
    }
}
