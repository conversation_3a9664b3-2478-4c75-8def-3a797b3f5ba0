using System.Collections;
using UnityEngine.UI;
using UnityEngine;
using TMPro;
/*
public class NGShowUpgradeLevel : MonoBehaviour
{
    public TextMeshProUGUI m_text;
    public Image m_icon;
    NGBusinessGift m_gift;
    private float m_endTimer = 0;
    private float m_initValue;

    private void Start()
    {
        m_initValue = GetInitValue();
    }

    private float GetInitValue()
    {
        var details = m_gift.GetUpgradeCardCurrentData(m_building);
        foreach (var d in details)
        {
            return d;
        }

        return 0;
    }

    void Update()
    {
        Debug.Log("Show Upgrade Level for " + m_building.name);
        var details = m_gift.GetUpgradeData(m_building);
        foreach (var d in details)
        {
            m_text.text = d;
            Debug.Log("value " + d);
        }
        if(m_endTimer > 0)
        {
            m_endTimer -= Time.deltaTime;
            if (m_endTimer <= 0)
                DestroyMe();
        }
    }

    public void DestroyMe()
    {
        Destroy(gameObject);
    }

    public void Close()
    {
        if (m_initValue != GetInitValue())
            m_endTimer = 3f;
        else
            DestroyMe();
    }

    void Activate(NGBusinessGift _gift)
    {
        m_gift = _gift;

        string path = "_Art/Sprites/BusinessRewards/" + _gift.m_spritePath;
        var sprite = ResManager.Load<Sprite>(path);
        m_icon.sprite = sprite;

        if (GameManager.Me.TownManagementMode == ETownManagementMode.ShowEfficiency)
        {
            AlwaysFaceCamera afc = GetComponent<AlwaysFaceCamera>();
            afc.m_isTopDown = true;
            afc.m_freezeXZ = false;
            afc.m_pushForward = 0.25f;
        }
    }

    public static NGShowUpgradeLevel Create(NGBusinessGift _gift)
    {
        Debug.Log("Create Upgrade Level Prefab for " + _building.name);
        var go = Instantiate(NGManager.Me.m_NGShowUpgradeLevelPrefab, _building.m_balloonHolder);
        go.transform.localPosition = new Vector3(0f, 4.3f, -0.22f);
        var se = go.GetComponent<NGShowUpgradeLevel>();
        se.Activate(_building, _gift);
        return se;
    }
}*/
