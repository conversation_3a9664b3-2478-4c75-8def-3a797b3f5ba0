using System.Collections;
using System;
using System.Collections.Generic;
using UnityEngine;
	[System.Serializable]
public class BusinessLevelLeaderboard
{
	/*public const float PollServerEvery = 5f;
	public BusinessLevelLeaderboard(NGServerPlayer _server)
	{
		Id = _server.Id;
		m_name = _server.Name;
		m_playerLevel = _server.BusinessLevel;
		m_money = _server.Money;
		var ct = DateTimeOffset.FromUnixTimeSeconds(_server.CreatedTime);
		m_createdTime = ct.DateTime;
		ct = DateTimeOffset.FromUnixTimeSeconds(_server.LastUpdate);
		m_updateTime = ct.DateTime;
		m_isOnline = _server.IsOnline;
        m_avatar = _server.AvatarDesign;
        m_awards = _server.AwardsReceived;
		//m_activity = _server.PlayerActivity;
		m_satisfaction = _server.TownSatisfaction;
	}
	public string m_name;
	public string Id;
	public int m_playerLevel;
	public float m_money;
	public DateTime m_createdTime;
	public DateTime m_updateTime;
	public bool m_isOnline;
    public string m_avatar;
    public string m_awards;
	public int m_activity;
	public float m_satisfaction;

	public long CompareValue => (((long) (999-m_playerLevel)) << 32) | (int) m_money;
	public bool IsThisMe => Id == GameManager.ServerPlayer.Id;
	public static float m_pollServerTimer;
	public static List<BusinessLevelLeaderboard> m_leaderboard = new List<BusinessLevelLeaderboard>();
	static Action<List<BusinessLevelLeaderboard>> m_leaguetableCallback;
	static void GetBusinessPlayerLeaderboardCallback(List<NGServerPlayer> _players)
	{
		if (_players == null) {
			Debug.LogWarning($"PlayerLeaderboardCb with null");
			return;
		}
		var newBoard = new List<BusinessLevelLeaderboard>();
		foreach (var p in _players)
		{
			var leaderboardPlayer = CreateIfLegal(p);
			if(leaderboardPlayer != null)
				newBoard.Add(leaderboardPlayer);
		}
		newBoard.Sort((x, y)=> x.CompareValue.CompareTo(y.CompareValue));
		bool changed = newBoard.Count != m_leaderboard.Count;
		for (int i = 0; i < newBoard.Count; i++)
		{
			if (changed)
				break;
			changed |= newBoard[i].m_name != m_leaderboard[i].m_name || newBoard[i].CompareValue != m_leaderboard[i].CompareValue;
		}
		if (changed)
		{
			m_leaderboard = newBoard;
			m_leaguetableCallback(m_leaderboard);
		}
	}
	public static BusinessLevelLeaderboard CreateIfLegal(NGServerPlayer _server)
	{
		if (_server.Money == 0 || _server.CreatedTime == 0 || _server.LastUpdate == 0)
			return null;
		var timeSinceUpdatedOpponent = NetworkController.Me.HTTPController.GetServerTime() - _server.LastUpdate;
		var timeSinceCreatedOpponent = NetworkController.Me.HTTPController.GetServerTime() - _server.CreatedTime;
		var timeSinceCreatedMe = NetworkController.Me.HTTPController.GetServerTime() - GameManager.ServerPlayer.CreatedTime;
		return new BusinessLevelLeaderboard(_server);
	}

	public static void GetLeagueTable(Action <List<BusinessLevelLeaderboard>> _callback, bool _forceNow = false)
	{
		m_leaguetableCallback = _callback;
		if (m_pollServerTimer > Time.time && _forceNow == false)
			return;
		m_pollServerTimer = Time.time + PollServerEvery;
		NGServerPlayer.GetAllPlayers(GetBusinessPlayerLeaderboardCallback);
	}*/
}
