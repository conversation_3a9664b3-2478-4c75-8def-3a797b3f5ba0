using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

using ProductDesign = GameState_Design;
using Product = GameState_Product;

public class NGReactPickupAny : ReactPickupPersistent
{
	public int m_vanIndex = -1;
	public Transform m_productHolder;
	public Product m_product => m_contents.GetProduct();
	public bool IsProduct => m_contents.IsProduct;
	
	public override bool GetLinkedOrder(out MAOrder _order)
	{
		_order = MAOrder.EmptyOrder;	
		if (IsProduct == false || string.IsNullOrEmpty(m_product.m_uniqueID)) return false;
		return m_product.GetLinkedOrder(out _order);
	}
	
	override public string WhatAmIText { get
		{
			if (m_product != null)
				m_product.ProductLineType.Depluralise();
			return base.WhatAmIText;
		}}

	private void Initialise(NGCommanderBase _spawnedFrom, NGCarriableResource _resource, PickupSetup _pickupSetup)
	{
		m_spawnedFrom = _spawnedFrom;
		m_quantity = _pickupSetup.m_quantity;
        m_contents = _resource;
		var product = ProductDesign.BuildProduct(_resource.GetProduct().Design, transform, true, _pickupSetup.m_onlyVisual, (_o,componentsChanged) => {
			Initialise(_spawnedFrom, _resource, _pickupSetup.m_quantity);
			if (!_pickupSetup.m_killPickup) {
				ConvertToPhysicsObject();
				var rb = GetComponentInChildren<Rigidbody>();
				if(rb)
				{
					rb.isKinematic = true;
					transform.localPosition = Vector3.zero;
				}
			}
			Block[] blocks = null;
			if(_o != null)
			{
				_o.transform.localScale = Vector3.one * (GlobalData.Me.m_heldItemScale * NGManager.Me.m_productPickupScale);
				blocks = _o.GetComponentsInChildren<Block>();
			}
			if (_pickupSetup.m_onComplete != null) _pickupSetup.m_onComplete(gameObject);
			if(blocks != null)
				foreach(Block block in blocks)
					block.enabled = false;
		});
	}

	/*override public void OnCreate()
	{
		if (!ReactManager.Me.m_pickupProductList.Contains(this))
			ReactManager.Me.m_pickupProductList.Add(this);
	}

	override public void OnRemove()
	{
		ReactManager.Me.m_pickupProductList.Remove(this);
	}

	public static NGReactPickupAny CreatePickupProductFromLoad(SaveContainers.SaveReactPickupProduct _pickupProduct)
    {
		var spawnedFrom = CountrysideManager.Me.GetReactCommander<NGFactory>(_pickupProduct.m_spawnedFrom);
		var product = ProductHolder.Me.GetProductByID(_pickupProduct.m_designID);
		NGReactPickupAny pickup;
		pickup = Create(spawnedFrom, product, GlobalData.Me.m_pickupsHolder);
		Vector3 pos = _pickupProduct.m_position;
		if (pos.y < 0)
			pos = pos.SetYToHeight();
		pos.y += 1f;
		pickup.transform.SetPositionAndRotation(pos, _pickupProduct.m_rotation);
		Vector3 targetPos = _pickupProduct.m_targetPos;
		if(targetPos.magnitude < 1e22)
			pickup.transform.position = targetPos;
		return pickup;
	}*/

	public SaveContainers.SaveReactPickupProduct GetPickupProductProto()
	{
		return new SaveContainers.SaveReactPickupProduct
		{
			m_position = transform.position,
			m_quantity = (int)Quantity,
			m_rotation = transform.rotation,
			m_spawnedFrom = m_spawnedFrom?.m_linkUID ?? -1,
			m_designID = m_product.m_uniqueID,
			m_targetPos = m_targetPosForSave,
			m_holderObject = m_holder?.m_ID ?? -1,
			m_holderDest = -1//(m_holder as NGWorker)?.m_controlledByBuilding?.m_linkUID ?? -1
		};
	}

	void ConvertToPhysicsObject()
	{
		var combineIt = false;
		var rb = GetComponentInChildren<Rigidbody>();
		if(rb == null) rb = gameObject.AddComponent<Rigidbody>();
		rb.collisionDetectionMode = CollisionDetectionMode.ContinuousSpeculative;
		if(combineIt == false)
		{
			var mcs = GetComponentsInChildren<MeshCollider>(true);
			foreach(var m in mcs)
				m.enabled = true;
			return;
		}
		MeshFilter[] meshFilters = GetComponentsInChildren<MeshFilter>();
		CombineInstance[] combine = new CombineInstance[meshFilters.Length];

        int i = 0;
        while (i < meshFilters.Length)
        {
            combine[i].mesh = meshFilters[i].sharedMesh;
            combine[i].transform = meshFilters[i].transform.localToWorldMatrix;
            meshFilters[i].gameObject.SetActive(false);
            i++;
        }
		if(GetComponent<MeshFilter>() == null) gameObject.AddComponent<MeshFilter>();
        transform.GetComponent<MeshFilter>().mesh = new Mesh();
        transform.GetComponent<MeshFilter>().mesh.CombineMeshes(combine);
        transform.gameObject.SetActive(true);
	}
	
	public static NGReactPickupAny Create(NGCommanderBase _spawnedFrom, NGCarriableResource _resource, PickupSetup _pickupSetup)
	{
		if(_resource == null || _resource.GetProduct()?.HasDesign == false) return null;
		var go = Instantiate(NGCarriableResource.GetPrefabForResource(_resource),_pickupSetup.m_holder);
		var rpa = go.GetComponent<NGReactPickupAny>();
		rpa.Initialise(_spawnedFrom, _resource, _pickupSetup);
		if (_pickupSetup.m_killPickup)
		{
			rpa.enabled = false;
			rpa.StartRemoveShadows();
			(rpa as ReactPickupPersistent)?.OnRemove();
			Destroy(go.GetComponent<Rigidbody>());
		}
		else
		{
			rpa.OnCreate();
		}

		return rpa;
	}
	
	public override bool ShouldBeCleanedUp()
	{
		if (IsProduct && false) 
			return false;
		return base.ShouldBeCleanedUp();
	}

	// If interrupted while carrying raw materials, send the stock back to the nearest warehouse/harvester
	public override void Interrupted(NGMovingObject _object)
	{/*
		float closestDistSqd = float.MaxValue;
		if(IsRawMaterial)
		{
			NGWarehouse closestWarehouse = null;
			foreach(var f in NGManager.Me.FactoryList)
			for (int i=0; i < NGManager.Me.FactoryList.Count; i++)
			{
				var wh = f as NGWarehouse;
				if (wh == null) continue;
				var dist = Vector3.SqrMagnitude(transform.position - wh.transform.position);
				if (dist < closestDistSqd)
				{
					closestDistSqd = dist;
					closestWarehouse = wh;
				}
			}

			if (closestWarehouse != null)
			{
				closestWarehouse.AddStock(ContentsAsMaterial, m_quantity);
				if(_object.MyJob is NGHarvester)
					_object.MyJob.RemoveStock(ContentsAsMaterial, m_quantity);
			}
		}
		else
		{
			NGHarvester closestHarvester = null;
			foreach(var f in NGManager.Me.FactoryList)
			{
				var harvester = f as NGHarvester;
				if(harvester == null)
					continue;
				//TODO if(harvester.ResourceType != ContentsAsMaterial)
				//	continue;

				var dist = Vector3.SqrMagnitude(transform.position - harvester.transform.position);
				if (dist < closestDistSqd)
				{
					closestDistSqd = dist;
					closestHarvester = harvester;
				}
			}

			if (closestHarvester != null)
				closestHarvester.AddStock(ContentsAsMaterial, m_quantity);
		}
*/
		_object.GetComponentInChildren<Animator>().SetBool(GlobalData.CarryProductAnim, false);
	}

	// public override void SetVanPickup(NGDispatchVehicle _van)
	// {
	// 	Pickup(_van, false);
	// 	_van.AddProductToVan(this);
	// }
	public override void OnBeginDrag(PointerEventData _eventData)
	{
		/*var van = GetComponentInParent<NGDispatchVehicle>();
		if (van)
		{
			if (van.CurrentDeliveryState != NGDispatchVehicle.DeliveryState.None)
				return; // NO YOU CANNOT TAKE FROM A MOVING VAN!!!
			van.RemoveProductFromVan(this);
		}*/
			
		base.OnBeginDrag(_eventData);
	}
	public override void Consume()
	{
//		AudioClipManager.Me.PlaySoundAndForget( "DropResource", this.gameObject );
	}
}
