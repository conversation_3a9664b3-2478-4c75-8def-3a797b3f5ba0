using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class ProductPacks
{
	public string id;
	public bool m_debugChanged;
	public string m_packName;
	[ScanField] public string m_nGProductInfo;
	public string m_rarity;
	[ScanField] public string m_nGBlocks;
	[ScanField] public string m_paintPotData;
	[ScanField] public string m_stickerData;
	[ScanField] public string m_patternData;
	public bool m_starterPack;
	
	public List<NGProductInfo> m_productInfos;
	public List<NGBlockInfo> m_blockInfos;
	public List<PaintPotData> m_paintDatas;
	public List<StickerData> m_stickerDatas;
	public List<PatternData> m_patternDatas;
	public bool HasDecorations => !string.IsNullOrEmpty(m_paintPotData) || !string.IsNullOrEmpty(m_stickerData) || !string.IsNullOrEmpty(m_patternData);
	public bool HasBlocks => !string.IsNullOrEmpty(m_nGBlocks);
	
	
	public static List<ProductPacks> s_packs = new();
	public static List<ProductPacks> GetList=>s_packs;
	public string DebugDisplayName => m_packName;

	public List<NGProductInfo> ToProductInfo
	{
		get
		{
			var results = new List<NGProductInfo>();
			var split = m_nGProductInfo.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
			foreach (var s in split)
			{
				if (NGProductInfo.s_allProducts.ContainsKey(s))
				{
					results.Add(NGProductInfo.s_allProducts[s]);
				}
			}
			return results;
		}
	}
	
	public List<NGBlockInfo> ToBlockInfo
	{
		get
		{
			var results = new List<NGBlockInfo>();
			var split = m_nGBlocks.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);
			foreach (var s in split)
			{
				if (NGBlockInfo.s_allBlocks.ContainsKey(s))
				{
					results.Add(NGBlockInfo.s_allBlocks[s]);
				}
			}
			return results;
		}
	}
	
	public List<PatternData> ToPatternDatas
	{
		get
		{
			var results = new List<PatternData>();
			var split = m_patternData.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
			
			foreach (var s in split)
			{
				int i = Int32.Parse(s);
				var found = PatternData.s_entries.Find(o => o.m_iD == i);
				if(found != null)
					results.Add(found);
			}
			return results;
		}
	}
	
	public List<StickerData> ToStickerDatas
	{
		get
		{
			var results = new List<StickerData>();
			var split = m_stickerData.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
			
			foreach (var s in split)
			{
				int i = Int32.Parse(s);
				var found = StickerData.s_entries.Find(o => o.m_iD == i);
				if(found != null)
					results.Add(found);
			}
			return results;
		}
	}
	
	public List<PaintPotData> ToPaintDatas
	{
		get
		{
			var results = new List<PaintPotData>();
			var split = m_paintPotData.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
			
			foreach (var s in split)
			{
				var found = PaintPotData.s_entries.Find(o => o.m_name.Equals(s));
				if(found != null)
					results.Add(found);
			}
			return results;
		}
	}

	public static bool PostImportARecord(ProductPacks _what)
	{
		if (_what.m_packName.IsNullOrWhiteSpace())
		{
			Debug.LogError($"ProductPacks item has no name");
			return false;
		}
		if (_what.m_rarity.IsNullOrWhiteSpace())
		{
			Debug.LogError($"ProductPacks item {_what.m_packName} has no rarity set");
			_what.m_rarity = "Common";
		}
		if (_what.m_nGProductInfo == null)
		{
			Debug.LogError($"ProductPacks item {_what.m_packName} has no product line set");
			return false;
		}
		_what.m_productInfos = _what.ToProductInfo;

		if (!string.IsNullOrEmpty(_what.m_nGBlocks))
			_what.m_blockInfos = _what.ToBlockInfo;

		if (!string.IsNullOrEmpty(_what.m_paintPotData))
			_what.m_paintDatas = _what.ToPaintDatas;
		
		if (!string.IsNullOrEmpty(_what.m_patternData))
			_what.m_patternDatas = _what.ToPatternDatas;

		if (!string.IsNullOrEmpty(_what.m_stickerData))
			_what.m_stickerDatas = _what.ToStickerDatas;
		
		return true;
	}
   
	public static List<ProductPacks> LoadInfo()
	{
		s_packs = NGKnack.ImportKnackInto<ProductPacks>(PostImportARecord);
		ResearchLabRewardsController.AddPacks();
		if (BlockBalanceManager.c_giveAllProductStarterPacksOnStart)
		{
			foreach (var pack in s_packs)
			{
				if (pack.m_starterPack)
				{
					foreach (var paint in pack.m_paintDatas)
						GameManager.AddUnlock(paint.m_name);
					foreach (var paint in pack.m_patternDatas)
						GameManager.AddUnlock(paint.m_name);
					foreach (var paint in pack.m_stickerDatas)
						GameManager.AddUnlock(paint.m_name);
				}
			}
		}
		return s_packs;
	}

	public static List<string> GetProductLinePacks(string _line, bool starterPackOnly)
	{
		List<string> result = new List<string>();
		foreach (var pack in s_packs)
		{
			if(starterPackOnly && !pack.m_starterPack)
				continue;
			
			if (pack.m_productInfos != null)
			{
				foreach (var info in pack.m_productInfos)
				{
					if(info.m_prefabName.Equals(_line))
						result.Add(pack.m_packName);
				}
			}
		}

		return result;
	}

	public static List<string> GetStarterPackDecorationIDs(string _line)
	{
		List<string> result = new List<string>();
		foreach (var pack in s_packs)
		{
			if(!pack.m_starterPack)
				continue;
			
			if (pack.m_productInfos == null)
				continue;
			
			foreach (var info in pack.m_productInfos)
			{
				if (info.m_prefabName.Equals(_line))
				{
					foreach (var paint in pack.m_paintDatas)
						result.Add(paint.m_name);
					foreach (var sticker in pack.m_stickerDatas)
						result.Add(sticker.m_name);
					foreach (var pattern in pack.m_patternDatas)
						result.Add(pattern.m_name);
				}
			}
			
		}
		return result;
	}

	public static List<string> GetDecorationsFromPack(string _packName)
	{
		List<string> result = new List<string>();
		var pack = s_packs.Find(o => o.m_packName.Equals(_packName));
		
		foreach (var paint in pack.m_paintDatas)
			result.Add(paint.m_name);
		foreach (var sticker in pack.m_stickerDatas)
			result.Add(sticker.m_name);
		foreach (var pattern in pack.m_patternDatas)
			result.Add(pattern.m_name);
		
		return result;
	}
}
