using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class NGBusinessDirectionBanner : MAGUIBase
{
    public TMP_Text m_message;
    public Image m_advisorImage;
    public System.Action<MAGameFlow> m_gameFlowTappedAction;
    public System.Action m_tappedAction;
    Animator m_anim;
    private int m_sound;

    private float m_pulseTime = 0;
    private float m_pulseFrequency = 4f;
    private NGBusinessFlow m_flow;
    private MAGameFlow m_gameFlow;
    [SerializeField]private float m_pulseFrequencyMax = 4f;
    [SerializeField] private float m_pulseFrequencyMin = 1f;
    [SerializeField] private float m_pulseFrequencyDelta = -0.2f;
    // Start is called before the first frame update
    void Start()
    {
        m_anim = GetComponentInChildren<Animator>(true);
        m_anim.enabled = true;
        m_pulseFrequency = m_pulseFrequencyMax;
        m_pulseTime = Time.time + m_pulseFrequency;
    }

    void Update()
    {
        if (Time.time > m_pulseTime)
        {
            m_anim.SetBool("Pulse", true);
            m_pulseFrequency = Mathf.Clamp(m_pulseFrequency + m_pulseFrequencyDelta, m_pulseFrequencyMin, m_pulseFrequencyMax);
            m_pulseTime = Time.time + m_pulseFrequency;
        }
    }

    void Activate(MAGameFlow _gameFlow, string _message, System.Action<MAGameFlow> _gameFlowTappedAction)
    {
        m_gameFlow = _gameFlow;
        m_gameFlowTappedAction = _gameFlowTappedAction;
        Activate(_gameFlow.m_flow, _message, null);
    }
    void Activate(NGBusinessFlow _flow, string _message, System.Action _tappedAction)
    {
        Activate("DirectionBanner");
        m_flow = _flow;
        m_message.text = _message;
        m_tappedAction = _tappedAction;
        transform.SetSiblingIndex(0);
        var advisor = NGBusinessAdvisor.GetInfo(m_flow.m_businessAdvisor);
        if (advisor != null)
            m_advisorImage.sprite = advisor.PortaitSprite;
        if (GameManager.Me.IsOKToPlayUISound())
            m_sound = AudioClipManager.Me.PlaySoundOld("PlaySound_HUDObjectiveAppear", transform);
    }

    public void ClickedMe()
    {
		if (GameManager.Me.IsOKToPlayUISound())
			AudioClipManager.Me.PlaySoundOld("PlaySound_HUDEnvelopeAnim", transform);
        if (m_tappedAction != null)
            m_tappedAction();
        if(m_gameFlowTappedAction != null)
            m_gameFlowTappedAction(m_gameFlow);
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        Destroy(gameObject);
    }

    public static NGBusinessDirectionBanner Create(NGBusinessFlow _flow, string _message, System.Action _tappedAction = null)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_businessDirectionBannerPrefab, NGBusinessDecisionManager.Me.m_NGBusinessObjectiveHolder);
        var bdd = go.GetComponent<NGBusinessDirectionBanner>();
        bdd.Activate(_flow, _message, _tappedAction);
        return bdd;
    }
    
    public static NGBusinessDirectionBanner Create(MAGameFlow _flow, string _message, System.Action<MAGameFlow> _gameFlowTappedAction = null)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_businessDirectionBannerPrefab, NGBusinessDecisionManager.Me.m_NGBusinessObjectiveHolder);
        var bdd = go.GetComponent<NGBusinessDirectionBanner>();
        bdd.Activate(_flow, _message, _gameFlowTappedAction);
        return bdd;
    }
}
