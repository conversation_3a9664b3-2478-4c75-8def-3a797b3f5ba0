using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
#if OldBusinessFlow

public class NGDirectionAdvisorDialog : MonoBehaviour
{
    public Transform m_directionGiftElementHolder;
    public TMP_Text m_advisorName;
    public TMP_Text m_advisorTitle;
    public Image m_advisorImage;
    public TMP_Text m_advisorExplain;
    public Button m_nopeButton;
    public Button m_hireButton;
    public ScrollRect m_scrollWindow;

    NGBusinessFlow m_flow;
    MAGameFlow m_gameFlow;
    NGBusinessGift m_gift;
    NGBusinessDirection m_direction;
    NGBusinessDirection m_spawnedDirection;
    bool m_forShow;

    private void Awake()
    {
        m_nopeButton.gameObject.SetActive(false);
        m_hireButton.gameObject.SetActive(false);

        float left = 10;
        float right = 10;
        float bottom = 0;
        float top = 0;

        m_scrollWindow.viewport.anchorMin = Vector3.zero;
        m_scrollWindow.viewport.anchorMax = Vector3.one;
        m_scrollWindow.viewport.anchoredPosition = new Vector2((left - right)/2, (top - bottom)/2);
        m_scrollWindow.viewport.sizeDelta = new Vector2(-(left + right), -(top + bottom));
    }

    public void ClickedCancel()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        DestroyMe();
    }

    public void ClickedAccept()
    {
        NGBusinessFlow.ChooseAdvisor(m_flow);
        if (NGDirectionDialog.Me)
            NGDirectionDialog.Me.DestroyMe();
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        DestroyMe();
    }

    void DestroyMe()
    {
        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySound("PlaySound_TextBoxClose", transform);
        Destroy(gameObject);
    }
    void Activate(MAGameFlow _gameFlow, bool all)
    {
        m_gameFlow = _gameFlow;
        var advisor = m_gameFlow.m_flow.BusinessAdvisor;
        m_advisorName.text = advisor.FullName;
        m_advisorTitle.text = advisor.m_title;
        m_advisorImage.sprite = advisor.PortaitSprite;
        m_advisorExplain.text = advisor.m_info;
        m_directionGiftElementHolder.DestroyChildren();

        List<NGBusinessFlow> sb = m_flow.GetSectionBlock();
        RectTransform currentFlowTransform = null;

        for (int i = 0; i < sb.Count; i++)
        {
            var element = NGDirectionGiftElementHolder.Create(m_directionGiftElementHolder, sb[i].BusinessGiftsTakeAllInitial, sb[i].BusinessGiftsChooseInitial, sb, i);

            if (element != null && sb[i] == _gameFlow)
            {
                currentFlowTransform = element.GetComponent<RectTransform>();
            }
        }

        ScrollTo(currentFlowTransform);
       
        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySound("PlaySound_WindowBubble_Large01", transform);
    }
    void Activate(NGBusinessFlow _flow, bool all)
    {
        m_flow = _flow;
        var advisor = m_flow.BusinessAdvisor;
        m_advisorName.text = advisor.FullName;
        m_advisorTitle.text = advisor.m_title;
        m_advisorImage.sprite = advisor.PortaitSprite;
        m_advisorExplain.text = advisor.m_info;
        m_directionGiftElementHolder.DestroyChildren();

        List<NGBusinessFlow> sb = m_flow.GetSectionBlock();
        RectTransform currentFlowTransform = null;

        for (int i = 0; i < sb.Count; i++)
        {
            var element = NGDirectionGiftElementHolder.Create(m_directionGiftElementHolder, sb[i].BusinessGiftsTakeAllInitial, sb[i].BusinessGiftsChooseInitial, sb, i);

            if (element != null && sb[i] == _flow)
            {
                currentFlowTransform = element.GetComponent<RectTransform>();
            }
        }

        ScrollTo(currentFlowTransform);
       
        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySound("PlaySound_WindowBubble_Large01", transform);
    }
    void Activate(NGBusinessGift _gift, NGBusinessDirection _spawnedDirection)
    {
        m_gift = _gift;
        m_spawnedDirection = _spawnedDirection;
        var advisorName = "";
        var direction = _gift.GetFirstDirection(out advisorName);
        if (direction == null) { Debug.LogError($"Cant find direction for {_gift.m_cardPower}"); return; }
        Activate(direction, false);
    }
    void Activate(NGBusinessDirection _direction, bool _forShow)
    {
        m_direction = _direction;
        m_forShow = _forShow;
        var advisor = NGDirectionCharacter.Find(_direction.m_advisor);
        if(advisor == null) { Debug.LogError( $"No such advisor as '{_direction.m_advisor}"); return;  }
        m_advisorName.text = advisor.FullName;
        m_advisorTitle.text = advisor.m_title;
        m_advisorImage.sprite = advisor.m_image;
        m_advisorExplain.text = _direction.m_inMessage;
        m_directionGiftElementHolder.DestroyChildren();
        foreach (var decision in m_direction.m_businessDecisions)
        {
            NGDirectionGiftElementHolder.Create(m_directionGiftElementHolder, decision);    
        }
        if(_forShow)
        {
            m_nopeButton.gameObject.SetActive(false);
            var okayText = m_hireButton.GetComponentInChildren<TMP_Text>();
            if (okayText)
                okayText.text = "Okay";
        }
        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySound("PlaySound_WindowBubble_Large01", transform);
    }

    private void ScrollTo(RectTransform _target)
    {
        if(_target == null) return;
        Canvas.ForceUpdateCanvases();
        RectTransform contentRect = m_directionGiftElementHolder.GetComponent<RectTransform>();
        float maxScroll = contentRect.rect.height - contentRect.parent.GetComponent<RectTransform>().rect.height;
        Vector2 anchor = contentRect.anchoredPosition;
        anchor.y = Mathf.Clamp(-_target.offsetMax.y, 0.0f, maxScroll);
        contentRect.anchoredPosition = anchor;
    }

    public static NGDirectionAdvisorDialog Create(NGBusinessFlow _flow, bool all = true)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_NGDirectionAdvisorDialogPrefab, NGBusinessDecisionManager.Me.m_NGDirectionAdvisorDialogHolder);
        var bd = go.GetComponent<NGDirectionAdvisorDialog>();
        bd.Activate(_flow, all);
        return bd;
    }
    public static NGDirectionAdvisorDialog Create(MAGameFlow _gameFlow, bool all = true)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_NGDirectionAdvisorDialogPrefab, NGBusinessDecisionManager.Me.m_NGDirectionAdvisorDialogHolder);
        var bd = go.GetComponent<NGDirectionAdvisorDialog>();
        bd.Activate(_gameFlow, all);
        return bd;
    }
    public static NGDirectionAdvisorDialog Create(NGBusinessDirection _direction, bool _forInfo)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_NGDirectionAdvisorDialogPrefab, NGBusinessDecisionManager.Me.m_NGDirectionAdvisorDialogHolder);
        var bd = go.GetComponent<NGDirectionAdvisorDialog>();
        bd.Activate(_direction, _forInfo);
        return bd;
    }

    public static NGDirectionAdvisorDialog Create(string m_businessAdvisor, NGBusinessGift _gift, NGBusinessDirection _spawnedDirection)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_NGDirectionAdvisorDialogPrefab, NGBusinessDecisionManager.Me.m_NGDirectionAdvisorDialogHolder);
        var bd = go.GetComponent<NGDirectionAdvisorDialog>();
        bd.Activate(_gift, _spawnedDirection);
        return bd;
    }
    
}
#endif