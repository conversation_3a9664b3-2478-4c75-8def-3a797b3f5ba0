#if HOVER
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using TMPro;
public class NGNFTTile : NGDirectionCardBase
{
    public GameObject m_cardPriceHolder;
    public TMP_Text m_quantityText;
    public NGTradingManager.SellableItem m_nft;
    
        override protected void Activate(NGBusinessGift _gift, MAGameFlow _gameFlow, INGDecisionCardHolder _fromHolder)
    {
        base.Activate(_gift, _gameFlow, _fromHolder);
        m_nft = m_gift.m_nft;
        m_cardPriceHolder.SetActive(false);
        m_quantityText.gameObject.SetActive(false);
        m_title.text = _gift.m_cardTitle;
        if (m_title.text.Length > 30) m_title.text = "NFT";

        switch(m_nft.m_catagory)
        {
            case NGTradingManager.SellableItem.Catagory.Building:
                if (m_nft.m_productDesign == null)
                    m_nft.m_productDesign = new GameState_Design() { m_design = m_gift.m_buildingDesign, };
                NGTradingManager.MakeSprite(m_nft, m_image);
                break;
            case NGTradingManager.SellableItem.Catagory.Decoration:
                break;
            case NGTradingManager.SellableItem.Catagory.Material:
                NGTradingManager.MakeSprite(m_nft, m_image);
                m_quantityText.gameObject.SetActive(true);
                m_quantityText.text = $"x{m_nft.m_quantity.ToString("F0")}";
                break;
            case NGTradingManager.SellableItem.Catagory.Product:
                NGTradingManager.MakeSprite(m_nft, m_image);
                break;
            case NGTradingManager.SellableItem.Catagory.Worker:
                NGTradingManager.MakeSprite(m_nft, m_image);
                break;
            case NGTradingManager.SellableItem.Catagory.Block:
                 var holder = new GameObject("block_holder_" + m_nft.m_partID);
                holder.transform.position += new Vector3(0f, 0f, 0f);

                if (Block.IsValidUID(m_nft.m_partID))
                {
                    Block.Create(m_nft.m_partID, holder.transform, null, (_o) => {
                        if (_o != null)
                        {
                            NGTradingManager.MakeSprite(_o, CaptureObjectImage.Use.Product, m_image);
                        }
                        GameObject.Destroy(holder);
                    });
                }
                break;
       }
    }
    override protected void Activate(NGBusinessGift _gift, NGBusinessFlow _flow, INGDecisionCardHolder _fromHolder)
    {
        base.Activate(_gift, _flow, _fromHolder);
        m_nft = m_gift.m_nft;
        m_cardPriceHolder.SetActive(false);
        m_quantityText.gameObject.SetActive(false);
        m_title.text = _gift.m_cardTitle;
        if (m_title.text.Length > 30) m_title.text = "NFT";

        switch(m_nft.m_catagory)
        {
            case NGTradingManager.SellableItem.Catagory.Building:
                if (m_nft.m_productDesign == null)
                    m_nft.m_productDesign = new GameState_Design() { m_design = m_gift.m_buildingDesign, };
                NGTradingManager.MakeSprite(m_nft, m_image);
                break;
            case NGTradingManager.SellableItem.Catagory.Decoration:
                break;
            case NGTradingManager.SellableItem.Catagory.Material:
                NGTradingManager.MakeSprite(m_nft, m_image);
                m_quantityText.gameObject.SetActive(true);
                m_quantityText.text = $"x{m_nft.m_quantity.ToString("F0")}";
                break;
            case NGTradingManager.SellableItem.Catagory.Product:
                NGTradingManager.MakeSprite(m_nft, m_image);
                break;
            case NGTradingManager.SellableItem.Catagory.Worker:
                NGTradingManager.MakeSprite(m_nft, m_image);
                break;
            case NGTradingManager.SellableItem.Catagory.Block:
                 var holder = new GameObject("block_holder_" + m_nft.m_partID);
                holder.transform.position += new Vector3(0f, 0f, 0f);

                if (Block.IsValidUID(m_nft.m_partID))
                {
                    Block.Create(m_nft.m_partID, holder.transform, null, (_o) => {
                        if (_o != null)
                        {
                            NGTradingManager.MakeSprite(_o, CaptureObjectImage.Use.Product, m_image);
                        }
                        GameObject.Destroy(holder);
                    });
                }
                break;
       }
    }

    public void RefreshNFTQuantity()
    {
        m_quantityText.text = $"x{m_nft.m_quantity.ToString("F0")}";
    }

    bool BuildingPlaced(bool _placed)
    {
        if (_placed)
        {
            if (m_fromHolder != null)
            {
                PayGiftCost();
                m_fromHolder.GiftReceived(this);
            }
        }
        else
        {
            ActionCancelled();
        }
        return true;
    }

    override public void DestroyHeldObject()
    {
        if(m_heldObject != null)
        {
            m_heldObject.DestroyMe();
            m_heldObject = null;
        }
    }

    override public NGCardInfoHolder GetDescriptionText()
    {
        var cih = new NGCardInfoHolder(m_nft);
        switch (m_nft.m_catagory)
        {
            case NGTradingManager.SellableItem.Catagory.Building:
                if (m_nft.m_buildingPrefab.IsNullOrWhiteSpace())
                {
                    cih.m_description = cih.m_title;
                    cih.m_title = "NFT";
                }
                else
                    cih.m_description = NGBuildingInfoManager.NGBuildingInfo.GetBuildingInfo(m_nft.m_buildingPrefab).m_description;
                break;
            case NGTradingManager.SellableItem.Catagory.Worker:
                cih.m_description = "TODO: Worker";
                    break;
            case NGTradingManager.SellableItem.Catagory.Product:
                cih.m_description = LocalizeKnack.TranslateLocalisedString(NGProductInfo.GetInfo(m_nft.m_product.m_productLine).m_description);
                break;
            case NGTradingManager.SellableItem.Catagory.Block:
                cih.m_description = m_gift.m_cardPower; // TODO - format
                break;
            case NGTradingManager.SellableItem.Catagory.Decoration:
                // TODO - Waiting on decoration functionality - cih.m_description = NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos.Find(o => o.m_prefabName.Equals(m_nft.));
                break;
            case NGTradingManager.SellableItem.Catagory.Material:
                cih.m_description = m_nft.m_material.m_description;
                break;
        }
        return cih;
    }

    override public void GiveReward()
    {
        switch (m_nft.m_catagory)
        {
            case NGTradingManager.SellableItem.Catagory.Building:
            case NGTradingManager.SellableItem.Catagory.Product:
            case NGTradingManager.SellableItem.Catagory.Material:
            case NGTradingManager.SellableItem.Catagory.Worker:
                break;
            case NGTradingManager.SellableItem.Catagory.Block:
                // Method to call to unlock the part that has been bought.
                ResearchLabRewardsController.GiveRewardPart(m_nft.m_partID);
                break;
            default:
                Debug.LogError($"{m_nft.m_catagory} not set up yet");
                break;
        }
        DestroyHeldObject();
        base.GiveReward();
    }

    public static NGMovingObject m_heldObject;
    override protected NGMovingObject DragActivate(PointerEventData _eventData)
    {
        switch (m_nft.m_catagory)
        {
            case NGTradingManager.SellableItem.Catagory.Building:
                InitBuildTool(_eventData);
                break;
            case NGTradingManager.SellableItem.Catagory.Product:
                SetCardHidden(true);
                float pickupQuantity = 1;
                PickupSetup pickupSetup = new PickupSetup()
                {
                    m_quantity = pickupQuantity,
                    ProductData = m_nft.m_product,
                    //AAAm_productIndex = GameManager.Me.m_state.m_products.FindIndex(x => x == m_nft.m_product),
                    m_holder = GlobalData.Me.m_pickupsHolder,
                };
                //AAAif(pickupSetup.m_productIndex == -1) Debug.LogError("MGMFTTile - m_productIndex -1, no unique GameState_Product found");
                var product = NGReactPickupAny.Create(null, pickupSetup);
                m_heldObject = product;
                product.Internal_BeginDrag(_eventData, true);
                break;
            case NGTradingManager.SellableItem.Catagory.Material:
                SetCardHidden(true);
                var resource = ReactPickupPersistent.Create(null, m_nft.m_material, m_nft.m_quantity, GlobalData.Me.m_pickupsHolder);
                m_heldObject = resource;
                resource.Internal_BeginDrag(_eventData, true);
                break;
            case NGTradingManager.SellableItem.Catagory.Worker:
                SetCardHidden(true);
    //            var worker = NGManager.Me.CreateNGObject(NGMovingObject.NGWorker, Vector3.zero, 1, NGManager.Me.m_energyRestThreshhold, NGManager.Me.m_energyWorkThreshhold, 1, (int)NGMovingObject.STATE.HELD_BY_PLAYER);
                var worker = MAWorker.Create(MAWorkerInfo.WorkerTypeEnum.Worker, Vector3.zero);
                m_heldObject = worker;
                worker.m_isFromNFT = true;
                worker.Internal_BeginDrag(_eventData, true);
                break;
            case NGTradingManager.SellableItem.Catagory.Block:
                var uc = gameObject.GetComponentInChildren<NGPickupUpgradeCard>();
                if(uc == null)
                    uc = gameObject.AddComponent<NGPickupUpgradeCard>();
                uc.ActivateExternal(this, m_gift, m_flow);
                break;
            default:
                Debug.LogError($"{m_nft.m_catagory} not set up for the system yet");
                break;
        }

        return m_heldObject;
    }
    
    protected override void OnDragWhenActivated()
    {
        BuildingPlacementManager.Me.m_overideRay = GetScreenRay();
    }

    protected override void OnCardDraggedBackOverHolder()
    {
        BuildMode.CancelExternally();
    }

    public override void ActionCancelled(bool _lockCardUntilDragEvent = false)
    {
        base.ActionCancelled(_lockCardUntilDragEvent);
        
        BuildingPlacementManager.Me.ToggleBuild(false, m_nft.m_buildingPrefab, -1, 0f);
    }

    protected override bool IsPendingUserAction()
    {
        return BuildingPlacementManager.Me.IsConfirming;
    }

    void InitBuildTool(PointerEventData _eventData)
    {
        switch(m_nft.m_catagory)
        {
            case NGTradingManager.SellableItem.Catagory.Building:
                BuildingPlacementManager.Me.ToggleBuild (true, m_nft.m_buildingPrefab, m_nft.m_productDesign.GetId(true), 1.0f, BuildingPlaced, () => SetCardHidden(true), _eventData);
                BuildingPlacementManager.Me.SetNFTId(m_nft.m_partID);
                break;
            case NGTradingManager.SellableItem.Catagory.Decoration:
                break;
            case NGTradingManager.SellableItem.Catagory.Material:
                break;
            case NGTradingManager.SellableItem.Catagory.Product:
                break;
            case NGTradingManager.SellableItem.Catagory.Worker:
                break;

        }
    }
}
#endif
