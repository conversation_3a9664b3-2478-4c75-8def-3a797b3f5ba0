using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class ShowBuildingLevel : MonoBehaviour
{
    public TextMeshProUGUI m_text;
    private float m_endTimer = 0;

    void Update()
    {
        if (m_endTimer > 0)
        {
            m_endTimer -= Time.deltaTime;
            if (m_endTimer <= 0)
                DestroyMe();
        }
    }

    public void DestroyMe()
    {
        Destroy(gameObject);
    }

    void Activate(NGCommanderBase _building)
    {
        m_endTimer = 5.0f;
    }

    public static ShowBuildingLevel Create(NGCommanderBase _building)
    {
        var go = Instantiate(NGManager.Me.m_ShowBuildingLevelPrefab, _building.m_balloonHolder);
        go.transform.localPosition = new Vector3(0f, 2.5f, 0f);
        var se = go.GetComponent<ShowBuildingLevel>();
        se.Activate(_building);
        return se;
    }
}
