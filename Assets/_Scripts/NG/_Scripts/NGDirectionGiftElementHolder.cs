using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
#if OldBusinessFlow

public class NGDirectionGiftElementHolder : MonoBehaviour
{
    public Image m_giftIcon, m_starIcon;
    public GameObject m_currentIcon, m_arrowIcon;
    public Transform m_giftParent;
    public TextMeshProUGUI m_challengeText;
    public Color m_alphaColour;

    [SerializeField] private int m_cardCountToEnableHorizScrollRect = 5;

    NGBusinessDecision m_decision;
    List<NGBusinessGift> m_gifts;
    List<NGBusinessGift> m_giftChoices;
    NGBusinessFlow m_flow;
    void Activate(List<NGBusinessGift> _gifts, List<NGBusinessGift> _giftChoices, List<NGBusinessFlow> _flows, int _index)
    {
        m_gifts = _gifts;
        m_giftChoices = _giftChoices;
        m_flow = _flows[_index];

        bool isLastGift = m_flow.m_section == "End";

        if (!isLastGift)
        {
            // check some edge cases
            for (int i = m_flow.Index + 1; i < NGBusinessFlow.Flows.Count; i++)
            {
                var nextFlow = NGBusinessFlow.Flows[i];

                if (nextFlow.BusinessGiftsTakeAllInitial.Count > 0 || nextFlow.BusinessGiftsChooseInitial.Count > 0)
                {
                    if ((nextFlow.m_section == "Continue" || nextFlow.m_section == "End"))
                    {
                        break;
                    }

                    if (nextFlow.m_section == "Start")
                    {
                        isLastGift = true;
                        break;
                    }
                }
            }
        }

        bool isStar = isLastGift;
        bool isCurrent = m_flow == NGBusinessFlow.CurrentFlow;
        bool isAlpha = m_flow.Index >= 0;
//        bool isAlpha = m_flow.Index >= NGBusinessFlow.FlowIndex;

        m_giftIcon.gameObject.SetActive(!isStar);
        m_starIcon.gameObject.SetActive(isStar);
        m_giftIcon.color = isAlpha ? m_alphaColour : Color.white;
        m_currentIcon.SetActive(isCurrent);
        m_arrowIcon.SetActive(isCurrent);
        var challengeText = m_flow.Decision.GetTargetExplainText(m_flow);

        //Build 1 house
        //and finish 3rd or above in most sales
        // and enter a design competions
        foreach (var c in m_flow.Decision.m_challangeStatusList)
        {
            if (c.m_minRank > 0)  
                challengeText += $"\nFinish {DesignTableManager.NumberToPosition(c.m_minRank)} or above in {TownAwardGUI.GetReadableAwardName(c.m_name)}";
            else 
                challengeText += $"\nEnter {TownAwardGUI.GetReadableAwardName(c.m_name)}";
        }
        m_challengeText.text = challengeText;
        
     //   m_challengeText.text = m_flow.Decision.GetTargetExplainText(m_flow);

        for (int i = 0; i < m_gifts.Count; i++)
        {
            var gift = m_gifts[i];
            NGDirectionGiftElement.Create(m_giftParent, gift, m_flow);
        }
        for (int i = 0; i < m_giftChoices.Count; i++)
        {
            var gift = m_giftChoices[i];
            NGDirectionGiftElement.Create(m_giftParent, gift, m_flow);
        }

        ScrollRect horizontalScrollRect = GetComponentInChildren<ScrollRect>(true);
        if(horizontalScrollRect != null)
        {
            horizontalScrollRect.enabled = (m_gifts.Count + m_giftChoices.Count) >= m_cardCountToEnableHorizScrollRect;
        }
    }
    
    void Activate(NGBusinessDecision _decision)
    {
        m_decision = _decision;

        for(int i =0; i < m_decision.m_gifts.Count; i++)
        {
            var gift = m_decision.m_gifts[i];
            NGDirectionGiftElement.Create(m_giftParent, gift, m_flow);
        }
    }
    public static NGDirectionGiftElementHolder Create(Transform _holder, List<NGBusinessGift> _gifts, List<NGBusinessGift> _giftChoices, List<NGBusinessFlow> _flows, int _index)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_NGDirectionGiftElementHolderPrefab, _holder);
        var bd = go.GetComponent<NGDirectionGiftElementHolder>();
        bd.Activate(_gifts, _giftChoices, _flows, _index);
        return bd;
    }

    public static NGDirectionGiftElementHolder Create(Transform _holder, NGBusinessDecision _decision)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_NGDirectionGiftElementHolderPrefab, _holder);
        var bd = go.GetComponent<NGDirectionGiftElementHolder>();
        bd.Activate(_decision);
        return bd;
    }
}
#endif