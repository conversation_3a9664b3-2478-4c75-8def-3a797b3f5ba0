using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using DG.Tweening;

public class NGBusinessDecisionDialog : NGDecisionCardHolder<NGBusinessDecisionDialog>
{
    public List<IBusinessCard> m_NFTCards; //These need to be kept separate in the decision dialog so that when a decision gift is recieved, the dialog doesn't clear everything
    static public int m_maxRecievedGifts = 1;
    public TMP_Text m_levelNumber;
    public NGBusinessFlow m_flow;
    private bool m_wasHidden = false;
    public TMP_Text m_choiceNumber;
    public GameObject m_background;

    static public void SaveNFTCards(ref SaveContainers.SaveGame _s)
	{
        // no longer saving and loading NFT cards, they are re-generated on load
        /*var list = new List<SaveContainers.SaveNFT>();
        if (Me != null && Me.m_NFTCards.Count > 0)
        {
            foreach (var card in Me.m_NFTCards)
                list.Add(card.m_gift.m_nft.GetSaveNFT());
        }
        _s.m_saveNFTCards = list;*/
	}

    private static DebugConsole.Command s_refreshnfts = new DebugConsole.Command("refreshnfts", _s => RefreshNFTStates());
    public static void RefreshNFTStates()
    {
    }
    
    static public void LoadNFTCards(SaveContainers.SaveGame _l)
	{
        RefreshNFTStates();
        /*foreach(var nft in _l.m_saveNFTCards)
		{
            NGTradingManager.PlaceNFT(NGTradingManager.SellableItem.GetItemFromSave(nft));
		}*/
	}
    override public void GiftReceived(IBusinessCard _card)
    {/*
        base.GiftReceived(_card);
        
        if (_card.Gift.Type != NGBusinessGift.GiftType.NFT)
        {
            m_cards.Remove(_card);

            if (m_flow?.GiftRecieved(m_maxRecievedGifts) == true)
                RecievedMaxGifts();

            m_flow.RemoveGift(_card.Gift);
            m_flow.SendBusinessGiftInteractionAnalyticsEvent(_card, "Used");
        }
        else
        {
            m_NFTCards.Remove(_card);
            //m_flow.NFTRecieved(_card);
            m_flow.RemoveGift(_card.Gift);
        }
        Destroy(_card.gameObject);

        if (m_flow != null)
        {
            m_background.SetActive(m_flow.m_chooseMaxGifts < m_cards.Count + m_flow.m_recievedGifts);
            if (m_flow.m_chooseMaxGifts - m_flow.m_recievedGifts <= 0)
                m_choiceNumber.text = "";
            else
                m_choiceNumber.text = (m_flow.m_chooseMaxGifts - m_flow.m_recievedGifts).ToString() + "/" + m_cards.Count;
        }
        
        if (m_cards.Count + m_NFTCards.Count == 0)
            DestroyMe();*/
    }

    override public void DestroyMe()
    {
        if (s_lastBusinessDecisionDialog == this)
            s_lastBusinessDecisionDialog = null;
        base.DestroyMe();
    }
    

    public void RecievedMaxGifts()
	{
        foreach (var card in m_cards)
            Destroy(card.gameObject);
        m_cards.Clear();
	}

    public void AddCard(NGBusinessGift _gift)
    {/*
        var card = NGBusinessDecisionCard.Create(this, _gift, m_flow);
        if (_gift.Type == NGBusinessGift.GiftType.NFT)
            m_NFTCards.Add(card as IBusinessCard);
        else
            m_cards.Add(card as IBusinessCard);*/
    }

    override public void Activate()
    {
        Activate(m_direction, m_decision, m_flow, m_gifts);
        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySoundOld("PlaySound_WindowBubble_Small02", transform);
    }
    void Activate(NGBusinessDirection _direction, NGBusinessDecision _decision, NGBusinessFlow _flow, List<NGBusinessGift> _gifts)
    {
        base.Activate();
        s_lastBusinessDecisionDialog = this;
        m_levelNumber.text = NGBusinessDecisionManager.Me.PlayerLevel.ToString() ;
        SetUpForBusinessDecision(_direction, _decision, _flow, _gifts);
        if (GameManager.Me.IsOKToPlayUISound())
            AudioClipManager.Me.PlaySoundOld("PlaySound_WindowBubble_Small02", transform);

        SetAutoOpenTriggerForUnafordableCards();
    }

    public void SetUpForBusinessDecision(NGBusinessDirection _direction, NGBusinessDecision _decision, NGBusinessFlow _flow, List<NGBusinessGift> _gifts)
	{
        m_direction = _direction;
        m_decision = _decision;
        m_flow = _flow;
        m_gifts = _gifts;
        m_title.text = (m_decision == null) ? "NFT" : m_decision.GetGiftTitle();
        if (m_flow != null)
        {
            //m_background.SetActive(m_flow.m_chooseMaxGifts > 0 && m_flow.m_chooseMaxGifts < m_gifts.Count + m_flow.m_recievedGifts);
            //m_choiceNumber.text = (_flow.m_chooseMaxGifts - _flow.m_recievedGifts).ToString() + "/" + m_gifts.Count;
        }
        else
        {
            m_background.SetActive(false);
            m_choiceNumber.text = "";
        }
        
        foreach(var g in m_gifts)
        {
    //           if (g.Type == NGBusinessGift.GiftType.Unlock && NGUnlocks.IsUnlocked(g.m_cardPower))
    //               continue; this is breaking saved rewards if it happens to be an unlock card
            AddCard(g);
        }
    }

    public static NGBusinessDecisionDialog s_lastBusinessDecisionDialog;
    
    public static NGBusinessDecisionDialog Create(NGBusinessDirection _direction, NGBusinessDecision _decision)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_businessDecisionDialogPrefab, NGBusinessDecisionManager.Me.m_decisionGUIHolder);
        var bdd = go.GetComponent<NGBusinessDecisionDialog>();        
        bdd.Activate(_direction, _decision, null, _decision.m_gifts);
        return bdd;
    }

    public static NGBusinessDecisionDialog CreateForNFT(List<NGBusinessGift> _gifts)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_businessDecisionDialogPrefab, NGBusinessDecisionManager.Me.m_decisionGUIHolder);
        var bdd = go.GetComponent<NGBusinessDecisionDialog>();
        bdd.Activate(null, null, null, _gifts);
        return bdd;
    }

    protected override void OnHidden()
    {
        SetAutoOpenTriggerForUnafordableCards();
    }

	override public void Update()
	{
        base.Update();

        if(m_wasHidden != m_isHidden)
        {
            foreach(var card in m_cards)
                card.DisableInteraction(m_isHidden);
            foreach(var card in m_NFTCards)
                card.DisableInteraction(m_isHidden);

            m_wasHidden = m_isHidden;
        }
    }
}
