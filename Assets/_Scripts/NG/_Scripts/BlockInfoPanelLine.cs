using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class BlockInfoPanelLine : MonoBehaviour
{
    public TMP_Text m_key;
    public TMP_Text m_value;
    
    void Activate(string _key, string _value)
    {
        m_key.text = _key;
        m_value.text = _value;
    }

    public static BlockInfoPanelLine Create(BlockInfoPanelLine _prefab, Transform _holder, string _key, string _value)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var bipl = go.GetComponent<BlockInfoPanelLine>();
        bipl.Activate(_key, _value);
        return bipl;
    }
}
