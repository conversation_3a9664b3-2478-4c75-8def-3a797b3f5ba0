using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
#if OldBusinessFlow

public class NGDirectionGiftIcon : MonoBehaviour
{
    public TMP_Text m_title;
    public TMP_Text m_subTitle;
    public Image m_giftPortrait;
    NGBusinessGift m_gift;
    NGDirectionDialog m_dialog;
    NGBusinessFlow m_flow;
    // Start is called before the first frame update
    void Start()
    {
        
    }

    void Activate(NGDirectionDialog _dialog, NGBusinessFlow _flow)
    {
        m_dialog = _dialog;
        m_flow = _flow;
        var advisor = m_flow.BusinessAdvisor;
        m_giftPortrait.sprite = advisor.PortaitSprite;
        m_title.text = advisor.m_title;
        m_subTitle.text = m_flow.m_introMessage;
    }
    void Activate(NGDirectionDialog _dialog, NGBusinessGift _gift)
    {
        m_dialog = _dialog;
        m_gift = _gift;
        var advisor = NGDirectionCharacter.Find(m_gift.DirectionAdvisorName);
        if(advisor != null)
            m_giftPortrait.sprite = advisor.m_image;
        m_title.text = _gift.DirectionTitle;
        m_subTitle.text = m_gift.DirectionBodyText;
    }

    public void ClickedMe()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        NGDirectionAdvisorDialog.Create(m_flow);

    //    m_dialog.ClickedGift(m_gift); 
    }
    public static NGDirectionGiftIcon Create(NGDirectionDialog _dialog, Transform _holder, NGBusinessFlow _flow)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_NGGiftIconPrefab, _holder);
        var bd = go.GetComponent<NGDirectionGiftIcon>();
        bd.Activate(_dialog, _flow);
        return bd;
    }
    public static NGDirectionGiftIcon Create(NGDirectionDialog _dialog, Transform _holder, NGBusinessGift _gift)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_NGGiftIconPrefab, _holder);
        var bd = go.GetComponent<NGDirectionGiftIcon>();
        bd.Activate(_dialog, _gift);
        return bd;
    }
}
#endif