#if CHOICES
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
public class NGTutorialMessageDialog : NGTutorialMessageBase
{
    public TMP_Text m_message;

    void OnDestroy()
    {
        NGTownSatisfactionManager.Me.m_NGTownSatisfactionGUIHolder.gameObject.SetActive(true);
    }

    override protected void Activate(NGTutorial _line, string _preBarText, string _postBarText, string _trackFuncString, string _type, string _pose)
    {
        base.Activate(_line, _preBarText, _postBarText, _trackFuncString, _type, _pose);
        var message = _preBarText;
        if (_postBarText.IsNullOrWhiteSpace() == false)
            message += "\n" + _postBarText;
        m_message.text = message;

        NGTownSatisfactionManager.Me.m_NGTownSatisfactionGUIHolder.gameObject.SetActive(false);
    }
}
#endif
