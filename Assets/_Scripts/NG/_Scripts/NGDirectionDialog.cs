using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
#if OldBusinessFlow

public class NGDirectionDialog : MonoSingleton<NGDirectionDialog>
{
    public TMP_Text m_advisorName;
    public TMP_Text m_advisorTitle;
    public TMP_Text m_explainText;
    public Image m_advisorImage;
    public Button m_letsGoButton;
    public Transform m_giftHolder;
    bool m_isInMessage;

    public TMP_Text m_businessLevelTitle;
    public Image[] m_levelProgressBar;
    public TMP_Text[] m_levelNumber;

    NGBusinessDirection m_direction;
    NGBusinessFlow m_flow;
    List<NGBusinessGift> m_gifts;
    private int m_sound;

    struct BusinessLevelTitle
    {
        public string title;
        public int lowerRange;
        public int upperRange;
    }

    private BusinessLevelTitle[] m_businessLevelData;

    private void InitBLT()
    {
        m_businessLevelData = new BusinessLevelTitle[12];

        m_businessLevelData[0].title = "Startup";
        m_businessLevelData[0].lowerRange = 0;
        m_businessLevelData[0].upperRange = 5;

        m_businessLevelData[1].title = "Small Business";
        m_businessLevelData[1].lowerRange = 5;
        m_businessLevelData[1].upperRange = 15;

        m_businessLevelData[2].title = "Medium Business";
        m_businessLevelData[2].lowerRange = 15;
        m_businessLevelData[2].upperRange = 30;

        m_businessLevelData[3].title = "Large Business";
        m_businessLevelData[3].lowerRange = 30;
        m_businessLevelData[3].upperRange = 50;

        m_businessLevelData[4].title = "Corporation";
        m_businessLevelData[4].lowerRange = 50;
        m_businessLevelData[4].upperRange = 55;

        m_businessLevelData[5].title = "Multi National";
        m_businessLevelData[5].lowerRange = 75;
        m_businessLevelData[5].upperRange = 125;

        m_businessLevelData[6].title = "Conglomerate";
        m_businessLevelData[6].lowerRange = 125;
        m_businessLevelData[6].upperRange = 275;

        m_businessLevelData[7].title = "Emipre";
        m_businessLevelData[7].lowerRange = 275;
        m_businessLevelData[7].upperRange = 475;

 
        m_businessLevelData[8].title = "Mega Corp";
        m_businessLevelData[8].lowerRange = 875;
        m_businessLevelData[8].upperRange = 1600;
    }

    public void ClickedGift(NGBusinessGift _gift)
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        //       m_direction.ClickedGift(_gift);
        NGDirectionAdvisorDialog.Create(m_flow);
    }

    public void DestroyMe()
    {
        if (m_flow != null)
        {
//            m_flow.CloseShowMessage();
        }
        Destroy(gameObject);
    }

    void Activate(string _message, bool _closeFlag)
    {
        m_explainText.text = _message;
        m_giftHolder.DestroyChildren();
        m_letsGoButton.gameObject.SetActive(_closeFlag);

        InitBLT();
        ShowBusinessLevel();
        if (GameManager.Me.IsOKToPlayUISound())
            m_sound = AudioClipManager.Me.PlaySound("PlaySound_WindowBubble_Large01", transform);
    }

    private void ShowBusinessLevel()
    {
        if (NGPlayer.Me.m_businessStage < m_businessLevelData.Length)
        {
            m_businessLevelTitle.text = m_businessLevelData[NGPlayer.Me.m_businessStage].title;
        }
    /*    float level = NGBusinessDecisionManager.Me.PlayerLevel;

        int subLevel = (int)level - 2;
        if (subLevel < 0)
            subLevel = 0;

        foreach (var bld in m_businessLevelData)
        {
            if (bld.upperRange > level)
            {
                m_businessLevelTitle.text = bld.title;
                for (int i = 0; i < 4; i++) 
                {
                    float percentage = (level - bld.lowerRange)/(bld.upperRange - bld.lowerRange);

                    if (i + subLevel > level)
                        m_levelProgressBar[i].fillAmount = 0;
                    else if (i + subLevel < level)
                        m_levelProgressBar[i].fillAmount = 1;
                    else
                        m_levelProgressBar[i].fillAmount = percentage;

                    m_levelNumber[i].text = (subLevel + i).ToString();
                }
                break;
            }
        }*/
    }

    void Activate(NGBusinessFlow _flow, bool _justMessage)
    {
        m_flow = _flow;
        m_gifts = _flow.BusinessGifts;
        m_advisorName.text = _flow.BusinessAdvisor.FullName;
        m_advisorTitle.text = _flow.BusinessAdvisor.m_title;
        m_explainText.text = _flow.m_message;
        m_advisorImage.sprite = _flow.BusinessAdvisor.PortaitSprite;
        SetupAdvisors(_justMessage);

        InitBLT();
        ShowBusinessLevel();
        if (GameManager.Me.IsOKToPlayUISound())
            m_sound = AudioClipManager.Me.PlaySound("PlaySound_WindowBubble_Large01", transform);
    }

    void SetupAdvisors(bool _justMessage = false)
    {
        m_giftHolder.DestroyChildren();
   //     foreach(var nf in m_flow.NextFlow)
   //     { 
   //         NGDirectionGiftIcon.Create(this, m_giftHolder, nf);
    //        m_letsGoButton.gameObject.SetActive(false);
     //   }
    }
    void SetupGifts(bool _justMessage = false)
    {
        m_giftHolder.DestroyChildren();
        if (m_gifts.Count > 0 && _justMessage == false)
        {
            m_letsGoButton.gameObject.SetActive(false);
            foreach (var gift in m_gifts)
            {
                NGDirectionGiftIcon.Create(this, m_giftHolder, gift);
            }
            m_giftHolder.GetComponent<HorizontalLayoutGroup>().enabled = true;
        }
        else
            m_letsGoButton.gameObject.SetActive(true);
    }
    void Activate(NGBusinessDirection _direction, bool _isInMessage)
    {
        m_direction = _direction;
        m_isInMessage = _isInMessage;
        m_gifts = m_direction.m_gifts;
        var advisor = NGDirectionCharacter.Find(m_direction.m_advisor);
        if(advisor == null) { Debug.LogError($"Cannot Find advisor {m_direction.m_advisor}"); return; }
        m_advisorName.text = advisor.FullName;
        m_advisorTitle.text = advisor.m_title;
        m_explainText.text = (m_isInMessage) ? _direction.m_inMessage : _direction.m_outMessage;
        m_advisorImage.sprite = advisor.m_image;
        m_giftHolder.DestroyChildren();
        SetupGifts();

        InitBLT();
        ShowBusinessLevel();
        if (GameManager.Me.IsOKToPlayUISound())
            m_sound = AudioClipManager.Me.PlaySound("PlaySound_WindowBubble_Large01", transform);
    }

    //Only shown if there are no gifts
    public void ClickedGo()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        if (m_direction != null)
            m_direction.ClickedGo();
        if (m_flow != null)
        {
            if (m_flow.m_tutorialPhase.IsNullOrWhiteSpace() == false)
            {
                if (NGTutorialManager.Me.TutorialPhaseHasBeenPlayed(m_flow.m_tutorialPhase) == false)
                    NGTutorialManager.Me.TutorialRestarted(m_flow.m_tutorialPhase, m_flow, null);
            }
        }

        if (GameManager.Me.IsOKToPlayUISound())
            m_sound = AudioClipManager.Me.PlaySound("PlaySound_ConfirmClick2", transform);
        NGTutorialInterface.Me.m_businessFlowOkayPressed = true;
        NGTutorialManager.Me.FireExternalTrigger();
        DestroyMe();
    }

    public static NGDirectionDialog Create(NGBusinessFlow _flow, bool _justMessage)
    {
        var prefab = NGBusinessDecisionManager.Me.m_NGDirectionDialogInPrefab;
        var go = Instantiate(prefab.gameObject, NGBusinessDecisionManager.Me.m_NGDirectionDialogHolder);
        var bd = go.GetComponent<NGDirectionDialog>();
        bd.Activate(_flow, _justMessage);
        return bd;
    }

    public static NGDirectionDialog Create(NGBusinessDirection _direction, bool _isInMessage)
    {
        var prefab = (_isInMessage) ? NGBusinessDecisionManager.Me.m_NGDirectionDialogInPrefab : NGBusinessDecisionManager.Me.m_NGDirectionDialogOutPrefab;
        var go = Instantiate(prefab.gameObject, NGBusinessDecisionManager.Me.m_NGDirectionDialogHolder);
        var bd = go.GetComponent<NGDirectionDialog>();
        bd.Activate(_direction, _isInMessage);
        return bd;
    }
    public static NGDirectionDialog Create(string _message, bool _closeFlag)
    {
        var prefab = NGBusinessDecisionManager.Me.m_NGDirectionDialogInPrefab;
        var go = Instantiate(prefab.gameObject, NGBusinessDecisionManager.Me.m_NGDirectionDialogHolder);
        var bd = go.GetComponent<NGDirectionDialog>();
        bd.Activate(_message, _closeFlag);
        return bd;
    }
}
#endif