using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class NGTreelike : NGDecoration, IPointerDownHandler, IBeginClickHoldHandler
{
    public GameObject m_chopPrefab;
    public string m_chopAudioHook;
    public GameObject m_completePrefab;
    public string m_completeAudioHook;
    public TransQuad m_progressTransQuadPrefab;
    public TransQuad m_completeTransQuadPrefab;
    public int m_treeIndex = -1;
    public string m_treeName;
    public string m_treeType;
    BoxCollider m_collider;
    Bounds m_originalBounds;
    private int m_harvestLoop;
    override protected void Awake() {
        TreeHolder.Register(m_treeIndex, gameObject);
        m_collider = GetComponent<BoxCollider>();
        if (m_collider == null)
            m_collider = AddBoxCollider(0.4f, out m_originalBounds);
        else
            m_originalBounds = new Bounds(m_collider.center, m_collider.size);
    }

    void OnDestroy() => TreeHolder.Deregister(m_treeIndex);

    public void OnPointerDown(PointerEventData _data) {
        if (_data.button == PointerEventData.InputButton.Left && !GameManager.IsVisitingInProgress)
            BeginTreeDestruction();
    }
    
    const float c_initialSecondsToDestroy = 2f;
    const float c_lowestSecondsToDestroy = .6f;
    const float c_secondsToDestroyImprovementPerSecond = .4f;
    const float c_timeBetweenEffects = .25f;
    static float s_secondsToDestroy;
    static float s_nextEffectTime = 0;
    static HashSet<NGTreelike> s_treeDeconstrusctionsInProgress = new HashSet<NGTreelike>();
    void BeginTreeDestruction() {
        s_secondsToDestroy = c_initialSecondsToDestroy;
        s_nextEffectTime = Time.time + c_timeBetweenEffects;
        if (TerrainManager.Me.m_ribbon != null) TerrainManager.Me.m_ribbon.m_active = false;
        TerrainManager.Me.m_ribbon = MouseRibbon.Create(TerrainManager.Me.transform, new Color(.2f, .4f, .1f, 1f), 15);
        GameManager.Me.StartCoroutine(Co_TreeDestruction());
    }
    void SpawnEffectPrefab(GameObject _prefab) {
        var go = Instantiate(_prefab);
        go.transform.position = transform.position + Vector3.up * transform.localScale.y * m_originalBounds.size.y * .85f;
        go.AddComponent<ParticleSystemDestroyOnFinish>();
    }
    TransQuad m_transQuadInstance = null;
    const float c_transQuadSpeed = .3f;
    const float c_transQuadRaise = .4f;
    public bool ContinueTreeDestruction() {
        bool res = true;

        if (m_harvestLoop == 0)
        {
            m_harvestLoop = AudioClipManager.Me.PlaySoundOld("PlaySound_HarvestLoop_Tree", transform);
        }

        s_secondsToDestroy = Mathf.Max(c_lowestSecondsToDestroy, s_secondsToDestroy - c_secondsToDestroyImprovementPerSecond * Time.deltaTime);
        if (m_transQuadInstance == null && m_progressTransQuadPrefab != null) {
            m_transQuadInstance = Instantiate(m_progressTransQuadPrefab);
            m_transQuadInstance.transform.position = transform.position + Vector3.up * c_transQuadRaise;
        }
        if (m_transQuadInstance != null) m_transQuadInstance.m_speed = .7f / s_secondsToDestroy;
        if (!s_treeDeconstrusctionsInProgress.Contains(this)) s_treeDeconstrusctionsInProgress.Add(this);
        if (Time.time > s_nextEffectTime) {
            s_nextEffectTime = Time.time + c_timeBetweenEffects;
            if (m_chopPrefab != null) SpawnEffectPrefab(m_chopPrefab);
//            if (!string.IsNullOrEmpty(m_chopAudioHook)) AudioClipManager.Me.PlaySoundAndForget(m_chopAudioHook, gameObject);
        }
        var scale = transform.localScale.y;
        scale -= Time.deltaTime / s_secondsToDestroy;
        if (scale < .01f) 
        {
            StopTreeDestruction();
            scale = 0;
            if (GetComponentInParent<TerrainPopulation>() != null)
                TerrainPopulation.Me.DisableInstancesInRange(transform.position - Vector3.one * .1f, transform.position + Vector3.one * .1f);
            else
                DestroyMe();
            SpawnReward();
            //NGTutorialManager.Me.m_treeChopped = true; //to enable tutorial after chopping down a tree
            if (m_completePrefab != null) SpawnEffectPrefab(m_completePrefab);
//            if (!string.IsNullOrEmpty(m_completeAudioHook)) AudioClipManager.Me.PlaySoundAndForget(m_completeAudioHook, gameObject);
            res = false;
        }
        float invScale = (scale < .01f) ? 100f : 1f / scale;
        float xzScale = .75f + scale * (1f - .75f);
        float invXZScale = 1f / xzScale;
        transform.localScale = new Vector3(xzScale, scale, xzScale);
        m_collider.size = Vector3.Scale(m_originalBounds.size, new Vector3(invXZScale, invScale, invXZScale));

        SetInstanceScale(scale);
        
        return res;
    }

    public void CopyDataFrom(NGTreelike _from)
    {
        if (_from == null) return;
        m_chopPrefab = _from.m_chopPrefab;
        m_completePrefab = _from.m_completePrefab;
        m_progressTransQuadPrefab = _from.m_progressTransQuadPrefab;
        m_completeTransQuadPrefab = _from.m_completeTransQuadPrefab;
        m_chopAudioHook = _from.m_chopAudioHook;
        m_completeAudioHook = _from.m_completeAudioHook;
    }

    void SetInstanceScale(float _scale)
    {
        TreeHolder.SetInstanceHeightScale(m_treeIndex, _scale);
    }

    void OnDisable()
    {
        SetInstanceScale(0);
    }

    public void StopTreeDestruction() 
    {
        if (m_harvestLoop != 0)
        {
            AudioClipManager.Me.StopSound(m_harvestLoop, gameObject);
            m_harvestLoop = 0;
        }

        if (m_transQuadInstance != null) m_transQuadInstance.m_speed = -c_transQuadSpeed;
    }
    public void ResetTreeDestruction() {
        //s_secondsToDestroy = Mathf.Min(c_initialSecondsToDestroy, s_secondsToDestroy + c_secondsToDestroyImprovementPerSecond * Time.deltaTime);
        s_nextEffectTime = Time.time + c_timeBetweenEffects;
    }
    public void TidyTreeDestruction()
    {
        if (m_collider == null) return;
        m_collider.size = m_originalBounds.size;
    }
    void SpawnReward() {
        if (m_completeTransQuadPrefab != null) {
            var complete = Instantiate(m_completeTransQuadPrefab);
            complete.transform.position = transform.position + Vector3.up * (c_transQuadRaise * .9f);
            complete.m_speed = 2;
            foreach (var t in complete.GetComponentsInChildren<TMPro.TextMeshProUGUI>()) t.text = $"{m_resourceQuantity:n0}";
        }
        if (m_resourceQuantity > 0) {
            var o = ReactPickupPersistent.Create(null, NGCarriableResource.GetInfo(m_resourceType), m_resourceQuantity, GlobalData.Me.m_pickupsHolder, false, (_o) => {
                _o.GetComponent<NGMovingObject>().SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
                _o.AddComponent<Pickup>();
            });
            var source = transform.position + Vector3.up * 3f;
            var randomDirection = Random.Range(-180.0f, 180.0f) * Mathf.Deg2Rad;
            var direction = new Vector3(Mathf.Sin(randomDirection), 0, Mathf.Cos(randomDirection));
            const float c_throwDistance = 3f, c_throwTime = .4f;
            var target = source + direction * c_throwDistance;
            var velocity = Global3D.GetVelocityRequiredForPointToPointOverTime(source, target, c_throwTime);
            o.GetComponent<Rigidbody>().linearVelocity = velocity;
            o.transform.position = source;
        }
    }
    IEnumerator Co_TreeDestruction() {
        NGTreelike lastHitTree = null;
        bool isFirst = true;

        while (Input.GetMouseButton(0)) {
            RaycastHit hit;
            NGTreelike hitTree = null;
            if (GameManager.Me.RaycastAtPoint(Input.mousePosition, out hit)) {
                if (DistrictManager.Me.IsWithinDistrictBounds(hit.collider.transform.position, true)) {
                    hitTree = hit.collider.gameObject.GetComponent<NGTreelike>();
                    if (hitTree != null) 
                    {
                        if (hitTree.ContinueTreeDestruction()) 
                        {
                            lastHitTree = hitTree;
                        } 
                        else 
                        {
                            lastHitTree = null;
                        }         
                    }
                }
            }
            if (hitTree != lastHitTree && lastHitTree != null) {
                lastHitTree.StopTreeDestruction();
            }
            if (isFirst && hitTree == null) break;
            isFirst = false;
            if (hitTree == null) {
                ResetTreeDestruction();
            }
            yield return null;
        }

        if (lastHitTree != null) lastHitTree.StopTreeDestruction();
        foreach (var dec in s_treeDeconstrusctionsInProgress) {
            dec.TidyTreeDestruction();
        }
        CleanupRibbon();
    }
    void CleanupRibbon() {
        if (TerrainManager.Me.m_ribbon != null) {
            TerrainManager.Me.m_ribbon.m_active = false;
            TerrainManager.Me.m_ribbon = null;
        }
    }
}

public class ParticleSystemDestroyOnFinish : MonoBehaviour {
    void Start() {
        StartCoroutine(Co_DestroyOnFinish());
    }
    IEnumerator Co_DestroyOnFinish() {
        var psys = GetComponentInChildren<ParticleSystem>();
        while (psys.particleCount == 0) yield return null;
        while (psys.particleCount > 0) yield return null;
        Destroy(gameObject);
    }
}
