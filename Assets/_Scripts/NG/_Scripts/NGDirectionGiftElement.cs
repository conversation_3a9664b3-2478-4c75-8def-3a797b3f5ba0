using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class NGDirectionGiftElement : MonoBehaviour, INGDecisionCardHolder
{
    public bool DragIn3D { get { return true; } }
    public INGDecisionCardHolder.ECardView CardView { get { return INGDecisionCardHolder.ECardView.Standard; } }
    public Transform Root { get { return this.transform; } }
    public bool ShowOrderBoardGUIOnCardClick { get { return true; } }
    public Transform m_iconHolder;
    public TMP_Text m_detailText;
    NGBusinessGift m_gift;
    MAParserSection m_maParserSection;
    
    RectTransform m_rt;
    // Update is called once per frame
    void Activate(NGBusinessGift _gift, MAParserSection _maParserSection)
    {
        m_rt = GetComponent<RectTransform>();
        m_gift = _gift;
        m_maParserSection = _maParserSection;
        m_detailText.text = m_gift.m_giftTitle;
        var card = NGBusinessDecisionCard.Create(this, _gift, _maParserSection);
        card.DisableInteraction(true);//stop the cards being dragged in this incarnation
        card.Disable();
    }

    public Transform ParentForDraggingCard(NGDirectionCardBase _card) { return null; }
    public bool EnableCardOnUpwardDrag() { return true; }
    public Transform GetParentHolder(NGDirectionCardBase _card) { return null; }
    
    public static NGDirectionGiftElement Create(Transform _holder, NGBusinessGift _gift, MAParserSection _maParserSection)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_NGDirectionGiftElementPrefab, _holder);
        var bd = go.GetComponent<NGDirectionGiftElement>();
        bd.Activate(_gift, _maParserSection);
        return bd;
    }
    
    //These are needed for this to hold NGBusinessDecisionCards, but they'll never actually be used
    public void OnCardClick() {}
    
    public void ToggleHiding(bool _hide, bool _isDueToClick = false)
    {
        
    }

    public void OnStartCardDrag() {}
    public void OnEndCardDrag() {}
    
    public bool IsHidden { get; }

    public void GiftReceived(IBusinessCard _card)
    {
        
    }

    public Transform GiftsHolder()
    {
        return m_iconHolder;
    }
}
