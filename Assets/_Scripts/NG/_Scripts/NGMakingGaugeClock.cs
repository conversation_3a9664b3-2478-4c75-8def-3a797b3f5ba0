#if false
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.PlayerLoop;
using UnityEngine.UI;
public class NGMakingGaugeClock : NGMakingGaugeBase
{
	public Vector2 m_touchOffset;

	public Transform m_mecanicsHolder;

	public TMP_Text m_message;
	public Image m_messageBackground;
	public TMP_Text m_title;
	public TMP_Text m_level;
	public Transform m_tapCounterHolder;
	public Image m_machine;
	//public PDMMakingGaugePip m_pipPrefab;

	
	[Header("Mover")]
	public Image m_inputProductImage; 
	public Image m_inputImageAlpha;
	public TMP_Text m_inputStock;
	public Image m_InputAnimImage;

	[Header("Ghost")]
	public Image m_inputProductImageGhost; 
	public Image m_inputImageAlphaGhost;
	public TMP_Text m_inputStockGhost;
	public Image m_InputAnimImageGhost;

	public Image InputProductImage
	{
		get => m_inputProductImage;
		set { m_inputProductImage = value; m_inputProductImage = value; }
	}
	public Image InputImageAlpha
	{
		get => m_inputImageAlpha;
		set { m_inputImageAlpha = value; m_inputImageAlphaGhost = value; }
	}
	public string InputStock
	{
		get => m_inputStock.text;
		set { m_inputStock.text = value; m_inputStockGhost.text = value; }
	}
	public float InputStockFontSize
	{
		get => m_inputStock.fontSize;
		set { m_inputStock.fontSize = value; m_inputStockGhost.fontSize = value; }
	}
	
	public Image InputAnimImage
	{
		get => m_InputAnimImage;
		set { m_InputAnimImage = value; m_InputAnimImageGhost = value; }
	}	
	
	public Image m_outputProductImage;
	public Image m_outputImageAlpha;
	public TMP_Text m_outputStock;
	
	public TMP_Text m_debugProductScore;
	public GameObject m_tipHolder;
	public TMP_Text m_tip;
	public bool m_showDebug;
	public float m_minStockFont = 16;
	public float m_maxStockFont = 32;
	public TMP_Text m_excessOutput;
	public Image m_tapBar;
	public bool m_isUsingBar;
	public Image m_background;
	public Image m_tipBackground;
	public RectTransform m_inputHolderRotationHolder;
	public RectTransform m_inputHolderChild;
	public RectTransform m_factoryShowStockHolder;
	public GameObject m_NGMakingGaugeSegmentPrefab;
	public Transform m_segmentHolder;
	public ParticleSystem m_madeOneParticle;

    private bool m_isMine;
    private bool m_isDock;
    private bool m_isBank;
	private bool m_isDispatch;
	private bool m_isRoadSite;

    // the current gauge fill is .09 - .091
    private float ClickPower => 0f;
	private int NumTapsToMake => (int)(1f / ClickPower * 0);
	private float m_previousProductScore;
	
	private Animator m_anim;

	override protected void Update()
	{
		base.Update();
		if (m_building as MABuilding)
			MAUpdate();
		else
			NGUpdate();
	}

	void MAUpdate()
	{
		/*var MABuilding = m_building as MABuilding;
		var components = null;
		if(components.Count == 0) return;
		var tapComponent = components[0]; //if a building has > 1 Tap then we should average the blocks 
		if (tapComponent == null) return;
		if (tapComponent.IsMaking == false && NGMakingGaugeManager.Me.m_isStickyGuage == false)
		{
			if (m_destroying == false)
				StartDestroy();			
		}
		var combo = tapComponent.CalculateProduceScoreMultiplier(true);
		m_combo.text = $"x{combo:n1}";
		float productScore = m_building.ToProductScore;
		m_tipHolder.SetActive(!Pickup.IsLongHold);
		m_tip.text = GetTipText();
		if (m_isUsingBar)
		{
			float barFill = Mathf.Min(1f, productScore);
			m_tapBar.fillAmount = barFill;
		}
		else
		{
			// var pips = m_tapCounterHolder.GetComponentsInChildren<PDMMakingGaugePip>();
			//
			// int numPipsToActivate = (int)(productScore / (ClickPower / m_building.DesignTapsToMake));
			// for (int i = pips.Length - 1; i >= 0; i--)
			// {
			// 	pips[i].Show(i < numPipsToActivate);
			// }
		}

		var gis = MABuilding.GetInputStock();
		if (gis.Items.Count > 0)
		{
			InputStock = $"{m_building.InputsAre.GetLowestStock()} / {m_building.MaxInputs:F0}";
		}
		else
		{
			InputStock = "";
		}
		var ratio = Mathf.Clamp(MABuilding.InputsAre.GetLowestStock() / m_building.MaxInputs, 0f, 1f);
		if (!m_isMine && !m_isBank)
			InputStockFontSize = Mathf.Lerp(m_minStockFont, m_maxStockFont, ratio);
		
		m_outputStock.text = $"{MABuilding.NumOutputs:F0} / {MABuilding.MaxOutputs}";
		ratio = Mathf.Clamp(MABuilding.NumOutputs / MABuilding.MaxOutputs, 0f, 1f);
		m_outputStock.fontSize = Mathf.Lerp(m_minStockFont, m_maxStockFont, ratio);

		InputProductImage.fillAmount = 1f - productScore;
			
		m_outputProductImage.fillAmount = productScore;
//		m_debugProductScore.gameObject.SetActive(m_showDebug);
//		m_debugProductScore.text = $"{ClickPower:F2} / {productScore:F2}";

		//Do the Rotation
		var source = Quaternion.Euler(0, 0, -0.001f);
		var destination = Quaternion.Euler(0, 0, -180);
		var newAngle = Quaternion.Slerp(source, destination, Mathf.Clamp(productScore, 0f, 1f));

		m_inputHolderRotationHolder.transform.rotation = newAngle;
		m_inputHolderChild.transform.rotation = Quaternion.identity;
		
		m_previousProductScore = productScore;*/
	}
	void NGUpdate() 
	{
		/*if (m_building.IsMaking == false && NGMakingGaugeManager.Me.m_isStickyGuage == false)
		{
			if (m_destroying == false)
				StartDestroy();
			return;
		}
		var combo = m_building.CalculateProduceScoreMultiplier(true);
		m_combo.text = $"x{combo:n1}";

		float productScore = m_building.ToProductScore;

		m_tipHolder.SetActive(!Pickup.IsLongHold);
		m_tip.text = GetTipText();

        if (m_isUsingBar)
		{
			float barFill = Mathf.Min(1f, productScore);
			m_tapBar.fillAmount = barFill;
		}
		else
		{
			// var pips = m_tapCounterHolder.GetComponentsInChildren<PDMMakingGaugePip>();
			//
			// int numPipsToActivate = (int)(productScore / (ClickPower / m_building.DesignTapsToMake));
			// for (int i = pips.Length - 1; i >= 0; i--)
			// {
			// 	pips[i].Show(i < numPipsToActivate);
			// }
		}
		if (m_building.InputsAre.Items.Count > 0)
		{
			InputStock = $"{m_building.InputsAre.GetLowestStock()} / {m_building.MaxInputs:F0}";
		}
        else
        {
			InputStock = "";
		}
		var ratio = Mathf.Clamp(m_building.InputsAre.GetLowestStock() / m_building.MaxInputs, 0f, 1f);
		if (!m_isMine && !m_isBank)
			InputStockFontSize = Mathf.Lerp(m_minStockFont, m_maxStockFont, ratio);
		
		m_outputStock.text = $"{m_building.NumOutputs:F0} / {m_building.MaxOutputs}";
		ratio = Mathf.Clamp(m_building.NumOutputs / m_building.MaxOutputs, 0f, 1f);
		m_outputStock.fontSize = Mathf.Lerp(m_minStockFont, m_maxStockFont, ratio);

		if (m_isMine || m_isBank)
		{
			InputProductImage.fillAmount = (float)m_building.InputsAre.GetLowestStock() / m_building.MaxInputs;
		}
		else
		{
			InputProductImage.fillAmount = 1f - productScore;
		}
			
		m_outputProductImage.fillAmount = productScore;
		m_debugProductScore.gameObject.SetActive(m_showDebug);
		m_debugProductScore.text = $"{ClickPower:F2} / {productScore:F2}";

		//Do the Rotation
		var source = Quaternion.Euler(0, 0, -0.001f);
		var destination = Quaternion.Euler(0, 0, -180);
		var newAngle = Quaternion.Slerp(source, destination, Mathf.Clamp(productScore, 0f, 1f));

		m_inputHolderRotationHolder.transform.rotation = newAngle;
		m_inputHolderChild.transform.rotation = Quaternion.identity;
		
		m_previousProductScore = productScore;*/
	}

	protected override bool ReadyToDestroy()
	{
		return !m_anim.GetCurrentAnimatorStateInfo(0).IsName("MakeOne");
	}

	override public void ShowNew(Type _type, string _message, bool _showDetails = false)
	{
		base.ShowNew(_type, _message, _showDetails);
		SetPosition();
		m_message.gameObject.SetActive(true);
		m_message.text = _message;
		m_messageBackground.gameObject.SetActive(true);

	}
	override public void Activate(NGCommanderBase _building, int _inputID)
	{
		base.Activate(_building, _inputID);
		gameObject.SetActive(true);

		m_title.text = m_building.GetBuildingTitle();
		var level = ClickPower / 0.1f;
		m_level.text = $"Level {level:F0}";
		var phrt = m_tapCounterHolder.GetComponent<RectTransform>();
		var pos = m_building.GaugePosition;
		transform.position = new Vector3( transform.position.x, pos.y, transform.position.z);
		var numTaps = (1 / ClickPower);
		m_tapCounterHolder.DestroyChildren();
		m_excessOutput.gameObject.SetActive(false);
		if (numTaps >= 1f)
		{
			if (numTaps <= NGManager.Me.m_maxMakingPips)
			{
				m_tapCounterHolder.gameObject.SetActive(true);

				// var width = 0f;
				// for (int i = 0; i < (int)numTaps; i++)
				// {
				// 	var pip = PDMMakingGaugePip.Create(m_pipPrefab, m_tapCounterHolder);
				// 	var h = pip.GetComponent<RectTransform>();
				// 	width += h.sizeDelta.x * h.localScale.x;
				// }
			}
			else
			{
				m_isUsingBar = true;
				m_tapBar.gameObject.SetActive(true);
				m_tapBar.fillAmount = m_building.ToProductScore;
			}
		}
		else
		{
			m_tapCounterHolder.gameObject.SetActive(false);
			m_excessOutput.gameObject.SetActive(true);
			m_excessOutput.text = $"x {numTaps:F1}";
		}
		m_factoryShowStockHolder.gameObject.SetActive(false);
		if (m_building.Name.Equals("NGFactory"))
		{
			m_factoryShowStockHolder.gameObject.SetActive(true);
			m_inputImageAlpha.enabled = false;
			m_inputProductImage.enabled = false;
			m_factoryShowStockHolder.DestroyChildren();
			//ShowStockGUI.Create(m_building as NGFactory, m_factoryShowStockHolder, true);
		}
        /*m_isMine = _building is NGMine;
        m_isDock = _building is NGDock;
        m_isBank = _building is NGBank;
        m_isDispatch = _building is NGDispatch;
        m_isRoadSite = _building is NGRoadBuildingSite;*/

        if (m_isMine || m_isDock || m_isDispatch)
		{
			InputProductImage.sprite = NGMakingGaugeManager.Me.GetMineSprite(m_building);
			m_inputProductImageGhost.sprite = InputProductImage.sprite;
			m_inputImageAlphaGhost.sprite  = InputProductImage.sprite;
		}
		else
		{
			//GameManager.Me.GetCarriableResourceSprite(_building.InputsAre.GetLowestStockItem(), _building, _s => { InputProductImage.sprite = _s; m_inputProductImageGhost.sprite = _s; m_inputImageAlphaGhost.sprite = _s; });
		}
		InputImageAlpha.sprite = InputProductImage.sprite;
		if (m_isBank || m_isRoadSite)
		{
            m_outputProductImage.sprite = NGMakingGaugeManager.Me.GetMineSprite(m_building);
		}
		else
		{
			GameManager.Me.GetCarriableResourceSprite(NGCarriableResource.GetInfo("None"), _building, _s => m_outputProductImage.sprite = _s);
		}
		m_outputImageAlpha.sprite = m_outputProductImage.sprite;
		InputAnimImage.sprite = InputProductImage.sprite;
		SetPosition();
		m_anim = GetComponent<Animator>();
		if (m_NGMakingGaugeSegmentPrefab)
		{
			for (int i = 0; i < numTaps; i++)
			{
				var score = i / numTaps;
				var source = Quaternion.Euler(0, 0, -0.001f);
				var destination = Quaternion.Euler(0, 0, -180);
				var newAngle = Quaternion.Slerp(source, destination, Mathf.Clamp(score, 0f, 1f));
				var go = Instantiate(m_NGMakingGaugeSegmentPrefab, m_segmentHolder);
				m_inputHolderRotationHolder.transform.rotation = newAngle;
				m_inputHolderChild.transform.rotation = Quaternion.identity;
			}
		}
		m_previousProductScore = m_building.ToProductScore;
		Update();
	}

	private static void DestroyActive(int _inputID)
	{
		Instance(_inputID)?.StartDestroy();
	}

	private static void DisableActive(int _inputID)
	{
		Instance(_inputID)?.StartDestroy();
	}

	override public void MadeAProduct(float _score)
	{
		if(m_anim)
			m_anim.SetTrigger("MakeOne");
	}
}
#endif