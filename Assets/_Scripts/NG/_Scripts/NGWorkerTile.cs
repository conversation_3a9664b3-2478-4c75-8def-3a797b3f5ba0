using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class NGWorkerTile : NGDirectionCardBase
{
    public TMP_Text m_genderText;
    override public string DragAudio => "PlaySound_WorkerCard_Take";
    override public string DragCancelAudio => "PlaySound_WorkerCard_Return";
    override public string DragAssignedAudio => "PlaySound_WorkerCard_Release";
    public override bool ScaleCardDownOnDrag { get { return true; } }
    private string m_workerInfoName;

    public override List<(string title, string info)> GetInfoStats()
    {
        var workerInfo = GetWorkerInfo();
        if(workerInfo == null) return null;
        
        List<(string title, string info)> info = new ();
        info.Add(("Cost To Hire", $"{GlobalData.CurrencySymbol}{Cost:F0}"));
        info.Add(("Persona", workerInfo.m_persona));
        info.Add(("Speed", NGCardInfoGUI.GetSpeed(workerInfo.m_lowWalkSpeed, workerInfo.m_highWalkSpeed)));
        info.Add(("Production Speed", NGCardInfoGUI.GetProductionSpeed(workerInfo.m_productionPower)));
        info.Add(("Likes", workerInfo.m_likes));
        info.Add(("Dislikes", workerInfo.m_dislikes));
        return info;
    }

    protected override void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _cardHolder)
    {
        base.Activate(_gift, _maParserSection, _cardHolder);
        
        m_title.text = _gift.m_cardTitle;
        m_workerInfoName = _gift.m_power;
        
        var info = GetWorkerInfo();
        if(info != null) m_genderText.text = info.m_gender;
        else m_genderText.text = "";
        
        var sprite = _gift.GetSprite;
        if(sprite != null)
            m_image.sprite = sprite;
    }

    private bool HaveFreeBedroom()
    {
        int totalBedroomSlots = 0;
        foreach(var building in NGManager.Me.m_maBuildings)
        {
            if(building.GetFreeWorkerBedrooms() > 0)
                return true;
        }
        return false;
    }
    
    private bool HaveFreeJob()
    {
        int totalJobSlots = 0;
        foreach(var building in NGManager.Me.m_maBuildings)
        {
            if(building.GetFreeWorkerSlots() > 0)
                return true;
        }
        return false;
    }

    protected override CardUnavailableReason GetCardUnavailableReason()
    {
        var worker = m_dragActivatedObject as MACharacterBase;
        
        if(HaveFreeBedroom() == false && (worker == null || worker.Home == null))
            return CardUnavailableReason.NotEnoughBedroomSlots;
        if(HaveFreeJob() == false && (worker == null || worker.Job == null))
            return CardUnavailableReason.NotEnoughJobSlots;
        return base.GetCardUnavailableReason();
    }

    override public void GiveReward()
    {
        AlignmentManager.Me.ApplyAction("EmployWorker", 0.002f, "");
        var worker = m_dragActivatedObject as MAWorker;
        worker.AddCharacterToLists();
        worker.CharacterGameState.m_dayHired = DayNight.Me.CurrentWorkingDay;
        
        PayGiftCost();
        base.GiveReward();
        MAParserSupport.TryParse(m_gift.m_cardPower, out var power);
        BCActionTavern.PauseUpdateTime = 15;
        GameManager.Me.m_state.m_gameStats.m_births.Increment(1);
    }
    
    public override void ActionCancelled(bool _lockCardUntilDragEvent = false)
    {
        base.ActionCancelled(_lockCardUntilDragEvent);
    }

    protected override void OnCardDraggedBackOverHolder()
    {
        
    }

    protected override bool IsValidDrag(PointerEventData _eventData)
    {
        var worker = m_dragActivatedObject as MACharacterBase;
        if(worker == null)
            return false;
        
        var target = worker.GetComponent<Pickup>().LastTarget;
        if(target == null) 
            return false;
        return target.GetComponent<BuildingCardHolderSegmentSlot>() == null && target.GetComponent<MABuilding>() != null;
    }
    
    public MAWorkerInfo GetWorkerInfo()
    {
        return MAWorkerInfo.GetInfo(m_gift.m_power);
    }

    override protected NGMovingObject DragActivate(PointerEventData _eventData)
    {
        // m_worker = NGManager.Me.CreateNGObject(NGMovingObject.NGWorker, Vector3.zero, 1, NGManager.Me.m_energyWorkThreshhold, NGManager.Me.m_energyWorkThreshhold, 1, (int)NGMovingObject.STATE.HELD_BY_PLAYER);
        var pos = PlayerHandManager.Me.Fingertip.position + Vector3.down * 3f;
        var worker = MAWorker.Create(m_workerInfoName, pos, false);
        var rot = Quaternion.LookRotation((Camera.main.transform.position - worker.transform.position).GetXZ(), Vector3.up);
        worker.transform.rotation = rot;
        worker.Internal_BeginDrag(_eventData, true, _eventData.clickCount);
        return worker;
    }
}
