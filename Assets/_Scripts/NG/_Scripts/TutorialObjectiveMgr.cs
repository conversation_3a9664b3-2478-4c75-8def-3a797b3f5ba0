using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;

public class TutorialObjectiveMgr : MonoBehaviour
{
    public TMP_Text m_objective;
    public Image m_progressBar;
    public TMP_Text m_progressText;
    public TMP_Text m_maxProgressText;
    public RectTransform m_textHandle;
    public Animator m_animator;
    
    private int m_minSteps;
    private int m_maxSteps;
    private bool m_dtMode;

    public void Initialise(string _name, int _minSteps, int _maxSteps, bool _m)
    {
        m_minSteps = _minSteps;
        m_maxSteps = _maxSteps;
        m_dtMode = _m;

        m_objective.text = _name;
        m_progressBar.fillAmount = Mathf.Clamp01(0);
        if (m_dtMode)
        {
//            m_progressText.text = "0/" + m_minSteps + " to " + m_maxSteps;
            m_progressText.text = _minSteps.ToString();
        }
        else
        {
            m_progressText.text = "0%";
        }
        m_maxProgressText.text = _maxSteps.ToString();
        
        m_objective.text = _name;
        UpdateSteps(m_minSteps);

    }

    public void UpdateSteps(float _cost)
    {
        if (m_dtMode)
        {
            if (m_animator)
            {
                m_animator.SetTrigger("AddPart");
            }
            var percent = (_cost - m_minSteps) / (m_maxSteps - m_minSteps);
            m_progressText.text = _cost.ToString();
            m_progressText.gameObject.SetActive(percent < 1f);

            var x = m_textHandle.rect.width * percent;
            m_progressText.rectTransform.anchoredPosition = new Vector2(x, m_progressText.rectTransform.anchoredPosition.y);
            m_progressBar.fillAmount = percent;

/*            if (_cost == 0)
            {
                m_progressBar.fillAmount = 0f;
                m_progressBar.color = Color.cyan;
            }

            if (_cost > m_maxSteps)
            {
                m_progressBar.fillAmount = 1f;
                m_progressBar.color = Color.red;
            }
            else if (_cost >= m_minSteps)
            {
                m_progressBar.fillAmount = 1f;
                m_progressBar.color = Color.green;
            }
            else
            {
                m_progressBar.fillAmount = _cost / (float)m_minSteps;
                m_progressBar.color = Color.cyan;
            }

            m_progressText.text = (int)_cost + "/" + m_minSteps + " to " + m_maxSteps;
            */
        }
        else
        {
            m_progressBar.fillAmount = _cost / 100f;
            m_progressBar.color = Color.cyan;
            m_progressText.text = (int)_cost + "%";
        }
    }
}
