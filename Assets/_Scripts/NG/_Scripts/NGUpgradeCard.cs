using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
using UnityEngine.EventSystems;

public class NGUpgradeCard : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IBegin<PERSON>rag<PERSON><PERSON><PERSON>, IEndDragHandler, IPointerClickHandler
{
    [System.Serializable]
    public class PowerIcons
    {
        public string m_PropertyName;
        public Sprite m_sprite;
    }
    public TMP_Text m_titleBasic;
    public TMP_Text m_titleDetails;
    public TMP_Text m_price;
    public Image m_image;
    public Transform m_cardBasic;
    public Transform m_cardDetails;
    public Transform m_detailsLines;
    public Transform m_powerIconsHolder;
    public TMP_Text m_detailsLinePrefab;
    public Image m_powerIconImagePrefab;
    [SerializeField] float m_maxDisplacedYOffset = 16f;

    NGPickupUpgradeCard m_pickupCard;
    NGBusinessGift m_gift;
    NGBusinessDecisionCard m_fromCard;
    NGBusinessFlow m_flow;
    Vector3 m_originalPosition;
    public List<PowerIcons> m_powerIcons;
    void Start()
    {
        m_originalPosition = transform.localPosition;
    }

    public void ShowDetailsCard(NGCommanderBase _building, NGMovingObject _object)
    {
        transform.localEulerAngles = new Vector3(0f, 0f, 90f);
        m_titleDetails.text = _building.GetBuildingTitle();
        m_cardBasic.gameObject.SetActive(false);
        m_cardDetails.gameObject.SetActive(true);
        var details = m_gift.GetUpgradeStrings(_building, $"<color=#009000>+");
        m_detailsLines.DestroyChildren();
        foreach(var d in details)
        {
            var go = Instantiate(m_detailsLinePrefab.gameObject, m_detailsLines);
            var txt = go.GetComponentInChildren<TMP_Text>();
            txt.text = d;
        }
    }
    public void ShowBasicCard()
    {
        transform.localEulerAngles = new Vector3(0f, 0f, 0f);
        m_cardBasic.gameObject.SetActive(true);
        m_cardDetails.gameObject.SetActive(false);
        m_powerIconsHolder.DestroyChildren();
        foreach(var l in m_gift.m_upgrades)
        {
            var powerIcon = m_powerIcons.Find(o => o.m_PropertyName == l.m_field);
            if(powerIcon != null)
            {
                var go = Instantiate(m_powerIconImagePrefab.gameObject, m_powerIconsHolder);
                go.GetComponent<Image>().sprite = powerIcon.m_sprite;
            }
        }
    }

 
    bool MoveTile(float _yDiff = 0f)
    {
        bool overCardTop = false;
        
        Vector3 movement = new Vector3(0f, _yDiff, 0f);
        Vector3 newPos = m_originalPosition + new Vector3(0f, _yDiff, 0f);
        if (newPos.y >= m_maxDisplacedYOffset)
            overCardTop = true;
        newPos.y = Mathf.Clamp(newPos.y, m_originalPosition.y, m_originalPosition.y + m_maxDisplacedYOffset);
        transform.localPosition = newPos;
        
        return overCardTop;
    }

    void DragActivate(PointerEventData _eventData)
    {
        var go = Instantiate(gameObject);
        var script = go.GetComponent<NGUpgradeCard>();
        script.Activate(m_gift, m_flow, m_fromCard);
        if (script) script.enabled = false;
       // m_pickupCard = NGPickupUpgradeCard.Create(m_gift, script, _eventData.position);
        
        m_pickupCard.Internal_BeginDrag(_eventData, true);
    }

    bool m_hasDragStarted;
    void IBeginDragHandler.OnBeginDrag(PointerEventData _eventData)
    {
        m_hasDragStarted = true;
    }

    void IDragHandler.OnDrag(PointerEventData _eventData)
    {
        if (!m_hasDragStarted)
            return;
        if (MoveTile(_eventData.position.y - _eventData.pressPosition.y) && m_pickupCard == null)
            DragActivate(_eventData);
    }

    void IEndDragHandler.OnEndDrag(PointerEventData _eventData)
    {
        if(m_pickupCard != null)
        {
            if (EventSystem.current.IsPointerOverGameObject())
            {
                var l = GetGUIRaycast();
                if (l.Find(o => o == m_fromCard || o == this.gameObject))
                {
                    CancelDraggedCard();
                }
            }
            else if (m_pickupCard.m_applyToCommander == null)
            {
                CancelDraggedCard();
            }
        }
    }

    void CancelDraggedCard()
    {
        Destroy(m_pickupCard.gameObject);
        transform.localPosition = m_originalPosition;
        m_pickupCard = null;
    }

    List<GameObject> GetGUIRaycast()
    {
        var pointerEventData = new PointerEventData(EventSystem.current);
        pointerEventData.position = Input.mousePosition;

        var raycastResults = new List<RaycastResult>();
        var results = new List<GameObject>();
        EventSystem.current.RaycastAll(pointerEventData, raycastResults);
        foreach(var r in raycastResults)
        {
            if (r.gameObject != null)
                results.Add(r.gameObject);

        }
        return results;
    }

    void IPointerClickHandler.OnPointerClick(PointerEventData eventData)
    {

    }

    public void Activate(NGBusinessGift _gift, NGBusinessFlow _flow, NGBusinessDecisionCard _fromCard)
    {
        m_fromCard = _fromCard;
        m_gift = _gift;
        m_flow = _flow;
        m_titleBasic.text = _gift.m_giftTitle;
        m_titleDetails.text = _gift.m_cardTitle;
        m_image.enabled = true;
        m_image.sprite = _gift.GetSprite;
        var cost = (m_flow != null) ? m_flow.m_giftCostMultiplier * m_gift.CardPrice : m_gift.CardPrice;
        m_price.text = "$" + cost.ToString("F0");
        ShowBasicCard();
    }

    public static NGUpgradeCard Create(NGUpgradeCard _prefab, Transform _holder, NGBusinessGift _gift, NGBusinessFlow _flow, NGBusinessDecisionCard _fromCard)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var uc = go.GetComponent<NGUpgradeCard>();
        uc.Activate(_gift, _flow, _fromCard);
        return uc;

    }
}

