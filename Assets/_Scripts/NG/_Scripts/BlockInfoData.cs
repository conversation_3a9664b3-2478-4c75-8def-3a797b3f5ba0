using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class BlockInfoData : MonoBehaviour
{
    public TMP_Text m_name;
    public TMP_Text m_rarity;
    public TMP_Text m_workerTime;
    public TMP_Text m_tapsToMake;
    public TMP_Text m_materials;
    public TMP_Text m_priceEffect;
    public TMP_Text m_marketEffect;
//    public TMP_Text m_timesUsed;

    public string Name { set { m_name.text = value; } }
    public string Rarity { set { m_rarity.text = value; } }
    public string WorkerTimeToMake { set { m_workerTime.text = value; } }
    public string TapsToMake { set { m_tapsToMake.text = value; } }
    public string Materials { set { m_materials.text = value; } }
    public string PriceEffect { set { m_priceEffect.text = value; } }
    public string MarketEffect { set { m_marketEffect.text = value; } }
//    public string TimesUsed { set { m_timesUsed.text = value; } }
}
