using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Reflection;
using System;

[System.Serializable]
public class NGBusinessGift
{
    public enum GiftType
    {
        None = -1,
        Building,
        UpgradeBuilding,
        DirectionGift,
        Unlock,
        Currency,
        Decoration,
        NFT,
        UnlockNewDispatchVehicle,
        UpgradeDispatchVehicleCapacity,
        Block,
        ChoiceCharacter,
        Command,
        Worker,
        Order,
        Hero,
        ParserCall,
        Last
    }
    [System.Serializable]
    public class UpgradeReflection
    {
        public const string c_operators = "*+-/";
        public enum ShowAs
        {
            Percent,
            Number,
            Interger,
            Permili,
            Perdeci
        }
        public string m_field;
        public string m_operator;
        public string m_value;
        public ShowAs m_showAs;
    }
    public string id;
    public bool m_debugChanged;

    public virtual void SetupContent(NGDirectionCardBase _card) { }
    
    public string m_name;
    public string m_type;
    public string m_giftTitle;
    public string m_cardTitle;
    public string m_cardPower;
    public string m_power;
    public float m_cardPrice;
    public float m_quantity;
    public string m_spritePath;
    public string m_description;
    public string m_buildingDesign;
    private const string c_imagePath = "_Art/Sprites/BusinessRewards/";
    public bool m_canBePutInVault;
    public string m_giftFunction;
    public string m_rewardFunction;
    public bool m_fromFlow = true;
    public float CardPrice 
    {
        get
        {
            switch (Type)
            {
                case GiftType.Building:
                    return m_cardPrice;
                    
                    case GiftType.Worker:
                        return m_cardPrice + (GetWorkerCount() * MAWorkerInfo.GetInfo(m_power).m_tavernCostMultiplier * m_cardPrice);
                    case GiftType.Hero:
                        return m_cardPrice + (GetHeroCount() * MACreatureInfo.GetInfo(m_power).m_tavernCostMultiplier * m_cardPrice);
            }
            return m_cardPrice;
        }
    }
    
    private int GetWorkerCount()
    {
        var character = NGDirectionCardBase.DraggingCard?.DragActivatedObject as MAWorker;
        
        if(character != null && NGManager.Me.m_MAWorkerList.Contains(character))
        {
            return NGManager.Me.m_MAWorkerList.Count - 1;
        }
        return NGManager.Me.m_MAWorkerList.Count;
    }
    
    private int GetHeroCount()
    {
        var character = NGDirectionCardBase.DraggingCard?.DragActivatedObject as MAHeroBase;
        
        if(character != null && NGManager.Me.m_MAHeroList.Contains(character))
        {
            return NGManager.Me.m_MAHeroList.Count - 1;
        }
        return NGManager.Me.m_MAHeroList.Count;
    }
    
    public Sprite GetSprite
    {
        get
        {
            string path = Utility.PathSimplify(c_imagePath + m_spritePath);
            var sprite = ResManager.Load<Sprite>(path);
            if (sprite == null)
            {
                Debug.LogError($"Not loading in sprite from Resources - check image is available at {path}.");
            }
            return sprite;
        }
    }


    //public NGTradingManager.SellableItem m_nft;
    private GiftType m_cachedType;
    private string m_cachedTypeSet = null;
    public GiftType Type { get
        {
            if (m_cachedTypeSet == m_type) return m_cachedType;
            var t = GiftType.None;
            Enum.TryParse(m_type, out t);
            m_cachedType = t;
            m_cachedTypeSet = m_type;
            return t;
        }
        set { 
            m_type = value.ToString();
            m_cachedType = value;
            m_cachedTypeSet = m_type;
        }
    }
    public string DirectionAdvisorName => m_cardPower;
    public string GetCardPowerParts
    {
        get
        {
            if (m_cardPower.IsNullOrWhiteSpace()) return string.Empty;
            var titles = "";
            var parts = m_cardPower.Split('\n', '|', ';');
            foreach (var part in parts)
            {
                if (part.IsNullOrWhiteSpace()) continue;
                var block = NGBlockInfo.GetInfo(part.Trim());
                if(block == null) continue;
                titles+= $"{block.m_displayName}, ";
            }
            return titles.Trim().TrimEnd(',');
        }
    }
    public string UnlockWhat => m_cardPower;
    public string DirectionBodyText => m_giftTitle;
    public string DirectionTitle => m_cardTitle;

    //UpgradeBuilding
    [ScanField] public string m_buildingsToUpgrade;
    [ScanField] public string m_componentsToUpgrade;
    public List<string> m_buildingsToUpgradeList = new List<string>();
    public List<MAComponentInfo> m_componentsToUpgradeInfos = new List<MAComponentInfo>();
    public List<UpgradeReflection> m_upgrades = new List<UpgradeReflection>();
    public string m_upgradeImports;
    public List<string> m_upgradeImportList = new List<string>();
    public static List<NGBusinessGift> s_gifts = new();
    public static List<NGBusinessGift> GetList => s_gifts;
    public string DebugDisplayName => m_name;
    public static NGBusinessGift GetNonFlowGift(string _name)
    {
        var gift = NGBusinessGift.s_gifts.Find(o => o.m_name.Equals(_name));
        if(gift == null) return null;
        return CreateFromSave(gift.GetSaveGift());
    }
    
    public static bool PostImport(NGBusinessGift _what)
    {
        if(_what.m_upgradeImports.IsNullOrWhiteSpace() == false)
        {
            var split = _what.m_upgradeImports.Split(new char[] { '|', ';' }, StringSplitOptions.RemoveEmptyEntries);
            _what.m_upgradeImportList.AddRange(split);
        }
        
        _what.DecodeUpgradeImports("NGBusinessGift", _what.m_name);
        
        if(_what.m_buildingsToUpgrade.IsNullOrWhiteSpace() == false)
        {
            var split = _what.m_buildingsToUpgrade.Split(';', '|', '\n', ',');
            _what.m_buildingsToUpgradeList.AddRange(split);
        }
        
        if(_what.m_componentsToUpgrade.IsNullOrWhiteSpace() == false)
        {
            string[] split = _what.m_componentsToUpgrade.Split(';', '|', '\n', ',');
            foreach(string componentName in split)
            {
                _what.m_componentsToUpgradeInfos.Add(MAComponentInfo.GetInfo(componentName));
            }
        }
        
#if UNITY_EDITOR
        if (!string.IsNullOrEmpty(_what.m_buildingDesign))
        {
            if (DesignTableManager.Me)
            {
                
            // check that the building design is consistent
                DesignTableManager.Me.CheckDesignConsistency(_what.m_buildingDesign, true, (_different, _newDesign) =>
                {
                    if (_different)
                    {
                        DesignTableManager.Me.CheckDesignConsistency(_newDesign, true, (_differentNew, _newNewDesign) =>
                        {
                            if (_differentNew)
                                Debug.LogError($"Gift building design persistent inconsistency: {_what.m_name}\nOld: {_what.m_buildingDesign}\nNew: {_newDesign}\nNewNew: {_newNewDesign}");
                            else
                                Debug.LogError($"Gift building design inconsistency: {_what.m_name}\nOld: {_what.m_buildingDesign}\nNew: {_newDesign}");
                        });
                    }
                });
            }
        }
#endif

        return true;
    }

    public static List<NGBusinessGift> LoadInfo()
    {
        s_gifts = NGKnack.ImportKnackInto<NGBusinessGift>(PostImport);

        return s_gifts;
    }

    public static NGBusinessGift GetInfo(string _id)
    {
        return s_gifts.Find(o => o.m_name.Equals(_id, StringComparison.OrdinalIgnoreCase));
    }
   
    float GetUpgradeValue(string _operator, float _fieldValue, float _upgradeValue)
    {
        switch (_operator)
        {
            case "*":
                return _fieldValue * _upgradeValue;
            case "+":
                return _fieldValue + _upgradeValue;
            case "-":
                return _fieldValue - _upgradeValue;
            case "/":
                return _fieldValue /= _upgradeValue;
            default:
                Debug.LogError($"Gift {m_giftTitle} has a illegal operator of {_operator}");
                return _fieldValue;
        }
    }

    public float CastObjToNum(System.Type _type, object _obj) //C.L. - Todo: refactor more elegantly
    {
        if (_obj.GetType() == typeof(float))
            return (float)_obj;
        if (_obj.GetType() == typeof(int))
            return (float)(int)_obj;
        var str = (string)_obj;
        if (_type == typeof(int))
            return (float)int.Parse(str);
        else
            return str.ToFloatInv();
    }
    public SaveContainers.SaveNGGift GetSaveGift()
    {
        return new SaveContainers.SaveNGGift
        {
            m_name = m_name,
            m_type = (int)Type,
            m_giftName = m_giftTitle,
            m_cardTitle = m_cardTitle,
            m_cardPower = m_cardPower,
            m_power = m_power,
            m_cardPrice = m_cardPrice,
            m_quantity = m_quantity,
            m_spritePath = m_spritePath,
            m_buildingsToUpgrade = m_buildingsToUpgradeList,
            m_buildingDesign = m_buildingDesign,
            m_description = m_description,
            m_giftFunction = m_giftFunction,
            m_rewardFunction = m_rewardFunction,
            m_canBePutInVault = m_canBePutInVault,
            m_upgradeImports = m_upgradeImports,
            m_upgradeImportList = m_upgradeImportList,
            m_upgradeReflectionFields = SaveUpgrades(),
            m_NFT = null,
            m_flow = -1
        };
    }
#if OldBusinessFlow

    public SaveContainers.SaveNGGift GetSaveGift(NGBusinessFlow _flow)
	{
        SaveContainers.SaveNFT saveNFT = null;
        if (Type == GiftType.NFT)
            saveNFT = m_nft.GetSaveNFT();
        return new SaveContainers.SaveNGGift
        {
            m_name = m_name,
            m_type = (int)Type,
            m_giftName = m_giftTitle,
            m_cardTitle = m_cardTitle,
            m_cardPower = m_cardPower,
            m_power = m_power,
            m_cardPrice = m_cardPrice,
            m_quantity = m_quantity,
            m_spritePath = m_spritePath,
            m_buildingsToUpgrade = m_buildingsToUpgradeList,
            m_buildingDesign = m_buildingDesign,
            m_description = m_description,
            m_tutorialTrigger = m_tutorialTrigger,
            m_canBePutInVault = m_canBePutInVault,
            m_upgradeImports = m_upgradeImports,
            m_upgradeImportList = m_upgradeImportList,
            m_upgradeReflectionFields = SaveUpgrades(),
            m_NFT = saveNFT,
            m_flow = NGBusinessFlow.Flows.IndexOf(_flow)
        };
	}
#endif
    public List<string> SaveUpgrades()
	{
        var list = new List<string>();
        foreach (var upgrade in m_upgrades)
            list.Add($"{upgrade.m_field}|{upgrade.m_operator}|{upgrade.m_value}|{(int)upgrade.m_showAs}");
        return list;
	}
    public static NGBusinessGift CreateFromSave(SaveContainers.SaveNGGift _saveGift)
	{
        var gift = new NGBusinessGift
        {
            m_name = _saveGift.m_name,
            Type = (GiftType)_saveGift.m_type,
            m_giftTitle = _saveGift.m_giftName,
            m_cardTitle = _saveGift.m_cardTitle,
            m_cardPower = _saveGift.m_cardPower,
            m_power = _saveGift.m_power,
            m_cardPrice = _saveGift.m_cardPrice,
            m_quantity = _saveGift.m_quantity,
            m_spritePath = _saveGift.m_spritePath,
            m_buildingsToUpgradeList = _saveGift.m_buildingsToUpgrade,
            m_description = _saveGift.m_description,
            m_buildingDesign = _saveGift.m_buildingDesign,
            m_giftFunction = _saveGift.m_giftFunction,
            m_rewardFunction = _saveGift.m_rewardFunction,
            m_canBePutInVault = _saveGift.m_canBePutInVault,
            m_upgradeImports = _saveGift.m_upgradeImports,
            m_upgradeImportList = _saveGift.m_upgradeImportList,
            //m_nft = NGTradingManager.SellableItem.GetItemFromSave(_saveGift.m_NFT),
            m_fromFlow = _saveGift.m_flow != -1
        };
        gift.RestoreUpgrades(_saveGift.m_upgradeReflectionFields);
        return gift;
	}

    public void RestoreUpgrades(List<string> reflectionFields)
	{
        if (reflectionFields == null)
            return;
        foreach(var str in reflectionFields)
		{
            var fields = str.Split(new char[] { '|' }, StringSplitOptions.RemoveEmptyEntries);
            m_upgrades.Add(new UpgradeReflection { m_field = fields[0], m_operator = fields[1],
                m_value = fields[2], m_showAs = (UpgradeReflection.ShowAs)int.Parse(fields[3]) });
		}
	}
    public void ApplyGiftToBuilding(object _building)
    {
        var cost = CardPrice;
        NGPlayer.Me.m_cash.Spend(cost, $"Upgrade card: {m_giftTitle}", m_giftTitle, "Upgrade card");
        foreach (var upgrade in m_upgrades)
        {
            var field = ReactReflection.GetProperty(_building, upgrade.m_field);
            if (field == null)
            {
                Debug.LogError($"Gift {m_giftTitle} has an invalid field of {upgrade.m_field}");
                continue;
            }
            var type = _building.GetType();
            float fieldValue = CastObjToNum(type, field.GetValue(_building));
            var upgradeValue = CastObjToNum(field.PropertyType, upgrade.m_value);
            upgradeValue = GetUpgradeValue(upgrade.m_operator, fieldValue, upgradeValue);
            var changeBack = Convert.ChangeType(upgradeValue, field.PropertyType);
            field.SetValue(_building, changeBack);
        }
	}
    public List<float> GetUpgradeCardCurrentData(object _building)
    {
        var results = new List<float>();
        foreach (var upgrade in m_upgrades)
        {
            var property = ReactReflection.GetProperty(_building, upgrade.m_field);
            if (property == null) { Debug.LogError($"Gift={m_giftTitle} {_building.ToString()}:: no such field as {upgrade.m_field}"); continue; }
            var fieldRawValue = property.GetValue(_building);
            float fieldValue = (float)Convert.ChangeType(fieldRawValue, typeof(float));
            float upgradeValue = CastObjToNum(property.PropertyType, upgrade.m_value);
            var showValue = GetUpgradeValue(upgrade.m_operator, fieldValue, upgradeValue);
            var showFieldName = upgrade.m_field.MakeNice();
            var change = showValue - fieldValue;
            var plusMinus = (change >= 0f) ? "+" : "";

            float value = 0;

            switch (upgrade.m_showAs)
            {
                case UpgradeReflection.ShowAs.Number:
                    value = fieldValue;
                    break;
                case UpgradeReflection.ShowAs.Perdeci:
                    value = fieldValue * 10f;
                    break;
                case UpgradeReflection.ShowAs.Percent:
                    value = fieldValue * 10f;
                    break;
                case UpgradeReflection.ShowAs.Permili:
                    break;
                case UpgradeReflection.ShowAs.Interger:
                    break;
            }
            results.Add(value);
        }
        return results;
    }
    public List<string> GetUpgradeData(object _building)
    {
        int showStartValue;
        var results = new List<String>();
        foreach (var upgrade in m_upgrades)
        {
            var property = ReactReflection.GetProperty(_building, upgrade.m_field);
            if (property == null) { Debug.LogError($"Gift={m_giftTitle} {_building.ToString()}:: no such field as {upgrade.m_field}"); continue; }
            var fieldRawValue = property.GetValue(_building);
            float fieldValue = (float)Convert.ChangeType(fieldRawValue, typeof(float));
            float upgradeValue = CastObjToNum(property.PropertyType, upgrade.m_value);
            var showValue = GetUpgradeValue(upgrade.m_operator, fieldValue, upgradeValue);
            var showFieldName = upgrade.m_field.MakeNice();
            var change = showValue - fieldValue;
            var plusMinus = (change >= 0f) ? "+" : "";

            string showString = "";
            
            switch (upgrade.m_showAs)
            {
                case UpgradeReflection.ShowAs.Number:
                    showStartValue = (int)(fieldValue);
                    change *= 10f;
                    showString = $"{showStartValue.ToString()}";
                    break;
                case UpgradeReflection.ShowAs.Perdeci:
                    showStartValue = (int)(fieldValue * 10f);
                    change *= 10f;
                    showString = $"{showStartValue.ToString()}";
                    break;
                case UpgradeReflection.ShowAs.Percent:
                    showStartValue = (int)(fieldValue * 10f);
                    change *= 10f;
                    showString = $"{showStartValue.ToString()}";
                    break;
                case UpgradeReflection.ShowAs.Permili:
                    showString = $"{(showValue * 1000).ToString("F1")}";
                    break;
                case UpgradeReflection.ShowAs.Interger:
                    showString = $"{showValue.ToString("F0")}";
                    break;
            }
            results.Add(showString);
        }
        return results;
    }
    public List<string> GetUpgradeStrings(object _building, string plusMinus)
    {
        int showStartValue;
        var results = new List<String>();
        foreach (var upgrade in m_upgrades)
        {
            var property = ReactReflection.GetProperty(_building, upgrade.m_field);
            if (property == null) { Debug.LogError($"Gift={m_giftTitle} {_building.ToString()}:: no such field as {upgrade.m_field}"); continue; }
            var fieldRawValue = property.GetValue(_building);
            float fieldValue = (float)Convert.ChangeType(fieldRawValue, typeof(float));
            float upgradeValue = CastObjToNum(property.PropertyType, upgrade.m_value);
            var showValue = GetUpgradeValue(upgrade.m_operator, fieldValue, upgradeValue);
            var showFieldName = upgrade.m_field.MakeNice();
            var change = showValue - fieldValue;

            string showString = "";
            switch (upgrade.m_showAs)
            {
                case UpgradeReflection.ShowAs.Number:
                    showStartValue = (int)(fieldValue);
                    showString = $"{showFieldName}: Level {showStartValue.ToString()} {plusMinus}{change.ToString("F0")}";
                    break;
                case UpgradeReflection.ShowAs.Perdeci:
                    showStartValue = (int)(fieldValue * 10f);
                    change *= 10f;
                    change += 0.5f;
                    int c = (int)change;
                    showString = $"{showFieldName}: Level {showStartValue.ToString()} {plusMinus}{c.ToString()}";
                    break;
                case UpgradeReflection.ShowAs.Percent:
                    showStartValue = (int)(fieldValue * 10f);
                    change *= 10f;
                    showString = $"{showFieldName}: Level {showStartValue.ToString()} {plusMinus}{change.ToString("F0")}";
                    break;
                case UpgradeReflection.ShowAs.Permili:
                    showString = $"{showFieldName}: {(showValue * 1000).ToString("F1")} {plusMinus}{(change * 1000).ToString("F1")}";
                    break;
                case UpgradeReflection.ShowAs.Interger:
                    showString = $"{showFieldName}: {showValue.ToString("F0")} {plusMinus}{change.ToString("F0")}";
                    break;
            }
            results.Add(showString);
        }
        return results;
    }
    public List<string> GetGiftStrings(object _building)
    {
        var results = new List<String>();
        foreach (var upgrade in m_upgrades)
        {
            var property = ReactReflection.GetProperty(_building, upgrade.m_field);
            if (property == null) { Debug.LogError($"Gift={m_giftTitle} {_building.ToString()}:: no such field as {upgrade.m_field}"); continue; }
            var fieldRawValue = property.GetValue(_building);
            var fieldValue = (float)Convert.ChangeType(fieldRawValue, typeof(float));
            float upgradeValue = CastObjToNum(property.PropertyType, upgrade.m_value);
            var showValue = GetUpgradeValue(upgrade.m_operator, fieldValue, upgradeValue);
            var showFieldName = upgrade.m_field.MakeNice();
            var change = showValue - fieldValue;
            var plusMinus = (change >= 0f) ? "+" : "";
            string showString = "";
            switch (upgrade.m_showAs)
            {
                case UpgradeReflection.ShowAs.Number:
                    showString = $"{showFieldName}: {showValue.ToString("F1")}({plusMinus}{change.ToString("F1")})";
                    break;
                case UpgradeReflection.ShowAs.Perdeci:
                    showString = $"{showFieldName}: {showValue.ToString("F1")}({plusMinus}{change.ToString("F1")})";
                    break;
                case UpgradeReflection.ShowAs.Percent:
                    showString = $"{showFieldName}: {(showValue * 100).ToString("F1")}({plusMinus}{(change * 100).ToString("F1")})";
                    break;
                case UpgradeReflection.ShowAs.Permili:
                    showString = $"{showFieldName}: {(showValue * 1000).ToString("F1")}({plusMinus}{(change * 1000).ToString("F1")})";
                    break;
                case UpgradeReflection.ShowAs.Interger:
                    showString = $"{showFieldName}: {showValue.ToString("F0")}({plusMinus}{change.ToString("F0")})";
                    break;
            }
            results.Add(showString);
        }
        return results;
    }
    public void DecodeUpgradeImports(string _fileName, string _row)
    {
        if (m_upgradeImportList == null || m_upgradeImportList.Count == 0) return;
        foreach (var u in m_upgradeImportList)
        {
            var upgradeRefelection = new UpgradeReflection();
            var typeSplit = u.Split('(', ')');
            var line = "";
            if (typeSplit.Length == 3)
            {
                if (Enum.TryParse(typeSplit[1], out upgradeRefelection.m_showAs) == false)
                    Debug.LogError($"Type Split of {typeSplit[1]} no found");
                line = typeSplit[2];
            }
            else
                line = typeSplit[0];
            bool foundOperator = false;
            foreach (var c in UpgradeReflection.c_operators)
            {
                var splits = line.Split(c);
                if (splits.Length > 1)
                {
                    upgradeRefelection.m_field = splits[0];
                    upgradeRefelection.m_operator = c.ToString();
                    upgradeRefelection.m_value = splits[1];
                    foundOperator = true;
                    break;
                }
            }
            if (foundOperator == false)
            {
                Debug.LogError($"{_fileName} row {_row} invalid expression: '{line}'");
            }
            else
            {
                m_upgrades.Add(upgradeRefelection);
            }
        }
    }

    public NGBusinessDirection GetFirstDirection(out string _advisorName)
    {
        var colonSplit = DirectionAdvisorName.Split(':', '|');
        _advisorName = colonSplit[0].Trim();
        float partNumber = -1f;
        if (colonSplit.Length > 1)
        {
            var partSplit = colonSplit[1].Split(' ', '>');
            if (partSplit.Length > 1) float.TryParse(partSplit[1].Trim(), out partNumber);
            else float.TryParse(partSplit[0].Trim(), out partNumber);
        }
        var character = NGDirectionCharacter.Find(_advisorName);
        if (character == null) { Debug.LogError($"Advisor '{DirectionAdvisorName}' not found"); return null; }
        var direction = character.CurrentDirection;
        if (partNumber != -1f)
        {
            for (int i = 0; i < character.m_directions.Count; i++)
            {
                if (character.m_directions[i].m_partNumber == partNumber)
                {
                    return character.m_directions[i];
                }
            }
            Debug.LogError($"Invalid Part {partNumber} in {m_cardPower}");
            return null;
        }
        return direction;
    }

    public static NGBusinessGift CreateNewHeroGift(string _creatureInfo)
    {
        var creatureInfo = MACreatureInfo.GetInfo(_creatureInfo);
        
        if(creatureInfo == null)
            return null;
        var gift = new NGBusinessGift();
        gift.m_fromFlow = false;
        gift.Type = GiftType.Hero;
        gift.m_power = creatureInfo.m_name;
        gift.m_name = "New Hero";
        gift.m_giftTitle = gift.m_cardTitle = creatureInfo.m_displayName;
        gift.m_description = creatureInfo.m_description;
        gift.m_quantity = 1;
        gift.m_cardPrice = creatureInfo.m_tavernCost;
        gift.m_spritePath = creatureInfo.m_spritePath;
        return gift;
    }
    
    public static NGBusinessGift CreateNewWorkerGift(string _workerInfo)
    {
        var workerInfo = MAWorkerInfo.GetInfo(_workerInfo);
        if(workerInfo == null)
            return null;
        var gift = new NGBusinessGift();
        gift.m_fromFlow = false;
        gift.Type = GiftType.Worker;
        gift.m_power = workerInfo.m_name;
        gift.m_name = "New Worker";
        gift.m_giftTitle = gift.m_cardTitle = workerInfo.m_displayName;
        gift.m_description = workerInfo.m_description;
        gift.m_quantity = 1;
        gift.m_cardPrice = workerInfo.m_tavernCost;
        gift.m_spritePath = workerInfo.m_spritePath;
        return gift;
    }
    
    
    public static NGBusinessGift CreateGiftFromBlockNames(string _blocks)
    {
        var gift = new NGBusinessGift();
        gift.m_fromFlow = false;
        gift.Type = GiftType.Block;
        gift.m_cardPower = _blocks;
        var split = _blocks.Split('|', ';', '\n');
        gift.m_name = "";
        foreach (var s in split)
        {
            var bi = NGBlockInfo.GetInfo(s);
            if (bi != null)
            {
                gift.m_name += $"{bi.m_displayName}+";
            }
        }
        gift.m_name = gift.m_name.TrimEnd('+');
        gift.m_quantity = 1;
        gift.m_cardPrice = 0f;
        return gift;
    }

}
