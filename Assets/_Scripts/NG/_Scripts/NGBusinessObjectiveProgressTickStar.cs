using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class NGBusinessObjectiveProgressTickStar : NGBusinessObjectiveProgressTick
{
    public Color disabledColour;
    public Color enabledColour;
    override public void Toggle(bool _flag)
    {
        if(m_tickImage != null)
            m_tickImage.color = _flag ? enabledColour : disabledColour;
    }
}
