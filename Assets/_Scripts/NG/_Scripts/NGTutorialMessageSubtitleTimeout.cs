#if CHOICES
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

public class NGTutorialMessageSubtitleTimeout : NGTutorialMessageBase
{
    public TMP_Text m_preBarMessage;
    public Animator m_animator;
    private string m_messageType;
    private float m_timer = 1.0f;

    void Update()
    {
        m_timer -= Time.deltaTime;
        if (m_timer <= 0)
            m_buttonFinished = true;
    }

    override public void Reactivate(NGTutorial _line, string _preBarText, string _postBarText, string _trackFuncString, string _type, string _pose)
    {
        string decoded = Decode(_preBarText);
        m_preBarMessage.text = decoded;
        m_preBarMessage.gameObject.SetActive(_preBarText.IsNullOrWhiteSpace() == false);
    }
    override protected void Activate(NGTutorial _line, string _preBarText, string _postBarText, string _trackFuncString, string _type, string _pose)
    {
        base.Activate(_line, _preBarText, _postBarText, _trackFuncString, _type, _pose);
        string decoded = Decode(_preBarText);
        if (SettingsUIController.Subtitles == false && _type == "Banner")
            m_preBarMessage.text = "";
        else
            m_preBarMessage.text = decoded;
        m_messageType = _type;
        m_preBarMessage.gameObject.SetActive(_preBarText.IsNullOrWhiteSpace() == false);
        m_animator.SetTrigger("open");
        m_timer = 5.0f;
    }
    public override bool CanDestroy(string _type)
    {
        Debug.Log(_type + " == " + m_messageType);
        if (m_messageType != _type)
            return true;
        return false;
    }

    private string Decode(string _text)
    {
        return "";
    }

    override public bool IsFinished()
    {
        return m_buttonFinished;
    }
}
#endif