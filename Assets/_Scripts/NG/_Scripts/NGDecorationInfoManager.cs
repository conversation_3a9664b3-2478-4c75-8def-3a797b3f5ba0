using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class NGDecorationInfoManager : MonoSingleton<NGDecorationInfoManager>
{
    [System.Serializable]
    public class NGDecorationInfo
    {
        public string id;
        public bool m_debugChanged;
        public string m_prefabName;
        public string m_location;
        public float m_price;
        public string m_screenName;
        public string m_subType;
        public NGDecoration m_prefab;
        public string m_displayName;
        public string m_description;
        public float m_townSatisfactionEffect;
        public string m_spritePath;
        public string m_rarity;
        public bool m_starterPack;
        public string m_touristAnim;
        public bool m_canPickupAndThrow = false;
        public float m_throwExplosionSpeedBase = 6;
        public float m_throwExplosionRadiusBase = 10;
        public float m_throwExplosionPowerBase = 40;
        public float m_throwExplosionDamageBase = .2f;
        public float m_throwableMass = 200;
        public int m_maxImpactCount = 0; // zero means infinite

        public static bool PostImportARecord(NGDecorationInfo _what)
        {
            var file = $"{_what.m_location}/{_what.m_prefabName}";
            var prefab = ResManager.Load<GameObject>(file);
            if (prefab == null)
                { Debug.LogError($"No such Decoration Prefab as {file}"); return true; }
            var decorationPrefab = prefab.GetComponent<NGDecoration>();
            if (decorationPrefab == null)
                { Debug.LogError($"Loaded Decoration {prefab} has no NGDecoration component"); return true; }
            if (decorationPrefab.Name != _what.m_prefabName)
                { Debug.LogWarning($"Loaded Decoration {_what.m_prefabName} != {decorationPrefab.Name}"); return true; }
            _what.m_prefab = decorationPrefab;
            return true;
        }
        public static List<NGDecorationInfo> LoadInfo()
        {
            s_decorationInfos = NGKnack.ImportKnackInto<NGDecorationInfo>(PostImportARecord);
            return s_decorationInfos;
        }

        public static NGDecorationInfo GetInfo(string _name)
        {
            return s_decorationInfos.Find(o => o.m_prefabName.Equals(_name, StringComparison.OrdinalIgnoreCase));
        }
        public static List<NGDecorationInfo> s_decorationInfos = new List<NGDecorationInfo>();
        public static List<NGDecorationInfo> GetList => s_decorationInfos;
        public string DebugDisplayName => m_prefabName;

    }
    public static NGDecorationInfo GetInfo(string _name)
    {
        return NGDecorationInfo.s_decorationInfos.Find(o => o.m_prefabName.Equals(_name, StringComparison.InvariantCultureIgnoreCase));
    }
    public static void MakeSprite(string _name, CaptureObjectImage.Use _use, Image _sprite)
    {
        MakeSprite(GetInfo(_name), _use, _sprite);
    }
    public static void MakeSprite(NGDecorationInfo _info, CaptureObjectImage.Use _use, Image _sprite)
    {
        if (_info == null || _info.m_prefab == null) 
            return;

        var tempObject = Instantiate(_info.m_prefab.gameObject);
		_sprite.sprite = CaptureObjectImage.Me.Capture(tempObject, _use);
		tempObject.SetActive(false);
		Destroy(tempObject);
    }
    public PDMDebugDecoration m_PDMDebugDecorationPrefab;
    public Transform m_PDMDebugDecorationHolder;
    public Transform m_decorationHolder;

    private static DebugConsole.Command s_dec = new DebugConsole.Command("dec", _s => Me.SetPlaceDeccoration(_s));
    private static DebugConsole.Command s_dec2 = new DebugConsole.Command("decoration", _s => Me.SetPlaceDeccoration(_s));
    public void SetPlaceDeccoration(string _deccoration, bool _useOnce = false, GameState_Deccoration gameState_Deccoration = null)
    {
         if (PDMDebugDecoration.Me == null)
             PDMDebugDecoration.Create(null, _deccoration, -1, _useOnce, null, gameState_Deccoration);
         else
             PDMDebugDecoration.Me.Activate(null, _deccoration, -1, _useOnce, null, gameState_Deccoration);
         /*m_placeDeccoration = GetInfo(_deccoration);
         if(m_placeDeccoration == null) {Debug.LogError($"No Such deccoration as {_deccoration}"); return; }
         //NGDecorationInfoManager.MakeSprite(m_placeDeccoration, ResearchRewardsUIController.s_rewardCaptureType, m_placeDecorrationSprite);
         m_placeDeccorationPrefab = Resources.Load<GameObject>($"{m_placeDeccoration.m_location}/{m_placeDeccoration.m_prefabName}");
         if (m_placeDeccorationPrefab == null)
         {
             Debug.LogError($"Deccoration {_deccoration} has no prefab at {m_placeDeccoration.m_location}/{m_placeDeccoration.m_prefabName}");
             m_placeDeccoration = null;
         }*/
    }

    void Update()
    {
        /*if (m_placeDeccoration == null)
            return;
        if (Input.GetKey(KeyCode.Escape))
        {
            m_placeDeccoration = null;
        }
        if (Input.GetMouseButtonDown(0))
        {
            if (Input.GetKey(KeyCode.LeftShift))
            {
                return;
            }
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
            if (Physics.Raycast(ray, out var hit))
            {
                var obj = hit.transform.gameObject;
                Transform objectHit = hit.transform;
                var hitDeccoration = obj.GetComponentInChildren<NGDecoration>(true);
                if (hitDeccoration  != null && Input.GetKey(KeyCode.Delete) || Input.GetKey(KeyCode.Backspace))
                {
                    var found = GameManager.Me.m_state.m_decorations.Find(obj =>
                        obj.m_name.Equals(hitDeccoration.Name) &&
                        obj.m_position.Equals(hitDeccoration.transform.position));
                    if (found != null)
                        GameManager.Me.m_state.m_decorations.Remove(found);
                    Destroy(hitDeccoration.gameObject);
                    return;
                }
                var go = Instantiate(m_placeDeccorationPrefab, m_deccorationHolder);
                go.transform.position = hit.point;
                var decoration = go.GetComponentInChildren<NGDecoration>();
                GameManager.Me.m_state.m_decorations.Add(new GameState_Deccoration() { m_name = decoration.Name, m_position = go.transform.position, m_rotation = go.transform.eulerAngles, m_scale = go.transform.localScale});
            }
        }*/
    }

    public void Reseat()
    {
        foreach (var dec in GameManager.Me.m_state.m_decorations)
        {
            var item = dec.Decoration;
            if (item == null || item.m_canPickupAndThrow) continue;
            item.transform.Reseat(0, Utility.ReseatType.PhysicsPositionExcludeRidigbodies);
        }
    }

    public const int c_decsPerSync = 1;

    public static IEnumerator PostLoadAsync()
    {
        var decs = GameManager.Me.m_state.m_decorations;
        for (int i = 0; i < decs.Count; ++i)
        {
            if ((i & (c_decsPerSync - 1)) == 0) { GameManager.Me.StepProgress($"DecPost{i}"); yield return null; }
            var d = decs[i];
            if (GlobalData.IsInTerrain(d.m_position) == false)
            {
                //Debug.LogError($"Removing off-world decoration {d.m_name} at {d.m_position}");
                decs.RemoveAt(i);
                --i;
                continue;
            }
            var info = GetInfo(d.m_name);
            if (info == null) continue;
            d.m_displayName = info.m_displayName;
            var prefab = Resources.Load<GameObject>($"{info.m_location}/{info.m_prefabName}");
            if (prefab == null)
            {
                Debug.LogError($"Decoration prefab not found: {info.m_location}/{info.m_prefabName}");
                continue;
            }
            var go = Instantiate(prefab, Me.m_decorationHolder);
            go.transform.position = d.m_position;
            go.transform.eulerAngles = d.m_rotation;
            go.transform.localScale = d.m_scale;
            go.GetComponent<NGDecoration>().SetDecorationData(d).SetDrop("", 0).Register();
        }
    }

    public static void PostLoad()
    {
        var decs = GameManager.Me.m_state.m_decorations;
        for (int i = 0; i < decs.Count; ++i)
        {
            var d = decs[i];
            if (GlobalData.IsInTerrain(d.m_position) == false)
            {
                //Debug.LogError($"Removing off-world decoration {d.m_name} at {d.m_position}");
                decs.RemoveAt(i);
                --i;
                continue;
            }
            var info = GetInfo(d.m_name);
            if (info == null) continue;
            d.m_displayName = info.m_displayName;
            var prefab = Resources.Load<GameObject>($"{info.m_location}/{info.m_prefabName}");
            if (prefab == null)
            {
                Debug.LogError($"Decoration prefab not found: {info.m_location}/{info.m_prefabName}");
                continue;
            }
            var go = Instantiate(prefab, Me.m_decorationHolder);
            go.transform.position = d.m_position;
            go.transform.eulerAngles = d.m_rotation;
            go.transform.localScale = d.m_scale;
            go.GetComponent<NGDecoration>().SetDecorationData(d).SetDrop("", 0).Register();
        }
    }
}
