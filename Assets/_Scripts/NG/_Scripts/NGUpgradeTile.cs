using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class NGUpgradeTile : NGDirectionCardBase
{
    [System.Serializable]
    public class PowerIcons
    {
        public string m_PropertyName;
        public Sprite m_sprite;
    }
    public TMP_Text m_titleDetails;
    public Transform m_cardBasic;
    public Transform m_cardDetails;
    public Transform m_detailsLines;
    public Transform m_powerIconsHolder;
    public TMP_Text m_detailsLinePrefab;
    public Image m_powerIconImagePrefab;
    

    public List<PowerIcons> m_powerIcons;

    public float GetNewValue(NGCommanderBase _building, string _power, bool _setValue = false)
    {
        //MaxWorkers+1
        var operators = new string[] {"/", "*", "-", "+", "%"};
        var prams = ReactReflection.GetParameters(_power);
        if (prams.Length != 1)
        {
            Debug.LogError($"{_power} bad syntax");
            return -9999f;
        }

        foreach (var o in operators)
        {
            var s = prams[0].Split(o);
            if (s.Length == 3)
            {
                var propertyName = s[0];
                var pi = _building.GetType().GetProperty(propertyName);
                if (pi == null) continue;
                var originalValue = (float)pi.GetValue(_building);
                var byValue = s[2].ToFloatInv();
                var newValue = 0f;
                switch (o)
                {
                    case "/":
                        newValue = originalValue / byValue;
                        break;
                    case "*":
                        newValue = originalValue * byValue;
                        break;
                    case "-":
                        newValue = originalValue - byValue;
                        break;
                    case "+":
                        newValue = originalValue + byValue;
                        break;
                    case "%":
                        newValue = originalValue * byValue/100f;
                        newValue += originalValue;
                        break;
                    default:
                        Debug.LogError($"in {m_gift.m_power} opperator {o} not supported");
                        return -9999f;
                }
                if(_setValue)
                   pi.SetValue(_building, newValue);
                return newValue;
            }
        }
        return -9999f;
    }

    public override void GiveReward()
    {
        PayGiftCost();
        base.GiveReward();
    }

    override public void ShowDetailsCard(NGCommanderBase _building, NGMovingObject _object)
    {
        
        Pickup.UpdateBuildingHighlight();
        
        m_titleDetails.text = _building.GetBuildingTitle();
        m_cardBasic.gameObject.SetActive(false);
        m_cardDetails.gameObject.SetActive(true);
        var details = m_gift.GetUpgradeStrings(_building, $"<color=#{ColorUtility.ToHtmlStringRGB(m_positiveColour)}>+");
        m_detailsLines.DestroyChildren();
        foreach (var d in details)
        {
            var go = Instantiate(m_detailsLinePrefab.gameObject, m_detailsLines);
            var txt = go.GetComponentInChildren<TMP_Text>();
            int ia = d.IndexOf('(');
            int ib = d.IndexOf(')');
            if (ia < 0)
            {
                txt.text = d;
            }
            else
            {
                var str = d.Insert(ib, "</color>");
                str = str.Insert(ia + 1, $"<color=#{ColorUtility.ToHtmlStringRGB(m_activeColour)}>");
                txt.text = str;
            }
        }
        NGDemoManager.Me.ShowUpgradeLevelDialogue(_object, m_gift);
    }

    public override NGCardInfoHolder GetDescriptionText()
    {
        return new NGCardInfoHolder(m_gift.m_giftTitle, m_gift.m_description, Cost, 1, "Block");
    }

    protected override void OnCardDraggedBackOverHolder()
    {
        Pickup.UpdateBuildingHighlight();
        ShowBasicCard();
        base.OnCardDraggedBackOverHolder();
    }

    override public void ShowBasicCard()
    {
        transform.localEulerAngles = new Vector3(0f, 0f, 0f);
        m_cardBasic.gameObject.SetActive(true);
        m_cardDetails.gameObject.SetActive(false);
        m_powerIconsHolder.DestroyChildren();
    }

    private void OnDestroy()
    {
        Pickup.UpdateBuildingHighlight();
        NGDemoManager.Me?.DestroyUpgradeLevelDialogue();
    }
    override protected void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _fromHolder)
    {
        base.Activate(_gift, _maParserSection, _fromHolder);
        m_title.text = _gift.m_giftTitle;
        m_titleDetails.text = _gift.m_cardTitle;
        m_image.enabled = true;
        m_image.sprite = _gift.GetSprite;
        ShowBasicCard();
    }
}
