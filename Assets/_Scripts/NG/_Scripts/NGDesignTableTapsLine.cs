using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class NGDesignTableTapsLine : MonoBehaviour
{
    public TMP_Text m_tapCount;
    public TMP_Text m_animText;
    public Animator m_anim;

    public float m_numTaps = 0f;
    
    public void SetValue(float _numTaps)
    {
        m_tapCount.text = _numTaps.ToString();
        var value = (_numTaps - m_numTaps);
        if (value != 0)
        {
            var plusMinus = (value < 0) ? " " : "+";
            m_animText.text = $"{plusMinus}{value}";
            m_anim.SetTrigger("FloatUp");
        }

        m_numTaps = _numTaps;
    }

    void Activate()
    {
        
    }

    public static NGDesignTableTapsLine Create(NGDesignTableTapsLine _prefab, Transform _holder)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var activate = go.GetComponent<NGDesignTableTapsLine>();
        activate.Activate();
        return activate;
    }
}
