using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ParticleSystemSelfDestroy : MonoBeh<PERSON>our
{
    [SerializeField]private ParticleSystem m_particleSystem;

    private void Start()
    {
        if (m_particleSystem == null)
            m_particleSystem = GetComponentInChildren<ParticleSystem>();
        
    }
    private void Update()
    {
        if (!m_particleSystem.gameObject.activeSelf)
            Destroy(gameObject);
    }

}
