using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class NGDecoration : NGLegacyBase, IOnClick, IDamageReceiver, IStrikePoint
{
    public string m_resourceType = NGCarriableResource.c_timber;
    public float m_resourceQuantity = 10f;
    public bool m_canPickupAndThrow => m_info?.m_canPickupAndThrow ?? false;
    public float m_throwExplosionSpeedBase => m_info.m_throwExplosionSpeedBase;
    public float m_throwExplosionRadiusBase => m_info.m_throwExplosionRadiusBase;
    public float m_throwExplosionPowerBase => m_info.m_throwExplosionPowerBase;
    public float m_throwExplosionDamageBase => m_info.m_throwExplosionDamageBase;
    public AkEventHolder m_throwExplosionImpactSound;
    
    public AkEventHolder m_impactAudio;
    
    public GameObject m_onExplodeVFX;
    public bool m_isSingleUse = false;
    
    public float m_dragRaise = 0;

    public bool m_isNonInteractive = false;
    public bool m_isSpecialCaseEnterCrypt = false;
    
    private NGDecorationInfoManager.NGDecorationInfo m_info;
    
    protected GameState_Deccoration m_state;

    public Transform m_transform = null;
    
    #region IDamageReceiver
    public virtual int TargetPriority { get; set; } = 1;
    public virtual bool CanBeTargeted { get { return true; } }
    public virtual MAMovingInfoBase GetMovingInfo() { return null; }
    public Transform Transform => m_transform;
    public HashSet<IDamager> TargettedBy => new();
    #endregion

    Vector3 IStrikePoint.StrikePoint => m_transform.position + Vector3.up * (m_dragRaise * .5f);

    public override string DisplayName => m_info?.m_displayName;

    public static NGDecoration FindDecorationByName(string _name)
    {
        foreach (var state in GameManager.Me.m_state.m_decorations)
            if (state.m_name == _name)
                return state.Decoration;
        return null;
    }

    private static DebugConsole.Command s_damageTargetCmd = new("damagetarget", _s =>
    {
        var bits = _s.Split(",");
        if (bits.Length == 1) bits = new[] {"Decoration[MA_Tardis_Crypt]", bits[0]};
        if (bits.Length != 2) return;
        var receiver = MACharacterBase.GetObjectiveTarget(bits[0]);
        if (receiver == null) return;
        var damage = floatinv.Parse(bits[1]);
        var totalDamage = damage;
        receiver.ApplyDamageEffect(IDamageReceiver.DamageSource.Debug, damage, Vector3.zero);
        Debug.LogError($"Damaged {bits[0]} by {totalDamage} - {totalDamage - damage} applied");
    });
    
    private static DebugConsole.Command s_damageDecorationCmd = new ("damagedecoration", _s => {
        var bits = _s.Split(",");
        if (bits.Length == 1) bits = new [] {"MA_Tardis_Crypt", bits[0]};
        if (bits.Length != 2) return;
        var dec = FindDecorationByName(bits[0]);
        if (dec == null) return;
        var damage = floatinv.Parse(bits[1]);
        var totalDamage = damage;
        dec.ApplyDamageEffect(IDamageReceiver.DamageSource.Debug, damage, Vector3.zero);
        Debug.LogError($"Damaged {bits[0]} by {totalDamage} to {dec.m_state.m_health} - {totalDamage - damage} applied");
    });

    public virtual void ApplyDamageEffect(IDamageReceiver.DamageSource source, float _damageDone, Vector3 _sourceOfDamage, MAAttackInstance attack = null, MAHandPowerInfo handPower = null)
    {
        var damageAbsorbed = Mathf.Min(_damageDone, m_state.m_health);
        m_state.m_health -= damageAbsorbed;
        _damageDone -= damageAbsorbed;
    }

    public void CheckExplosionEffect(IDamageReceiver.DamageSource _customReason = IDamageReceiver.DamageSource.None, bool _createExplosion = false)
    {
        var explodeInteraction = GetComponent<ExplodeInteraction>();
        if (explodeInteraction != null && explodeInteraction.m_useGodThrowBreak)
        {
            if (_customReason == IDamageReceiver.DamageSource.None)
                _customReason = IDamageReceiver.DamageSource.ThrownObject;
            explodeInteraction.Explode(_customReason, null, 1f, transform.position);
        }
        if (m_onExplodeVFX != null) Instantiate(m_onExplodeVFX, transform.position, Quaternion.identity, transform.parent);
        if (m_isSingleUse) Destroy(gameObject);

        if (m_state != null)
        {
            ++m_state.m_impactCount;
            if (m_info.m_maxImpactCount > 0 && m_state.m_impactCount > m_info.m_maxImpactCount) Destroy(gameObject);
        }
    }

    public void NewDay()
    {
        if (m_state != null)
            m_state.m_health = 1;
    }

    public bool WasEverInOwnedDistrict => m_state?.m_wasEverInOwnedDistrict ?? false;
    virtual protected void Awake()
    {
        m_transform = transform;
        if (GetComponentInChildren<Collider>() == null)
            AddBoxCollider(1, out _);
    }

    virtual protected void Start()
    {
        m_info = NGDecorationInfoManager.GetInfo(Name);
        gameObject.AddComponent<NGDecorationInputHandler>();
        if (m_canPickupAndThrow)
        {
            gameObject.AddComponent<PickupAndThrowBehaviour>();
            var rb = GetComponent<Rigidbody>(); 
            if (rb == null) rb = gameObject.AddComponent<Rigidbody>();
            const float c_defaultThrowableMass = 200;
            var mass = m_info?.m_throwableMass ?? 0;
            if (mass == 0) mass = c_defaultThrowableMass;
            rb.mass = mass;
            PhysicsAudio.Create(gameObject, string.IsNullOrEmpty(m_impactAudio?.Name) ? "PlaySound_BarrelCollision" : m_impactAudio.Name, "ThrowableObjectImpactSpeed");
            GlobalData.Me.RegisterTransformChangeTracker(transform, UpdateState);
            
            const float c_boundaryMargin = 1;
            if (GetComponentInChildren<MeshFilter>().sharedMesh is {} smesh && GetComponent<Pickup>() is {} pickup)
                pickup.m_districtTestRadius = smesh.bounds.size.magnitude * .5f + c_boundaryMargin; 

            float GetRadiusFromMesh()
            {
                var mf = gameObject.GetComponentInChildren<MeshFilter>();
                var mesh = mf.sharedMesh;
                var meshExt = Vector3.Scale(mesh.bounds.extents, mf.transform.lossyScale);
                return meshExt.xzMagnitude();// Mathf.Max(meshExt.x, meshExt.y, meshExt.z);
            }

            var bnb = gameObject.GetComponentInChildren<BuildingNavBlocker>();
            if (bnb == null)
            {
                var bnbgo = new GameObject("NavBlocker");
                bnbgo.transform.SetParent(transform, false);

                var sc = bnbgo.AddComponent<SphereCollider>();
                sc.isTrigger = true;

                var meshRadius = GetRadiusFromMesh();
                sc.radius = meshRadius;

                bnb = bnbgo.AddComponent<BuildingNavBlocker>();
                bnb.m_addNavMargin = true;
            }
#if UNITY_EDITOR
            else
            {
                var cll = bnb.GetComponent<Collider>();
                if (cll == null)
                    Debug.LogError($"NGDecoration {Name} has a BuildingNavBlocker without a Collider", gameObject);
                else
                {
                    Vector3 center = Vector3.zero;
                    float radius = 0;
                    if (cll is SphereCollider sc)
                    {
                        center = sc.center;
                        radius = sc.radius;
                    }
                    else if (cll is BoxCollider bc)
                    {
                        center = bc.center;
                        radius = bc.size.magnitude * .5f;
                    }
                    if (bnb.m_addNavMargin) radius += BuildingNavBlocker.c_navMargin;
                    var meshRadius = GetRadiusFromMesh() + BuildingNavBlocker.c_navMargin;
                    if (meshRadius > radius)
                    {
                        if (bnb.m_addNavMargin == false)
                        {
                            if (meshRadius > radius + BuildingNavBlocker.c_navMargin)
                                Utility.ShowErrorOnce($"Art Team! NGDecoration {Name} has a BuildingNavBlocker with a collider that is smaller than the mesh bounds - make the collider bigger and use Add Nav Margin - cll {radius:n1} < mesh {meshRadius:n1}", gameObject);
                            else
                                Utility.ShowErrorOnce($"Art Team! NGDecoration {Name} has a BuildingNavBlocker with a collider that is smaller than the mesh bounds - use Add Nav Margin to fix - cll {radius:n1} < mesh {meshRadius:n1}", gameObject);
                        }
                        else
                            Utility.ShowErrorOnce($"Art Team! NGDecoration {Name} has a BuildingNavBlocker with a collider that is smaller than the mesh bounds - cll {radius:n1} < mesh {meshRadius:n1}", gameObject);
                    }
                }
            }
#endif
        }
        else
        {
            transform.Reseat(0, Utility.ReseatType.PhysicsPositionExcludeRidigbodies);
            gameObject.SetStaticRecursively(true);
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
            DayNight.Me.RegisterNewDayCallback(NewDay);
#endif
        }
    }

    public override bool IsHighlightable()
    {
        if (m_canPickupAndThrow) return true;
        // non-pickup, interactable if has ClickToAnimate or similar
        if (GetComponentInChildren<ClickToInteract>() != null) return true;
        if (GetComponentInChildren<ClickToAnimate>() != null) return true;
        if (GetComponentInChildren<DayNightControl>() != null) return true;
        return false;
    }

    protected BoxCollider AddBoxCollider(float _scale, out Bounds _bounds)
    {
        var oldRot = transform.rotation;
        transform.rotation = Quaternion.identity;
        var bounds = ManagedBlock.GetTotalVisualBounds(gameObject, null, true);
        transform.rotation = oldRot;
        var cll = gameObject.AddComponent<BoxCollider>();
        bounds.size *= _scale;
        cll.center = bounds.center - transform.position; cll.size = bounds.size;
        cll.isTrigger = true;
        _bounds = bounds;
        return cll;
    }

    protected virtual void UpdateState()
    {
        if (m_state == null) return;
        m_state.m_position = transform.position;
        m_state.m_rotation = transform.eulerAngles;
    }

    public void OnClick(int _inputId, bool _long)
    {
        // bool inDecEditMode = PDMDebugDecoration.Me != null;
        // if (PISSManager.UsePISS)
        // {
        //     if(PISSManager.PISSActive)
        //         ContextMenuManager.Me.ShowContextMenu(GetContextMenuData(), transform.position, this);
        // }
        // else 
 //       if (_long && m_state != null && !GameManager.IsVisitingInProgress && !inDecEditMode)
        if ((m_isSpecialCaseEnterCrypt || m_isNonInteractive == false) && _long && m_state != null && !GameManager.IsVisitingInProgress && DistrictManager.Me.IsWithinDistrictBounds(transform.position, true))
        {
            ContextMenuManager.Me.ShowContextMenu(GetContextMenuData(), transform.position, this);
        }
    }
    public void Register()
    {
        //var rp = gameObject.GetComponentInChildren<NGReactionPoint>();
        //if (rp != null) rp.Activate();
    }

    private ContextMenuData GetContextMenuData()
    {
        ContextMenuData data = new ContextMenuData();
        data.m_title = m_state.m_displayName;
        data.m_buttonDataList = GetContextMenuButtonData();
        return data;
    }

    virtual protected List<ContextMenuData.ButtonData> GetContextMenuButtonData()
    {
        List<ContextMenuData.ButtonData> buttonDatas;
        if (m_isSpecialCaseEnterCrypt)
        {
            buttonDatas = new List<ContextMenuData.ButtonData>
            {
                new ContextMenuData.ButtonData
                {
                    m_label = "Enter",
                    m_onClick = () => IntroControl.Me.ReEnterCrypt(),
                },
            };
            return buttonDatas;
        }
        buttonDatas = new List<ContextMenuData.ButtonData>
        {
                new ContextMenuData.ButtonData{
                    m_label = Localizer.Get(TERM.GUI_INFO),
                    m_onClick = ShowInfoPlaque
                },
                /*new ContextMenuData.ButtonData{
                    m_label = "Salvage Decoration",
                    m_onClick = ConfirmSalvage
                },*/
                new ContextMenuData.ButtonData{
                    m_label = "Move Decoration",
                    m_onClick = PickupDecoration
                }
        };

        return buttonDatas;
    }
    
    protected void ShowInfoPlaque()
    {
        NGDecorationInfoGUI.Create(this);
        return;
    }

    private void ConfirmSalvage()
    {
        List<ContextMenuData.ButtonData> buttonDatas;
        buttonDatas = new List<ContextMenuData.ButtonData>
        {
                new ContextMenuData.ButtonData{
                    m_label = "Confirm",
                    m_onClick = Salvage
                },
                new ContextMenuData.ButtonData{
                    m_label = "Cancel",
                    m_onClick = Cancel
                }
        };

        ContextMenuData data = new ContextMenuData();
        data.m_title = "Salvage";
        data.m_buttonDataList = buttonDatas;

        ContextMenuManager.Me.ShowContextMenu(data, transform.position, this);
    }

    private void Cancel()
    {
        AudioClipManager.Me.PlayUISound("PlaySound_SalvageDecorationCancel");
    }

    private void Salvage()
    {
        AudioClipManager.Me.PlayUISound("PlaySound_SalvageDecorationConfirm");
        SpawnReward();
        DestroyMe();
    }

    private void SpawnReward()
    {
        if (m_resourceQuantity > 0)
        {
            var o = ReactPickupPersistent.Create(null, NGCarriableResource.GetInfo(m_resourceType), m_resourceQuantity, GlobalData.Me.m_pickupsHolder, false, (_o) => {
                _o.GetComponent<NGMovingObject>().SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
                _o.AddComponent<Pickup>();
            });
            var source = transform.position + Vector3.up * 3f;
            var randomDirection = Random.Range(-180.0f, 180.0f) * Mathf.Deg2Rad;
            var direction = new Vector3(Mathf.Sin(randomDirection), 0, Mathf.Cos(randomDirection));
            const float c_throwDistance = 3f, c_throwTime = .4f;
            var target = source + direction * c_throwDistance;
            var velocity = Global3D.GetVelocityRequiredForPointToPointOverTime(source, target, c_throwTime);
            o.GetComponent<Rigidbody>().linearVelocity = velocity;
            o.transform.position = source;
        }
    }

    protected void PickupDecoration()
    {
        NGDecorationInfoManager.Me.SetPlaceDeccoration(m_state.m_name, true, m_state);
        DestroyMe();
    }

    void OnDestroy()
    {
        DestroyMe();
    }
    public override void DestroyMe()
    {
        if (GameManager.Me != null)
        {
            GameManager.Me.m_state.m_decorations.Remove(m_state);
        }

        Destroy(gameObject);
    }
    public virtual NGDecoration SetDecorationData(GameState_Deccoration _data) {
        m_state = _data;
        m_state.m_wasEverInOwnedDistrict |= DistrictManager.Me.IsWithinDistrictBounds(transform.position, true);
        if (m_state.m_health <= 0) m_state.m_health = 1;
        if (m_state.m_wasEverInOwnedDistrict && NGManager.Me.m_canPickUpNonCharacterOutOfDistrictIfPreviouslyHeld) gameObject.IgnoreDistrictFilter();
        Name = m_state?.m_name;
        _data.Decoration = this;
        return this;
    }

    public NGDecoration SetDrop(string _type, float _count) {
        m_resourceType = _type;
        m_resourceQuantity = _count;
        return this;
    }
}
