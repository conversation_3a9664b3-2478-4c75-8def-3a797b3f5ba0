using System.Collections;
using System.Collections.Generic;
using UnityEngine;
[System.Serializable]
public class NGToTranslateHC 
{
    public static List<NGToTranslateHC> s_toTranslateHC = new List<NGToTranslateHC>();
    public static List<NGToTranslateHC> GetList => s_toTranslateHC;
    public string DebugDisplayName => m_text;

    public string id;
    public bool m_debugChanged;
    public string m_text;
    public string m_uld;
    public static List<NGToTranslateHC> LoadInfo()
    {    
        s_toTranslateHC = NGKnack.ImportKnackInto<NGToTranslateHC>(); 
        return s_toTranslateHC;
    }
}
