using System.Collections;
using System.Collections.Generic;
using UnityEngine;
[System.Serializable]
public class NGTranslatable 
{
    public static List<NGTranslatable> s_translatable = new List<NGTranslatable>();
    public static List<NGTranslatable> GetList => s_translatable;
    public string DebugDisplayName => m_knackFile;
    
    public string id;
    public bool m_debugChanged;
    public string m_knackFile;
    public string m_fieldToTranslate;
    public string m_uniqueIdFields;
    public static List<NGTranslatable> LoadInfo()
    { 
        s_translatable = NGKnack.ImportKnackInto<NGTranslatable>(); 
        return s_translatable;
    }
}
