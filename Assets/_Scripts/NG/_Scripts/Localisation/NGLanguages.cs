using System.Collections;
using System.Collections.Generic;
using UnityEngine;
[System.Serializable]
public class NGLanguages
{
    public static List<NGLanguages> s_languages = new List<NGLanguages>();
    public static List<NGLanguages> GetList => s_languages;
    public string DebugDisplayName => m_name;

    public string id;
    public bool m_debugChanged;
    public string m_name;
    public static List<NGLanguages> LoadInfo()
    { 
        s_languages = NGKnack.ImportKnackInto<NGLanguages>(); 
        return s_languages;
    }
}
