using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGVisualContainer
{
	//Index to a materal allows object to take color of a commander typically when assigned to a factory 
	public int MatIndex { get; set; }
	public float SkinGradValue { get; set; }
	public float HairGradValue { get; set; }
	public Transform HoldingTransform { get; set; }
	public Rigidbody PickupRigidBody { get; set; }
	public AnimationHandler AnimHandler { get; set; }
	public event System.Action<NGVisualContainer> OnContainerChanged;

	public void Init(NGMovingObject _rob)
	{
		AnimHandler = _rob.gameObject.GetComponentInChildren<AnimationHandler>();
		var chest = _rob.transform.FindChildRecursiveByName("Chest");
		if (chest)
			PickupRigidBody = chest.GetComponent<Rigidbody>();
		HoldingTransform = _rob.transform.FindChildRecursiveByName("HandAttach_L");

		OnContainerChanged?.Invoke(this);
	}

	public void Update(NGMovingObject _rob, Transform _animationRoot)
	{
		Init(_rob);
		if (AnimHandler != null)
			AnimHandler.m_toApplyRootTo = _rob.transform;
	}
}
