using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGBalloonManager : MonoSingleton<NGBalloonManager>
{
	public enum BalloonType
	{
		None = -1,
		Red                                            = 0,
		Products,
		Letter,
		Upgrade,
		Repair,
		Resources,
		Tap,
		Flick,
		Warning,
		Reward,
		Research,
		ResearchComplete,
		Timer,
		ProductionStopped,
		Last
	}
	public List<Balloon> m_balloonPrefabs;
	public List<Balloon> m_activeBalloons = new List<Balloon>();
	private float m_balloonInactivityDelay = .3f;
	private float m_balloontime = 0f;
	// Click on Balloon Force Properties
	private float m_radius = 0.5f;
	private float m_power = 0.2f;
	

	void Start()
	{
		m_balloontime = Time.time;
		m_activeBalloons = new List<Balloon>();
	}

    // Update is called once per frame
    void Update()
    {
	    BalloonUpdate();
    }

    private void BalloonUpdate()
	{
		bool playerInactive = false;
		if (Input.GetMouseButtonDown(0) || Input.GetMouseButtonDown(1))
		{
    		if (GameManager.Me.VerboseBalloons == false && GameManager.Me.InOutBalloons == false && GameManager.Me.EmploymentBalloons == false)
				m_balloontime = Time.time + m_balloonInactivityDelay;
		}

		if (Time.time > m_balloontime)
			playerInactive = true;
//		else
//			NGTutorialManager.Me.ResetConditionalTimer();
	}
	
	public static Balloon CheckBalloonClicked(GameObject hitObject)
	{
		var balloon = hitObject.GetComponentInChildren<Balloon>();
		if (balloon == null)
			return null;
		RaycastHit hit;
		Ray ray;
		ray = Camera.main.ScreenPointToRay(Input.mousePosition);
		if (Physics.Raycast(ray, out hit))
		{
			if (balloon == hit.collider.gameObject.GetComponentInParent<Balloon>())
			{
				Me.AddForce(hit.point);
				balloon.BalloonClicked();
				return balloon;
			}
		}
		return null;
	}

	
	public void AddForce(Vector3 _pos)
	{
		Collider[] colliders = Physics.OverlapSphere(_pos, m_radius);
		foreach (Collider hit in colliders)
		{
			Rigidbody rb = hit.GetComponentInParent<Rigidbody>();
			if (rb != null)
				rb.AddExplosionForce(m_power, _pos, m_radius);
		}
	}

	public void RemoveBalloon(Balloon _balloon) => m_activeBalloons.Remove(_balloon);
	public void AddBalloon(Balloon _balloon) => m_activeBalloons.AddUnique(_balloon);

	public Balloon CreateBalloon(BalloonType _type, Transform _holder, string _balloonText, Sprite _balloonSprite, System.Action<Balloon> _updateAction = null, System.Action<Balloon> _clickedBalloonAction = null)
	{
		var balloon = Balloon.Create(m_balloonPrefabs[(int) _type], _holder, _balloonText, _balloonSprite, _updateAction, _clickedBalloonAction);
		AddBalloon(balloon);
		return balloon;
	}
}
