using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;
/*
 * To add an existing class to Knack the following steps must be taken (See the Below TestKnackImport for example)
 * 1) create a table in knack with the fields needed to import, these fields must be the same names as the class fields without the m_
 * 2) Note down the object number (in the address bar after of object_)
 * 3) Create A page making sure all defined fields are included not down the scene number and the view number (in the address bar after scene_ & view_)
 * 4) Create a new entry in the NGKnack Inspector under ImportKnack these fields are
 *      Class: The class name of the item
 *      jsonName: Name of the JsonFile that will be saved
 *      KnackTable: is the object number from step 2
 *      KnackPagesScene: is the scene number from step 3
 *      KnackPagesView: is the view number from step 3
 *      IsView: a bool that always to be true
 *      FileLocation: Is the file location where the cache is saved
 * 5) Now add thee LoadInfo() static function to your class (see TestKnackImport below for and example)
 * 6) Place the call to your class's LoadInfo() in NGManager.LoadInfos()
 * 7) Regenerate the Cache Files by activating menu 22Cans->Knack->CacheAllKnacks
 * 8) Note 22Cans->Knack->ReadFromCache will toggle reading from the cache or directly from knack
 */     
[System.Serializable]
public class TestInheritance 
{
    public string i_name;
}
[System.Serializable]
public class TestKnackImport : TestInheritance
{
    public string id;
    public string m_name; // Tests Short Text
    public string m_description; // Tests Paragraph Text
    public int m_index; //Tests number
    public string m_choice; //Tests Multiple Choice
    [ScanField] public string m_link; // Tests Multiple links to other class not use of Attribute [ScanField]
    public string m_carry; // Tests interface to ECarriableResource
    [ScanField] public string m_toDecoration; //Tests Caching a class
    public NGDecorationInfoManager.NGDecorationInfo m_decoration; // example of caching field
    public static List<TestKnackImport> s_records;
    
    
    //public ECarriableResource Carry {  get { Enum.TryParse(m_carry, out ECarriableResource r); return r; } set { m_carry = value.ToString(); } }
    //This is called after a record has been imported returning true will tell the imported the record is valid, false means the import will skip the record
    public static bool PostImportARecord(TestKnackImport _what)
    {
        if (_what.m_toDecoration.IsNullOrWhiteSpace() == false)
        {
            _what.m_decoration = NGDecorationInfoManager.NGDecorationInfo.s_decorationInfos.Find(o => o.m_prefabName.Equals(_what.m_toDecoration));
        }
        return true;
    }
    // this is main imported example
    public static List<TestKnackImport> LoadInfo()
    {
        //This will download (or read from cache) all records on Knack NB can be called without the PostImportAReacord.
        s_records = NGKnack.ImportKnackInto<TestKnackImport>(PostImportARecord);
        return s_records;
    }
}
