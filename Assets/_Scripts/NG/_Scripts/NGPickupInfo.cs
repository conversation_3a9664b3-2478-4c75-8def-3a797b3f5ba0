using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class NGPickupInfo : MonoBehaviour
{ 
    public enum Mode
    {
        Timer,
        MouseButton,
        Single
    };
    public TMP_Text m_infoText;
    public Vector2 m_touchOffset;

    private float m_showForTimer;
    private Mode m_mode;
    private static NGPickupInfo s_showingInfo;
    private Vector3 m_worldPos;

    // Start is called before the first frame update
    void Start()
    {
        
    }

    // Update is called once per frame
    void Update()
    {
        switch(m_mode)
        {
            case Mode.Timer:
                if (m_showForTimer > 0 && Time.time > m_showForTimer)
                    DestroyMe();
                break;
            case Mode.MouseButton:
                if(Input.GetMouseButton(0) == false)
                {
                    m_mode = Mode.Timer;
                    m_showForTimer = Time.time + 1f;
                }
                break;
        }
        SetPosition();
    }

    void SetPosition()
    {
        var canvas = GameManager.Me.TownCanvas;//UIManager.Me.GetComponent<Canvas>();
		var pos = canvas.WorldToCanvas(m_worldPos, Camera.main);
		transform.localPosition = pos;
    }

    void DestroyMe()
    {
        Destroy(gameObject);
        s_showingInfo = null;
    }

    void Activate(Vector3 _worldPos, string _message, Mode _mode, float _time)
    {
        m_worldPos = _worldPos;
        m_infoText.text = _message;
        m_mode = _mode;
        if (_time.IsZero())
        {
            if (s_showingInfo)
                s_showingInfo.DestroyMe();
            m_showForTimer = 0f;
        }
        else
        {
            m_showForTimer = Time.time + _time;
        }
        s_showingInfo = this;
        SetPosition();
    }

    public static void DestroyActive()
    {
        if(s_showingInfo != null)
        {
            s_showingInfo.DestroyMe();
            s_showingInfo = null;
        }
    }

    public static NGPickupInfo Create(Vector3 _worldPos, string _message, Mode _mode, float _showForTime = 3f)
    {
        DestroyActive();

        var go = Instantiate(NGDemoManager.Me.m_pickupInfoPrefab.gameObject, NGDemoManager.Me.m_pickupInfoHolder);
        var pi = go.GetComponent<NGPickupInfo>();
        pi.Activate(_worldPos, _message, _mode, _showForTime);
        return pi;
    }

}
