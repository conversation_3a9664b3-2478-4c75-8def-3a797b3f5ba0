#if false
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class NGMakingGaugeBase : GUIInfoBase
{
    public TMP_Text m_combo;

    public enum Type
    {
        Making,
        Warning,
    }
    public NGCommanderBase Building { get { return m_building; } }
    protected NGCommanderBase m_building;
    protected static Dictionary<int, NGMakingGaugeBase> s_activeGauge = new Dictionary<int, NGMakingGaugeBase>(); public static bool Active => s_activeGauge.Count > 0;
    public bool m_destroying;
    protected int m_inputID;
    private bool m_updatePosition = true;
    
    public static NGMakingGaugeBase Instance(int _inputID)
    {
        if (s_activeGauge.TryGetValue(_inputID, out var gauge)) return gauge;
        return null;
    }
    
    virtual public void ShowNew(Type _type, string _message, bool _showDetails = false)
    {
    }
    virtual public void StartDestroy()
    {
        if(m_destroying) return;
        
        m_destroying = true;
        
        if (Instance(m_inputID) == this)
            s_activeGauge.Remove(m_inputID);
        if(m_combo)
            StartCoroutine(RunValuesDown(m_combo.text[1..].ToFloatInv(), DestroyMe));
        else
            DestroyMe();
    }
    virtual protected void Update()
    {
        SetPosition();
    }
    virtual protected bool ReadyToDestroy()
    {
        return true;
    }
    
    protected IEnumerator RunValuesDown(float _startValue, System.Action OnComplete)
    {
        m_destroying = true;
        float time = 0;
        float howLong = Mathf.Min(_startValue - 1, 1) * 0.5f;
        while (time < howLong)
        {
            time += Time.deltaTime;
            if (time > howLong)
                time = howLong;
            float val = 1 + (_startValue - 1) * (1 - time / howLong);
            if(m_combo)
                m_combo.text = $"x{val:n1}";
            yield return null;
        }
        while(!ReadyToDestroy())
        {
            yield return null;
        }
        OnComplete();
    }
    public void DestroyMe()
    {
        if (Instance(m_inputID) == this)
            s_activeGauge.Remove(m_inputID);

        if (m_building != null && m_building.MakingGauge == this)
            m_building.MakingGauge = null;
        Destroy(gameObject);
    }

    public void SetPosition()
    {
        if(m_updatePosition)
        {
            m_worldPosition = m_building.CenterPosition();
#if UNITY_IOS
            if(m_inputID >= 0)
            {
                var ray = Camera.main.RayAtScreenPosition(GameManager.InputPosition(m_inputID));
                var plane = new Plane(-Camera.main.transform.forward, m_building.CenterPosition());
                float dist;
                if(plane.Raycast(ray, out dist))
                {
                    m_worldPosition = ray.GetPoint(dist);
                }
            }
#endif       
            m_updatePosition = false; 
        }
    
        transform.localPosition = GameManager.Me.TownCanvas.WorldToCanvas(m_worldPosition, Camera.main);
    }

    private Vector3 m_worldPosition;

    virtual public void Activate(NGCommanderBase _building, int _inputID)
    {
        m_building = _building;
        m_inputID = _inputID;
        s_activeGauge[_inputID] = this;
    }

    protected string GetTipText()
    {
        switch (m_building.Name)
        {
            case "NGRoadBuilder":
            case "NGBuildingSite":
                return "TIP:  <size=18>Try tap+hold to build.";
            case "NGBank":
                return "TIP:  <size=18>Try tap+hold to pay.";
            case "NGPub":
            case "NGCafe":
            case "NGSalon": 
            case "NGCinema":
            case "NGDisco":
            case "NGLibrary":
            case "NGShop":
                return "TIP:  <size=18>Try tap+hold to serve.";
            default:
                return "TIP:  <size=18>Try tap+hold to make.";
        }
    }

    virtual public void MadeAProduct(float _score)
    {
        
    }

    protected static void DestroyActive(int _inputID)
    {
        Instance(_inputID)?.StartDestroy();
    }
    public static NGMakingGaugeBase Create(NGCommanderBase _building, int _inputID)
    {
        NGPickupInfo.DestroyActive();
        DestroyActive(_inputID);
        if(NGMakingGaugeManager.Me == null) return null;
        NGMakingGaugeBase prefab = null;
        if (NGMakingGaugeManager.Me.m_useMakingGaugeInOut)
            prefab = NGMakingGaugeManager.Me.m_NGMakingGaugeInOutPrefab;
        else if (NGMakingGaugeManager.Me.m_useMakingGaugeClock)
            prefab = NGMakingGaugeManager.Me.m_NGMakingGaugeClock;
        else
            prefab = NGMakingGaugeManager.Me.m_NGMakingGaugeFillPrefab;
        var go = Instantiate(prefab, NGMakingGaugeManager.Me.m_GUIHolder);
        var mg = go.GetComponent<NGMakingGaugeBase>();
		
        mg.Activate(_building, _inputID);
        return mg;
    }
}
#endif