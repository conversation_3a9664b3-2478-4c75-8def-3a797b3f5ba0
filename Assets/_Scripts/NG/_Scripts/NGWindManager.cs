using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGWindManager : MonoSingleton<NGWindManager>
{
    private float m_outerPadding = 100f;
    private float m_windBoundsMin = float.MaxValue;
    private float m_windBoundsMax = float.MinValue;
    [SerializeField]private float m_windSpeed = 20f;
    [SerializeField]private float m_interactionRange = 10f;
    private float m_windX = float.MinValue;
    [SerializeField]private float m_windForce = 80f;
    private bool m_updateWindBounderies = true;
    
    void Update()
    {
        BreezeUpdate();
    }
    
    private void BreezeUpdate()
    	{
    		if (m_updateWindBounderies)
    		{
    			RefreshWind();
    		}
    		else
    		{
    			m_windX += m_windSpeed * Time.smoothDeltaTime;
    			if (m_windX > m_windBoundsMax)
    				m_windX = m_windBoundsMin;
    		}
    		
    		foreach (var ibc in CommanderBalloons.s_commanderBalloons)
    		{
    			for (int i = 0; i < ibc.m_balloons.Length; i++)
    			{
    				if(ibc.m_balloons[i] == null)
    					continue;
    				if (!ibc.m_balloons[i].gameObject.activeSelf)
    					continue;
                    AddWindForceToBalloon(ibc.m_balloons[i].Body);
    			}
    		}
    	}
    
	    public void AddWindForceToBalloon(Rigidbody _body)
	    {
		    Vector3 force = new Vector3(m_windForce, 0, (Random.value * 20f) - 10f);
			var wp = transform.TransformPoint(_body.transform.localPosition);
			float diff = wp.x - m_windX;
			if (Mathf.Abs(diff) < m_interactionRange)
				_body.AddForce(force * Time.smoothDeltaTime);
	    }
     
    
    	public void UpdateWindBounderies()
    	{
    		m_updateWindBounderies = true;
    	}
    	private void RefreshWind()
    	{
    		if(NGManager.Me.m_NGCommanderList == null || NGManager.Me.m_NGCommanderList.Count == 0)
    			return;
    		
    		foreach (var building in NGManager.Me.m_NGCommanderList)
    		{
    			var pos = building.transform.position;
    			m_windBoundsMin = Mathf.Min(pos.x - m_outerPadding, m_windBoundsMin);
    			m_windBoundsMax = Mathf.Max(pos.x + m_outerPadding, m_windBoundsMax);
    		}

            m_windX = m_windBoundsMin;
    		m_updateWindBounderies = false;
    	}
}
