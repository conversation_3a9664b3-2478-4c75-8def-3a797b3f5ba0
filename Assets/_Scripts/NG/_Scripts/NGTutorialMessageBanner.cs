#if CHOICES

using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
public class NGTutorialMessageBanner : NGTutorialMessageBase
{
    public TMP_Text m_preBarMessage;

    void Update()
    {
    }

    // override protected void Activate(NGTutorial _line, string _preBarText, string _postBarText, string _trackFuncString, string _type, string _pose)
    // {
    //     base.Activate(_line, _preBarText, _postBarText, _trackFuncString, _type, _pose);
    //     string decoded = Decode(_preBarText);
    //     m_preBarMessage.text = decoded;
    //     m_preBarMessage.gameObject.SetActive(_preBarText.IsNullOrWhiteSpace() == false);
    // }

    private string Decode(string _text)
    {
        return "";
    }

    public override bool IsOKToContinue()
    {
        return true;
    }

    override public bool IsFinished()
    {
        return m_buttonFinished;
    }

}
#endif
