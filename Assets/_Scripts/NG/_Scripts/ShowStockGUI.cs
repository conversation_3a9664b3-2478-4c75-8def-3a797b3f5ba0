using System.Collections;
using System.Collections.Generic;
using System.Net.Mime;
using UnityEngine;
using UnityEngine.UI;
public class ShowStockGUI : MonoBehaviour
{
    public Transform m_verticalHolder;
    public int m_lineStockNum = 4;
    public GameObject m_showStockBlockGUIPrefab;
    public GameObject m_showStockGUILinePrefab;
    public float m_noStockColorMultiplier = .5f;
    
    private int blockCount = -1;
    private Transform m_currentLine;
    private float m_height;
    void CreateStockBlock(NGStock.NGStockItem _stock, int _index)
    {
        blockCount++;
        if ((blockCount % m_lineStockNum) == 0)
        {
            var newLine = Instantiate(m_showStockGUILinePrefab, m_verticalHolder);
            m_currentLine = newLine.transform;
            var rt = newLine.GetComponent<RectTransform>();
            m_height += rt.GetHeight();
            newLine.transform.DestroyChildren();
            newLine.transform.SetSiblingIndex(0);
        }
        var go = Instantiate(m_showStockBlockGUIPrefab, m_currentLine);
        var image = go.GetComponent<Image>();
        ColorUtility.TryParseHtmlString(_stock.Resource.m_spriteColour, out var color);
        if (_index > _stock.m_stock)
            color = new Color(color.r, color.g, color.b, color.a * m_noStockColorMultiplier);
        image.color = color;
    }
    void Activate(RectTransform _holder, bool _scaleToHolder = true)
    {/*
        m_verticalHolder.DestroyChildren();
        m_height = 0f;
        foreach (var stock in m_factory.InputsAre.Items)
        {
            if (stock.m_neededToProduce >= 1f)
            {
                for (int i = 0; i < stock.m_neededToProduce; i++)
                {
                    CreateStockBlock(stock, i);
                }
            }
        }

        if (_scaleToHolder)
        {
            var parentHeight = _holder.GetHeight();
            if (parentHeight < m_height)
            {
                var scale = parentHeight / m_height;
                transform.localScale = new Vector3(scale, scale, scale);
            }
        }*/
    }

    public static ShowStockGUI Create(RectTransform _holder, bool _scaleToHolder = true)
    {
        var go = Instantiate(NGManager.Me.m_showStockGUIPrefab, _holder.transform);
        var ssg = go.GetComponent<ShowStockGUI>();
        ssg.Activate(_holder, _scaleToHolder);
        return ssg;
    }
}
