using System;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public partial class SettingsUIController
{
	static List<DisplayInfo> s_displayLayout = new List<DisplayInfo>();

	private static List<DisplayInfo> GetDisplays()
	{
		s_displayLayout.Clear();
		Screen.GetDisplayLayout(s_displayLayout);
		return s_displayLayout;
	}
	
	public static int CurrentDisplayIndex()
	{
		return GetDisplays().IndexOf(Screen.mainWindowDisplayInfo);
	}

    private int m_currentDisplayIndex = -1; 
	private void CheckDisplayIndex()
	{
#if UNITY_IOS || UNITY_ANDROID
#else
		if (m_currentDisplayIndex != CurrentDisplayIndex())
		{
			if (s_saveEnabled)
				MPlayerPrefs.SetInt(c_displayPlayerPrefsKey, CurrentDisplayIndex());
			InitialiseResolutionDropDown();
		}
#endif
	}

	private void InitialiseDisplayIndex()
	{
#if UNITY_IOS || UNITY_ANDROID
		m_displaysDropdownHolder.SetActive(false);
#else
		m_currentDisplayIndex = MPlayerPrefs.GetInt(c_displayPlayerPrefsKey, 0);
		if (m_currentDisplayIndex != CurrentDisplayIndex()) SetDisplay(m_currentDisplayIndex);
#endif
	}
	
	const string c_resWidthPlayerPrefsKey = "ResW";
	const string c_resHeightPlayerPrefsKey = "ResH";
	const string c_resRefreshPlayerPrefsKey = "ResR";
	const string c_resWindowedPlayerPrefsKey = "ResWin";
	const string c_resWindowedWidthPlayerPrefsKey = "ResWW";
	const string c_resWindowedHeightPlayerPrefsKey = "ResWH";
	const string c_displayPlayerPrefsKey = "DisplayName";

	private static void SaveResolution(Resolution _res, int _windowed)
	{
		if (!s_saveEnabled) return;
		if (_windowed == c_settingsWindowedMode_Windowed)
		{
			MPlayerPrefs.SetInt(c_resWindowedWidthPlayerPrefsKey, _res.width);
			MPlayerPrefs.SetInt(c_resWindowedHeightPlayerPrefsKey, _res.height);
		}
		else
		{
			MPlayerPrefs.SetInt(c_resWidthPlayerPrefsKey, _res.width);
			MPlayerPrefs.SetInt(c_resHeightPlayerPrefsKey, _res.height);
		}
		MPlayerPrefs.SetInt(c_resRefreshPlayerPrefsKey, (int)(_res.refreshRateRatio.value * 1000));
		MPlayerPrefs.SetInt(c_resWindowedPlayerPrefsKey, _windowed);
	}
	
	static Resolution s_currentResolution;
	
	static int CurrentPrefWindowed => MPlayerPrefs.GetInt(c_resWindowedPlayerPrefsKey) & 3;

	public static void ChangeResolution(int _width, int _height, float _refresh = 60f)
	{
		var res = new Resolution
        {
            width = _width,
            height = _height,
            refreshRateRatio = new RefreshRate { numerator = (uint)(_refresh * 1000), denominator = 1000 }
        };
		SetResolution(res, CurrentPrefWindowed);
	}

	static void SetResolution(Resolution _res, int _windowed)
	{
		s_currentResolution = MatchResolution(_res, _windowed);
		Utility.DoNextFrame(() =>
		{
			var windowMode = GetWindowMode(_windowed);
			Screen.SetResolution(s_currentResolution.width, s_currentResolution.height, windowMode, s_currentResolution.refreshRateRatio);
			if (s_saveEnabled) SaveResolution(_res, _windowed);

			Debug.Log($"Set Res {s_currentResolution.width} x {s_currentResolution.height} @ {s_currentResolution.refreshRateRatio.value:n2} win {CurrentPrefWindowed}");
			for (int i = 0; i < Display.displays.Length; ++i)
				Debug.Log($"Display {i} - {Display.displays[i].systemWidth}x{Display.displays[i].systemHeight} ({Display.displays[i].renderingWidth}x{Display.displays[i].renderingHeight})");
		});
	}
	
	static Resolution MatchResolution(Resolution _res, int _windowed)
	{
		var res = _windowed == c_settingsWindowedMode_Windowed ? s_windowedResolutions : Screen.resolutions;
		var best = (Resolution)default;
		var bestRefreshDeltaSqrd = float.MaxValue;
		var bestFound = false;
		for (int i = 0; i < res.Length; ++i)
		{
			if (res[i].width == _res.width && res[i].height == _res.height)
			{
				var refreshDelta = (float)(res[i].refreshRateRatio.value - _res.refreshRateRatio.value);
				if (refreshDelta * refreshDelta < bestRefreshDeltaSqrd)
				{
					bestRefreshDeltaSqrd = refreshDelta * refreshDelta;
					best = res[i];
					bestFound = true;
				}
			}
		}
		if (bestFound == false)
		{
			// No exact match found, find the closest one
			int bestMatchDeltaSqrd = int.MaxValue;
			for (int i = 0; i < res.Length; ++i)
			{
				int dw = res[i].width - _res.width, dh = res[i].height - _res.height;
				int deltaSqrd = dw * dw + dh * dh;
				if (deltaSqrd <= bestMatchDeltaSqrd)
				{
					if (deltaSqrd < bestMatchDeltaSqrd) bestRefreshDeltaSqrd = float.MaxValue;
					bestMatchDeltaSqrd = deltaSqrd;
					var refreshDelta = (float) (res[i].refreshRateRatio.value - _res.refreshRateRatio.value);
					if (refreshDelta * refreshDelta < bestRefreshDeltaSqrd)
					{
						bestRefreshDeltaSqrd = refreshDelta * refreshDelta;
						best = res[i];
						bestFound = true;
					}
				}
			}
			if (bestFound == false)
				Debug.LogWarning($"No matching resolution found for {_res.width}x{_res.height} @ {_res.refreshRateRatio.value:n2}Hz");
			else
				Debug.LogWarning($"No matching resolution found for {_res.width}x{_res.height} @ {_res.refreshRateRatio.value:n2}Hz - chose {best.width}x{best.height} @ {best.refreshRateRatio.value:n2}Hz");
		}
		return new Resolution { width = best.width, height = best.height, refreshRateRatio = best.refreshRateRatio };
	}
	
	private static int s_originalWindowW, s_originalWindowH, s_originalWindowId;
	private static bool s_originalWindowActive = true;
	public static void ToggleWindow() // Only used by clicker
	{
		s_originalWindowActive = !s_originalWindowActive;
		if (s_originalWindowActive)
		{
			LoadResolution(s_originalWindowW, s_originalWindowH, s_originalWindowId, false);
		}
		else
		{
			LoadResolution(1280, 1024, 0, false);
		}
	}
	
	public static void LoadResolution(int _overrideW, int _overrideH, int _overrideId = -1, bool _storeDefaults = true)
	{
		if (_overrideW != -1) // only used by clicker
		{
			Screen.fullScreen = false;
			Utility.DoNextFrame(() =>
			{
				var display = GetDisplays()[0];
				int displayW = display.width;
				if (_overrideW < 0) _overrideW = displayW / -_overrideW; // -n means fit n windows across
				
				Screen.SetResolution(_overrideW, _overrideH, FullScreenMode.Windowed);
				if (_overrideId != -1)
				{
					int cols = displayW / _overrideW;
					int x = _overrideId % cols, y = _overrideId / cols;
					Debug.LogError($"Window {_overrideId} {_overrideW}x{_overrideH} disp:{display.width}x{display.height} c:{cols} x:{x} y:{y}");
					const int c_titleBarHeight = 24;
					var wpos = new Vector2Int(x * _overrideW, c_titleBarHeight + y * (_overrideH + c_titleBarHeight));
					Screen.MoveMainWindowTo(display, wpos);
					if (_storeDefaults)
					{
						s_originalWindowW = _overrideW;
						s_originalWindowH = _overrideH;
						s_originalWindowId = _overrideId;
					}
				}
			});
			return;
		}
#if UNITY_IOS || UNITY_ANDROID
		return;
#endif
		var (res, windowed) = GetResolutionFromPrefs();
		SetResolution(res, windowed);
	}
	
	private static (Resolution, int) GetResolutionFromPrefs(int _windowed = -1)
	{
		_windowed = _windowed == -1 ? CurrentPrefWindowed : _windowed;
		int w, h;
		uint rateThousandths;
		if (_windowed == c_settingsWindowedMode_Windowed)
		{
			w = MPlayerPrefs.GetInt(c_resWindowedWidthPlayerPrefsKey, 1280);
			h = MPlayerPrefs.GetInt(c_resWindowedHeightPlayerPrefsKey, 720);
			rateThousandths = 60 * 1000;
		}
		else
		{
			w = MPlayerPrefs.GetInt(c_resWidthPlayerPrefsKey, 1920);
			h = MPlayerPrefs.GetInt(c_resHeightPlayerPrefsKey, 1080);
			rateThousandths = (uint)MPlayerPrefs.GetInt(c_resRefreshPlayerPrefsKey, 60*1000);
		}
		var rate = new RefreshRate {numerator = rateThousandths, denominator = 1000};
		return (new Resolution { width = w, height = h, refreshRateRatio = rate }, _windowed);
	}

	private void SetDisplay(int _index)
	{
		if (_index < 0 || _index >= GetDisplays().Count) _index = 0;
		var async = Screen.MoveMainWindowTo(GetDisplays()[_index], Screen.mainWindowPosition);
		async.completed += _s =>
		{
			if (s_saveEnabled)
				MPlayerPrefs.SetInt(c_displayPlayerPrefsKey, _index);
			InitialiseResolutionDropDown();
			InitialiseWindowDropDown();
		};
	}



	void InitialiseWindowDropDown()
	{
#if UNITY_IOS || UNITY_ANDROID
		m_windowModeDropdownHolder.gameObject.SetActive(false);
#else
        m_windowModeDropdownHolder.SetActive(true);
		var window = m_windowModeDropdownHolder.GetComponentInChildren<TMP_Dropdown>();
		//add options
		var options = new List<TMP_Dropdown.OptionData>();
		options.Add(new TMP_Dropdown.OptionData("Window Borderless"));
		options.Add(new TMP_Dropdown.OptionData("FullScreen"));
		options.Add(new TMP_Dropdown.OptionData("Windowed"));
		window.options = options;

		window.SetValueWithoutNotify(CurrentPrefWindowed);

		window.onValueChanged.RemoveAllListeners();
        window.onValueChanged.AddListener(_w => { SetWindowMode(window.value); });
#endif
    }
    private void SetWindowMode(int _index)
    {
	    if (s_saveEnabled)
		    MPlayerPrefs.SetInt(c_resWindowedPlayerPrefsKey, _index);
		Screen.fullScreenMode = GetWindowMode(_index);
        InitialiseWindowDropDown();
        InitialiseResolutionDropDown();
        var (res, windowed) = GetResolutionFromPrefs(_index);
        SetResolution(res, windowed);
    }

    const int c_settingsWindowedMode_MaxWindow = 0;
    const int c_settingsWindowedMode_FullScreen = 1;
    const int c_settingsWindowedMode_Windowed = 2;
	private static FullScreenMode GetWindowMode(int _forceWindowMode = -1)
	{
		var _window = _forceWindowMode == -1 ? CurrentPrefWindowed : _forceWindowMode;
        switch (_window)
        {
            case c_settingsWindowedMode_MaxWindow:
                return FullScreenMode.MaximizedWindow;
            case c_settingsWindowedMode_FullScreen:
                return FullScreenMode.ExclusiveFullScreen;
            case c_settingsWindowedMode_Windowed:
                return FullScreenMode.Windowed;
            default:
                return FullScreenMode.ExclusiveFullScreen;
        }
    }

    void InitialiseResolutionDropDown()
	{
#if UNITY_IOS || UNITY_ANDROID
		m_windowModeDropdownHolder.SetActive(false);
		m_vsyncToggle.gameObject.SetActive(false);
		m_resolutionDropdownHolder.SetActive(false);
#else
		m_currentDisplayIndex = CurrentDisplayIndex();
		
		m_resolutionDropdownHolder.SetActive(true);
		var dropdown = m_resolutionDropdownHolder.GetComponentInChildren<TMP_Dropdown>();
		//var toggle = m_windowedToggle;//m_resolutionDropdownHolder.GetComponentInChildren<Toggle>();

		m_displaysDropdownHolder.SetActive(true);
		var displaysDropdown = m_displaysDropdownHolder.GetComponentInChildren<TMP_Dropdown>();
		var displays = new List<TMP_Dropdown.OptionData>();
		var activeDisplays = GetDisplays();
		int selDisplay = -1;
		for (int i = 0; i < activeDisplays.Count; ++i)
		{
			displays.Add(new TMP_Dropdown.OptionData(activeDisplays[i].name));
			if (i == m_currentDisplayIndex) selDisplay = i;
		}
		displaysDropdown.options = displays;
		displaysDropdown.SetValueWithoutNotify(selDisplay);
		displaysDropdown.onValueChanged.RemoveAllListeners();
		displaysDropdown.onValueChanged.AddListener(_d =>
		{
			SetDisplay(_d);
		});

		var options = new List<TMP_Dropdown.OptionData>();
		int sel = -1;
		int bestSelD2 = 0x7FFFFFFF;
		int currW = Screen.width, currH = Screen.height;
		var resolutions = Screen.resolutions;
		if (GetWindowMode() == FullScreenMode.Windowed)
		{
			var display = activeDisplays[m_currentDisplayIndex];
			int maxWidth = display.width, maxHeight = display.height;
			
			var winRes = new List<Resolution>();
			for (int i = 0; i < s_windowedResolutions.Length; ++i)
			{
				var r = s_windowedResolutions[i];
				if (r.width <= maxWidth && r.height <= maxHeight)
					winRes.Add(r);
			}
			if (winRes[winRes.Count - 1].width != maxWidth || winRes[winRes.Count - 1].height != maxHeight)
				winRes.Add(new Resolution { width = maxWidth, height = maxHeight, refreshRate = 0 });
			resolutions = winRes.ToArray();
		}
		else
		{
			// remove duplicates that somehow keep showing up
			var resStringsUsed = new HashSet<string>();
			var fsRes = new List<Resolution>();
			foreach (var r in resolutions)
			{
				var dr = r.refreshRate > 0 ? $"{r.width} x {r.height} @ {r.refreshRate}Hz" : $"{r.width} x {r.height}";
				if (resStringsUsed.Add(dr) == false) continue;
				fsRes.Add(r);
			}
			resolutions = fsRes.ToArray();
		}
		foreach (var r in resolutions)
		{
			var dr = r.refreshRate > 0 ? $"{r.width} x {r.height} @ {r.refreshRate}Hz" : $"{r.width} x {r.height}";
			options.Add(new TMP_Dropdown.OptionData(dr));
			int w = r.width, h = r.height;
			int dw = w - currW, dh = h - currH;
			var d2 = dw * dw + dh * dh;
			if (d2 < bestSelD2)
			{
				bestSelD2 = d2;
				sel = options.Count - 1;
			}
		}
		dropdown.options = options;
		dropdown.SetValueWithoutNotify(sel);
		//toggle.isOn = s_currentResolutionWindowed;	
		dropdown.onValueChanged.RemoveAllListeners();
		dropdown.onValueChanged.AddListener((r) => SetResolution(resolutions[r], CurrentPrefWindowed));
		//toggle.onValueChanged.AddListener((b) => SetResolution(s_currentResolution, b));
		m_currentSelectedResolution = resolutions[sel];
#endif
	}
    static Resolution[] s_windowedResolutions =
	{
		new () { width = 1280, height = 720, refreshRateRatio = new () {numerator = 60, denominator = 1}},
		new () { width = 1366, height = 768, refreshRateRatio = new() {numerator = 60, denominator = 1}},
		new () { width = 1600, height = 900, refreshRateRatio = new() {numerator = 60, denominator = 1}},
		new () { width = 1920, height = 1080, refreshRateRatio = new() {numerator = 60, denominator = 1}},
		new () { width = 2560, height = 1440, refreshRateRatio = new() {numerator = 60, denominator = 1}},
	};
    Resolution m_currentSelectedResolution;

    void CheckResolution()
    {
	    int w = Screen.width, h = Screen.height;
	    if (w != m_currentSelectedResolution.width || h != m_currentSelectedResolution.height)
		    SetResolution(m_currentSelectedResolution, CurrentPrefWindowed);
    }
}
