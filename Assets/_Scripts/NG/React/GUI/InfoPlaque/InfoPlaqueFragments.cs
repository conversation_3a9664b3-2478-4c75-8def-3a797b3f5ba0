using System;
using System.Collections.Generic;
using UnityEngine;

namespace InfoPlaqueOverhaul {
	public enum EScreenPosition
	{
		raw,
		middle,
		top,
		topRight,
		right,
		bottomRight,
		bottom,
		bottomLeft,
		left,
		topLeft
	}
	public enum EModuleType
	{
		Info,
		InfoShort,
		DesignInfo,
		Title,
		Ratings,
		Upgrade,
		Hint,
		FactoryLayout,
		NPCDialogueTutorial,
		NPCDialogue,
		OrderReward,
		OrderSender,
		OrderCompletionBreakdown,
		BAMCompetitors,
		ProduceBy, //Ratings Flow
		Summary, //Ratings Flow
		SimpleMessage,
		SupplierSelection,
		Tutorial
	}
	public enum EElementType
	{
		Stat,
		LiveStat,
		UpgradableStat,
		WorkerInformation,
		OrderCompletionBonus,
		DynamicProgressBar,
		Button,
		SupplierButton,
		BAMCompetitor,
		TitleAndIcon,
		Comments, //Ratings Flow
		Cost,
		PortraitAndName,
		ImageStatElement,
		ValueStatElement,
		Table,
		IconsStatElementData,
	}
	public static class ModuleDataInstances
	{
		public static InfoPlaqueBehaviourData CameraControllableInfoBehaviour = 
			new InfoPlaqueBehaviourData { IsCameraControlEnabled = true };

		public static EScreenPosition GetScreenPos(string _type)
		{
			if(!s_tutorialStringToScreenPos.ContainsKey(_type.ToLower()))
			{
				Debug.LogErrorFormat("Unable to locate screen position: {0}", _type);
				return s_tutorialStringToScreenPos["middle"];
			}
			return s_tutorialStringToScreenPos[_type.ToLower()];
		}
		
		private static readonly Dictionary<string, EScreenPosition> s_tutorialStringToScreenPos = 
			new Dictionary<string, EScreenPosition>
		{
			{ "middle",         EScreenPosition.middle		},
			{ "top",            EScreenPosition.top		    },
			{ "left",           EScreenPosition.left		},
			{ "right",          EScreenPosition.right		},
			{ "bottom",         EScreenPosition.bottom		},
			{ "topleft",        EScreenPosition.topLeft		},
			{ "topright",       EScreenPosition.topRight	},
			{ "bottomleft",     EScreenPosition.bottomLeft	},
			{ "bottomright",    EScreenPosition.bottomRight	},
		};
	}
	public class InfoPlaqueBezierLineData
	{
		public Transform targetTransform;
		public Vector3 targetOffset;
		public bool targetInScreenSpace;
	}
	public class InfoPlaqueBehaviourData {
		public bool IsCameraControlEnabled = false; // set this to true to allow camera panning while this element is visible
		public EScreenPosition ScreenPosition = EScreenPosition.right;
		public bool IsOffClickDismissable = true;
		public Action<bool> OnDismiss;
		public InfoPlaqueBezierLineData BezierData;
		public bool RestrictInputToThisObject = false; //is not compatible with IsOffClickDismissable
	}
	public class ParentOffClickData {}
	public interface IModuleData
	{
		EModuleType Type { get; }
		bool IsValid();
	}
	public interface IElementData
	{
		EElementType Type { get; }
	}
	public class NPCDialogueDataTutorial : IModuleData {
		public EModuleType Type { get { return EModuleType.NPCDialogueTutorial; } }
		public bool IsValid()
		{
			return
				!Title.IsNullOrWhiteSpace() &&
				!Dialogue.IsNullOrWhiteSpace();
		}
        public string Title;
		public string Dialogue;
		public Sprite Portrait;
        public Action OnDismissCallback;
	}
	public class InfoModuleData : IModuleData {
		public EModuleType Type { get { return EModuleType.Info; } }
		public bool IsValid() { return true; }
	}
    public class ButtonData : IElementData
	{
        public EElementType Type { get { return EElementType.Button; } }
		public string Text;
		public Action OnClickCallback;
        public bool DismissInfoPlaqueOnPress;
		public Func<bool> ReregisterParentOffClickOnPress = null;
		public bool DisableBtnOnClick = false;
		public bool MakeInteractable = true;
		public int Money = int.MinValue;
		public int Gold = int.MinValue;
		public int Tickets = int.MinValue;
		public int Bams = int.MinValue;
		public int Fabric = int.MinValue;
		public int Produce = int.MinValue;
		public int Metal = int.MinValue;
		public int Mineral = int.MinValue;
		public int Wood = int.MinValue;
		public bool IsDouble = false; // AM 18-12-18, hacky addition to specifiy which button prefab we want to use
		public bool ShouldReregisterParentOffClickOnPress()
		{
			if (ReregisterParentOffClickOnPress == null)
				return true;
			return ReregisterParentOffClickOnPress();
		}
	}
	public abstract class TintableUI : MonoEditorDebug {
		public virtual void TintComponents(Color _colour) {
			/*TODO for (int i = 0; i < m_tintableText.Length; i++)
				if (m_tintableText[i] != null)
					m_tintableText[i].color = _colour;
			for (int i = 0; i < m_tintableImages.Length; i++)
				if (m_tintableImages[i] != null)
					m_tintableImages[i].color = _colour;*/
		}
	}
	public abstract class InfoPlaqueModule : TintableUI {
		public abstract void Initialise(IModuleData _data);
		public abstract string ModuleName { get; }
	}
	public abstract class InfoPlaqueElement : TintableUI {
	}

	public abstract class InfoPlaque : MonoEditorDebug {
		protected InfoPlaqueBehaviourData m_behaviour;
		public bool IsInteractable { get{ return !m_isDismissed; } }
		public InfoPlaqueBehaviourData Behaviour { get { return m_behaviour; } }
		protected bool m_isDismissed = false;
		public bool IsDismissed => m_isDismissed;
		public static InfoPlaque s_currentInstance;
		protected InfoPlaquePrefabReferenceHolder m_references;
		private bool m_initialized;

		protected virtual void Start()
		{
			//AudioClipManager.Me.PlaySound("PlaySound_OldManPopUp", transform);
			//TODO m_references.AnimHandler.PlaySingleAnimation(ANIM_APPEAR, (x) => { m_isPlayingOpenAnimation = false; });
			//TODO transform.localScale = Vector3.zero;
			m_initialized = true;

			var _data = Behaviour.BezierData;
			if(_data != null)
				;//TODO InitialiseBezierTarget(_data.targetTransform, _data.targetOffset, _data.targetInScreenSpace);
		}

		public virtual void AssignReferences(InfoPlaquePrefabReferenceHolder _reference, InfoPlaqueBehaviourData _behaviour)
		{
			m_references = _reference;
			m_behaviour = _behaviour ?? new InfoPlaqueBehaviourData();
		}
		protected void OnInitialisationComplete() {
			SetPosition();
		}
		protected virtual void SetPosition() {}
		public void DismissByInternalButton(ButtonData _btnData)
		{
			DismissInternal(true, _btnData);
		}
		public void Dismiss()
        {
            DismissInternal(true);
        }

		// -------------------------------------------------------------------------------------------------
		void DismissInternal(bool _deregisterOffClick = true, ButtonData _dismissedByBtn = null)
		{
			// Don't fire offclicks multiple times
			if(m_isDismissed)
				return;

			//AudioClipManager.Me.PlaySound(InfoPlaqueManager.Me.m_closePlaqueSound, transform);
			CleanUpBezierLine();

			m_isDismissed = true;
			GameManager.OnSceneLoad -= Dismiss; 
			s_currentInstance = null;
			Behaviour.OnDismiss?.Invoke(_dismissedByBtn != null);

		   	/*TODO if (!Global.Me.IsSceneLoading && (m_initialized && gameObject.activeInHierarchy))
				m_references.AnimHandler.PlaySingleAnimation(ANIM_DISAPPEAR, (x) => {
					Destroy(gameObject);
				});
			else */{
				Destroy(gameObject);
			}
		}
		void CleanUpBezierLine() {}
	}
	public class InfoPlaqueTypeT : InfoPlaque {
	}

	public class SimpleMessageData : IModuleData
	{
		public EModuleType Type { get { return EModuleType.SimpleMessage; } }

		public string Title;
		public string Description;
		//public List<ValueStatElementData> Stats;
		public float Bams;

		public bool IsValid()
		{
			return
				!Title.IsNullOrWhiteSpace();
		}
	}
}
