using UnityEngine;
using UnityEngine.Playables;

[System.Serializable]
public class CameraControlTransitionBehaviour : PlayableBehaviour
{
	public float normalized_position = 0f;
	public float normalized_rotation = 0f;

	public override void ProcessFrame(Playable playable, FrameData info, object playerData)
	{
		var transitionReference = playerData as CameraTransitionReference;
		Debug.Assert( transitionReference != null, "Camera transition reference is null." );
		transitionReference.reference.ApplyTransitionBehaviour( this );
	}
}

