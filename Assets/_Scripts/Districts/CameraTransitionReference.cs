using UnityEngine;

[System.Serializable]
public class CameraTransitionReference : ScriptableObject {
	public TimelineCameraTransition reference { get; private set; }

	public Vector3 initialPosition { get; private set; }
	public Vector3 initialRotation { get; private set; }

	// This is me cheating, CameraMovement is a monster.
	public Transform cameraReference { get; private set; }

	public void Init( Camera camera ) {
		Debug.Assert( cameraReference == null, "Reference has already been init'd" );
		cameraReference = camera.transform;
		initialPosition = cameraReference.localPosition;
		initialRotation = cameraReference.localRotation.eulerAngles;
		reference = null;
	}

	public void ResetTimelineCameraTransition( TimelineCameraTransition transition ) {
		initialPosition = cameraReference.localPosition;
		initialRotation = cameraReference.localRotation.eulerAngles;
		reference = transition;
	}
}