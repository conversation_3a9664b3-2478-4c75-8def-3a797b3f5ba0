using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;

public class CameraControlOffsetAsset : PlayableAsset
{
	public CameraControlOffsetBehaviour template;

	public override Playable CreatePlayable(PlayableGraph graph, GameObject owner)
	{
		var playable = ScriptPlayable<CameraControlOffsetBehaviour>.Create(graph, template);
       	return playable;   
	}
}