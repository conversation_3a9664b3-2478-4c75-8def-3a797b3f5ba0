using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;

[System.Serializable]
public class CanvasGroupControlBehaviour : PlayableBehaviour
{
	public float alpha = 0f;

	public override void ProcessFrame(Playable playable, FrameData info, object playerData)
	{
		CanvasGroup canvasGroup = playerData as CanvasGroup;
		Debug.Assert( canvasGroup != null, "Canvas group has not been assigned." );
		canvasGroup.alpha = alpha;
	}
}

