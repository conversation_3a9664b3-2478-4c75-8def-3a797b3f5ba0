//#define UPGRADE_TO_PATHS

using System.Collections.Generic;
using UnityEngine;

public enum AttachmentBone
{
    NONE,
    LEFT_HAND,
    RIGHT_HAND,
    HAT,
    BODY,
    LAST,
}

[System.Serializable]
public class AnimationClipData
{
    [SerializeField] public string Name;
#if UPGRADE_TO_PATHS
    [SerializeField] public AnimationClip Clip;
#else
	private AnimationClip CachedClip;
	private bool CachedClipRead = false;
	public AnimationClip Clip {
		get {
			if (!CachedClipRead) {
				CachedClipRead = true;
				var allClips = ResManager.LoadAll<AnimationClip>(ClipPath);
				CachedClip = allClips.Length == 1 ? allClips[0] : allClips.Find(ac => ac.name == ClipName);
				//Debug.Log($"CachedClip read from {ClipPath}", CachedClip);
			}
			return CachedClip;
		}
		set {
#if UNITY_EDITOR
			if (CachedClip == null || value.GetInstanceID() != CachedClip.GetInstanceID()) {
				var newPath =  AssetUtils.GetResourcePath(value);
				if (!string.IsNullOrEmpty(newPath)) {
					ClipName = value.name;
					ClipPath = newPath;
					CachedClipRead = false;
				}
			}
#endif
        }
	}
#endif
	[SerializeField] public string ClipName;
    [SerializeField] public string ClipPath;
	[SerializeField] public float BlendTime = 0.25f; // This is the default blend time, which was defined by c_BlendingTime in AnimationOverride.
	[SerializeField] public string AttachedAnimationName;
    [SerializeField] public List<AnimationAttachData> AttachData = new List<AnimationAttachData>();
	[SerializeField] public List<AnimationTagData> TagData = new List<AnimationTagData>();

	public AnimationClipData() {}
	
    public AnimationClipData(string _name, AnimationClip _clip, float _blendTime)
	{
		Name = _name;
		Clip = _clip;
		BlendTime = _blendTime;
	}

	public void Upgrade() {
		CachedClipRead = false;
#if UNITY_EDITOR
#if UPGRADE_TO_PATHS
		if (string.IsNullOrEmpty(ClipPath)) {
			ClipPath = AssetUtils.GetResourcePath(Clip);			
		}
#endif
#endif
    }
}

[System.Serializable]
public class AnimationAttachData
{
	[SerializeField] public AttachmentBone AttachBone;
	[SerializeField] public GameObject PropPrefab;
}

[System.Serializable]
public class AnimationTagData
{
	[SerializeField] public string Tag;

	private AnimationClip CachedClip;
	private bool CachedClipRead = false;
	public AnimationClip Clip {
		get {
			if (!CachedClipRead) {
				CachedClipRead = true;
				CachedClip = ResManager.Load<AnimationClip>(ClipPath);
				Debug.Log($"CachedClip read from {ClipPath}", CachedClip);
			}
			return CachedClip;
		}
	}
    [SerializeField] public string ClipPath;	
}