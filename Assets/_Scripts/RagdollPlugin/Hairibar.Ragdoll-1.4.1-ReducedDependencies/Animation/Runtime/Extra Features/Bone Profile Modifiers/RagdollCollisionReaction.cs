using System;
using System.Collections.Generic;
using Hairibar.EngineExtensions;
using Hairibar.NaughtyExtensions;
using UnityEngine;

namespace Hairibar.Ragdoll.Animation
{
    /// <summary>
    /// Lowers a ragdoll bone's alpha when it collides with something.
    /// </summary>
    [AddComponentMenu("Ragdoll/Ragdoll Collision Reaction"), RequireComponent(typeof(RagdollAnimator))]
    public class RagdollCollisionReaction : MonoBehaviour
    {
        // RagdollCollisionEventDispatcher collisionEventDispatcher = null;
        // public RagdollCollisionEventDispatcher CollisionEventDispatcher
        // {
        //     get { return collisionEventDispatcher; }
        // }

        // public event Action<RagdollCollisionEventDispatcher> OnDispatcherCreated;

        // #region Initialization
        // public void Initialize(IEnumerable<RagdollAnimator.AnimatedPair> pairs)
        // {
        //     SetUpCollisionEventDispatcher();
        // }

        // void SetUpCollisionEventDispatcher()
        // {
        //     RagdollDefinitionBindings bindings = GetComponent<RagdollAnimator>().RagdollBindings;
        //     collisionEventDispatcher = bindings.gameObject.AddComponent<RagdollCollisionEventDispatcher>();

        //     collisionEventDispatcher.OnCollisionEnter += CollisionListener;

        //     OnDispatcherCreated?.Invoke(collisionEventDispatcher);
        // }
        // #endregion

        // void CollisionListener(Collision collision, RagdollBone bone)
        // {
            
        // }
        
        // void OnDestroy()
        // {
        //     if (collisionEventDispatcher)
        //     {
        //         Destroy(collisionEventDispatcher);
        //     }
        // }
    }
}
