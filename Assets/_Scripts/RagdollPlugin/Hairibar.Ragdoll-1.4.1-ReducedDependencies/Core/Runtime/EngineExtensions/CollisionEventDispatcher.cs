using UnityEngine;

namespace Hairibar.EngineExtensions
{
    public class CollisionEventDispatcher : MonoBehaviour
    {
        public event System.Action<Collision, CollisionEventDispatcher> OnCollisionEntered;
        public event System.Action<Collision, CollisionEventDispatcher> OnCollisionExited;

        public event System.Action<Collider, CollisionEventDispatcher> OnTriggerEntered;
        public event System.Action<Collider, CollisionEventDispatcher> OnTriggerExited;
        
        void OnCollisionEnter(Collision collision)
        {
            OnCollisionEntered?.Invoke(collision, this);
        }

        void OnCollisionExit(Collision collision)
        {
            OnCollisionExited?.Invoke(collision, this);
        }

        void OnTriggerEnter(Collider other)
        {
            OnTriggerEntered?.Invoke(other, this);
        }

        void OnTriggerExit(Collider other)
        {
            OnTriggerExited?.Invoke(other, this);
        }
    }
}