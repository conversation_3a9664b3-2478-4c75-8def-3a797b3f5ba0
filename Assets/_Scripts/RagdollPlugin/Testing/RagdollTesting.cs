using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Hairibar.Ragdoll.Animation;
using UnityEngine;
using TMPro;
#if UNITY_EDITOR
using UnityEditor;
using System.IO;
#endif

namespace Hairibar.Ragdoll.Demo
{
    public class RagdollTesting : MonoBehaviour
    {
        private int commandCode = 0;

        private List<string> characterNames = new List<string>
        {
            "Giant",
            "Zombie",
            "Undead",
            "Werewolf",
            "Worker",
            "Tourist",
        };
        private int characterNameIndex = 0;
        [SerializeField]
        private TextMeshProUGUI bottomText = null;
        public TextMeshProUGUI BottomText => bottomText;

        private MACharacterBase character = null;
        private Rigidbody body = null;
        
        private float explosionSpeed = 0f;
        private float throwSpeed = 0f;
        [SerializeField]
        private Transform throwDestination = null;

        [SerializeField]
        private Transform spawnPoint = null;

        private Vector3 cameraPos = Vector3.zero;
        private Vector3 cameraRot = Vector3.zero;

        private bool uiNeedsRefresh = false;
        private float keyPressElapsedTime = 0f;

        private bool sceneReady = false;
        
        public void Start()
        {
            StartCoroutine(Co_InitTestingScene());
        }

        public IEnumerator Co_InitTestingScene()
        {
            NGBlockInfo.LoadInfo();
            MAAttackSkill.LoadInfo();
            MAAttackCombo.LoadInfo();
            MACreatureInfo.LoadInfo();
            MAWorkerInfo.LoadInfo();
            StickerData.LoadInfo();
            MACameraControl.LoadInfo();
            GameManager.Me.enabled = true;
            GameManager.Me.DataReady = true;
            GameManager.Me.LoadComplete = true;

            yield return null;

            SpawnCharacter();

            yield return null;

            keyPressElapsedTime = 0f;
            uiNeedsRefresh = true;
            sceneReady = true;

            // var assembly = Assembly.GetAssembly(typeof(UnityEditor.Editor));
            // var type = assembly.GetType("UnityEditor.LogEntries");
            // var method = type.GetMethod("Clear");
            // method.Invoke(new object(), null);
        }

        public void SpawnCharacter()
        {
            if (character != null)
            {
                Destroy(character.gameObject);
                character = null;
                body = null;
            }

            var pos = spawnPoint.position.AboveGround(1f);
            switch (characterNames[characterNameIndex])
            {
                case "Giant":
                {
                    character = MACharacterBase.Create(MACreatureInfo.GetInfo("Giant"), pos);
                    character.CharacterUpdateState.ApplyState(CharacterStates.Idle);
                    
                    explosionSpeed = 100f;
                    throwSpeed = 2200f;

#if UNITY_EDITOR
                    ragdollTestingProfile = heroRagdollTestingProfile;
#endif
                    break;
                }
                case "Zombie":
                {
                    character = MACharacterBase.Create(MACreatureInfo.GetInfo("Zombie"), pos);
                    character.CharacterUpdateState.ApplyState(CharacterStates.Idle);

                    explosionSpeed = 20f;
                    throwSpeed = 1200f;

#if UNITY_EDITOR
                    ragdollTestingProfile = zombieRagdollTestingProfile;
#endif
                    break;
                }
                case "Undead":
                {
                    character = MACharacterBase.Create(MACreatureInfo.GetInfo("Undead"), pos);
                    character.CharacterUpdateState.ApplyState(CharacterStates.Idle);

                    explosionSpeed = 20f;
                    throwSpeed = 1200f;

#if UNITY_EDITOR
                    ragdollTestingProfile = undeadRagdollTestingProfile;
#endif
                    break;
                }
                case "Werewolf":
                {
                    character = MACharacterBase.Create(MACreatureInfo.GetInfo("Werewolf"), pos);
                    character.CharacterUpdateState.ApplyState(CharacterStates.Idle);

                    explosionSpeed = 20f;
                    throwSpeed = 1200f;

#if UNITY_EDITOR
                    ragdollTestingProfile = werewolfRagdollTestingProfile;
#endif
                    break;
                }
                case "Worker":
                {
                    character = MAWorker.Create(MAWorkerInfo.GetInfo("CommonWorker"), pos);
                    character.SetState(NGMovingObject.STATE.IDLE);

                    explosionSpeed = 50f;
                    throwSpeed = 1500f;
                    break;
                }
                case "Tourist":
                {
                    character = MATourist.Create(MAWorkerInfo.GetInfo("TouristMale"), pos, 1, null);
                    character.SetState(NGMovingObject.STATE.IDLE);

                    explosionSpeed = 50f;
                    throwSpeed = 1500f;
                    break;
                }
            }
            body = character.GetComponent<Rigidbody>();
            uiNeedsRefresh = true;

            cameraPos = character.transform.position + new Vector3(10f, 5f, 10f);
            cameraRot = Quaternion.LookRotation(character.transform.position - cameraPos).eulerAngles;
        }
        
        public void FixedUpdate()
        {
            if (!sceneReady)
                return;
            
            if (commandCode == 100)
            {
                var ras = GetComponentsInChildren<RagdollAnimator>(false);
                foreach (RagdollAnimator ra in ras)
                {
                    ra.SnapToTargetPose();
                }
            }
            if (commandCode == 200)
            {
                var rcs = GetComponentsInChildren<RagdollController>(false);
                foreach (RagdollController rc in rcs)
                {
                    rc.AdjustRagdollToTargetPose();
                }
            }
            if (commandCode == 900)
            {
                var rcs = GetComponentsInChildren<RagdollController>(false);
                foreach (RagdollController rc in rcs)
                {
                    rc.ResetJoints();
                }
            }
            if (commandCode == 300)
            {
                character.ActivateRagDoll();
            }
            if (commandCode == 400)
            {
                var rcs = GetComponentsInChildren<RagdollController>(false);
                foreach (RagdollController rc in rcs)
                {
                    rc.StartAnimatedState();
                }
            }
            if (commandCode == 500)
            {
                var rcs = GetComponentsInChildren<RagdollController>(false);
                foreach (RagdollController rc in rcs)
                {
                    rc.StartResponsiveState();
                }
            }

            commandCode = 0;
        }

        public void Update()
        {
            if (!sceneReady)
                return;
            
            if (Input.GetKeyDown(KeyCode.Alpha1))
            {
                SpawnCharacter();
            }
            if (Input.GetKeyDown(KeyCode.Alpha2))
            {
                ApplyExplosionForce();
            }
            if (Input.GetKeyDown(KeyCode.Alpha3))
            {
                RagdollHelper.ThrowObjectRagdoll(character.gameObject, throwDestination.position, throwSpeed, (b) => {}, null);
                MACharacterStateFactory.ApplyCharacterState(CharacterStates.KnockedDown, character);
            }
            
            if (Input.GetKeyDown(KeyCode.RightArrow))
            {
                characterNameIndex = ++characterNameIndex % characterNames.Count;
                uiNeedsRefresh = true;
            }
            if (Input.GetKeyDown(KeyCode.LeftArrow))
            {
                characterNameIndex = --characterNameIndex < 0 ? characterNames.Count - 1 : characterNameIndex;
                uiNeedsRefresh = true;
            }

            float maxExplosionSpeed = 500f;
            if (Input.GetKeyDown(KeyCode.RightBracket))
            {
                keyPressElapsedTime = 0f;
                explosionSpeed = Mathf.Clamp(explosionSpeed + 1f, 0f, maxExplosionSpeed);
                
                uiNeedsRefresh = true;
            }
            if (Input.GetKey(KeyCode.RightBracket))
            {
                if (keyPressElapsedTime >= 0.25f)
                {
                    keyPressElapsedTime = 0f;
                    explosionSpeed = Mathf.Clamp(explosionSpeed + 5f, 0f, maxExplosionSpeed);
                }
                else
                {
                    keyPressElapsedTime += Time.deltaTime;
                }

                uiNeedsRefresh = true;
            }
            if (Input.GetKeyDown(KeyCode.LeftBracket))
            {
                keyPressElapsedTime = 0f;
                explosionSpeed = Mathf.Clamp(explosionSpeed - 1f, 0f, maxExplosionSpeed);
                
                uiNeedsRefresh = true;
            }
            if (Input.GetKey(KeyCode.LeftBracket))
            {
                if (keyPressElapsedTime >= 0.25f)
                {
                    keyPressElapsedTime = 0f;
                    explosionSpeed = Mathf.Clamp(explosionSpeed - 5f, 0f, maxExplosionSpeed);
                }
                else
                {
                    keyPressElapsedTime += Time.deltaTime;
                }

                uiNeedsRefresh = true;
            }

            float maxThrowSpeed = 5000f;
            if (Input.GetKeyDown(KeyCode.Period))
            {
                keyPressElapsedTime = 0f;
                throwSpeed = Mathf.Clamp(throwSpeed + 1f, 0f, maxThrowSpeed);
                
                uiNeedsRefresh = true;
            }
            if (Input.GetKey(KeyCode.Period))
            {
                if (keyPressElapsedTime >= 0.25f)
                {
                    keyPressElapsedTime = 0f;
                    throwSpeed = Mathf.Clamp(throwSpeed + 10f, 0f, maxThrowSpeed);
                }
                else
                {
                    keyPressElapsedTime += Time.deltaTime;
                }

                uiNeedsRefresh = true;
            }
            if (Input.GetKeyDown(KeyCode.Comma))
            {
                keyPressElapsedTime = 0f;
                throwSpeed = Mathf.Clamp(throwSpeed - 1f, 0f, maxThrowSpeed);
                
                uiNeedsRefresh = true;
            }
            if (Input.GetKey(KeyCode.Comma))
            {
                if (keyPressElapsedTime >= 0.25f)
                {
                    keyPressElapsedTime = 0f;
                    throwSpeed = Mathf.Clamp(throwSpeed - 10f, 0f, maxThrowSpeed);
                }
                else
                {
                    keyPressElapsedTime += Time.deltaTime;
                }

                uiNeedsRefresh = true;
            }

            if (Input.GetKey(KeyCode.B))
            {
                character.m_ragdollController.StartRagdolledState();
                character.m_ragdollController.ScheduleBreakJoints();
            }

            if (uiNeedsRefresh)
            {
                uiNeedsRefresh = false;

                bottomText.text = "1 Spawn " + characterNames[characterNameIndex];
                bottomText.text += " - 2 Explosion " + explosionSpeed;
                bottomText.text += " - 3 Throw " + throwSpeed;
            }
        }

        private void ApplyExplosionForce()
        {
            const float impulseBaseMass = 50f;
            float scaledPower = explosionSpeed * (impulseBaseMass / body.mass);
            float impulse = impulseBaseMass * scaledPower;
            Vector3 forceDir = (throwDestination.position - character.transform.position).normalized;

            var forceDirXZ = forceDir.GetXZNorm();
            float absDirX = Mathf.Abs(forceDirXZ.x);
            float absDirZ = Mathf.Abs(forceDirXZ.z);
            float forceDirY = (absDirX > absDirZ) ? absDirX : absDirZ;
            const float attackForceDirYFactor = 3f;
            var forceDirXZY = (forceDirXZ + (Vector3.up * forceDirY * attackForceDirYFactor)).normalized;
            character.ActivateRagDoll(forceDirXZY * impulse, (_interrupted) => { });
            MACharacterStateFactory.ApplyCharacterState(CharacterStates.KnockedDown, character);
        }

        [SerializeField]
        private RagdollController.RagdollProfileState heroRagdollTestingProfile = null;
        [SerializeField]
        private RagdollController.RagdollProfileState zombieRagdollTestingProfile = null;
        [SerializeField]
        private RagdollController.RagdollProfileState undeadRagdollTestingProfile = null;
        [SerializeField]
        private RagdollController.RagdollProfileState werewolfRagdollTestingProfile = null;
#if UNITY_EDITOR
        [Serializable]
        public class RagdollTestingDataList
        {
            public List<RagdollTestingData> datas = new List<RagdollTestingData>();
        }

        [Serializable]
        public class RagdollTestingData
        {
            public string boneName = "";
            public Vector3 rotationAxis = Vector3.zero;
            public Vector3 rotationSecondaryAxis = Vector3.zero;
            public float lowXLimit = 0f;
            public float highXLimit = 0f;
            public float yLimit = 0f;
            public float zLimit = 0f;
        };

        private RagdollController.RagdollProfileState ragdollTestingProfile = null;

        [SerializeField]
        private GameObject prefabRagdollToSetup = null;

        [SerializeField]
        private List<RagdollController.RagdollTestingSetup> ragdollTestingSetups = null;

        private string ragdollTestingFileName = "RagdollTesting.json";
        
        public void CreateDefaultRagdollTestingSetups()
        {
            ragdollTestingSetups = new List<RagdollController.RagdollTestingSetup>();

            string[] ragdollBoneTypeNames = System.Enum.GetNames(typeof(RagdollController.RagdollBoneType));
            foreach (var rbtn in ragdollBoneTypeNames)
            {
                var type = (RagdollController.RagdollBoneType)System.Enum.Parse(typeof(RagdollController.RagdollBoneType), rbtn);
                if (type == RagdollController.RagdollBoneType.None)
                    continue;
                
                ragdollTestingSetups.Add(
                    new RagdollController.RagdollTestingSetup
                    {
                        boneType = type,
                    });
            }

            UnityEngine.Debug.LogError("RAGDOLLTESTING - Ragdoll Testing Setups Created!");
        }

        public void PopulateRagdollTestingSetupsFromFile()
        {
            string fileName = Application.persistentDataPath + "/" + ragdollTestingFileName;
            if (!File.Exists(fileName))
                return;
            
            var testingJson = File.ReadAllText(fileName);
            var testingDataList = JsonUtility.FromJson<RagdollTestingDataList>(testingJson);
            
            ragdollTestingSetups = new List<RagdollController.RagdollTestingSetup>();
            
            var namePrefix = "mixamorig:";
            foreach (var data in testingDataList.datas)
            {
                var typeName = data.boneName.Remove(0, namePrefix.Length);
                var type = (RagdollController.RagdollBoneType)System.Enum.Parse(typeof(RagdollController.RagdollBoneType), typeName);
                if (type == RagdollController.RagdollBoneType.None)
                    continue;
                
                ragdollTestingSetups.Add(
                    new RagdollController.RagdollTestingSetup
                    {
                        boneType = type,
                        rotationAxis = data.rotationAxis,
                        rotationSecondaryAxis = data.rotationSecondaryAxis,
                        lowXLimit = data.lowXLimit,
                        highXLimit = data.highXLimit,
                        yLimit = data.yLimit,
                        zLimit = data.zLimit
                    });
            }

            UnityEngine.Debug.LogError("RAGDOLLTESTING - Ragdoll Testing Setups Populated!");
        }

        public void SelectRagdollTransformsFromRagdollTestingSetups()
        {
            character.m_ragdollController.SelectRagdollTestingObjects();
        }

        public void ExportRagdollTestingSetupsToFile()
        {
            string fileName = Application.persistentDataPath + "/" + ragdollTestingFileName;
            if (File.Exists(fileName))
            {
                File.Copy(fileName, fileName + "_" + DateTime.Now.ToString("yyyy-MM-dd-HH-mm-ss") + ".txt");
                File.Delete(fileName);
            }
            
            var testingDataList = new RagdollTestingDataList();
            foreach (var setup in ragdollTestingSetups)
            {
                var data = new RagdollTestingData();
                var boneName = "mixamorig:" + Enum.GetName(typeof(RagdollController.RagdollBoneType), setup.boneType);
                data.boneName = boneName;
                data.rotationAxis = setup.rotationAxis;
                data.rotationSecondaryAxis = setup.rotationSecondaryAxis;
                data.lowXLimit = setup.lowXLimit;
                data.highXLimit = setup.highXLimit;
                data.yLimit = setup.yLimit;
                data.zLimit = setup.zLimit;
                testingDataList.datas.Add(data);
            }

            var testingJson = JsonUtility.ToJson(testingDataList);
            File.WriteAllText(fileName, testingJson);

            UnityEngine.Debug.LogError("RAGDOLLTESTING - Ragdoll Testing Setups Exported!");
        }

        public void ApplyRagdollTestingSetupsToPrefabRagdoll()
        {
            foreach (var setup in ragdollTestingSetups)
            {
                var boneName = "mixamorig:" + Enum.GetName(typeof(RagdollController.RagdollBoneType), setup.boneType);
                var bone = prefabRagdollToSetup.transform.FindChildRecursiveByName(boneName);

                var joint = bone.GetComponent<ConfigurableJoint>();
                joint.axis = setup.rotationAxis;
                joint.secondaryAxis = setup.rotationSecondaryAxis;
                var lowXLimit = joint.lowAngularXLimit;
                lowXLimit.limit = setup.lowXLimit;
                joint.lowAngularXLimit = lowXLimit;
                var highXLimit = joint.highAngularXLimit;
                highXLimit.limit = setup.highXLimit;
                joint.highAngularXLimit = highXLimit;
                var yLimit = joint.angularYLimit;
                yLimit.limit = setup.yLimit;
                joint.angularYLimit = yLimit;
                var zLimit = joint.angularZLimit;
                zLimit.limit = setup.zLimit;
                joint.angularZLimit = zLimit;

                var jointFixer = bone.GetComponent<ConfigurableJointFixer>();
                jointFixer.SetupJointFixerProperties();
            }

            UnityEngine.Debug.LogError("RAGDOLLTESTING - Ragdoll Testing Setups Applied!");
        }

        public void ApplyRagdollTestingSetupsToCharacterRagdoll()
        {
            character.m_ragdollController.ApplyRagdollTestingSetups(ragdollTestingSetups);
        }

        public void StartCharacterRagdollTesting()
        {
            character.m_ragdollController.StartRagdollTestingState(ragdollTestingSetups, ragdollTestingProfile);

            var rb = character.GetComponent<Rigidbody>();
            rb.isKinematic = true;
            rb.transform.position += Vector3.up * 2f;

            UnityEngine.Debug.LogError("RAGDOLLTESTING - Ragdoll Testing Started!");
        }
#endif
    }


#if UNITY_EDITOR
    [CanEditMultipleObjects]
    [CustomEditor(typeof(RagdollTesting))]
    public class RagdollTestingEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            base.OnInspectorGUI();
            
            RagdollTesting testing = (RagdollTesting)target;

            if (GUILayout.Button($"Create Default RagdollTesting Setups"))
            {
                testing.CreateDefaultRagdollTestingSetups();
            }
            if (GUILayout.Button($"Populate RagdollTesting Setups From File"))
            {
                testing.PopulateRagdollTestingSetupsFromFile();
            }

            GUILayout.Space(5f);

            if (GUILayout.Button($"Select Ragdoll Transforms From RagdollTesting Setups"))
            {
                testing.SelectRagdollTransformsFromRagdollTestingSetups();
            }

            GUILayout.Space(5f);

            if (GUILayout.Button($"Export RagdollTesting Setups To File"))
            {
                testing.ExportRagdollTestingSetupsToFile();
            }
            if (GUILayout.Button($"Apply RagdollTesting Setups To Prefab Ragdoll"))
            {
                testing.ApplyRagdollTestingSetupsToPrefabRagdoll();
            }

            GUILayout.Space(10f);

            if (GUILayout.Button($"Apply RagdollTesting Setups To Character Ragdoll"))
            {
                testing.ApplyRagdollTestingSetupsToCharacterRagdoll();
            }

            if (GUILayout.Button($"Start Character RagdollTesting"))
            {
                testing.StartCharacterRagdollTesting();
            }
        }
    }
#endif
}
