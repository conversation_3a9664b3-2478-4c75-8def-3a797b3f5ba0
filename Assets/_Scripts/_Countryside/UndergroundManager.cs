using System.Collections;
using System.Collections.Generic;
using UnityEngine;


public class UndergroundManager : MonoBehaviour
{
    public static UndergroundManager Me;
    
    public Transform m_threats;
    public Transform m_adventurer;
    public bool m_undergroundCamera = false;
    public Camera m_undergroundCameraObject;
    private Dictionary<Transform, float> m_deadList = new();
    private float m_attackProgress = 0, m_attackAngle = 0;
    private int m_attackThreat = -1;

    public static bool IsActive => Me != null && Me.gameObject.activeSelf;
    
    void Awake()
    {
        Me = this;
    }

    void Update()
    {
        if (GameManager.Me.m_camera.gameObject.activeSelf == m_undergroundCamera) GameManager.Me.m_camera.gameObject.SetActive(!m_undergroundCamera);
        
        var zoom = GameManager.Me.GetZoom();
        m_undergroundCameraDistance = Mathf.Clamp(m_undergroundCameraDistance + zoom * -10 * Time.deltaTime, 7, 15);
        
        var cutout = transform.GetChild(0).gameObject;
        var topAndBottom = transform.GetChild(1).gameObject;
        if (cutout.activeSelf == m_undergroundCamera)
            cutout.SetActive(!m_undergroundCamera);
        if (topAndBottom.activeSelf == !m_undergroundCamera)
            topAndBottom.SetActive(m_undergroundCamera);
        if (m_undergroundCameraObject.gameObject.activeSelf != m_undergroundCamera)
        {
            GameManager.Me.m_camera.gameObject.SetActive(!m_undergroundCamera);
            m_undergroundCameraObject.gameObject.SetActive(m_undergroundCamera);
        }

        var advRB = m_adventurer.GetComponent<Rigidbody>();
        advRB.angularVelocity = Vector3.zero;

        if (m_attackProgress > 0)
        {
            var threat = m_threats.GetChild(m_attackThreat);
            var toThreat = (threat.position - m_adventurer.position).GetXZNorm();
            if (m_attackProgress <= .0011f && Vector3.Dot(m_adventurer.forward, toThreat) < .8f)
            {
                m_adventurer.rotation = Quaternion.Slerp(m_adventurer.rotation, Quaternion.LookRotation(toThreat, Vector3.up), .3f);
                m_attackAngle = m_adventurer.eulerAngles.y;
            }
            else
            {
                const float c_attackLength = .25f;
                var attackProgressWas = m_attackProgress;
                m_attackProgress += Time.deltaTime / c_attackLength;
                if (m_attackProgress >= .8f && attackProgressWas < .8f)
                {
                    var rb = threat.GetComponent<Rigidbody>();
                    threat.forward = toThreat;
                    rb.linearVelocity = toThreat * 20 + Vector3.up * 20;
                    m_deadList[threat] = 0;
                    var psys = threat.GetComponentInChildren<ParticleSystem>();
                    psys.Play();
                }
                else if (m_attackProgress >= 1f)
                {
                    m_attackProgress = 0;
                }
                m_adventurer.eulerAngles = Vector3.up * (m_attackAngle + Mathf.Sin(m_attackProgress * Mathf.PI * 2) * 40);
            }
        }
        else
        {        
            int index = FindClosestLivingThreat();
            if (index != -1)
            {
                var threat = m_threats.GetChild(index);
                if (MoveTowards(m_adventurer, threat))
                {
                    // attack
                    m_attackProgress = .001f;
                    m_attackThreat = index;
                }
            }
            else
            {
                advRB.linearVelocity = Vector3.Lerp(advRB.linearVelocity, Vector3.zero, .2f);
            }
        }
        int numAlive = 0;
        for (int i = 0; i < m_threats.childCount; ++i)
        {
            var threat = m_threats.GetChild(i);
            var rb = threat.GetComponent<Rigidbody>();
            rb.angularVelocity = Vector3.zero;
            if (m_deadList.TryGetValue(threat, out var time))
            {
                time += Time.deltaTime;
                m_deadList[threat] = time;
                var rotDest = (threat.forward.normalized + Vector3.up * 5).normalized;
                var fwd = Vector3.Slerp(threat.forward, rotDest, .1f);
                var up = Vector3.Cross(fwd, threat.right);
                threat.LookAt(threat.position + fwd, up);
                const float c_deathTime = 1;
                if (time < c_deathTime)
                {
                    ++numAlive;
                }
                else
                {
                    threat.localPosition = new Vector3(Random.Range(5f, 6f) * (Random.Range(0, 2) * 2 - 1), threat.localPosition.y, Random.Range(-2f, 2f));
                    threat.eulerAngles = Vector3.zero;
                    rb.linearVelocity = Vector3.zero;
                    m_deadList.Remove(threat);
                }
            }
            else
            {
                MoveTowards(threat, m_adventurer);
                ++numAlive;
            }
        }
    }

    public float m_undergroundCameraDistance = 12;
    void LateUpdate()
    {
        if (m_undergroundCamera)
        {
            var cam = m_undergroundCameraObject.transform;
            var focus = m_adventurer.position;
            cam.forward = transform.forward;
            var camPos = focus - cam.forward * m_undergroundCameraDistance + Vector3.up * 1; 
            cam.position = Vector3.Lerp(cam.position, camPos, .1f);
        }
    }

    void OnDisable()
    {
        if (m_undergroundCamera)
        {
            if (GameManager.Me != null) GameManager.Me.m_camera.gameObject.SetActive(true);
        }
    }

    bool MoveTowards(Transform _who, Transform _target)
    {
        var d = _target.position - _who.position;
        const float c_hitDistance = .7f;
        if (d.xzSqrMagnitude() < c_hitDistance * c_hitDistance)
        {
            return true;
        }
        d = d.GetXZNorm();
        _who.rotation = Quaternion.Slerp(_who.rotation, Quaternion.LookRotation(d, Vector3.up), .5f);
        var rb = _who.GetComponent<Rigidbody>();
        const float c_moveSpeed = 7f;
        rb.linearVelocity = Vector3.Lerp(rb.linearVelocity, d * c_moveSpeed, .35f);
        //_who.position += d * 2f * Time.deltaTime;
        return false;
    }

    int FindClosestLivingThreat()
    {
        float bestD2 = 1e23f;
        int bestI = -1;
        for (int i = 0; i < m_threats.childCount; ++i)
        {
            var threat = m_threats.GetChild(i);
            var rb = threat.GetComponent<Rigidbody>();
            if (m_deadList.TryGetValue(threat, out var _)) continue;
            var d = threat.position - m_adventurer.position;
            var d2 = d.xzSqrMagnitude();
            if (d2 < bestD2)
            {
                bestD2 = d2;
                bestI = i;
            }
        }
        return bestI;
    }

    public static UndergroundManager Create(GameObject _prefab)
    {
        var go = Instantiate(_prefab);
        return go.GetComponent<UndergroundManager>();
    }
}
