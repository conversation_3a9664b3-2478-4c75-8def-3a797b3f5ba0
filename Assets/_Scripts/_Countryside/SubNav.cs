using System.Collections.Generic;
using Unity.Collections;
using UnityEngine;

#if UNITY_EDITOR
using System.Collections;
using UnityEditor;
using UnityEditor.SceneManagement;
using Unity.EditorCoroutines.Editor;

[CustomEditor(typeof(SubNav))]
public class SubNavEditor : Editor
{
    private Texture2D m_cachedImage;
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        SubNav subNav = (SubNav)target;
        
        GUILayout.Space(10);
        GUILayout.Label($"{subNav.DebugInfo}"); 

        if (GUILayout.Button("Regenerate Nav"))
        {
            subNav.RegenerateNav();
            m_cachedImage = null;
        }
        if (m_cachedImage == null) m_cachedImage = subNav.GenerateTexture();
        var imgWidth = m_cachedImage.width;
        var imgHeight = m_cachedImage.height;
        var imgDisplayWidth = EditorGUIUtility.currentViewWidth - 32;
        var imgDisplayHeight = imgHeight * imgDisplayWidth / imgWidth;
        GUILayout.Label(m_cachedImage, GUILayout.Width(imgDisplayWidth), GUILayout.Height(imgDisplayHeight));
    }
}


[InitializeOnLoad]
public static class PrefabEditModeWatcher
{
    static PrefabEditModeWatcher()
    {
        // Subscribe once when the editor loads
        PrefabStage.prefabStageClosing += OnPrefabStageClosing;
    }

    private static void OnPrefabStageClosing(PrefabStage stage)
    {
        var root = stage.prefabContentsRoot;
        var subNav = root.GetComponentInChildren<SubNav>(true);
        if (subNav == null) return;
        Debug.LogError($"Regenerating SubNav for {root.name}");
        subNav.RegenerateNav();
    }
}

#endif

public class SubNavInformation
{
    public int m_width, m_height;
    public Vector3 m_origin;
    public NativeArray<byte> m_subNav;
    public NativeArray<float> m_subNavHeights;
    public SubNavInformation() {}
    public SubNavInformation(int _width, int _height, Vector3 _origin, byte[] _subNav, float[] _heights)
    {
        m_width = _width;
        m_height = _height;
        m_origin = _origin;
        m_subNav = new NativeArray<byte>(_subNav, Allocator.Persistent);
        m_subNavHeights = new NativeArray<float>(_heights, Allocator.Persistent);
    }

    public void Import(string _resourcePath)
    {
        var data = Resources.Load<TextAsset>(_resourcePath)?.bytes;
        if (data == null) return;
        m_width = System.BitConverter.ToInt32(data, 0);
        m_height = System.BitConverter.ToInt32(data, 4);
        m_origin = new Vector3(System.BitConverter.ToSingle(data, 8), 0, System.BitConverter.ToSingle(data, 12));
        m_subNav = new NativeArray<byte>(m_width * m_height, Allocator.Persistent);
        m_subNavHeights = new NativeArray<float>(m_width * m_height, Allocator.Persistent);
        for (int i = 0; i < m_subNav.Length; i++)
            m_subNav[i] = data[i + 16];
        for (int i = 0; i < m_subNav.Length; i++)
            m_subNavHeights[i] = System.BitConverter.ToSingle(data, i * sizeof(float) + 16 + m_subNav.Length);
    }

    public void Export(string _resourcePath)
    {
#if UNITY_EDITOR
        var data = new byte[m_subNav.Length + m_subNav.Length * sizeof(float) + 16];
        System.BitConverter.GetBytes(m_width).CopyTo(data, 0);
        System.BitConverter.GetBytes(m_height).CopyTo(data, 4);
        System.BitConverter.GetBytes(m_origin.x).CopyTo(data, 8);
        System.BitConverter.GetBytes(m_origin.z).CopyTo(data, 12);
        for (int i = 0; i < m_subNav.Length; i++)
            data[i + 16] = m_subNav[i];
        for (int i = 0; i < m_subNav.Length; i++)
            System.BitConverter.GetBytes(m_subNavHeights[i]).CopyTo(data, i * sizeof(float) + 16 + m_subNav.Length);
        var path = $"Assets/Resources/{_resourcePath}.bytes";
        var pathWithoutName = System.IO.Path.GetDirectoryName(path);
        System.IO.Directory.CreateDirectory(pathWithoutName);
        System.IO.File.WriteAllBytes(path, data);
        Debug.LogError($"Wrote SubNav data ({m_width} x {m_height}) to {path}");
        AssetDatabase.Refresh();
#endif
    }
}

public class SubNav : MonoBehaviour
{
    public string m_subNavName;
    private SubNavInformation m_subNavInfo;
    public GlobalData.SubNavData m_subNav;
    public float m_passHeight = 1.5f;
    public int m_widen = 3;

    public string DebugInfo
    {
        get
        {
            Initialise();
            return $"{m_subNavInfo.m_origin.x},{m_subNavInfo.m_origin.z} {m_subNavInfo.m_width}x{m_subNavInfo.m_height}";
        }
    }

    private bool IsExcluded(Transform _t)
    {
        return _t.gameObject.GetComponentInParent<SubNavExclude>() != null;
    }

    public static int RoundToNextPower2(int _n)
    {
        if ((_n & (_n - 1)) == 0)
            return _n; // already a power of 2
        return Mathf.NextPowerOfTwo(_n);
    }

#if UNITY_EDITOR
    void SetProgress(string _info, float _progress)
    {
        EditorUtility.DisplayProgressBar("Generating SubNav data", _info, _progress);
    }

    public void RegenerateNav()
    {
        try
        {
            SetProgress("Start", 0);
            RegenerateNavInternal();
        }
        finally
        {
            EditorUtility.ClearProgressBar();
        }
    }
    private void RegenerateNavInternal()
    {
        if (string.IsNullOrEmpty(m_subNavName))
        {
            Debug.LogError("SubNavName must be set to a unique name for this subNav");
            return;
        }

        int errors = 0;
        const int c_maxErrors = 20;

        const float c_progressStart_Extents = .05f;
        const float c_progressStart_Floor = c_progressStart_Extents + .05f;
        const float c_progressEnd_Floor = c_progressStart_Floor + .4f;
        const float c_progressStart_Expand = c_progressEnd_Floor + .1f;
        const float c_progressStart_Convert = c_progressStart_Expand + .1f;
        const float c_progressStart_Widen = c_progressStart_Convert + .1f;
        const float c_progressStart_BuildingNavBlockers = c_progressStart_Widen + .1f;
        const float c_progressStart_Export = c_progressStart_BuildingNavBlockers + .1f;

        SetProgress("Calculating extents", c_progressStart_Extents);
        
        var meshes = new List<(Mesh, Transform)>();
        Vector3 globalMin = Vector3.one * 1e23f, globalMax = Vector3.one * -1e23f;
        foreach (var mf in GetComponentsInChildren<MeshFilter>())
        {
            if (IsExcluded(mf.transform)) continue;
            var mesh = mf.sharedMesh;
            meshes.Add((mesh, mf.transform));
            var xform = mf.transform;
            var vertices = mesh.vertices;
            for (int i = 0; i < vertices.Length; i++)
            {
                var v = xform.TransformPoint(vertices[i]);
                globalMin = Vector3.Min(globalMin, v);
                globalMax = Vector3.Max(globalMax, v);
            }
        }
        int x0 = Mathf.FloorToInt(globalMin.x * GlobalData.c_terrainXZScale);
        int x1 = Mathf.CeilToInt(globalMax.x * GlobalData.c_terrainXZScale);
        int z0 = Mathf.FloorToInt(globalMin.z * GlobalData.c_terrainXZScale);
        int z1 = Mathf.CeilToInt(globalMax.z * GlobalData.c_terrainXZScale);
        int width = x1 + 1 - x0;
        int height = z1 + 1 - z0;
        width = RoundToNextPower2(width);
        height = RoundToNextPower2(height);
        var minHeights = new float[width * height];
        for (int i = 0; i < minHeights.Length; i++)
            minHeights[i] = float.MaxValue;
        var maxUpfacingHeights = new float[width * height];
        for (int i = 0; i < maxUpfacingHeights.Length; i++)
            maxUpfacingHeights[i] = -float.MaxValue;
        int totalMeshes = meshes.Count;
        int nextMesh = 0;
        foreach (var (mesh, xform) in meshes)
        {
            var progress = Mathf.Lerp(c_progressStart_Floor, c_progressEnd_Floor, (float)nextMesh++ / totalMeshes);
            SetProgress("Calculating floor", progress);

            var vertices = mesh.vertices;
            var normals = mesh.normals;
            for (int i = 0; i < vertices.Length; i++)
                vertices[i] = xform.TransformPoint(vertices[i]);
            for (int i = 0; i < mesh.subMeshCount; ++i)
            {
                var inds = mesh.GetIndices(i);
                for (int t = 0; t < inds.Length; t += 3)
                {
                    var v0 = vertices[inds[t]];
                    var v1 = vertices[inds[t + 1]];
                    var v2 = vertices[inds[t + 2]];
                    var faceNormal = (normals[inds[t]] + normals[inds[t+1]] + normals[inds[t+2]]).normalized;
                    var minX = Mathf.FloorToInt(Mathf.Min(v0.x, v1.x, v2.x) * GlobalData.c_terrainXZScale) - x0;
                    var maxX = Mathf.CeilToInt(Mathf.Max(v0.x, v1.x, v2.x) * GlobalData.c_terrainXZScale) - x0;
                    var minZ = Mathf.FloorToInt(Mathf.Min(v0.z, v1.z, v2.z) * GlobalData.c_terrainXZScale) - z0;
                    var maxZ = Mathf.CeilToInt(Mathf.Max(v0.z, v1.z, v2.z) * GlobalData.c_terrainXZScale) - z0;
                    var minY = Mathf.Min(v0.y, v1.y, v2.y);
                    var maxY = Mathf.Max(v0.y, v1.y, v2.y);
                    if (minX < 0)
                    {
                        if (errors++ < c_maxErrors) Debug.LogError($"minX {minX} < 0");
                        minX = 0;
                    }
                    if (minZ < 0)
                    {
                        if (errors++ < c_maxErrors) Debug.LogError($"minZ {minX} < 0");
                        minZ = 0;
                    }
                    if (maxX >= width)
                    {
                        if (errors++ < c_maxErrors) Debug.LogError($"maxX {maxX} >= width {width}");
                        maxX = width - 1;
                    }
                    if (maxZ >= height)
                    {
                        if (errors++ < c_maxErrors) Debug.LogError($"maxZ {maxZ} >= height {height}");
                        maxZ = height - 1;
                    }
                    for (int z = minZ; z <= maxZ; z++)
                    {
                        for (int x = minX; x <= maxX; x++)
                        {
                            //var p = new Vector3(x, 0, z);
                            //if (PointInTriangle(p, v0, v1, v2))
                            {
                                var index = z * width + x;
                                if (minHeights[index] > minY)
                                    minHeights[index] = minY;
                                // RW-04-APR-25: Previously, we were identifying the minimum height at this index.
                                // For things like which characters should be able to stand on, but also have other polys underneath
                                // them (like the pillars in the first cave, which are separate objects in a larger chamber), this didn't work.
                                // Instead, find the highest Y that's facing up.
                                if (maxUpfacingHeights[index] < maxY && faceNormal.y > 0.8f)
                                {
                                    if (maxY > -160)
                                        Debug.LogError($"High point at {xform.Path()}", xform.gameObject);
                                    maxUpfacingHeights[index] = maxY;
                                }
                            }
                        }
                    }
                }
            }
        }
        for (int i = 0; i < maxUpfacingHeights.Length; i++)
            if (maxUpfacingHeights[i] < -1e23f)
                if (minHeights[i] < 1e23f)
                    maxUpfacingHeights[i] = minHeights[i];
        // expand cells heights out into the unknown a bit
        SetProgress("Expanding heights", c_progressStart_Expand);
        var tmp = new float[width * height];
        for (int j = 0; j < 3; ++j)
        {
            for (int i = 0; i < maxUpfacingHeights.Length; ++i)
            {
                void CheckAndSet(int _index, int _neighbour)
                {
                    if (maxUpfacingHeights[_neighbour] > -1e23f)
                        tmp[_index] = maxUpfacingHeights[_neighbour];
                }

                tmp[i] = maxUpfacingHeights[i];
                if (maxUpfacingHeights[i] < -1e23f)
                {
                    int x = i % width, y = i / width;
                    if (x > 0) CheckAndSet(i, i - 1);
                    if (x < width - 1) CheckAndSet(i, i + 1);
                    if (y > 0) CheckAndSet(i, i - width);
                    if (y < height - 1) CheckAndSet(i, i + width);
                }
            }
            (tmp, maxUpfacingHeights) = (maxUpfacingHeights, tmp);
        }
        byte[] subNav = new byte[width * height];
        for (int i = 0; i < maxUpfacingHeights.Length; i++)
            subNav[i] = (byte) GlobalData.NavCostTypes.OffRoad;
        SetProgress("Converting", c_progressStart_Convert);
        foreach (var (mesh, xform) in meshes)
        {
            var vertices = mesh.vertices;
            for (int i = 0; i < vertices.Length; i++)
                vertices[i] = xform.TransformPoint(vertices[i]);
            for (int i = 0; i < mesh.subMeshCount; ++i)
            {
                var inds = mesh.GetIndices(i);
                for (int t = 0; t < inds.Length; t += 3)
                {
                    var v0 = vertices[inds[t]];
                    var v1 = vertices[inds[t + 1]];
                    var v2 = vertices[inds[t + 2]];

                    var minX = Mathf.FloorToInt(Mathf.Min(v0.x, v1.x, v2.x) * GlobalData.c_terrainXZScale) - x0;
                    var maxX = Mathf.CeilToInt(Mathf.Max(v0.x, v1.x, v2.x) * GlobalData.c_terrainXZScale) - x0;
                    var minZ = Mathf.FloorToInt(Mathf.Min(v0.z, v1.z, v2.z) * GlobalData.c_terrainXZScale) - z0;
                    var maxZ = Mathf.CeilToInt(Mathf.Max(v0.z, v1.z, v2.z) * GlobalData.c_terrainXZScale) - z0;
                    var minY = Mathf.Min(v0.y, v1.y, v2.y);
                    var maxY = Mathf.Max(v0.y, v1.y, v2.y);

                    for (int z = minZ; z <= maxZ; z++)
                    {
                        for (int x = minX; x <= maxX; x++)
                        {
                            //var p = new Vector3(x, 0, z);
                            //if (PointInTriangle(p, v0, v1, v2))
                            {
                                var index = z * width + x;
                                var baseHeight = maxUpfacingHeights[index];
                                if (baseHeight < -1e23f) baseHeight = 0;
                                var passHeight = baseHeight + m_passHeight;
                                if (minY < passHeight && maxY > passHeight)
                                    subNav[index] = (byte) GlobalData.NavCostTypes.NoNav;
                            }
                        }
                    }
                }
            }
        }
        for (int i = 0; i < maxUpfacingHeights.Length; i++)
            if (maxUpfacingHeights[i] < -1e23f || maxUpfacingHeights[i] > 1e23f)
            {
                subNav[i] = (byte)GlobalData.NavCostTypes.NoNav;
                maxUpfacingHeights[i] = 0;
            }
        // widen nav
        SetProgress("Widen", c_progressStart_Widen);
        byte[] subNavOriginal = new byte[width * height];
        for (int i = 0; i < subNavOriginal.Length; i++)
            subNavOriginal[i] = subNav[i];
        for (int y = 0; y < height; ++y)
        {
            for (int x = 0; x < width; ++x)
            {
                var index = y * width + x;
                if (subNavOriginal[index] == (byte) GlobalData.NavCostTypes.NoNav)
                {
                    for (int yy = Mathf.Max(0, y - m_widen); yy <= Mathf.Min(height - 1, y + m_widen); ++yy)
                    {
                        var dy = yy - y;
                        for (int xx = Mathf.Max(0, x - m_widen); xx <= Mathf.Min(width - 1, x + m_widen); ++xx)
                        {
                            var dx = xx - x;
                            if (dx * dx + dy * dy < m_widen * m_widen)
                                subNav[yy * width + xx] = (byte) GlobalData.NavCostTypes.NoNav;
                        }
                    }
                }
            }
        }

        SetProgress("Add BuildingNavBlockers", c_progressStart_BuildingNavBlockers);
        foreach (var bnb in GetComponentsInChildren<BuildingNavBlocker>(true))
        {
            if (!bnb.m_isBaked)
            {
                continue;
            }
            bnb.enabled = false;
            var (center, rightExtent, forwardExtent) = bnb.GetBounds();
            rightExtent.y = forwardExtent.y = 0;
            var abs = new Vector3(Mathf.Abs(rightExtent.x) + Mathf.Abs(forwardExtent.x), 0, Mathf.Abs(rightExtent.z) + Mathf.Abs(forwardExtent.z));
            var min = center - abs;
            var max = center + abs;
            var rightExtentMagSqrd = rightExtent.sqrMagnitude;
            var forwardExtentMagSqrd = forwardExtent.sqrMagnitude;
            var rightExtentMagFourth = rightExtentMagSqrd * rightExtentMagSqrd;
            var forwardExtentMagFourth = forwardExtentMagSqrd * forwardExtentMagSqrd;

            var minX = Mathf.FloorToInt(min.x * GlobalData.c_terrainXZScale) - x0;
            var maxX = Mathf.CeilToInt(max.x * GlobalData.c_terrainXZScale) - x0;
            var minZ = Mathf.FloorToInt(min.z * GlobalData.c_terrainXZScale) - z0;
            var maxZ = Mathf.CeilToInt(max.z * GlobalData.c_terrainXZScale) - z0;
            minX = Mathf.Clamp(minX, 0, width - 1);
            maxX = Mathf.Clamp(maxX, 0, width - 1);
            minZ = Mathf.Clamp(minZ, 0, height - 1);
            maxZ = Mathf.Clamp(maxZ, 0, height - 1);
            Vector3 realPos = Vector3.zero;
            for (int z = minZ; z <= maxZ; ++z)
            {
                realPos.z = (float)(z + z0) / GlobalData.c_terrainXZScale - center.z;
                for (int x = minX; x <= maxX; ++x)
                {
                    realPos.x = (float)(x + x0) / GlobalData.c_terrainXZScale - center.x;
                    var rightDot = Vector3.Dot(realPos, rightExtent);
                    var forwardDot = Vector3.Dot(realPos, forwardExtent);
                    if (rightDot * rightDot < rightExtentMagFourth && forwardDot * forwardDot < forwardExtentMagFourth)
                    {
                        var index = z * width + x;
                        subNav[index] = (byte) GlobalData.NavCostTypes.LowNoNav;
                    }
                }
            }
        }

        SetProgress("Export", c_progressStart_Export);
        var origin = new Vector3(x0 / GlobalData.c_terrainXZScale, 0, z0 / GlobalData.c_terrainXZScale);
        var info = new SubNavInformation(width, height, origin, subNav, maxUpfacingHeights);
        info.Export($"SubNav/{m_subNavName}");

        m_subNavInfo = null;
    }
    
    public Texture2D GenerateTexture()
    {
        Initialise();
        var info = m_subNavInfo;
        int width = info.m_width, height = info.m_height;
        var origin = info.m_origin;
        var subNav = info.m_subNav;

        var clrFloor = new Color(.2f, .2f, .2f);
        var clrWall = new Color(1f, .3f, .2f);
        var clrExit = new Color(.3f, .7f, .9f);
        var clrEnter = new Color(.3f, .9f, .6f);
        
        int minX = 100000, maxX = -100000, minY = 100000, maxY = -100000;
        float minHeight = 1e23f, maxHeight = -1e23f;
        for (int y = 0; y < height; ++y)
        {
            for (int x = 0; x < width; ++x)
            {
                var cHeight = info.m_subNavHeights[x + y * width];
                if (cHeight.Nearly(0) == false)
                {
                    minX = Mathf.Min(minX, x);
                    minY = Mathf.Min(minY, y);
                    maxX = Mathf.Max(maxX, x);
                    maxY = Mathf.Max(maxY, y);
                    minHeight = Mathf.Min(minHeight, cHeight);
                    maxHeight = Mathf.Max(maxHeight, cHeight);
                }
            }
        }

        int oWidth = width;
        width = maxX + 1 - minX;
        height = maxY + 1 - minY;
        var tex = new Texture2D(width, height);

        for (int yy = 0; yy < height; ++yy)
        {
            var y = yy + minY;
            for (int xx = 0; xx < width; ++xx)
            {
                var x = xx + minX;
                var value = subNav[y * oWidth + x];

                var cHeight = info.m_subNavHeights[x + y * oWidth];
                var interp = (cHeight - minHeight) / (maxHeight - minHeight);
                var clr = cHeight.Nearly(0) ? Color.black : Color.Lerp(Color.black, Color.blue, interp);
                if (value == (byte) GlobalData.NavCostTypes.NoNav) clr.r = 1;
                else if (value == (byte) GlobalData.NavCostTypes.LowNoNav) clr.g = clr.b = 1;
                tex.SetPixel(xx, yy, clr);
            }
        }

        void DrawBlobXY(int x, int y, int _radius, Color clr, float _alpha)
        {
            for (int yy = Mathf.Max(0, y - _radius); yy <= Mathf.Min(height - 1, y + _radius); ++yy)
            {
                var dy = yy - y;
                for (int xx = Mathf.Max(0, x - _radius); xx <= Mathf.Min(width - 1, x + _radius); ++xx)
                {
                    var dx = xx - x;
                    if (dx * dx + dy * dy < _radius * _radius)
                        tex.SetPixel(xx, yy, Color.Lerp(tex.GetPixel(xx, yy), clr, _alpha));
                }
            }
        }

        void DrawBlob(Vector3 _pos, int _radius, Color _clr, float _alpha)
        {
            var x = (int) ((_pos.x - origin.x) * GlobalData.c_terrainXZScale) - minX;
            var y = (int) ((_pos.z - origin.z) * GlobalData.c_terrainXZScale) - minY;
            DrawBlobXY(x, y, _radius, _clr, _alpha);
        }

        foreach (var exit in GetComponentsInChildren<GenericSubSceneExitInteraction>())
        {
            var pos = exit.transform.position;
            DrawBlob(pos, 3, clrExit, .7f);
        }

        tex.Apply();
        return tex;
    }
#endif
    
    private static SubNav s_currentSubNav;
    
    void OnEnable()
    {
        Initialise();
    }

    void OnDisable()
    {
        s_currentSubNav = null;
    }

    void Initialise()
    {
        if (m_subNavInfo == null)
        {
            var info = new SubNavInformation();
            info.Import($"SubNav/{m_subNavName}");
            m_subNavInfo = info;

            if (info.m_width != 0)
                m_subNav = new GlobalData.SubNavData(info.m_width, info.m_height, info.m_origin, info.m_subNav, info.m_subNavHeights);
        }
        s_currentSubNav = this;
    }
    
    public static GlobalData.SubNavData GetSubNav(GameObject _o)
    {
        var sub = _o.GetComponentInParent<SubNav>();
        if (sub == null) return null;
        return sub.m_subNav;
    }
}
