using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAManaBall : MonoBehaviour
{
    public ParticleSystem m_system;
    public ParticleSystemForceField m_forceField;
    private Transform m_systemTransform;
    private Transform m_forceFieldTransform;
    
    public Vector3 m_rotateSpeed;
    public float m_unitContents = 50;
    public float m_maxScale = 2.5f;
    public bool m_allowCoalesce = true;
    public float m_moveTowardsAbsorberSpeed = 5.0f;

    public enum EState
    {
        Starting,
        Idle,
        Absorbing,
        Transferring,
        Dying,
    }
    private EState m_state;
    private float m_timeInState;

    private GameState_ManaBall m_gameState;
    private Transform m_source;
    private float m_absorbPerSecond;
    private float m_currentScale = 0;
    private Vector3 m_eulerAngles;
    public void Activate(GameState_ManaBall _state, Transform _source)
    {
        var groundPos = _state.m_position.GroundPosition(.5f);
        if (_state.m_position.y < groundPos.y) _state.m_position.y = groundPos.y;
        m_gameState = _state;
        m_source = _source;
        m_currentScale = 0;
        m_eulerAngles = new Vector3(Random.Range(0, 360), Random.Range(0, 360), 0);
        m_state = m_gameState.m_content < .0001f ? EState.Dying : EState.Starting;
        m_timeInState = 0;
        m_gameState.Object = gameObject.transform;
        m_absorbPerSecond = m_gameState.m_content / c_absorbTime;
        m_systemTransform = m_system.transform;
        m_forceFieldTransform = m_forceField.transform;
        Update();
    }

    public string m_createAudio;
    public string m_absorbAudio;
    private int m_absorbAudioId;

    private void OnInitialCreate() // only called when created, not when loaded
    {
        PlayCreateAudio();
    }
    void PlayCreateAudio()
    {
        if (string.IsNullOrEmpty(m_createAudio) == false)
            AudioClipManager.Me.PlaySound(m_createAudio, gameObject);
    }
    void PlayAbsorbAudio()
    {
        if (m_absorbAudioId > 0) return;
        if (string.IsNullOrEmpty(m_absorbAudio) == false)
            m_absorbAudioId = AudioClipManager.Me.PlaySound(m_absorbAudio, gameObject);
    }

    void StopAbsorbAudio()
    {
        if (m_absorbAudioId <= 0) return;
        AudioClipManager.Me.StopSound(m_absorbAudioId, gameObject);
        m_absorbAudioId = 0;
    }

    const float c_startingDuration = 2;
    void UpdateState()
    {
        switch (m_state)
        {
            case EState.Starting:
                if (m_source == null)
                {
                    m_state = EState.Idle;
                    return;
                }
                if (m_system.isPlaying == false) m_system.Play();
                m_systemTransform.position = m_source.position;
                m_forceFieldTransform.position = transform.position;
                m_timeInState += Time.deltaTime;
                if (m_timeInState > c_startingDuration)
                {
                    m_system.Stop();
                    m_state = EState.Idle;
                    m_timeInState = 0;
                }
                break;
            case EState.Dying:
                if (m_gameState.m_content > .001f)
                {
                    // re-absorbed enough to come back to life
                    m_state = EState.Idle;
                }
                else if (m_system.particleCount == 0 && m_currentScale < .001f)
                {
                    GameManager.Me.m_state.m_manaBalls.Remove(m_gameState);
                    Destroy(gameObject);
                }
                else if (m_moveTowardsAbsorberSpeed > 0)
                {
                    MoveToAbsorber(Absorber);
                }
                break;
            default:
                UpdateAbsorb();
                break;
        }
    }

    const bool c_latchAbsorb = true;
    bool m_absorbLatch = false;
    float c_absorbDistance => NGManager.Me.m_manaBallAbsorbRange;
    float c_absorbTime => NGManager.Me.m_manaBallAbsorbTotalTime;
    const float c_mergeDistance = 48;

    private Transform Absorber => GameManager.Me.PossessedObject?.transform ?? PlayerHandManager.Me.Fingertip;
    
    const bool c_alwaysAllowManaAbsorb = true;
    void UpdateAbsorb()
    {
        var absorber = Absorber;
        var distanceSqrd = (absorber.position - m_gameState.m_position).sqrMagnitude;
        var gameState = GameManager.Me.m_state;
        float remainingSpace = m_gameState.m_contentType == c_contentTypeMana && c_alwaysAllowManaAbsorb == false ? MAUnlocks.Me.MaxPowerMana - gameState.m_powerMana : 1e10f;
        var targetState = (distanceSqrd < c_absorbDistance * c_absorbDistance || m_absorbLatch) && remainingSpace > 0 ? EState.Absorbing : EState.Idle;
        m_absorbLatch |= c_latchAbsorb && targetState == EState.Absorbing;
        float absorbPerSecond = m_absorbPerSecond;
#if UNITY_EDITOR || DEVELOPMENT_BUILD
        bool superAbsorb = (Input.GetKey(KeyCode.M) || GameManager.Me.IsInPlayground) && m_gameState.m_contentType == c_contentTypeMana;
        if (superAbsorb)
        {
            targetState = EState.Absorbing;
            remainingSpace = MAUnlocks.Me.MaxPowerMana;
            absorbPerSecond *= 4;
        }
#endif
        var absorbPosition = absorber.position;
        GameState_ManaBall ballAbsorber = null;
        if (targetState == EState.Idle && m_allowCoalesce)
        {
            float ballAbsorberSqrDistance = c_mergeDistance * c_mergeDistance;
            var ballState = GameManager.Me.m_state.m_manaBalls;
            int thisIndex = ballState.IndexOf(m_gameState);
            for (int i = 0; i < ballState.Count; ++i)
            {
                if (ballState[i] == m_gameState) continue;
                if (ballState[i].m_contentType != m_gameState.m_contentType) continue;
                var distanceSqrdToOther = (ballState[i].m_position - m_gameState.m_position).sqrMagnitude;
                if (distanceSqrdToOther < ballAbsorberSqrDistance && m_gameState.m_content + thisIndex * .02f < ballState[i].m_content + i * .02f)
                {
                    ballAbsorber = ballState[i];
                    ballAbsorberSqrDistance = distanceSqrdToOther;
                }
            }
            if (ballAbsorber != null)
            {
                absorbPosition = ballAbsorber.Object.position;
                targetState = EState.Transferring;
            }
        }
        m_systemTransform.position = transform.position;
        m_forceFieldTransform.position = absorbPosition;
        m_forceField.enabled = targetState != EState.Idle;
        if (m_state != targetState)
        {
            m_state = targetState;
            if (m_state == EState.Absorbing || m_state == EState.Transferring)
            {
                if (m_state == EState.Absorbing) PlayAbsorbAudio();
                m_system.Play();
            }
            else
            {
                StopAbsorbAudio();
                m_system.Stop();
            }
        }
        if (m_state == EState.Absorbing)
        {
            if (m_moveTowardsAbsorberSpeed > 0)
            {
                MoveToAbsorber(absorber);
            }
            switch (m_gameState.m_contentType)
            {
                case c_contentTypeMana:
                    AbsorbMana(absorbPerSecond, remainingSpace);
                    break;
                case c_contentTypeMysticFavour:
                    AbsorbCurrency(NGPlayer.Me.m_mysticFavors);
                    break;
                case c_contentTypeRoyalFavour:
                    AbsorbCurrency(NGPlayer.Me.m_royalFavors);
                    break;
                case c_contentTypeLordsFavour:
                    AbsorbCurrency(NGPlayer.Me.m_lordsFavors);
                    break;
                case c_contentTypePeoplesFavour:
                    AbsorbCurrency(NGPlayer.Me.m_commonersFavors);
                    break;
                case c_contentTypeCash:
                    AbsorbCurrency(NGPlayer.Me.m_cash);
                    break;
            }
        }
        else if (m_state == EState.Transferring)
        {
            m_gameState.m_position = Vector3.Lerp(m_gameState.m_position, ballAbsorber.m_position, .01f);
            const float c_secondsToTransfer = .5f;
            absorbPerSecond = Mathf.Max(m_gameState.m_content / c_secondsToTransfer, absorbPerSecond); // take max n seconds to transfer
            float manaDrawn = Mathf.Min(Time.deltaTime * absorbPerSecond, m_gameState.m_content);
            m_gameState.m_content -= manaDrawn;
            ballAbsorber.m_content += manaDrawn;
            CheckManaExpended();
        }
    }

    void MoveToAbsorber(Transform _absorber)
    {
        var toAbsorber = _absorber.position - m_gameState.m_position;
        var movement = m_moveTowardsAbsorberSpeed * Time.deltaTime;
        if (toAbsorber.sqrMagnitude > movement * movement)
            toAbsorber = toAbsorber.normalized * movement;
        m_gameState.m_position += toAbsorber.normalized * movement;
    }


    void AbsorbMana(float _absorbPerSecond, float _remainingSpace)
    {
        var gameState = GameManager.Me.m_state;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        PlayerHandManager.Me.ForcePowerUIDisplay();
#endif
        float manaDrawn = Mathf.Min(Mathf.Min(Time.deltaTime * _absorbPerSecond, m_gameState.m_content), _remainingSpace);
        if (manaDrawn > 0)
        {
            m_gameState.m_content -= manaDrawn;
            gameState.m_powerMana += manaDrawn;
            CheckManaExpended();
        }
    }

    float m_absorbCurrencyProgress = 0;
    void AbsorbCurrency(CurrencyContainer _c)
    {
        const float c_currencyAbsorbedPerSecond = 10;
        m_absorbCurrencyProgress += Time.deltaTime * c_currencyAbsorbedPerSecond;
        if (m_absorbCurrencyProgress >= 1)
        {
            m_absorbCurrencyProgress -= 1;
            m_gameState.m_content -= 1;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
            _c.Add(CurrencyContainer.TransactionType.Earned, 1, "ManaBall", transform);
#endif
            CheckManaExpended();
        }
    }

    void CheckManaExpended()
    {
        if (m_gameState.m_content <= .01f)
        {
            m_gameState.m_content = 0;
            StopAbsorbAudio();
            m_system.Stop();
            m_state = EState.Dying;
        }
    }

    void Update()
    {
        UpdateState();
        m_eulerAngles += m_rotateSpeed * Time.deltaTime;
        m_eulerAngles.x = Utility.ClampAngle(m_eulerAngles.x);
        m_eulerAngles.y = Utility.ClampAngle(m_eulerAngles.y);
        m_eulerAngles.z = Utility.ClampAngle(m_eulerAngles.z);
        const float c_baseHeight = 1.5f;
        const float c_bobHeight = .3f;
        const float c_baseBobSpeed = 2.0f;
        const float c_rndBobSpeed = .3f;
        var targetScale = Mathf.Min(m_maxScale, m_gameState.m_content / m_unitContents);
        m_currentScale = Mathf.Lerp(m_currentScale, targetScale, .5f);
        float bobSpeed = c_baseBobSpeed + Utility.XorShift01(gameObject.GetInstanceID()) * c_rndBobSpeed;
        transform.position = m_gameState.m_position + Vector3.up * (m_gameState.m_heightBase + c_baseHeight + (Mathf.Sin(Time.time * bobSpeed) + 1) * c_bobHeight);
        transform.localScale = Vector3.one * m_currentScale;
        transform.localEulerAngles = m_eulerAngles;
    }

    public static void LoadAll()
    {
        var balls = GameManager.Me.m_state.m_manaBalls;
        for (int i = balls.Count - 1; i >= 0; --i)
            if (Create(null, balls[i]) == null)
                balls.RemoveAt(i);
    }

    public static MAManaBall Create(Transform _source, GameState_ManaBall _gameState)
    {
        var prefab = Resources.Load<GameObject>($"PowerEffects/{_gameState.m_contentType}Ball");
        if (prefab == null) return null;
        var go = Instantiate(prefab, PlayerHandManager.Me.transform);
        var manaball = go.GetComponent<MAManaBall>();
        manaball.Activate(_gameState, _source);
        return manaball;
    }

    private static void CreateSubItems(Transform _source, MAMovingInfoBase _content)
    {
        const float c_heightBaseStep = 1.5f;
        int index = 0;
        var chance = _content.m_deathDropChance;
        var types = _content.m_deathDropOptions;
        //chance = 1; types = c_contentTypeMysticFavour;
        if (chance > 0 && types.Length > 0)
            if (Random.Range(0f, 1f) < chance)
            {
                // Pick one item from the drop options randomly.
                int r = (int)Random.Range(0, types.Length);
                string type = types[r];
                // RW-10-MAR-25: Peter's asked that if there's a blank row in the pool and it gets 
                // picked, the user shouldn't get a reward.
                if (!string.IsNullOrEmpty(type))
                {
                    float amount = 1f;

                    // RW-10-MAR-25: Cash is a special case. Detect it and figure out how much we want to give.
                    if (type.StartsWith("$"))
                    {
                        amount = float.Parse(type.Substring(1));
                        type = c_contentTypeCash;
                    }
                    // RW-12-MAR-25: Mystic Favours can't be dropped until the relevant unlock is unlocked.
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
                    else if (type == c_contentTypeMysticFavour && !MAUnlocks.Me.HarvestMysticRunes)
                    {
                        return;
                    }
#endif //!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)

                    Create(_source, amount, type, (++index) * c_heightBaseStep);
								}
            }
    }

    public static MAManaBall Create(Transform _source, MAMovingInfoBase _content)
    {
        CreateSubItems(_source, _content);
        float contentValue = _content.DeathMana;
        return Create(_source, contentValue);
    }
    
    public const string c_contentTypeMana = "Mana";
    public const string c_contentTypeMysticFavour = "MysticFavour";
    public const string c_contentTypePeoplesFavour = "PeoplesFavour";
    public const string c_contentTypeLordsFavour = "LordsFavour";
    public const string c_contentTypeRoyalFavour = "RoyalFavour";
    public const string c_contentTypeCash = "Cash";

    public static MAManaBall Create(Transform _source, float _contents, string _contentType = c_contentTypeMana, float _heightBase = 0)
    {
        var gameState = new GameState_ManaBall();
        GameManager.Me.m_state.m_manaBalls.Add(gameState);
        gameState.m_position = _source.position;
        gameState.m_content = _contents;
        gameState.m_contentType = _contentType;
        gameState.m_heightBase = _heightBase;
        var ball = Create(_source, gameState);
        ball?.OnInitialCreate();
        return ball;
    }
}
