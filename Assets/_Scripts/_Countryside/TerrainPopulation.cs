using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Unity.Jobs;
using Unity.Burst;
using Unity.Collections;
using Unity.Mathematics;
using Unity.Collections.LowLevel.Unsafe;
using UnityEngine.Rendering;
using UnityEngine.Serialization;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(TerrainPopulation))]
public class TerrainPopulationEditor : Editor {
	public override void OnInspectorGUI() {
		DrawDefaultInspector();
		var thing = target as TerrainPopulation;

		if (Application.isPlaying) {
			if (GUILayout.Button("Reset All Trees")) thing.ResetAllTrees();
		}
		if (GUILayout.Button("Import From Terrain")) thing.ImportFromTerrain();
	}
}
#endif

public class FastRnd {
	uint m_current;
	public uint Next() {
		m_current = m_current * 71731431 - 981343;
		m_current = (m_current << 25) | (m_current >> 7);
		return m_current;
	}
	public float NextFloat() {
		uint n = Next();
		return (float)(n & 0xFFFF) / 65535.0f;
	}
	public float Range(float _minInc, float _maxInc) {
		return _minInc + (_maxInc - _minInc) * NextFloat();
	}
	public void Seed(int _n) { m_current = (uint)_n; }
}

[System.Serializable]
public class TerrainPopulationPrefab {
	public string m_group = "";
	public float m_weightInGroup = 100;
	private List<int> m_links;
	public GameObject m_prefab;
	public LODRenderer[] m_prefabLODGroups;
	
	public float m_uniformSize;
	public int m_uniformCount;
	
	public void ClearLinks() { m_links = null; }
	public void Link(int _child) {
		if (m_links == null) {
			m_links = new List<int>();
		}
		m_links.Add(_child);
	}
	public bool IsChild { get { return !string.IsNullOrEmpty(m_group) && m_links == null; } }
	public int GetRandomLink(TerrainPopulationPrefab[] _layers, float _normalisedRandomWeight, int _default) {
		if (m_links == null) return _default;
		var weightSum = 0f;
		for (int i = 0; i < m_links.Count; i ++) weightSum += _layers[m_links[i]].m_weightInGroup;
		var rndWeight = weightSum * _normalisedRandomWeight;
		for (int i = 0; i < m_links.Count; i ++) {
			rndWeight -= _layers[m_links[i]].m_weightInGroup;
			if (rndWeight < 0) return m_links[i];
		}
		return _default;
	}

	public void ForceBounds(Vector3 _onScreenPoint)
	{
		for (int i = 0; i < m_prefabLODGroups.Length; i++)
		{
			var group = m_prefabLODGroups[i];
			group.ForceBounds(_onScreenPoint);
		}
	}

	public void Refresh() {
		GameObject prefab = m_prefab;
		if (prefab == null)
			return;
		var lodGroups = prefab.GetComponentsInChildren<LODGroup>();
		var lodRenderers = new List<LODRenderer>();
		for (int i = 0; i < lodGroups.Length; i++) {
			lodRenderers.Add(new LODRenderer(lodGroups[i]));
		}

		var renderers = prefab.GetComponentsInChildren<MeshRenderer>();
		var finalRenderers = new List<MeshRenderer>();
		foreach (var r in renderers) {
			var lod = r.GetComponentInParent<LODGroup>();
			if (lod != null) continue;
			var lod2 = r.transform.parent?.GetComponent<LODGroup>();
			if (lod2 != null) continue;
			finalRenderers.Add(r);
		}
		if (finalRenderers.Count > 0) {
			lodRenderers.Add(new LODRenderer(finalRenderers));
		}

		m_prefabLODGroups = lodRenderers.ToArray();
	}
	public void Setup(string _name, float _density, GameObject _prefab) {
		m_prefab = _prefab;
	}

	public float GetRandomRotY(FastRnd _rng) {
		return 0;
	}
	public Vector3 GetRandomScale(FastRnd _rng) {
		return Vector3.one;
	}
	public float GetRandomRotY(System.Random _rng) {
		return 0;
	}
	public Vector3 GetRandomScale(System.Random _rng) {
		return Vector3.one;
	}
}

[System.Serializable]
public class TerrainPopulationInstance {
	public Matrix4x4 m_transform;
	[SerializeField] bool m_autoGenerated;
	bool m_disabled;
	float m_pollution;
	public float m_height = 0f;
	public int m_layerNmbr = 0;
	public int m_prevLodIndex;
	public float m_temporalDitherTimer;

	public static TerrainPopulationInstance Create(Vector3 _pos, float _rotY, Vector3 _scale, int _layer, bool _autoGen = true, float _overrideHeight = -999f) {
		var i = new TerrainPopulationInstance();
		i.m_transform = Matrix4x4.TRS(_pos, Quaternion.Euler(0, _rotY, 0), _scale);
		i.m_autoGenerated = _autoGen;
		i.m_disabled = false;
		i.m_pollution = 0;
		i.m_layerNmbr = _layer;
		i.m_height = (_overrideHeight > -990f) ? _overrideHeight : _pos.y;
		
		return i;
	}
	public void DropToTerrain(Terrain _t) {
        var pos = Pos;
        var origin = _t.transform.position;
        var size = _t.terrainData.size;
		m_transform.m13 = _t.terrainData.GetInterpolatedHeight((pos.x - origin.x) / size.x, (pos.z - origin.z) / size.z);
    }
	//public Matrix4x4 Transform { get { return m_transform; } }
	public bool AutoGenerated { get { return m_autoGenerated; } }
	public Vector3 Pos { get { return new Vector3(m_transform.m03, m_transform.m13, m_transform.m23); } }
	public Vector3 Scale { get { return m_transform.lossyScale; } }
	public Quaternion Rot { get { return m_transform.rotation; } }
	public bool Disabled { get { return m_disabled; } set { m_disabled = value; } }
	public bool IsInRange(Vector3 _min, Vector3 _max, bool _ignoreY, int _layer = -1) {
		if (Disabled) return false;
		if (m_transform.m03 < _min.x) return false;
		if (m_transform.m03 > _max.x) return false;
		if (m_transform.m23 < _min.z) return false;
		if (m_transform.m23 > _max.z) return false;
		if (!_ignoreY) {
			if (m_transform.m13 < _min.y) return false;
			if (m_transform.m13 > _max.y) return false;
		}
		return true;
	}
	public float Pollution { get { return m_pollution; } set { m_pollution = value; } }
	public bool IsInDefaultState { get { return !m_disabled && m_pollution <= 0.0001f; } }

	public static void DecodeData(int _packedData, out bool _disabled, out float _pollution) {
		int pollution = (_packedData >> 1) & 255;
		int disabled = (_packedData >> 0) & 1;
		_disabled = disabled != 0;
		_pollution = (float)pollution / 255.0f;
	}
	public static int EncodeData(bool _disabled, float _pollution) {
		int pollution = (int)(_pollution * 255);
		int disabled = _disabled ? 1 : 0;
		return (pollution << 1) | (disabled << 0);
	}

	public int Export() {
		return EncodeData(m_disabled, m_pollution);
	}
	public void Import(int _packed) {
		DecodeData(_packed, out m_disabled, out m_pollution);
	}
	public void Reset() {
		m_pollution = 0;
		m_disabled = false;
	}
}
[System.Serializable]
public class TerrainPopulationInstanceList {
	public TerrainPopulationInstance[] m_instances;
	TerrainPopulationInstance[] m_autoInstances;
	public TerrainPopulationInstance[] Instances { get { return (m_autoInstances != null) ? m_autoInstances : m_instances; } }
	public TerrainPopulationInstanceList(List<TerrainPopulationInstance> _l) {
		m_instances = _l.ToArray();
	}
	public void Add(TerrainPopulationInstance _i) {
		System.Array.Resize(ref m_instances, m_instances.Length + 1);
		m_instances[m_instances.Length - 1] = _i;
	}
	public void Remove(int _index) {
		m_instances[_index] = m_instances[m_instances.Length - 1];
		System.Array.Resize(ref m_instances, m_instances.Length - 1);
	}
	public void Disable(long _id) {
	}

	public void Export(List<int> _exportData) {
		int headerIndex = _exportData.Count;
		_exportData.Add(0);
		for (int i = 0; i < m_instances.Length; i++) {
			if (!m_instances[i].IsInDefaultState) {
				_exportData[headerIndex]++;
				_exportData.Add(i);
				_exportData.Add(m_instances[i].Export());
			}
		}
	}
	public int Import(int[] _list, int _firstIndex, int _count) {
		for (int i = 0; i < _count; i++) {
			int index = _list[_firstIndex++];
			int data = _list[_firstIndex++];
			if (index < m_instances.Length) m_instances[index].Import(data);
		}
		return _firstIndex;
	}
}

public class LODRenderer {
	float[] m_levels;
	int[] m_cumulativeEndOfLevel;
	Mesh[] m_totalMeshes;
	Renderer[] m_totalRenderers;
	int[] m_totalLayers;
	RenderParams[] m_totalRPs;
	
	Material[] m_totalMaterials;
	int[] m_totalSubMeshes;
	float m_referenceSize;
	void CheckIDs() {
		if (s_cutoffID == -1) {
			s_cutoffID = Shader.PropertyToID("_Cutoff");
			s_colourMaskClipValueID = Shader.PropertyToID("_colour_mask_clip_value");
			s_crossfadeParamsID = Shader.PropertyToID("_CrossfadeParams");
		}
	}

	public byte GetModelSplatPresenceBitmap()
	{
		if (m_totalMaterials.Length == 0) return 0;
		if (!m_totalMaterials[0].HasProperty("_GrassPresence")) return 0;
		var presence = m_totalMaterials[0].GetVector("_GrassPresence");
		var presence2 = m_totalMaterials[0].GetVector("_GrassPresence2");
		byte presenceBitmap = 0;
		if (presence.x > 0.0f) presenceBitmap |= 1;
		if (presence.y > 0.0f) presenceBitmap |= 2;
		if (presence.z > 0.0f) presenceBitmap |= 4;
		if (presence.w > 0.0f) presenceBitmap |= 8;
		if (presence2.x > 0.0f) presenceBitmap |= 16;
		if (presence2.y > 0.0f) presenceBitmap |= 32;
		if (presence2.z > 0.0f) presenceBitmap |= 64;
		if (presence2.w > 0.0f) presenceBitmap |= 128;
		return presenceBitmap;
	}

	public void ForceBounds(Vector3 _onScreenPoint)
	{
		for (int i = 0; i < m_totalRPs.Length; i++)
		{
			var rp = m_totalRPs[i];
			rp.worldBounds = new Bounds(_onScreenPoint, Vector3.one);
			m_totalRPs[i] = rp;
		}
	}

	public LODRenderer(LODGroup _group) {
		CheckIDs();

		var lods = _group.GetLODs();
		m_levels = new float[lods.Length];
		m_cumulativeEndOfLevel = new int[lods.Length];
		m_referenceSize = _group.size;

		var allMeshes = new List<Mesh>();
		var allMaterials = new List<Material>();
		var allRenderers = new List<Renderer>();
		var allLayers = new List<int>();
		var allSubMeshes = new List<int>();
		var allRPs = new List<RenderParams>();

		for (int i = 0; i < lods.Length; i++) {
			m_levels[i] = lods[i].screenRelativeTransitionHeight;
			var rnds = lods[i].renderers;
			foreach (var rnd in rnds) {
				var mats = rnd.sharedMaterials;
				var mesh = rnd.gameObject.GetComponent<MeshFilter>().sharedMesh;
				int layer = rnd.gameObject.layer;
				var renderer = rnd.gameObject.GetComponent<Renderer>();
				if (mesh != null) {
					for (int s = 0; s < mesh.subMeshCount; s++)
					{
						allRPs.Add(new RenderParams
						{
							material = mats[s],
							shadowCastingMode = renderer.shadowCastingMode,
							receiveShadows = true,
							layer = layer,
							matProps = null,
							renderingLayerMask = 0xFFFFFFFFu,
							instanceID = TerrainPopulation.Me.gameObject.GetInstanceID(),
							lightProbeUsage = UnityEngine.Rendering.LightProbeUsage.BlendProbes,
						});
						allMeshes.Add(mesh);
						allMaterials.Add(mats[s]);
						allSubMeshes.Add(s);
						allRenderers.Add(renderer);
						allLayers.Add(layer);
					}
				}
			}
			m_cumulativeEndOfLevel[i] = allMeshes.Count;
		}
		m_totalMeshes = allMeshes.ToArray();
		m_totalMaterials = allMaterials.ToArray();
		m_totalSubMeshes = allSubMeshes.ToArray();
		m_totalRenderers = allRenderers.ToArray();
		m_totalLayers = allLayers.ToArray();
		m_totalRPs = allRPs.ToArray();
	}
	static int s_cutoffID = -1;
	static int s_colourMaskClipValueID = -1;
	static int s_crossfadeParamsID = -1;
	public LODRenderer(List<MeshRenderer> _rnds) {
		CheckIDs();

		m_levels = new float[1]; m_levels[0] = 1.0f;
		var allMeshes = new List<Mesh>();
		var allMaterials = new List<Material>();
		var allRenderers = new List<Renderer>();
		var allLayers = new List<int>();
		var allSubMeshes = new List<int>();
		var allRPs = new List<RenderParams>();
		m_referenceSize = 2;

		foreach (var rnd in _rnds) {
			var mats = rnd.sharedMaterials;
			var mesh = rnd.gameObject.GetComponent<MeshFilter>().sharedMesh;
			var renderer = rnd.gameObject.GetComponent<Renderer>();
			int layer = rnd.gameObject.layer;
			for (int s = 0; s < mesh.subMeshCount; s++) {
				allRPs.Add(new RenderParams
				{
					material = mats[s],
					shadowCastingMode = renderer.shadowCastingMode,
					receiveShadows = true,
					layer = layer,
					matProps = null,
					renderingLayerMask = 0xFFFFFFFFu,
					instanceID = TerrainPopulation.Me.gameObject.GetInstanceID(),
					lightProbeUsage = UnityEngine.Rendering.LightProbeUsage.BlendProbes,
				});
				allMeshes.Add(mesh); allMaterials.Add(mats[s]); allSubMeshes.Add(s);
				allRenderers.Add(renderer); allLayers.Add(layer);
			}
		}

		m_cumulativeEndOfLevel = new int[1];
		m_cumulativeEndOfLevel[0] = allMeshes.Count;
		m_totalMeshes = allMeshes.ToArray();
		m_totalMaterials = allMaterials.ToArray();
		m_totalSubMeshes = allSubMeshes.ToArray();
		m_totalRenderers = allRenderers.ToArray();
		m_totalLayers = allLayers.ToArray();
		m_totalRPs = allRPs.ToArray();
	}

	public void RenderLOD(int _lod, ref Matrix4x4 _xform, Material _overrideMat, TerrainPopulationInstance _currentInstance, Vector3 _crossFadeParams)
	{
		_crossFadeParams.z = Mathf.SmoothStep(0.0f, 1.0f, _crossFadeParams.z);
		int startOfGroup = (_lod > 0) ? m_cumulativeEndOfLevel[_lod - 1] : 0;
		int endOfGroup = m_cumulativeEndOfLevel[_lod];
		for (int i = startOfGroup; i < endOfGroup; i++) {
			if (_currentInstance.Pollution <= 0f && _crossFadeParams.z <= 0f) {
				// optimised path - no pollution and not cross-fading, don't use material properties block - avoids a memcpy and some setup
				Graphics.DrawMesh(m_totalMeshes[i], _xform, _overrideMat ? _overrideMat : m_totalMaterials[i], m_totalLayers[i], (Camera)null, m_totalSubMeshes[i], null, m_totalRenderers[i].shadowCastingMode, true, s_lightProbeReference, UnityEngine.Rendering.LightProbeUsage.BlendProbes);
			} else {
				float pollutionBase = 0.2f; // 0.2 is the cutoff for no pollution
				float pollutionValue = pollutionBase + ((_currentInstance != null) ? _currentInstance.Pollution * (1 - pollutionBase) : 0);
				TerrainPopulation.s_matProp.SetFloat(s_cutoffID, pollutionValue);
				TerrainPopulation.s_matProp.SetFloat(s_colourMaskClipValueID, pollutionValue);
				
				TerrainPopulation.s_matProp.SetVector(s_crossfadeParamsID, _crossFadeParams);
				Graphics.DrawMesh(m_totalMeshes[i], _xform, _overrideMat ? _overrideMat : m_totalMaterials[i], m_totalLayers[i], (Camera)null, m_totalSubMeshes[i], TerrainPopulation.s_matProp, m_totalRenderers[i].shadowCastingMode, true, s_lightProbeReference, UnityEngine.Rendering.LightProbeUsage.BlendProbes);
			}
		}
	}

	public int GetLODLevel(ref Matrix4x4 _t, Vector4 _viewProjY, Vector4 _viewProjW) {
		float bx = _viewProjY.x * _t.m03 + _viewProjY.y * _t.m13 + _viewProjY.z * _t.m23 + _viewProjY.w;
		float bz = _viewProjW.x * _t.m03 + _viewProjW.y * _t.m13 + _viewProjW.z * _t.m23 + _viewProjW.w;
		float tx = bx + _viewProjY.y * m_referenceSize;
		float tz = bz + _viewProjW.y * m_referenceSize;
		var vpPosBase = bx / bz;
		var vpPosTop = tx / tz;
		//var vpPosBase = (Vector3.Dot(_viewProjY, _pos) + _viewProjY.w) / (Vector3.Dot(_viewProjW, _pos) + _viewProjW.w);
		//var vpPosTop = (Vector3.Dot(_viewProjY, posTop) + _viewProjY.w) / (Vector3.Dot(_viewProjW, posTop) + _viewProjW.w);
		var screenFraction = (vpPosTop - vpPosBase) * .5f;
		int levelsLen = m_levels.Length - 1;
		int i = s_minimumLoD; if (i > levelsLen) i = levelsLen;
		for (; i <= levelsLen; i++) {
			if (screenFraction > m_levels[i]) {
				break;
			}
		}
		return i;
	}
	public int NumSubMeshes(int _lodLevel) { return FirstSubMesh(_lodLevel+1) - FirstSubMesh(_lodLevel); }
	public int FirstSubMesh(int _lodLevel) { return _lodLevel > 0 ? m_cumulativeEndOfLevel[_lodLevel-1] : 0; }
	public bool IsLODTransitioning(int _lod, TerrainPopulationInstance _currentInstance) {
		if (_currentInstance.m_temporalDitherTimer > 0.0f) return true;
		bool hasChanged = _lod != _currentInstance.m_prevLodIndex;
		if (hasChanged && _currentInstance.m_temporalDitherTimer <= 0.0f)
			return true;
		return false;
	}
	public int GetLODTransitionDetails(int _lod, TerrainPopulationInstance _currentInstance, float _maxDitherTime, float _randomDitherTimeOffsetRange, ref Vector3 _currentRP, ref Vector3 _previousRP) {
		float maxDitherTime = _maxDitherTime + UnityEngine.Random.value * _randomDitherTimeOffsetRange;
		bool hasChanged = _lod != _currentInstance.m_prevLodIndex;
		if (hasChanged && _currentInstance.m_temporalDitherTimer <= 0.0f) {
			_currentInstance.m_temporalDitherTimer = maxDitherTime;
		}
		bool transitioning = _currentInstance.m_temporalDitherTimer > 0.0f;
		float transitionAmount = _currentInstance.m_temporalDitherTimer / maxDitherTime;
		if (transitioning) {
			_currentInstance.m_temporalDitherTimer -= Time.unscaledDeltaTime;
			_currentRP.x = 1f; _currentRP.y = 0f; _currentRP.z = transitionAmount;
			_previousRP.x = -1f; _previousRP.y = 1f; _previousRP.z = 1f - transitionAmount;
			return _currentInstance.m_prevLodIndex;
		}
		return -1;
	}
	public void Render(Vector4 _viewProjY, Vector4 _viewProjW, ref Matrix4x4 _xform, Material _overrideMat, float _maxDitherTime, float _randomDitherTimeOffsetRange, TerrainPopulationInstance _currentInstance = null) {
		int i = GetLODLevel(ref _xform, _viewProjY, _viewProjW);

		Vector3 standardRenderParams = Vector3.right;

		if(_currentInstance != null) {
			float maxDitherTime = _maxDitherTime + UnityEngine.Random.value * _randomDitherTimeOffsetRange;
			bool hasChanged = i != _currentInstance.m_prevLodIndex;
			if (hasChanged && _currentInstance.m_temporalDitherTimer <= 0.0f) {
				_currentInstance.m_temporalDitherTimer = maxDitherTime;
			}

			bool transitioning = _currentInstance.m_temporalDitherTimer > 0.0f;
			float transitionAmount = _currentInstance.m_temporalDitherTimer / maxDitherTime;
			if (transitioning) {
				_currentInstance.m_temporalDitherTimer -= Time.unscaledDeltaTime;
				if (_currentInstance.m_prevLodIndex < m_levels.Length)
					RenderLOD(_currentInstance.m_prevLodIndex, ref _xform, _overrideMat, _currentInstance, new Vector3(-1.0f, 1.0f, 1.0f - transitionAmount));
				standardRenderParams.z = transitionAmount;
			}

			if(_currentInstance.m_temporalDitherTimer <= 0.0f) 
				_currentInstance.m_prevLodIndex = i;
		}
		if (i < m_levels.Length)
			RenderLOD(i, ref _xform, _overrideMat, _currentInstance, standardRenderParams);
	}
	public void RenderSimple(ref Matrix4x4 _xform, int _lod) {
		int startOfGroup = (_lod > 0) ? m_cumulativeEndOfLevel[_lod - 1] : 0;
		int endOfGroup = m_cumulativeEndOfLevel[_lod];
		for (int i = startOfGroup; i < endOfGroup; i++) {
			Graphics.DrawMesh(m_totalMeshes[i], _xform, m_totalMaterials[i], m_totalLayers[i], (Camera)null, m_totalSubMeshes[i], null, m_totalRenderers[i].shadowCastingMode, true, s_lightProbeReference, UnityEngine.Rendering.LightProbeUsage.BlendProbes);
		}
	}
	public void RenderInstances(Matrix4x4[] _xforms, int _count, int _lod, bool _clampLOD = false) {
		if (_lod >= m_levels.Length) {
			if (!_clampLOD)
				return;
			_lod = m_levels.Length - 1;
		}
		int startOfGroup = (_lod > 0) ? m_cumulativeEndOfLevel[_lod - 1] : 0;
		int endOfGroup = m_cumulativeEndOfLevel[_lod];
		for (int i = startOfGroup; i < endOfGroup; i++) {
			if (Application.isPlaying)
			{
				//Graphics.RenderMeshInstanced(m_totalRPs[i], m_totalMeshes[i], m_totalSubMeshes[i], _xforms, _count);
				Graphics.DrawMeshInstanced(TerrainPopulation.Me.DebugGrassMesh ?? m_totalMeshes[i], m_totalSubMeshes[i], m_totalMaterials[i], _xforms, _count, null, m_totalRenderers[i].shadowCastingMode, true, m_totalLayers[i], (Camera) null, UnityEngine.Rendering.LightProbeUsage.BlendProbes, null);
			}
			else
			{
				// looks like DrawMeshInstanced isn't happy in editor mode
				for (int j = 0; j < _count; j++)
					Graphics.DrawMesh(m_totalMeshes[i], _xforms[j], m_totalMaterials[i], m_totalLayers[i], (Camera)null, m_totalSubMeshes[i], null, m_totalRenderers[i].shadowCastingMode, true, s_lightProbeReference, UnityEngine.Rendering.LightProbeUsage.BlendProbes);
			}
		}
	}
	
	public void RenderInstances(NativeArray<Matrix4x4> _xforms, int _count, int _lod, bool _clampLOD = false)
	{
		if (_lod >= m_levels.Length)
		{
			if (!_clampLOD)
				return;
			_lod = m_levels.Length - 1;
		}
		int startOfGroup = (_lod > 0) ? m_cumulativeEndOfLevel[_lod - 1] : 0;
		int endOfGroup = m_cumulativeEndOfLevel[_lod];

		var xformsArr = new Matrix4x4[_xforms.Length];
		NativeArray<Matrix4x4>.Copy(_xforms, xformsArr);
		for (int i = startOfGroup; i < endOfGroup; i++)
		{
			Graphics.DrawMeshInstanced(m_totalMeshes[i], m_totalSubMeshes[i], m_totalRPs[i].material, xformsArr, _count);
		}
	}
	
	public static Transform s_lightProbeReference;
	static int s_minimumLoD = 0;
	static DebugConsole.Command _poplod = new DebugConsole.Command("optpoplod", (_s) => {
		int minLoD = 0;
		if (int.TryParse(_s, out minLoD)) s_minimumLoD = minLoD;
	});

}

public static class RandomExt {
	public static float Range(this System.Random _this, float _min, float _max) {
		int r = _this.Next(0xFFFF);
		float rf = (float)r / 65535.0f;
		return _min + (_max - _min) * rf;
	}
}
/*public static class LODGroupExt {
	public static void DrawMesh(this LODGroup _this, Matrix4x4 _xform, Vector3 _camPos, Vector3 _camFwd) {
		var pos = new Vector3(_xform.m03, _xform.m13, _xform.m23);   
		var dist = Mathf.Abs(Vector3.Dot(pos - _camPos, _camFwd));
		var screenHeight = 2.0f / dist;
		var lods = _this.GetLODs();
		for (int i = 1; i < lods.Length; i ++) {
			if (screenHeight > lods[i].screenRelativeTransitionHeight) {
				var rnd = lods[i-1].renderers;
				foreach (var r in rnd) {
					var mat = r.sharedMaterials;
					var mesh = r.gameObject.GetComponent<MeshFilter>().sharedMesh;
					for (int s = 0; s < mesh.subMeshCount; s ++)
						Graphics.DrawMesh(mesh, _xform, mat[s], 0, (Camera)null, s);
				}
				break;
			}
		}
	}
}*/


[ExecuteAlways]
public class TerrainPopulation : MonoSingleton<TerrainPopulation>
{
	public TerrainPopulationPrefab[] m_layers;

	[HideInInspector] public TerrainPopulationInstanceList[] m_instances;
	int m_DEBUG_numInstances;
	public int DEBUG_numInstances
	{
		get { return m_DEBUG_numInstances; }
	}
	int m_DEBUG_numInstancesRendered;
	public int DEBUG_numInstancesRendered
	{
		get { return m_DEBUG_numInstancesRendered; }
	}
	int m_DEBUG_numGrassInstancesRendered;
	public int DEBUG_numGrassInstancesRendered
	{
		get { return m_DEBUG_numGrassInstancesRendered; }
	}

	public float m_maxGrassDistance = 100f;

	public void Refresh()
	{
	}
	
	Terrain GetTerrain()
	{
		return transform.parent.GetComponentInChildren<Terrain>();
	}

	public static Vector3 HeightmapScale => Vector3.one * TerrainBlock.GlobalScale;
	public static float HeightmapResolution => 1024;
	public static Vector3 HeightmapSize => HeightmapScale * HeightmapResolution;
	public static Vector3 HeightmapPos => HeightmapSize * -.5f;

	public bool m_debugGrass;
	private Mesh m_debugGrassMesh;
	public Mesh DebugGrassMesh
	{
		get {
#if UNITY_EDITOR
			if (m_debugGrass == false) return null;
			if (m_debugGrassMesh == null)
			{
				var verts = new List<Vector3>();
				var uvs = new List<Vector2>();
				var uv2s = new List<Vector2>();
				var inds = new List<int>();
				const float c_patchSize = 8.0f;
				const int c_cellsPerPatch = 32;
				const float c_cellSize = c_patchSize / c_cellsPerPatch;
				for (int x = 0; x < c_cellsPerPatch - 1; x++)
				{
					for (int z = 0; z < c_cellsPerPatch - 1; z++)
					{
						int baseIndex = verts.Count;
						var uv = new Vector2(1 - (x + .5f) / (float)c_cellsPerPatch, 1 - (z + .5f) / (float)c_cellsPerPatch);
						verts.Add(new Vector3(x * c_cellSize - c_patchSize * .5f, 0, z * c_cellSize - c_patchSize * .5f));
						verts.Add(new Vector3((x + 1) * c_cellSize - c_patchSize * .5f, 0, z * c_cellSize - c_patchSize * .5f));
						verts.Add(new Vector3((x + 1) * c_cellSize - c_patchSize * .5f, 0, (z + 1) * c_cellSize - c_patchSize * .5f));
						verts.Add(new Vector3(x * c_cellSize - c_patchSize * .5f, 0, (z + 1) * c_cellSize - c_patchSize * .5f));
						uvs.Add(new Vector2(x / (float)c_cellsPerPatch, z / (float)c_cellsPerPatch));
						uvs.Add(new Vector2((x + 1) / (float)c_cellsPerPatch, z / (float)c_cellsPerPatch));
						uvs.Add(new Vector2((x + 1) / (float)c_cellsPerPatch, (z + 1) / (float)c_cellsPerPatch));
						uvs.Add(new Vector2(x / (float)c_cellsPerPatch, (z + 1) / (float)c_cellsPerPatch));
						uv2s.Add(uv);
						uv2s.Add(uv);
						uv2s.Add(uv);
						uv2s.Add(uv);
						inds.Add(baseIndex + 0);
						inds.Add(baseIndex + 1);
						inds.Add(baseIndex + 2);
						inds.Add(baseIndex + 0);
						inds.Add(baseIndex + 2);
						inds.Add(baseIndex + 3);
					}
				}
				
				m_debugGrassMesh = new Mesh();
				m_debugGrassMesh.vertices = verts.ToArray();
				m_debugGrassMesh.uv = uvs.ToArray();
				m_debugGrassMesh.uv2 = uv2s.ToArray();
				m_debugGrassMesh.SetIndices(inds.ToArray(), MeshTopology.Triangles, 0);
				var dummy = new int[] { 0, 0, 0 };
				for (int i = 1; i < 8; ++i)
					m_debugGrassMesh.SetIndices(dummy, MeshTopology.Triangles, i);
				m_debugGrassMesh.RecalculateBounds();
			}
			return m_debugGrassMesh;
#else
			return null;
#endif
		}
	}

#if UNITY_EDITOR
	public void ResetAllTrees()
	{
		for (int i = 0; i < m_instances.Length; i++)
		{
			var instArray = m_instances[i].m_instances;
			for (int j = 0; j < instArray.Length; j++)
			{
				var inst = instArray[j];
				inst.Disabled = false;
			}
		}
	}

	public static GameObject InstantiatePrefab(GameObject _prefab, Vector3 _position, Vector3 _eulers, Vector3 _scale)
	{
		GameObject go = PrefabUtility.InstantiatePrefab(_prefab) as GameObject;
		if (go == null) go = Instantiate(_prefab);
		go.transform.position = _position;
		go.transform.eulerAngles = _eulers;
		go.transform.localScale = _scale;
		return go;
	}
	
	public void ImportFromTerrain()
	{
		if (GlobalData.Me == null)
		{
			GlobalData.SetEditorMe();
			//GlobalData.Me.InitialiseTerrainData();
		}

		var terrain = Terrain.activeTerrain;
		var data = terrain.terrainData;
		var prototypes = data.treePrototypes;
		var boundsList = new Bounds[prototypes.Length];
		var treelikes = new NGTreelike[prototypes.Length];
		var soundTriggers = new SoundTrigger[prototypes.Length];
		bool problem = false;
		for (int i = 0; i < prototypes.Length; ++i)
		{
			var prefab = prototypes[i].prefab;
			var inst = Instantiate(prefab);
			boundsList[i] = ManagedBlock.GetTotalVisualBounds(inst, null, false, true);
			var sxz = Mathf.Max(boundsList[i].size.x, boundsList[i].size.z);
			var sy = boundsList[i].size.y;
			if (sxz > 12  && sxz > sy)
			{
				Debug.LogError($"Prototype {prefab.name} has degenerate bounds {boundsList[i].size}");
				problem = true;
			}
			DestroyImmediate(inst);
			treelikes[i] = null;//prefab.GetComponent<NGTreelike>();
			soundTriggers[i] = prefab.GetComponent<SoundTrigger>();
		}
		if (problem)
		{
			Debug.LogError("Problem detected, abandoning import");
			return;
		}
		
		m_container.Initialise(GlobalData.c_terrainOriginX, GlobalData.c_terrainOriginX + GlobalData.c_heightmapW / (int) GlobalData.c_terrainXZScale,
			GlobalData.c_terrainOriginZ, GlobalData.c_terrainOriginZ + GlobalData.c_heightmapH / (int) GlobalData.c_terrainXZScale, 64);

		var offset = GlobalData.c_terrainOrigin;
		var scale = new Vector3(data.detailWidth, data.size.y, data.detailHeight);
		if (scale.x == 0) scale = data.size;
		for (int i = 0; i < data.treeInstances.Length; ++i)
		{
			var tree = data.treeInstances[i];
			var pos = Vector3.Scale(tree.position, scale) + offset;
			var rot = tree.rotation * Mathf.Rad2Deg;
			var localScale = new Vector3(tree.widthScale, tree.heightScale, tree.widthScale);
			var treePrototype = prototypes[tree.prototypeIndex];
			var treeBounds = boundsList[tree.prototypeIndex];
			var parentTreelike = treelikes[tree.prototypeIndex];
			var path = PrefabUtility.GetPrefabAssetPathOfNearestInstanceRoot(treePrototype.prefab);
			var bits = path.Split(System.IO.Path.DirectorySeparatorChar);
			var name = bits[^1];
			var type = bits[^2];
			var extIndex = name.LastIndexOf('.');
			if (extIndex != -1) name = name[..extIndex];

			GameObject go;
			if (CreateGameObjects)
			{
				go = InstantiatePrefab(treePrototype.prefab, pos, Vector3.up * rot, localScale);
			}
			else if (parentTreelike != null)
			{
				go = new GameObject($"TreeBox{i}");
				go.transform.position = pos;
				go.transform.localScale = localScale;
				var cll = go.AddComponent<BoxCollider>();
				cll.center = treeBounds.center;
				cll.size = Vector3.Scale(treeBounds.size, localScale);
				cll.isTrigger = true;
				var treelike = go.AddComponent<NGTreelike>();
				treelike.m_treeIndex = i;
				treelike.m_treeType = type;
				treelike.m_treeName = name;
				treelike.CopyDataFrom(parentTreelike);
			}
			else
			{
				go = new GameObject($"TreeHolder{i}");
				go.transform.position = pos;
				var cll = go.AddComponent<BoxCollider>();
				cll.center = treeBounds.center;
				cll.size = Vector3.Scale(treeBounds.size, localScale);
				cll.isTrigger = true;
				var treeHolder = go.AddComponent<TreeHolder>();
				treeHolder.m_treeIndex = i;
				treeHolder.m_treeType = type;
				treeHolder.m_treeName = name;
			}
			if (!CreateGameObjects && soundTriggers[tree.prototypeIndex] != null)
			{
				var soundHolder = new GameObject("Audio");
				soundHolder.transform.SetParent(go.transform);
				soundHolder.transform.localPosition = Vector3.up * (treeBounds.size.y * localScale.y * .8f);
				soundHolder.isStatic = true;
				var st = soundHolder.AddComponent<SoundTrigger>();
				st.CopyDataFrom(soundTriggers[tree.prototypeIndex]);
			}
			go.isStatic = true;
			m_container.Add(go);
		}

		EditorUtility.SetDirty(gameObject);
	}
#endif
	public static bool CreateGameObjects => false;


	public GridContainer m_container;

	public GameObject NearestInstanceToPosition(Vector3 _pos, float _maxDistance, bool _check2D, System.Func<GameObject, bool> _predicate = null)
	{
		return m_container.ClosestInRange(_pos, Vector3.one * _maxDistance, _check2D, _predicate);
	}

	public List<GameObject> InstancesInRange(Vector3 _pos, float _maxDistance, bool _check2D = false)
	{
		return m_container.InstancesInRange(_pos, _maxDistance, _check2D);
	}

	public void DisableInstancesInRange(Vector3 _min, Vector3 _max, float _maxHeightToDestroyAt = 1e23f, bool _save = true)
	{
		m_container.DestroyInRange(_min, _max, _maxHeightToDestroyAt, _save);
	}

	public void DisableInstancesInRange(Vector3 _pos, float _rad, float _maxHeightToDestroyAt = 1e23f, bool _save = true)
	{
		m_container.DestroyInRange(_pos, _rad, _maxHeightToDestroyAt, _save);
	}

	public void EnableInstancesInRange(Vector3 _min, Vector3 _max, float _maxHeightToDestroyAt = 1e23f)
	{
		m_container.UnDestroyInRange(_min, _max, _maxHeightToDestroyAt);
	}

	public void EnableInstancesInRange(Vector3 _pos, float _rad, float _maxHeightToDestroyAt = 1e23f)
	{
		m_container.UnDestroyInRange(_pos, _rad, _maxHeightToDestroyAt);
	}
	
	public Dictionary<string, int> TreeDetailsInRange(Vector3 _center, float _radius, string _containerName)
	{
		return m_container.DetailsInRange(_center, _radius, _containerName);
	}

	public void CheckAllInstances()
	{
		m_container.CheckAllInstances();
	}

	static DebugConsole.Command s_resetTrees = new DebugConsole.Command("resettrees", (_s) => { Me.m_container.ResetAllInstances(); });

	//=============================================

	void ResetInstances()
	{
		for (int i = 0; i < m_instances.Length; i++)
		{
			for (int j = 0; j < m_instances[i].m_instances.Length; j++)
			{
				m_instances[i].m_instances[j].Reset();
			}
		}
	}
	
	public static MaterialPropertyBlock s_matProp;

	List<TerrainPopulationInstance>[] m_instanceLists;
	Matrix4x4[][] m_instanceMats;
	int[] m_instanceCounts = new int[8];
	
	GameObject m_gameObjectHolder = null;

	public bool m_showInEditor = false;
	void LateUpdate()
	{
		Camera camMain = Camera.main;
#if UNITY_EDITOR
		if (Application.isPlaying == false)
		{
			if (m_showInEditor)
				camMain = UnityEditor.SceneView.lastActiveSceneView.camera;
			else
				return;
		}
#endif
		DrawGrassLayers(camMain);
	}

	void RenderInstancesAndResetCount(TerrainPopulationPrefab _prefab, int _lod)
	{
		int count = m_instanceCounts[_lod];
		m_instanceCounts[_lod] = 0;
		for (int k = 0; k < _prefab.m_prefabLODGroups.Length; k++)
			_prefab.m_prefabLODGroups[k].RenderInstances(m_instanceMats[_lod], count, _lod);
	}
#if UNITY_EDITOR || DEVELOPMENT_BUILD
	static Dictionary<TerrainPopulationPrefab, int> s_prefabCounts = new();
	static HashSet<string> s_prefabsToIgnore = new();
	private static DebugConsole.Command s_toggleGrassPrefab = new ("grassprefab", (_s) => {
		if (s_prefabsToIgnore.Contains(_s))
			s_prefabsToIgnore.Remove(_s);
		else
			s_prefabsToIgnore.Add(_s);
	});
	private static DebugConsole.Command s_toggleGrassDebug = new ("grassdebug", _s => Utility.SetOrToggle(ref s_showGrassDebug, _s));
	public static string GrassDebug {
		get
		{
			string s = "";
			foreach (var kvp in s_prefabCounts)
				s += $"{kvp.Key.m_prefab.name} : {kvp.Value} {(s_prefabsToIgnore.Contains(kvp.Key.m_prefab.name) ? "X" : "")}\n";
			return s;
		}
	}
	private static bool s_showGrassFade = false;
	private static DebugConsole.Command s_showgrassfadecmd = new ("debuggrassfade", _s => {
		Utility.SetOrToggle(ref s_showGrassFade, _s);
		if (s_showGrassFade)
			Shader.EnableKeyword("_DEBUG_FADE");
		else
			Shader.DisableKeyword("_DEBUG_FADE");
	});
	private static bool s_showGrassDebug = false;
	public static bool ShowGrassDebug => s_showGrassDebug;
	private static void ClearDebugStats() => s_prefabCounts.Clear();
#else
	public static string GrassDebug => "";
	public static bool ShowGrassDebug => false;
	private static void ClearDebugStats() {}
#endif
	static void RenderInstances(TerrainPopulationPrefab _prefab, Matrix4x4[] _mats, int _count, int _lod)
	{
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		if (s_prefabCounts.TryGetValue(_prefab, out var currentCount))
			s_prefabCounts[_prefab] = currentCount + _count;
		else
			s_prefabCounts[_prefab] = _count;
		if (s_prefabsToIgnore.Contains(_prefab.m_prefab.name)) return;
#endif
		for (int k = 0; k < _prefab.m_prefabLODGroups.Length; k++)
			_prefab.m_prefabLODGroups[k].RenderInstances(_mats, _count, _lod, true);
	}

	static void RenderInstances(TerrainPopulationPrefab _prefab, NativeArray<Matrix4x4> _mats, int _count, int _lod)
	{
		for (int k = 0; k < _prefab.m_prefabLODGroups.Length; k++)
			_prefab.m_prefabLODGroups[k].RenderInstances(_mats, _count, _lod, true);
	}

#if UNITY_EDITOR
	private void OnDrawGizmosSelected()
	{
		foreach (var kvp in TreeHolder.AllTrees)
		{
			var height = TreeHolder.GetInstanceHeightScale(kvp.Key);
			Gizmos.color = height > .01f ? Color.green : Color.red;
			var p = kvp.Value.transform.position.GroundPosition();
			Gizmos.DrawSphere(p, 1);
			Handles.Label(p + Vector3.up * 2, $"{kvp.Key}");
		}
	}

	void _OnDrawGizmos()
	{
		if (m_matList == null) return;
		for (int i = 0; i < m_matList.Length; ++i)
		{
			var pos = new Vector3(m_matList[i].m03, m_matList[i].m13, m_matList[i].m23);
			Gizmos.DrawCube(pos, new Vector3(16, .1f, 16));
		}
	}
#endif
	
	public Material m_fogMaterial;
	public float m_fogHeight = 2.5f;
	public float m_fogBubbleRadius = 15f;
	public Vector2 m_fogWobble1 = new Vector2(27f, .7f);
	public Vector2 m_fogWobble2 = new Vector2(37f, .3f);
	public float m_fogWobbleBase = .3f;
	[Range(0f, 0.1f)] public float m_fogHeightWithDistanceFactor = 0;

	public bool m_newFogSystem = false;
	
	private Mesh m_fogMesh;
	const int c_fogPatchSize = 128;
	const int c_fogPatchPatches = 8;
	const int c_fogPatchCells = 64;
	const float c_fogPatchCellSize = (float)c_fogPatchSize / (float)c_fogPatchCells;
	const float c_fogMaxDistance = 3000;
	
	private static bool s_districtMap = true;
	private static DebugConsole.Command s_districtMapCmd = new ("districtmap", _s => Utility.SetOrToggle(ref s_districtMap, _s), "Toggle showing district map on zoom out", "<bool>");
	
	public float m_mapAppearFraction = .6f;
	
	private float m_mapModeTarget = 0; public float MapModeTarget => m_mapModeTarget;
	public int MapMode(float _lowerBoundary = -1, float _upperBoundary = -1)
	{
		if (_lowerBoundary < 0) _lowerBoundary = m_mapAppearFraction;
		if (_upperBoundary < 0) _upperBoundary = m_mapAppearFraction + .1f;
		float maxHeightFraction = GameManager.Me.m_camera.transform.position.y / GameManager.Me.m_cameraHeightMaximum;
		if (maxHeightFraction > _upperBoundary) return 1;
		if (maxHeightFraction < _lowerBoundary) return 0;
		return -1;
	}
	public Transform[] m_mapRenderers;
	public Transform[] m_nonMapRenderers;
	private float m_mapMode = 0; public bool IsInMapMode => m_mapMode > .5f;

	public void ShowMapVisuals(float _mapMode)
	{
		bool mapVisualsEnabled = _mapMode > .5f;
		foreach (var r in m_mapRenderers)
			if (r.gameObject.activeSelf != mapVisualsEnabled)
				r.gameObject.SetActive(mapVisualsEnabled);
		foreach (var r in m_nonMapRenderers)
			if (r.gameObject.activeSelf == mapVisualsEnabled)
				r.gameObject.SetActive(!mapVisualsEnabled);
		Shader.SetGlobalFloat("_MapMode", _mapMode);
	}
	
	private void CheckAndGenerateFogMesh()
	{
		if (s_districtMap)
		{
			var mapMode = MapMode();
			if (mapMode != -1) m_mapModeTarget = mapMode;
			bool wasInMapMode = IsInMapMode;
			m_mapMode = Mathf.Lerp(m_mapMode, m_mapModeTarget, .6f);//.15f);
			bool isInMapMode = IsInMapMode;
			if (wasInMapMode != isInMapMode)
			{
				MapUIController.Me.Show(isInMapMode);
				AudioClipManager.Me.PlaySound(isInMapMode ? "PlaySound_MapView_OPEN" : "PlaySound_MapView_CLOSED", GameManager.Me.gameObject);
			}
		}
		else
			m_mapMode = 0;

		ShowMapVisuals(m_mapMode);
		var fogIntensity = 1 - m_mapMode;
		CameraRenderSettings.Me.SetDistrictFilterIntensity(fogIntensity);
		DistrictManager.Me.SetDistrictMapIntensity(m_mapMode, m_mapModeTarget > .5f);
		float bubbleRadius = Mathf.Lerp(GameManager.Me.m_cameraHeightMaximum * 3, m_fogBubbleRadius, fogIntensity);
		Shader.SetGlobalFloat("_FogMaxDistance", c_fogMaxDistance);
		Shader.SetGlobalFloat("_FogRaise", m_fogHeight);
		Shader.SetGlobalFloat("_FogBubbleInvSqrd", 1.0f / (bubbleRadius * bubbleRadius));
		Shader.SetGlobalVector("_FogWobble1", m_fogWobble1);
		Shader.SetGlobalVector("_FogWobble2", m_fogWobble2);
		Shader.SetGlobalFloat("_FogWobbleBase", m_fogWobbleBase);
		Shader.SetGlobalFloat("_FogHeightWithDistanceFactor", m_fogHeightWithDistanceFactor);
		Shader.SetGlobalFloat("_NEW_FOG", m_newFogSystem ? 1 : 0);
		/*const string c_newFogKeyword = "_NEW_FOG";
		if (Shader.IsKeywordEnabled(c_newFogKeyword) != m_newFogSystem)
		{
			if (m_newFogSystem)
				Shader.EnableKeyword(c_newFogKeyword);
			else
				Shader.DisableKeyword(c_newFogKeyword);
		}*/

		if (m_fogMesh != null) return;
		
		var verts = new List<Vector3>();
		var uvs = new List<Vector2>();
		var uv2s = new List<Vector2>();
		var inds = new List<int>();
		for (int z = 0; z <= c_fogPatchCells; ++z)
		{
			for (int x = 0; x <= c_fogPatchCells; ++x)
			{
				verts.Add(new Vector3(x * c_fogPatchCellSize, 0, z * c_fogPatchCellSize));
				var uv = new Vector2(x / (float)c_fogPatchCells, z / (float)c_fogPatchCells);
				uvs.Add(uv);
				uv2s.Add(Vector2.one - uv);
			}
		}
		for (int z = 0; z < c_fogPatchCells; ++z)
		{
			for (int x = 0; x < c_fogPatchCells; ++x)
			{
				int baseIndex = z * (c_fogPatchCells + 1) + x;
				inds.Add(baseIndex + 0); inds.Add(baseIndex + c_fogPatchCells + 1); inds.Add(baseIndex + 1);
				inds.Add(baseIndex + 1); inds.Add(baseIndex + c_fogPatchCells + 1); inds.Add(baseIndex + c_fogPatchCells + 1 + 1);
			}
		}
		var mesh = new Mesh();
		mesh.SetVertices(verts);
		mesh.SetUVs(0, uvs);
		mesh.SetUVs(1, uv2s);
		mesh.SetIndices(inds, MeshTopology.Triangles, 0);
		mesh.UploadMeshData(false);
		m_fogMesh = mesh;
	}

	public static bool ShowTrees
	{
		get => Me.m_container.gameObject.activeSelf;
		set => Me.m_container.gameObject.SetActive(value);
	}
	public static bool ShowGrass
	{
		get { return s_showGrass; }
		set { s_showGrass = value; }
	}

	public struct NativePlane
	{
		public float3 normal;
		public float3 signs;
		public float distance;

		public NativePlane(Plane _p)
		{
			normal = _p.normal;
			signs = math.sign(_p.normal);
			distance = _p.distance;
		}

		public bool IsInside(float3 _center, float3 _extent)
		{
			var test = _center + _extent * signs;
			return math.dot(normal, test) + distance > 0;
		}
	}

	
	public void AdjustTreeQualityFromSetting(int _setting)
	{
		_setting += _setting / 3; // to give 0, 1, 2, 4
		float lodFactor = 6.0f / (_setting + 2); // 3, 2, 1.5, 1
		AdjustTreeQuality(lodFactor);
	}

	private Transform m_prototypeHolder;
	private void AdjustTreeQuality(float _lodFactor)
	{
		Terrain terrain = Terrain.activeTerrain;
		TerrainData terrainData = terrain.terrainData;
		TreePrototype[] prototypes = terrainData.treePrototypes;
		if (m_prototypeHolder == null)
		{
			m_prototypeHolder = new GameObject("TreePrototypeHolder").transform;
			m_prototypeHolder.SetParent(transform);
			for (int i = 0; i < prototypes.Length; i++)
			{
				var newPrototype = new TreePrototype(prototypes[i]);
				newPrototype.prefab = Instantiate(prototypes[i].prefab, m_prototypeHolder);
				AdjustSpeedTreeLOD(newPrototype.prefab, _lodFactor);
				prototypes[i] = newPrototype;
				newPrototype.prefab.SetActive(false);
			}
		}
		else
		{
			for (int i = 0; i < prototypes.Length; i++)
				AdjustSpeedTreeLOD(prototypes[i].prefab, _lodFactor);
		}
		terrainData.treePrototypes = prototypes;
		terrain.Flush();
	}

	private Dictionary<GameObject, float[]> m_treeLODBaseLevels = new();
	void AdjustSpeedTreeLOD(GameObject _treePrefab, float _lodFactor)
	{
		LODGroup lodGroup = _treePrefab.GetComponent<LODGroup>();
		if (lodGroup == null) return;
		LOD[] lods = lodGroup.GetLODs();
		if (m_treeLODBaseLevels.TryGetValue(_treePrefab, out var baseLevels) == false)
		{
			baseLevels = new float[lods.Length];
			for (int i = 0; i < lods.Length; i++)
				baseLevels[i] = lods[i].screenRelativeTransitionHeight;
			m_treeLODBaseLevels[_treePrefab] = baseLevels;
		}
		float lastValue = 2;
		for (int i = 0; i < lods.Length; i++)
		{
			var value = Mathf.Clamp01(baseLevels[i] * _lodFactor);
			if (value >= lastValue)
				value = lastValue - 0.001f;
			lastValue = value;
			lods[i].screenRelativeTransitionHeight = value;
		}
		lodGroup.SetLODs(lods);
	}
	
	static DebugConsole.Command s_treeLODCmd = new ("treelod", (_s) => {
		if (float.TryParse(_s, out var f))
			Me.AdjustTreeQuality(f);
	});
	
	static bool s_useGrassJobs = true;
	static long s_grassProfileTotal = 0, s_grassProfileCount = 0;
	static DebugConsole.Command s_useGrassJobsCmd = new("grassjobs", (_s) => {
		Utility.SetOrToggle(ref s_useGrassJobs, _s);
		s_grassProfileTotal = 0; s_grassProfileCount = 0;
	});
	NativeArray<Matrix4x4> m_grassMatList;
	NativeArray<int> m_grassMatListLengthReturn;
	bool m_grassMatListHasInitialised = false;
	static bool s_showGrass = true;
	static DebugConsole.Command s_showGrassCmd = new DebugConsole.Command("grass", (_s) => Utility.SetOrToggle(ref s_showGrass, _s));
	Matrix4x4[] m_matList;
	public bool m_DEBUG_refresh = false;
	private NativeArray<NativePlane> m_frustumPlanesNative;
	void DrawGrassLayers(Camera _cam) {
		CheckAndGenerateFogMesh();
		
		if (Application.isPlaying == false)
		{
			if (GlobalData.Me == null || m_DEBUG_refresh)
			{
				GlobalData.SetEditorMe();
				GlobalData.Me.EditorInit();
				GlobalData.Me.m_terrainData = Terrain.activeTerrain.terrainData;
				GlobalData.Me.m_originalTerrainData = Terrain.activeTerrain.terrainData;
			}
			if (CameraRenderSettings.Me == null || m_DEBUG_refresh)
			{
				CameraRenderSettings.SetEditorMe();
				CameraRenderSettings.Me.ApplyRenderSettings();
				CameraRenderSettings.Me.LoadTerrainData();
				CameraRenderSettings.Me.BeginSplatOperations(Vector3.zero, Vector3.zero);
				CameraRenderSettings.Me.EndSplatOperations(); // force a hl splat refresh
			}
			m_DEBUG_refresh = false;
			Shader.SetGlobalVector("_CameraWorldPosition", _cam.transform.position);
			Shader.SetGlobalVector("_CameraWorldForward", _cam.transform.forward);
		}

		if (_cam == null) return;
		if (m_matList == null || m_matList.Length < 128) {
			m_matList = new Matrix4x4[128];
			for (int i = 0; i < m_matList.Length; i ++) m_matList[i] = Matrix4x4.identity;
		}

		if (!m_grassMatListHasInitialised)
		{
			m_grassMatListLengthReturn = new NativeArray<int>(1, Allocator.Persistent, NativeArrayOptions.ClearMemory);
			m_grassMatList = new NativeArray<Matrix4x4>(2560, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
			for (int i = 0; i < m_grassMatList.Length; ++i) m_grassMatList[i] = Matrix4x4.identity;
			m_grassMatListHasInitialised = true;
		}

		int matsLength = m_matList.Length;
		int matCount = 0;
		bool showingGrass = s_showGrass;//HardwareLevels.ShowingGrass;
		int grassLOD = 0;//HardwareLevels.Settings == null ? 0 : HardwareLevels.Settings.GrassLOD;
		{
			ClearDebugStats();

			Vector3[] frustumCorners = new Vector3[4];
			var depth = m_maxGrassDistance;
			frustumCorners[0] = _cam.ViewportToWorldPoint(new Vector3(0, 0, depth));
			frustumCorners[1] = _cam.ViewportToWorldPoint(new Vector3(1, 0, depth));
			frustumCorners[2] = _cam.ViewportToWorldPoint(new Vector3(0, 1, depth));
			frustumCorners[3] = _cam.ViewportToWorldPoint(new Vector3(1, 1, depth));
			Vector3 minCornerBase = Vector3.Min(frustumCorners[0], Vector3.Min(frustumCorners[1], Vector3.Min(frustumCorners[2], Vector3.Min(frustumCorners[3], _cam.transform.position - _cam.transform.forward))));
			Vector3 maxCornerBase = Vector3.Max(frustumCorners[0], Vector3.Max(frustumCorners[1], Vector3.Max(frustumCorners[2], Vector3.Max(frustumCorners[3], _cam.transform.position - _cam.transform.forward))));
			
			var minCorner = minCornerBase - Vector3.one * 8;
			var maxCorner = maxCornerBase + Vector3.one * 8;
			
			int max = 0, allTotal = 0, drawCount = 0;
			if (s_useGrassJobs)
			{
				Plane[] frustumPlanes = GeometryUtility.CalculateFrustumPlanes(_cam);
				if (m_frustumPlanesNative.IsCreated == false)
					m_frustumPlanesNative = new NativeArray<NativePlane>(6, Allocator.Persistent);
				for (int i = 0; i < 6; ++i)
					m_frustumPlanesNative[i] = new (frustumPlanes[i]);
				
				int layerCount = m_layers.Length;
				if (true)
				{
					if (showingGrass)
					{
						for (int i = 0; i < layerCount; i++)
						{
							var prefab = m_layers[i];
							if (prefab.m_prefabLODGroups == null) prefab.Refresh();
							prefab.ForceBounds(_cam.transform.position + _cam.transform.forward);
							var job = new CalculateGrassInstances(_cam, prefab, minCorner, maxCorner);
							job.Schedule().Complete();
							int total = job.DrawResults(prefab, grassLOD);
							if (total > max) max = total;
						}
					}
					if (m_newFogSystem)
					{
						var jobFog = new CalculateGrassInstances(_cam, c_fogPatchSize, c_fogPatchPatches, c_fogMaxDistance, GlobalData.c_terrainMin, GlobalData.c_terrainMax/*minCorner - Vector3.one * c_fogPatchSize, maxCorner + Vector3.one * c_fogPatchSize*/);
						jobFog.Schedule().Complete();
						jobFog.DrawResults(m_fogMesh, m_fogMaterial);
					}
				}
				else
				{
					// this version is theoretically faster but in practice is slower; it also requires multiple output buffers
					List<CalculateGrassInstances> jobs = new();
					List<JobHandle> jobScheds = new();
					List<TerrainPopulationPrefab> jobPrefabs = new();
					for (int i = 0; i < layerCount; i++)
					{
						var prefab = m_layers[i];
						if (prefab.m_prefabLODGroups == null) prefab.Refresh();
						prefab.ForceBounds(_cam.transform.position + _cam.transform.forward);
						var job = new CalculateGrassInstances(_cam, prefab, minCorner, maxCorner);
						jobs.Add(job);
						jobScheds.Add(job.Schedule());
						jobPrefabs.Add(prefab);
					}
					for (int i = 0; i < jobScheds.Count; ++i)
					{
						var job = jobs[i];
						var sched = jobScheds[i];
						var prefab = jobPrefabs[i];
						sched.Complete();
						int total = job.DrawResults(prefab, grassLOD);
						allTotal += total;
						if (total > max) max = total;
					}
				}
			}
			else
			{
				float drawRange = m_maxGrassDistance;
				var camFwd = _cam.transform.forward;
				var camSide = _cam.transform.right;
				var camUp = _cam.transform.up;
				var camPos = _cam.transform.position;

				float invProjX = 1f / _cam.projectionMatrix.m00;
				float invProjXSqrd = invProjX * invProjX;

				var posDotFwd = Vector3.Dot(camPos, camFwd);
				var posDotSide = Vector3.Dot(camPos, camSide);

				float highLevelSplatScale = GlobalData.c_terrainXZScale / CameraRenderSettings.c_grassPatchCells;

				var gdata = GlobalData.Me;
				var splatPresenceData = CameraRenderSettings.Me.HighLevelGrassPresence;
				bool hasSplatPresence = Application.isPlaying && s_ignoreSplatPresence == false;
				int layerCount = m_layers.Length;//Mathf.Min(m_instances.Length, m_layers.Length);
				for (int i = 0; i < layerCount; i++)
				{
					var prefab = m_layers[i];
					if (prefab.m_prefabLODGroups == null) prefab.Refresh();
					if (prefab.m_prefabLODGroups.Length == 0) continue;
					prefab.ForceBounds(_cam.transform.position + _cam.transform.forward);
					var size = prefab.m_uniformSize;
					var count = prefab.m_uniformCount;
					var halfCountSize = (float) count * .5f * size;
					
					var seed = (uint)prefab.m_prefab.gameObject.name.GetHashCode();
					var chance = (int)prefab.m_weightInGroup;

					matCount = 0;

					var pos = Vector3.up * 100f; // rough position for distance from camera
					
					float minX = minCorner.x - size * 1.5f;
					float minZ = minCorner.z - size * 1.5f;
					float maxX = maxCorner.x + size * 1.5f;
					float maxZ = maxCorner.z + size * 1.5f;

					int xStart = Mathf.FloorToInt((minX + halfCountSize) / size - .5f);
					int yStart = Mathf.FloorToInt((minZ + halfCountSize) / size - .5f);
					int xEnd = Mathf.CeilToInt((maxX + halfCountSize) / size + .5f);
					int yEnd = Mathf.CeilToInt((maxZ + halfCountSize) / size + .5f);
					xStart = Mathf.Max(0, xStart);
					yStart = Mathf.Max(0, yStart);
					xEnd = Mathf.Min(count - 1, xEnd);
					yEnd = Mathf.Min(count - 1, yEnd);
					
					//GameManager.Me.ClearGizmos("Grass");
					var minB = new Vector3(xStart * size - halfCountSize, 90, yStart * size - halfCountSize);
					var maxB = new Vector3(xEnd * size - halfCountSize, 120, yEnd * size - halfCountSize);
					//GameManager.Me.AddGizmoCubeMinMax("Grass", minB, maxB, new Color(.7f, .7f, 1f, .6f), true);
					
					var splatPresenceBitmap = prefab.m_prefabLODGroups[0].GetModelSplatPresenceBitmap();

					// GL - note: could rasterise the frustum to the grid rather than use this loose rectangular bound
					for (int y = yStart; y <= yEnd; y++)
					{
						pos.z = (float) y * size - halfCountSize;

						for (int x = xStart; x <= xEnd; x++)
						{
							pos.x = (float) x * size - halfCountSize;
							pos.y = gdata.GetRawHeight(pos);

							var fwd = pos.x * camFwd.x + pos.y * camFwd.y + pos.z * camFwd.z - posDotFwd;
							if (fwd < 0 || fwd > drawRange + size) continue;
							fwd += size * 2;
							var side = pos.x * camSide.x + pos.y * camSide.y + pos.z * camSide.z - posDotSide;
							if (side * side > fwd * fwd * invProjXSqrd) continue;

							int hlSplatX = (int) ((pos.x - GlobalData.c_terrainOrigin.x) * highLevelSplatScale);
							int hlSplatZ = (int) ((pos.z - GlobalData.c_terrainOrigin.z) * highLevelSplatScale);
							if (hlSplatX < 0 || hlSplatZ < 0 || hlSplatX >= CameraRenderSettings.c_grassPatchCountW || hlSplatZ >= CameraRenderSettings.c_grassPatchCountH) continue;

							if (hasSplatPresence && splatPresenceData != null && (splatPresenceData[hlSplatX + hlSplatZ * CameraRenderSettings.c_grassPatchCountW] & splatPresenceBitmap) == 0) continue;
							
							var iseed = seed ^ (uint)(x * 7171717) ^ (uint)(y * 1717171);
							Utility.XorShift(ref iseed);
							if ((((Utility.XorShift(ref iseed) & 0xFFFF) * 100) >> 16) > chance) continue;

							m_DEBUG_numGrassInstancesRendered++;
							m_matList[matCount].m03 = pos.x;
							m_matList[matCount].m23 = pos.z;
							m_matList[matCount].m13 = pos.y;

							matCount++;
							if (matCount >= matsLength)
							{
								RenderInstances(prefab, m_matList, matCount, grassLOD);
								++drawCount;
								allTotal += matCount;
								matCount = 0;
							}
						}
					}
					if (matCount >= 0)
					{
						RenderInstances(prefab, m_matList, matCount, grassLOD);
						++drawCount;
						allTotal += matCount;
					}
				}
			}
		}
	}
	
	static Matrix4x4[] s_finalMatrixArray = new Matrix4x4[256];
	static int s_maxGrassInstanceError = 0;

	static bool s_ignoreSplatPresence = false;
	static DebugConsole.Command s_ignoreSplatPresenceCmd = new DebugConsole.Command("ignoresplatpresence", (_s) => Utility.SetOrToggle(ref s_ignoreSplatPresence, _s)); 
	
	[BurstCompile]
	public struct CalculateGrassInstances : IJob
	{
		[ReadOnly] float3 m_camPos;
		[ReadOnly] float3 m_camFwd;
		[ReadOnly] float3 m_camSide;
		[ReadOnly] float3 m_camUp;
		[ReadOnly] float m_invProjX;
		[ReadOnly] float m_maxGrassDistance;
		[ReadOnly] float m_size;
		[ReadOnly] int m_count;
		[ReadOnly] int m_splatPresenceBitmap;
		[NativeDisableContainerSafetyRestriction]
		[ReadOnly] NativeArray<byte> m_splatPresenceData;
		[NativeDisableContainerSafetyRestriction]
		[ReadOnly] NativeArray<float> m_heights;
		[ReadOnly] float3 m_terrainOrigin;
		[ReadOnly] float3 m_terrainMax;
		[ReadOnly] uint m_seed;
		[ReadOnly] int m_chance;
		[ReadOnly] float3 m_minCorner, m_maxCorner;
		[ReadOnly] bool m_ignoreSplatPresence;
		[ReadOnly] NativeArray<NativePlane> m_frustum;
		[ReadOnly] int m_terrainOriginX, m_terrainOriginZ;
		[ReadOnly] float m_terrainXZScale;
		[ReadOnly] int m_heightmapW, m_grassPatchCells, m_grassPatchCountW;
		
		NativeArray<Matrix4x4> m_results;
		NativeArray<int> m_resultsLength;
		
		public CalculateGrassInstances(Camera _cam, TerrainPopulationPrefab _prefab, Vector3 _minCorner, Vector3 _maxCorner)
		{
			m_minCorner = _minCorner;
			m_maxCorner = _maxCorner;
			m_camFwd = _cam.transform.forward;
			m_camSide = _cam.transform.right;
			m_camUp = _cam.transform.up;
			m_camPos = _cam.transform.position;
			m_invProjX = 1f / _cam.projectionMatrix.m00;
			m_maxGrassDistance = Me.m_maxGrassDistance;
			m_grassPatchCells = CameraRenderSettings.c_grassPatchCells;
			m_grassPatchCountW = CameraRenderSettings.c_grassPatchCountW;

			m_terrainOriginX = GlobalData.c_terrainOriginX;
			m_terrainOriginZ = GlobalData.c_terrainOriginZ;
			m_terrainXZScale = GlobalData.c_terrainXZScale;
			m_heightmapW = GlobalData.c_heightmapW;

			m_frustum = Me.m_frustumPlanesNative;

			m_seed = (uint)_prefab.m_prefab.gameObject.name.GetHashCode();
			m_chance = (int)_prefab.m_weightInGroup;

			m_size = _prefab.m_uniformSize;
			m_count = _prefab.m_uniformCount;
			m_splatPresenceBitmap = _prefab.m_prefabLODGroups[0].GetModelSplatPresenceBitmap();
			m_splatPresenceData = CameraRenderSettings.Me.HighLevelGrassPresence;
			
			m_heights = GlobalData.Me.Heights;

			m_terrainOrigin = GlobalData.c_terrainOrigin;
			m_terrainMax = GlobalData.c_terrainOrigin + GlobalData.c_terrainExtent;
			
			m_results = Me.m_grassMatList;
			m_resultsLength = Me.m_grassMatListLengthReturn;
			
			m_ignoreSplatPresence = s_ignoreSplatPresence;
		}

		public CalculateGrassInstances(Camera _cam, float _size, int _count, float _maxDistance, Vector3 _minCorner, Vector3 _maxCorner)
		{
			m_minCorner = _minCorner;
			m_maxCorner = _maxCorner;
			m_camFwd = _cam.transform.forward;
			m_camSide = _cam.transform.right;
			m_camUp = _cam.transform.up;
			m_camPos = _cam.transform.position;
			m_invProjX = 1f / _cam.projectionMatrix.m00;
			m_maxGrassDistance = _maxDistance;
			m_grassPatchCells = CameraRenderSettings.c_grassPatchCells;
			m_grassPatchCountW = CameraRenderSettings.c_grassPatchCountW;

			m_terrainOriginX = GlobalData.c_terrainOriginX;
			m_terrainOriginZ = GlobalData.c_terrainOriginZ;
			m_terrainXZScale = GlobalData.c_terrainXZScale;
			m_heightmapW = GlobalData.c_heightmapW;

			m_frustum = Me.m_frustumPlanesNative;

			m_seed = 0;
			m_chance = 100;

			m_size = _size;
			m_count = _count;
			m_splatPresenceBitmap = 0;
			m_splatPresenceData = CameraRenderSettings.Me.HighLevelGrassPresence;

			m_heights = GlobalData.Me.Heights;

			m_terrainOrigin = GlobalData.c_terrainOrigin;
			m_terrainMax = GlobalData.c_terrainOrigin + GlobalData.c_terrainExtent;

			m_results = Me.m_grassMatList;
			m_resultsLength = Me.m_grassMatListLengthReturn;

			m_ignoreSplatPresence = true;
		}
		
		private bool IsBoundsInFrustum(float3 center, float3 extents)
		{
			for (int i = 0; i < m_frustum.Length; ++i)
				if (m_frustum[i].IsInside(center, extents) == false)
					return false;
			return true;
		}
		
		private int ConvertResults()
		{
			int count = m_resultsLength[0];
			if (count > Me.m_grassMatList.Length)
			{
				if (count > s_maxGrassInstanceError)
				{
					Debug.LogError($"Grass instance count exceeded - {count} >  {Me.m_grassMatList.Length}");
					s_maxGrassInstanceError = count;
				}
				count = Me.m_grassMatList.Length;
			}
			if (s_finalMatrixArray.Length < count) s_finalMatrixArray = new Matrix4x4[count];
			NativeArray<Matrix4x4>.Copy(m_results, s_finalMatrixArray, count);
			return count;
		}

		public int DrawResults(TerrainPopulationPrefab _prefab, int _lod)
		{
			var count = ConvertResults();
			RenderInstances(_prefab, s_finalMatrixArray, count, _lod);
			return count;
		}

		public int DrawResults(Mesh _mesh, Material _material)
		{
			var count = ConvertResults();
			Graphics.DrawMeshInstanced(_mesh, 0, _material, s_finalMatrixArray, count, null, ShadowCastingMode.Off, true, 0, (Camera) null, UnityEngine.Rendering.LightProbeUsage.BlendProbes, null);
			return count;
		}

		public float TerrainXf(float _worldX) => (_worldX - m_terrainOriginX) * m_terrainXZScale;
		public float TerrainZf(float _worldZ) => (_worldZ - m_terrainOriginZ) * m_terrainXZScale;
		public int TerrainX(float _worldX) => (int) TerrainXf(_worldX);
		public int TerrainZ(float _worldZ) => (int) TerrainZf(_worldZ);

		public static uint XorShift(ref uint _seed)
		{
			_seed ^= _seed << 13;
			_seed ^= _seed >> 17;
			_seed ^= _seed << 5;
			return _seed;
		}
		
		public void Execute()
		{
			m_resultsLength[0] = 0;

			float drawRange = m_maxGrassDistance;

			var posDotFwd = math.dot(m_camPos, m_camFwd);

			float highLevelSplatScale = m_terrainXZScale / m_grassPatchCells;

			float ox = m_terrainOrigin.x, oz = m_terrainOrigin.z;

			var pos = new float3();
			
			uint seed = m_seed;
			int chance = m_chance;

			float minX = m_minCorner.x - m_size * 1.5f;
			float minZ = m_minCorner.z - m_size * 1.5f;
			float maxX = m_maxCorner.x + m_size * 1.5f;
			float maxZ = m_maxCorner.z + m_size * 1.5f;

			int xStart = (int)math.floor((minX - ox) / m_size - .5f);
			int yStart = (int)math.floor((minZ - oz) / m_size - .5f);
			int xEnd = (int)math.ceil((maxX - ox) / m_size + .5f);
			int yEnd = (int)math.ceil((maxZ - oz) / m_size + .5f);
			
			int splatStride = m_grassPatchCountW;
			
			float3 result = new float3();
			float3 extent = new float3(m_size * .5f, 50, m_size * .5f);
			float3 toCenter = new float3(m_size * .5f, 0, m_size * .5f);

			// GL - note: could rasterise the frustum to the grid rather than use this loose rectangular bound
			for (int y = yStart; y <= yEnd; y++)
			{
				pos.z = (float) y * m_size + oz;
				int iz = TerrainZ(pos.z);
				if (iz < 0 || iz >= m_heightmapW) continue;
				for (int x = xStart; x <= xEnd; x++)
				{
					pos.x = (float) x * m_size + ox;
					int ix = TerrainX(pos.x);
					if (ix < 0 || ix >= m_heightmapW) continue;
					
					pos.y = m_heights[ix + iz * m_heightmapW];
					
					if (IsBoundsInFrustum(pos + toCenter, extent) == false) continue;
					var fwd = math.dot(pos, m_camFwd) - posDotFwd;
					if (fwd > drawRange + m_size) continue;

					int hlSplatX = (int) ((pos.x - m_terrainOrigin.x) * highLevelSplatScale);
					int hlSplatZ = (int) ((pos.z - m_terrainOrigin.z) * highLevelSplatScale);

					if (m_ignoreSplatPresence == false && (m_splatPresenceData[hlSplatX + hlSplatZ * splatStride] & m_splatPresenceBitmap) == 0) continue;

					if (chance < 100)
					{
						var iseed = seed ^ (uint) (x * 7171717) ^ (uint) (y * 1717171);
						XorShift(ref iseed);
						if ((((XorShift(ref iseed) & 0xFFFF) * 100) >> 16) > chance) continue;
					}

					int next = m_resultsLength[0]++;
					if (next < m_results.Length)
					{
						var m = m_results[next];
						m.m03 = pos.x;
						m.m13 = pos.y;
						m.m23 = pos.z;
						m_results[next] = m;
					}
				}
			}
		}
	}
}
