using UnityEngine;

public class Bracelet : MonoBehaviour
{
    public Transform[] m_gems;
    public Renderer m_manaBar;
    public Renderer m_icon;

    void Start()
    {
        for (int i = 0; i < m_gems.Length; ++i)
            ShowGem(i, false);
    }

    public void ShowGem(string _power, bool _show)
    {
        ShowGem(PlayerHandManager.PowerIndex(_power), _show);
    }
    public void ShowGem(int _index, bool _show)
    {
        if (_index < 0) return;
        if (m_gems[_index].gameObject.activeSelf != _show)
            m_gems[_index].gameObject.SetActive(_show);
    }
    
    private Vector3[] m_gemOriginalLocalPositions = new Vector3[32];
    private Vector3[] m_gemOriginalLocalEulers = new Vector3[32];

    private void CachePos(int _index)
    {
        if (m_gemOriginalLocalPositions[_index].sqrMagnitude < .0001f * .0001f)
        {
            m_gemOriginalLocalPositions[_index] = m_gems[_index].localPosition;
            m_gemOriginalLocalEulers[_index] = m_gems[_index].localEulerAngles;
        }
    }

    const float c_gemDropDistance = .5f;
    const float c_gemPowerUpDistance = .1f;
    public void UnlockGem(int _index, float _progress)
    {
        if (_index < 0) return;
        CachePos(_index);
        ShowGem(_index, true);
        var pos = (1 - _progress) * c_gemDropDistance;
        m_gems[_index].localPosition = m_gemOriginalLocalPositions[_index] + Vector3.forward * pos;
    }

    public void PowerUpGem(int _index, float _progress, int _level)
    {
        if (_index < 0) return;
        CachePos(_index);
        ShowGem(_index, true);
        var pos01 = Mathf.Min(_progress * 2, 2 - _progress * 2);
        pos01 = pos01 * pos01 * (3 - pos01 - pos01);
        var pos = pos01 * c_gemPowerUpDistance;
        m_gems[_index].localPosition = m_gemOriginalLocalPositions[_index] + Vector3.forward * pos;
        //var rot = _progress * 720;
        //m_gems[_index].localEulerAngles = m_gemOriginalLocalEulers[_index] + Vector3.up * rot;
    }

    public void SetManaBar(float _f)
    {
        m_manaBar.material.SetVector("_UV_Offset", new Vector2((1 - _f) * .5f, 0));
    }

    private Sprite m_currentIcon = null;
    private float m_currentAlpha = 0;
    public void SetIcon(string _s, float _intensity = 1)
    {
        var sprite = PlayerHandManager.Me.PowerIcon(_s);
        var showIcon = sprite != null;
        var mat = m_icon.material;
        if (m_currentIcon != sprite || showIcon != m_icon.enabled)
        {
            m_currentIcon = sprite;
            m_icon.enabled = showIcon;
            if (sprite != null)
                mat.mainTexture = sprite.texture;
        }
        if (sprite != null && m_currentAlpha.Nearly(_intensity) == false)
        {
            m_currentAlpha = _intensity;
            mat.SetFloat("_AlphaMultiply", _intensity);
        }
    }
}
