#define TEMP_VIDEO_HELMET_SEQUENCE

using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class IntroControl : MonoSingleton<IntroControl>
{
    private Transform m_intro3rdPersonSpawnPoint;
    private MACharacterBase m_intro3rdPersonCharacter;
    private bool m_inIntroPossess;
    private bool m_inCrypt;
    private bool m_isReEnter;
    private bool m_haveSkippedIntro = false; public bool HaveSkippedIntro => m_haveSkippedIntro;
    const string c_skipLabel = "Intro"; 
    public float m_wipeTimeFromEndOfSequence = 1f;
    public float m_wipeStartLocation = 0;
    public float m_wipeEndLocation = 1;

    public AkEventHolder m_possessStartEvent;
    public AkEventHolder m_enterCryptEvent;
    public AkEventHolder m_leaveCryptEvent;
    public AkSwitchHolder m_takeCrownSwitch;
    
    public bool InCrypt => m_inCrypt;
    public bool InIntroPossess => m_inIntroPossess;
    
    public static bool IsInIntro => Me.InIntroPossess || CharacterSelectionSubscene.IsOpenOrLeaving() || CryptManager.Me.IsFrescoActiveOrLeaving;
    
    void Start()
    {
        m_intro3rdPersonSpawnPoint = GameObject.Find("Intro3rdPersonSpawnPoint").transform;
    }

    private static DebugConsole.Command s_startIntro3rdPersonCmd = new ("startintro3rdperson", _s => Me.StartIntro3rdPerson());
    private static DebugConsole.Command s_startCrypt3rdPersonCmd = new("startcrypt3rdperson", _s => {
        Me.StartIntro3rdPerson();
        Me.ProceedIntoCrypt();
    });
    private static DebugConsole.Command s_reEnterCryptCmd = new("reentercrypt", _s => Me.ReEnterCrypt());

    public bool IsPossessedCharacter(NGMovingObject _chr) => _chr == m_intro3rdPersonCharacter;

    private (float, Vector3, Vector3) m_cameraDetails;
    public void ReEnterCrypt()
    {
        m_isReEnter = true;
        m_cameraDetails = GetCameraDetails();
        ProceedIntoCrypt(true);
        this.Do(() => {
            var escapePressed = EKeyboardFunction.Cancel.AnyClicked();
            if (escapePressed)
                LeaveCrypt();
            return escapePressed == false && m_inCrypt;
        });
    }

    public void StartIntro3rdPerson(bool _setOrientation = true)
    {
        if (m_inIntroPossess) return;
        DisableDuring.Disable(DisableDuring.EDuring.HoodedIntro, true);
        m_inIntroPossess = true;
        var info = MAWorkerInfo.GetInfo("HoodedCharacter");
        m_possessStartEvent?.Play(gameObject);
        m_intro3rdPersonCharacter = MAHoodedCharacter.Create(info, m_intro3rdPersonSpawnPoint.position.GroundPosition());
        this.DoNextFrame(() => // settle the character for a frame after creation
        {
            if (_setOrientation)
                m_intro3rdPersonCharacter.transform.rotation = m_intro3rdPersonSpawnPoint.rotation;
            GameManager.Me.PossessObject(m_intro3rdPersonCharacter, true, true);
            GameManager.Me.SnapPossessionCamera();
        });
        if (m_isReEnter == false)
            SkipManager.Me.DebugEnableSkip(c_skipLabel, true, LeaveCrypt);
    }

    private Vector3 m_intro3rdPersonCharacterEnteredFrom;
    private Quaternion m_intro3rdPersonCharacterEnteredFromDirection;
    public void ProceedIntoCrypt(bool _reEnter = false)
    {
        if (m_inCrypt) return;
        m_inCrypt = true;
        m_enterCryptEvent?.Play(gameObject);
        Crossfade.Me.Fade(() =>
        {
            if (m_inIntroPossess == false)
            {
                StartIntro3rdPerson(false);
                return false;
            }

            PlayerHandManager.Me.ActivatePower("");
            
            CryptManager.Me.EnterNonFresco();
            var cam = Camera.main;
            var camXform = cam.transform;
            if (GameManager.Me.IsPossessed(m_intro3rdPersonCharacter) == false)
                GameManager.Me.PossessObject(m_intro3rdPersonCharacter, true, true);
            m_intro3rdPersonCharacter.SetSubScene("Crypt");
            var renderingLayerMask = CryptManager.Me.m_introTapestry.GetComponentInChildren<Renderer>().renderingLayerMask;
            foreach (var rnd in m_intro3rdPersonCharacter.GetComponentsInChildren<Renderer>())
                rnd.renderingLayerMask = renderingLayerMask;
            m_intro3rdPersonCharacterEnteredFrom = m_intro3rdPersonCharacter.transform.position;
            m_intro3rdPersonCharacterEnteredFromDirection = m_intro3rdPersonCharacter.transform.rotation;
            var spawn = _reEnter ? CryptManager.Me.m_characterReEnterSpawn : CryptManager.Me.m_characterSpawn;
            m_intro3rdPersonCharacter.transform.position = spawn.position + Vector3.up * .2f;
            m_intro3rdPersonCharacter.transform.rotation = spawn.rotation;
            m_intro3rdPersonCharacter.GetComponent<Rigidbody>().isKinematic = false;
            GameManager.Me.SnapPossessionCamera();
            return true;
        },
        () => { });
    }
    
    public void LeaveCryptIn3rdPerson() // walked back out, didn't put on helmet yet
    {
        if (m_isReEnter)
        {
            LeaveCrypt();
            return;
        }
        m_intro3rdPersonCharacter.SetSubScene("Crypt");
        m_intro3rdPersonCharacter.transform.position = m_intro3rdPersonCharacterEnteredFrom;
        m_intro3rdPersonCharacter.transform.rotation = m_intro3rdPersonCharacterEnteredFromDirection;
        GameManager.Me.SnapPossessionCamera();
        m_inCrypt = false;
        CryptManager.Me.ExitNonFresco();
    }

    public void LeaveCrypt()
    {
        DisableDuring.Disable(DisableDuring.EDuring.HoodedIntro, false);
        if (m_inCrypt == false)
        {
            m_haveSkippedIntro = true;
            if (m_intro3rdPersonCharacter != null)
            {
                GameManager.Me.Unpossess(false);
                Destroy(m_intro3rdPersonCharacter.gameObject);
                m_inIntroPossess = false;
                m_intro3rdPersonCharacter = null;
                if (m_isReEnter == false)
                    SkipManager.Me.DebugEnableSkip(c_skipLabel, false);
                ResetGameCamera();
            }
            return;
        }
        m_leaveCryptEvent?.Play(gameObject);
        if (m_isReEnter == false)
        {
            m_takeCrownSwitch?.SetAsOverride();
            SkipManager.Me.DebugEnableSkip(c_skipLabel, false);
        }
        StartCoroutine(Co_LeaveCrypt());
    }
    
    void SetProjectionOffset(Camera cam, float t)
    {
        cam.ResetProjectionMatrix();
        var camProj = cam.projectionMatrix;
        camProj[1, 2] = t;
        cam.projectionMatrix = camProj;
    }

    void ResetGameCamera()
    {
        var cam = GameManager.Me.m_camera;
        cam.fieldOfView = GameManager.Me.DefaultCameraFOV;
        GameManager.Me.SetCameraControlFromCameraPositionRotation(GameManager.Me.m_defaultCameraResetPosition, GameManager.Me.m_defaultCameraResetRotation, GameManager.ECameraControlBlendIn.FromNextStage);
        GameManager.Me.ResetBlendCamera();
    }

    (float, Vector3, Vector3) GetCameraDetails()
    {
        var cam = GameManager.Me.m_camera;
        var camXform = cam.transform;
        return (cam.fieldOfView, camXform.position, camXform.eulerAngles);
    }

    void SetCameraDetails((float, Vector3, Vector3) _details)
    {
        /*var cam = GameManager.Me.m_camera;
        var camXform = cam.transform;
        cam.fieldOfView = _details.Item1;
        camXform.position = _details.Item2;
        camXform.eulerAngles = _details.Item3;*/
    }

    public void StartShortCameraZoom(float _delay = 0) => StartCoroutine(Co_StartShortCameraZoom(_delay));
    IEnumerator Co_StartShortCameraZoom(float _delay)
    {
        var cam = GameManager.Me.m_camera;
        var finalPos = cam.transform.position;
        const float c_shortZoomDistance = 50;
        const float c_shortZoomTime = 1;
        var startPos = finalPos - cam.transform.forward * c_shortZoomDistance;
        for (float t = -_delay; t < c_shortZoomTime; t += Time.deltaTime)
        {
            var t01 = Mathf.Max(0, t / c_shortZoomTime);
            var tEndBlend = 1 - (1 - t01) * (1 - t01);
            cam.transform.position = Vector3.Lerp(startPos, finalPos, tEndBlend);
            yield return null;
        }
    }

    private static bool s_repeatLeaveCrypt = false;
    private static DebugConsole.Command s_repeatLeaveCryptCmd = new ("repeatleavecrypt", _s => Utility.SetOrToggle(ref s_repeatLeaveCrypt, _s));
    
    IEnumerator Co_LeaveCrypt()
    {
    #if TEMP_VIDEO_HELMET_SEQUENCE
        if (m_isReEnter == false || s_repeatLeaveCrypt)
        {
            GameManager.Me.InhibitSettings = true;
            yield return CryptManager.Me.Co_PlayHelmetSequence(() =>
            {
                // Leave possession mode
                GameManager.Me.Unpossess(false);
                m_intro3rdPersonCharacter.DestroyMe();
                GameManager.Me.m_lightsAndCameras.SetActive(true);
                var cryptCamera = CryptManager.Me.m_frescoCamera;
                cryptCamera.enabled = false;
                m_inCrypt = false;
                m_inIntroPossess = false;
                if (m_isReEnter)
                    SetCameraDetails(m_cameraDetails);
                else
                    ResetGameCamera();
                //StartShortCameraZoom();
                CryptManager.Me.ExitNonFresco();
                GameManager.Me.InhibitSettings = false;
            });
            yield break;
        }
#endif
        var helmetLine = UIManager.Me.m_helmetLine;
        helmetLine.gameObject.SetActive(true);
        helmetLine.transform.position = new Vector3(0, 6, 0);
        
        // 
        const float c_fadeTime = .3f;
        for (float t = 0; t < 1;)
        {
            t += Time.deltaTime / c_fadeTime;
            t = Mathf.Min(t, 1);
            Crossfade.Me.SetFade(t);
            yield return null;
        }
        // Leave possession mode
        GameManager.Me.Unpossess(false);
        m_intro3rdPersonCharacter.DestroyMe();
        // Start timeline
        GameManager.Me.m_lightsAndCameras.SetActive(false);
        var cryptCamera = CryptManager.Me.m_frescoCamera;
        var cryptSequence = CryptManager.Me.m_throneSequence;
        cryptCamera.enabled = true;
        if (cryptSequence != null)
            cryptSequence.Play();
        else // TEMP - until we have a sequence
        {
            cryptCamera.transform.position = GameManager.Me.m_camera.transform.position + Vector3.up * 1f;
            cryptCamera.transform.rotation = GameManager.Me.m_camera.transform.rotation;
        }
        for (float t = 0; t < 1;)
        {
            t += Time.deltaTime / c_fadeTime;
            t = Mathf.Min(t, 1);
            Crossfade.Me.SetFade(1 - t);
            yield return null;
        }

        // wait for sequence to end
        if (m_isReEnter)
            ;
        else if (cryptSequence != null)
            while (cryptSequence.time < cryptSequence.duration - m_wipeTimeFromEndOfSequence) yield return null;
        else
            for (float t = 0; t < 6; t += Time.deltaTime) yield return null;
        GameManager.Me.m_lightsAndCameras.SetActive(true);
        cryptCamera.enabled = false;
        m_inCrypt = false;
        m_inIntroPossess = false;

        // now copy the crypt camera into the temporary transition camera
        var transitionCam = new GameObject("TransitionCam").AddComponent<Camera>();
        transitionCam.transform.position = cryptCamera.transform.position;
        transitionCam.transform.rotation = cryptCamera.transform.rotation;
        transitionCam.fieldOfView = cryptCamera.fieldOfView;
        transitionCam.depth = cryptCamera.depth + 1;
        float currentFOV = cryptCamera.fieldOfView;

        // reset the game camera
        if (m_isReEnter)
            SetCameraDetails(m_cameraDetails);
        else
            ResetGameCamera();
        //StartShortCameraZoom();
        
        const float c_duration = 2f;
        for (float tt = 0; tt < 1; tt += Time.deltaTime / c_duration)
        {
            float t = tt * tt * (3f - 2f * tt);
            if (s_repeatLeaveCrypt) { if (tt > .9f) tt = -1;  if (tt < 0) t = tt * tt * (3f + 2f * tt); }

            t = Mathf.Lerp(m_wipeStartLocation, m_wipeEndLocation, t);
            helmetLine.rectTransform.anchoredPosition3D = new Vector3(0, -t * 1080, 0);

            t = Mathf.Clamp(t, .01f, .99f);
            transitionCam.rect = new Rect(0, 0, 1, 1 - t);
            transitionCam.fieldOfView = currentFOV * (1 - t);
            SetProjectionOffset(transitionCam, Mathf.Lerp(-1 / (1 - t), 0, (1 - t)));
            
            yield return null;
        }
        Destroy(transitionCam.gameObject);
        CryptManager.Me.ExitNonFresco();
        helmetLine.gameObject.SetActive(false);
    }
}
