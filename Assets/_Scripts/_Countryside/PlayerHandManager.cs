using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Rendering.HighDefinition;
using UnityEngine.Rendering.Universal;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using Unity.Burst;

public partial class PlayerHandManager : MonoSingleton<PlayerHandManager>
{
    public GameObject m_handPrefab;
    public Texture2D m_cursor2DNormal, m_cursor2DPoint;
    public bool m_keepHandAboveGround = true;
    private GameObject m_handInstance;
    private SkinnedMeshRenderer m_handRenderer;
    public Material m_fingerGlowMaterial;
    private float m_glowSize = .25f;
    public string m_tipOfFingerBone = "Index2_L";//"Bone.Index.Tip"
    public GameObject m_infoMessageHolder;
    public bool HandShowing => m_handRenderer != null && m_handRenderer.enabled;
    
    private bool m_isOver2D; public bool IsOver2D => m_isOver2D;
    AnimationOverride m_castAnimOverride;
    
    private float m_handRaiseTarget = 0;
    private float m_handRaiseCurrent = 0;
    
    [NonSerialized] public bool m_forceHideHand = false;
    private static DebugConsole.Command s_forcehidehand = new ("hidehand", _s => Utility.SetOrToggle(ref Me.m_forceHideHand, _s));
    
    [System.Serializable]
    public class PowerVisuals
    {
        public string m_power;
        public Material m_powerMaterial;
        public string m_particleSystemName;
        public string m_noManaParticleSystemName;
        public bool m_hideMainHand = false;
        
        private Material m_powerMaterialInstance;
        public Material PowerMaterial
        {
            get
            {
                if (m_powerMaterialInstance == null)
                {
                    m_powerMaterialInstance = new Material(m_powerMaterial);
                    m_powerMaterialInstance.name = $"{m_power}PowerMaterial";
                }
                return m_powerMaterialInstance;
            }
        }
        public Material PowerMaterialBase => m_powerMaterial;
        private ParticleSystem[] m_particleSystems;
        private ParticleSystem[] m_noManaParticleSystems;
        private GameObject m_instanceUsed;

        public void SetParticleState(GameObject _instance, bool _enable, bool _enableNoMana, HashSet<ParticleSystem> _enabled, HashSet<ParticleSystem> _disabled)
        {
            if (string.IsNullOrEmpty(m_particleSystemName)) return;
            if (m_instanceUsed != _instance)
            {
                m_instanceUsed = _instance;
                m_particleSystems = null;
            }
            if (m_particleSystems == null)
            {
                var root = _instance.transform.FindChildRecursiveByName(m_particleSystemName);
                if (root == null)
                {
                    Debug.LogError($"PowerVisuals {m_power} - No particle system found with name '{m_particleSystemName}'");
                    m_particleSystems = new ParticleSystem[0];
                }
                else
                    m_particleSystems = root.GetComponentsInChildren<ParticleSystem>(true);

                root = _instance.transform.FindChildRecursiveByName(m_noManaParticleSystemName);
                if (root == null)
                {
                    if (string.IsNullOrEmpty(m_noManaParticleSystemName) == false)
                        Debug.LogError($"PowerVisuals {m_power} - No no mana particle system found with name '{m_noManaParticleSystemName}'");
                    m_noManaParticleSystems = new ParticleSystem[0];
                }
                else
                    m_noManaParticleSystems = root.GetComponentsInChildren<ParticleSystem>(true);
            }

            void EnableParticleSystems(ParticleSystem[] _list, bool _enableSystems)
            {
                foreach (var ps in _list)
                {
                    if (_enableSystems)
                    {
                        _enabled.Add(ps);
                        _disabled.Remove(ps);
                    }
                    else
                    {
                        if (_enabled.Contains(ps) == false)
                            _disabled.Add(ps);
                    }
                }
            }

            EnableParticleSystems(m_particleSystems, _enable);
            EnableParticleSystems(m_noManaParticleSystems, _enableNoMana);
        }
        //
        public Color m_lightColour = new Color(.8f, .6f, .3f);
    }
    public PowerVisuals[] m_powerVisuals;
    private Dictionary<string, PowerVisuals> m_powerVisualsLookup;

    public Transform Hand => m_handInstance.transform;
    public Transform Fingertip => m_pointer;

    protected override void _Awake()
    {
        base._Awake();
        if(m_cursorHeld3DObjectContainer != null) m_cursorHeld3DObjectContainer.gameObject.SetActive(false);
        m_stigmataID = Animator.StringToHash("Stigmata");
        m_stigmataRandomID = Animator.StringToHash("StigmataRandom");
        m_stigmataRandomTriggerID = Animator.StringToHash("StigmataRandomTrigger");
        m_stigmataPartialReleaseID = Animator.StringToHash("StigmataPartialRelease");
        InitStigmataRandomWeights();
    }

    protected override void _OnDestroy()
    {
        base._OnDestroy();
        foreach(var VARIABLE in m_debugImages)
        {
            Destroy(VARIABLE.gameObject);
        }
        m_debugImages.Clear();
    }

    public void StopCastAnimation()
    {
        if (m_castAnimOverride != null)
        {
            m_castAnimOverride.Interrupt(false);
            m_castAnimOverride = null;
        }
    }

    public void SetHandRaise(float _height)
    {
        m_handRaiseTarget = _height;
    }

    private float UpdateHandRaise()
    {
        var target = m_handRaiseTarget;
        m_handRaiseCurrent = Mathf.Lerp(m_handRaiseCurrent, target, .1f);
        return m_handRaiseCurrent;
    }

    public void SetFingertipPosition(Vector3 _pos)
    {
        m_handInstance.transform.position += _pos - m_pointer.position;
        UpdateAttach();
    }

    public Color m_glowNormal = new Color(.7f, .7f, .9f);
    public Color m_glowClickable = new Color(.8f, .7f, .9f);
    public Color m_glowUI = new Color(.9f, .7f, .7f);
    public Color m_glowUIClickable = new Color(.9f, .8f, .7f);

    public Vector3 m_cursor3DOffset = new Vector3(-.1f, 0, -1.5f);
    static Vector2 c_cursor2DHotspot = new Vector2(72.0f / 128.0f, 9.0f / 128.0f);
    private Texture2D m_lastCursor2D = null;
    private Animator m_handAnimator;
    private bool m_handAnimatorHasGrip = false;
    private Transform m_pointer;
    private UnityEngine.UI.Image m_pointerImage;
    private float m_currentPointTarget = 0;

    public bool m_handVisible = false;
    public float m_handBaseScale = .05f;
    public bool m_handConstantScreenSize = false;
    
    [Header("WorldSpace Card Dragging")]
    [SerializeField]
    private CursorHeld3DObject m_cursorHeld3DObjectContainer;
    public CursorHeld3DObject CursorHeld3DObjectContainer => m_cursorHeld3DObjectContainer;
    
    [Header("Debug")]
#if UNITY_EDITOR
    public bool m_debugShowSystemCursor = false;
    public bool m_showDebugWorldSpaceCardToScreen = false;
#else
    public bool m_debugShowSystemCursor => false;
    public bool m_showDebugWorldSpaceCardToScreen => false;
#endif

    void SetNewScheme()
    {
        m_handAnimator.SetBool("NewScheme", true);
    }

    PowerVisuals GetPowerVisuals(string _power = null)
    {
        if (_power == null)
        {
            _power = m_currentPowerType;
        }
        if (string.IsNullOrEmpty(_power)) _power = "none";
        if (m_powerVisualsLookup == null || m_powerVisualsLookup.Count != m_powerVisuals.Length)
        {
            m_powerVisualsLookup = new();
            foreach (var pv in m_powerVisuals)
                m_powerVisualsLookup[pv.m_power.ToLower()] = pv;
        }
        _power = _power.ToLower();
        return m_powerVisualsLookup.TryGetValue(_power, out var p) ? p : null;
    }

    private static float s_handPowerBrightnessMultiplier = 10;
    private static DebugConsole.Command s_brightnessMultCmd = new ("handpowerbrightness", _s => floatinv.TryParse(_s, out s_handPowerBrightnessMultiplier), "Set the Hand Power brightness multiplier", "<float,1,200>");
    private static DebugConsole.Command s_skintoneCmd = new ("skintone", _s => GameManager.Me.m_state.m_gameInfo.m_skintone = floatinv.Parse(_s), "Set the player skintone", "<float,0,1>");
    
    private Texture2D m_currentEmissiveTexture;
    private bool m_currentEmissiveEnabled;
    private PowerVisuals m_appliedPowerVisuals;
    private float m_powerVisibility = 0;
    private float m_smoothedSkintone = 1;

    int m_currentAlignmentTexture = 0, m_currentSkintoneTexture = 0;
    void ApplyAlignmentVisuals()
    {
        var alignment = m_alignmentBlend;
        var mat = HandMainMaterial;
        int alignmentTexture = 0;
        float alignmentBlend = 0;
        Color alignmentFresnelColour;
        float alignmentFresnelAmount;
        bool applyMetalOverride = false, applySmoothOverride = false;
        float metalOverride = 0, smoothOverride = 0;
        float baseNormalStrength = m_baseNormalStrength, alignmentNormalStrength = 1;
        bool baseUseSSS = m_baseUseSSS, alignmentUseSSS = true;
        if (alignment < 0)
        {
            alignmentBlend = Mathf.Clamp01(-alignment);
            alignmentTexture = -1;
            alignmentFresnelColour = m_evilAlignmentFresnelColour;
            alignmentFresnelAmount = m_evilAlignmentFresnelAmount * alignmentBlend;
            applyMetalOverride = m_evilAlignmentMetalOverrideEnabled;
            applySmoothOverride = m_evilAlignmentSmoothOverrideEnabled;
            metalOverride = m_evilAlignmentMetalOverride;
            smoothOverride = m_evilAlignmentSmoothOverride;
            alignmentNormalStrength = m_evilAlignmentNormalStrength;
            alignmentUseSSS = m_evilAlignmentUseSSS;
        }
        else
        {
            alignmentBlend = Mathf.Clamp01(alignment);
            alignmentTexture = 1;
            alignmentFresnelColour = m_goodAlignmentFresnelColour;
            alignmentFresnelAmount = m_goodAlignmentFresnelAmount * alignmentBlend;
            applyMetalOverride = m_goodAlignmentMetalOverrideEnabled;
            applySmoothOverride = m_goodAlignmentSmoothOverrideEnabled;
            metalOverride = m_goodAlignmentMetalOverride;
            smoothOverride = m_goodAlignmentSmoothOverride;
            alignmentNormalStrength = m_goodAlignmentNormalStrength;
            alignmentUseSSS = m_goodAlignmentUseSSS;
        }
        if (m_currentAlignmentTexture != alignmentTexture)
        {
            m_currentAlignmentTexture = alignmentTexture;
            mat.SetTexture("_BaseMapBlend2", alignmentTexture > 0 ? m_handBlendTextureGood : m_handBlendTextureEvil);
        }
        mat.SetFloat("_BaseMapBlend2Amount", alignmentBlend);
        mat.SetColor("_FresnelColour", alignmentFresnelColour);
        mat.SetFloat("_FresnelStrength", alignmentFresnelAmount);
        
        var useSSS = alignmentBlend > .5f ? alignmentUseSSS : baseUseSSS;
        float metalMultiplier = applyMetalOverride ? (1 - alignmentBlend) : 1;
        float metalAdder = applyMetalOverride ? metalOverride * alignmentBlend : 0;
        float smoothMultiplier = applySmoothOverride ? (1 - alignmentBlend) : 1;
        float smoothAdder = applySmoothOverride ? smoothOverride * alignmentBlend : 0;
        float normalStrength = Mathf.Lerp(baseNormalStrength, alignmentNormalStrength, alignmentBlend);
        mat.SetFloat("_MetalMultiply", metalMultiplier);
        mat.SetFloat("_MetalAdd", metalAdder);
        mat.SetFloat("_SmoothMultiply", smoothMultiplier);
        mat.SetFloat("_SmoothAdd", smoothAdder);
        mat.SetFloat("_NormalStrength", normalStrength);
        var materialID = useSSS ? 0 : 1;
        if (mat.GetFloat("_MaterialID").Nearly(materialID) == false)
        {
            mat.SetFloat("_MaterialID", materialID);
            HDMaterial.ValidateMaterial(mat);
        }

        m_smoothedSkintone = Mathf.Lerp(m_smoothedSkintone, GameManager.Me.m_state.m_gameInfo.m_skintone, .1f);
        float skintone = m_smoothedSkintone, skintoneBlend;
        int skintoneTexture;
        if (skintone < .5f)
        {
            skintoneTexture = 0;
            skintoneBlend = 1 - skintone * 2;
        }
        else
        {
            skintoneTexture = 1;
            skintoneBlend = (skintone - .5f) * 2;
        }
        if (m_currentSkintoneTexture != skintoneTexture)
        {
            m_currentSkintoneTexture = skintoneTexture;
            mat.SetTexture("_BaseMapBlend", skintoneTexture == 0 ? m_handBlendTextureDark : m_handBlendTextureLight);
        }
        mat.SetFloat("_BaseMapBlendAmount", skintoneBlend);
    }

    string AlphaProperty(Material _mat) => _mat.HasProperty("_AlphaMultiply") ? "_AlphaMultiply" : "_Alpha";
    void ApplyPowerVisualBrightness(string _power, bool _inCooldown)
    {
        ApplyAlignmentVisuals();
        var anyPowerActive = AnyPowerActive;
        var currentPowerVisuals = GetPowerVisuals(_power);
        var visuals = currentPowerVisuals;
        float powerIntensity = 0, powerVisibilityTarget = 0, powerVisibilityLerp = .15f;
        bool hideMainHand = false;
        if (visuals != null)
        {
            hideMainHand = visuals.m_hideMainHand;
            powerIntensity = visuals.PowerMaterialBase == null ? 0 : visuals.PowerMaterialBase.GetFloat(AlphaProperty(visuals.PowerMaterialBase));
            powerVisibilityTarget = anyPowerActive ? 1 : 0;
            if (visuals != m_appliedPowerVisuals)
            {
                powerVisibilityTarget = 0;
                powerVisibilityLerp = .3f;
                if (m_powerVisibility < .01f)
                {
                    m_appliedPowerVisuals = visuals;
                    HandPowerMaterial = visuals.PowerMaterial; 
                }
            }
        }
        if (_inCooldown) powerVisibilityTarget *= .1f;
        m_powerVisibility = Mathf.Lerp(m_powerVisibility, powerVisibilityTarget, powerVisibilityLerp);
        var alpha = powerIntensity * m_powerVisibility;
        HandPowerMaterial.SetFloat(AlphaProperty(HandPowerMaterial), alpha);
        var enabledPS = new HashSet<ParticleSystem>();
        var disabledPS = new HashSet<ParticleSystem>();
        foreach (var pv in m_powerVisuals)
            pv.SetParticleState(m_handInstance, pv == visuals && _inCooldown == false, pv == visuals && _inCooldown, enabledPS, disabledPS);
        foreach (var ps in enabledPS)
            if (ps.isPlaying == false)
                ps.Play();
        foreach (var ps in disabledPS)
            if (ps.isPlaying)
                ps.Stop();
        HideMainHand(hideMainHand);
    }

    private void HideMainHand(bool _hide)
    {
        HandMainMaterial.SetVector("_Jolt", _hide ? Vector3.one * 1e23f : Vector3.zero);
        HandXrayMaterial.SetFloat("_Alpha", _hide ? 0 : 1);
    }

    private static bool s_lockHandPosition = false;
    private static Vector3 s_lockedHandPosition;
    private static DebugConsole.Command s_lockhandcmd = new ("lockhand", _s => LockHandPosition(_s));
    private static void LockHandPosition(string _s)
    {
        Utility.SetOrToggle(ref s_lockHandPosition, _s);
        if (s_lockHandPosition)
            s_lockedHandPosition = Me.m_pointer.position;
    }

    Material HandMainMaterial => m_handRenderer.materials[0];
    Material HandPowerMaterial
    {
        get { return m_handRenderer.materials[1]; }
        set { var mats = m_handRenderer.materials; mats[1] = value; m_handRenderer.materials = mats; }
    }
    Material HandXrayMaterial => m_handRenderer.materials[2];
    
    void SetMaterialValues()
    {
        if (m_handRenderer == null) return;
        
        const int c_handRenderQueue = 2499;
        const int c_xrayRenderQueue = 2498;
        if (HandMainMaterial.renderQueue != c_handRenderQueue)
        {
            HandMainMaterial.renderQueue = c_handRenderQueue;
            HandXrayMaterial.renderQueue = c_xrayRenderQueue;
        }
    }

    public float m_baseNormalStrength = 1;
    public bool m_baseUseSSS = true;
    public GameObject m_handBlendEvil;
    public Texture2D m_handBlendTextureEvil;
    [ColorUsage(false, true)] public Color m_evilAlignmentLightColour;
    [ColorUsage(false, true)] public Color m_evilAlignmentFresnelColour;
    public float m_evilAlignmentFresnelAmount;
    public bool m_evilAlignmentMetalOverrideEnabled = false;
    public float m_evilAlignmentMetalOverride;
    public bool m_evilAlignmentSmoothOverrideEnabled = false;
    public float m_evilAlignmentSmoothOverride;
    public float m_evilAlignmentNormalStrength = 1;
    public bool m_evilAlignmentUseSSS = true;
    public GameObject m_handBlendGood;
    public Texture2D m_handBlendTextureGood;
    [ColorUsage(false, true)] public Color m_goodAlignmentLightColour;
    [ColorUsage(false, true)] public Color m_goodAlignmentFresnelColour;
    public float m_goodAlignmentFresnelAmount;
    public bool m_goodAlignmentMetalOverrideEnabled = false;
    public float m_goodAlignmentMetalOverride;
    public bool m_goodAlignmentSmoothOverrideEnabled = false;
    public float m_goodAlignmentSmoothOverride;
    public float m_goodAlignmentNormalStrength = 1;
    public bool m_goodAlignmentUseSSS = true;
    public Texture2D m_handBlendTextureDark;
    public Texture2D m_handBlendTextureLight;

    void GenerateBlends()
    {
        var mesh = m_handRenderer.sharedMesh;
        var evilMesh = m_handBlendEvil.GetComponent<MeshFilter>().sharedMesh;
        var goodMesh = m_handBlendGood.GetComponent<MeshFilter>().sharedMesh;
        var verts = mesh.vertices;
        var nrms = mesh.normals;
        var tans = mesh.tangents;
        var evilVerts = evilMesh.vertices;
        var evilNrms = evilMesh.normals;
        var evilTans = evilMesh.tangents;
        var goodVerts = goodMesh.vertices;
        var goodNrms = goodMesh.normals;
        var goodTans = goodMesh.tangents;
        
        var evilOffsets = new Vector3[verts.Length];
        var goodOffsets = new Vector3[verts.Length];
        var evilNrmOffsets = new Vector3[verts.Length];
        var goodNrmOffsets = new Vector3[verts.Length];
        var evilTangentOffsets = new Vector3[verts.Length];
        var goodTangentOffsets = new Vector3[verts.Length];
        for (int i = 0; i < verts.Length; ++i)
        {
            var vert = verts[i];
            var nrm = nrms[i];
            var tan = tans[i];
            var evilVert = evilVerts[i];
            var evilNrm = evilNrms[i];
            var evilTan = evilTans[i];
            var goodVert = goodVerts[i];
            var goodNrm = goodNrms[i];
            var goodTan = goodTans[i];
            goodOffsets[i] = goodVert - vert;
            goodNrmOffsets[i] = goodNrm - nrm;
            goodTangentOffsets[i] = goodTan - tan;
            evilOffsets[i] = evilVert - vert;
            evilNrmOffsets[i] = evilNrm - nrm;
            evilTangentOffsets[i] = evilTan - tan;
        }
        m_handRenderer.sharedMesh.ClearBlendShapes();
        m_handRenderer.sharedMesh.AddBlendShapeFrame("Evil", 1, evilOffsets, evilNrmOffsets, evilTangentOffsets);
        m_handRenderer.sharedMesh.AddBlendShapeFrame("Good", 1, goodOffsets, goodNrmOffsets, goodTangentOffsets);
    }

    private float m_alignmentBlend = 0;
    void UpdateBlends()
    {
        if (m_handRenderer == null) return;
        var alignment = AlignmentManager.Me.Alignment;
        m_alignmentBlend = Mathf.Lerp(m_alignmentBlend, alignment, .02f.TCLerp());
        var weightEvil = Mathf.Clamp01(-m_alignmentBlend);
        var weightGood = Mathf.Clamp01(m_alignmentBlend);
        m_handRenderer.SetBlendShapeWeight(0, weightEvil);
        m_handRenderer.SetBlendShapeWeight(1, weightGood);
    }

    Bracelet m_bracelet;
    MAPowerUI m_powerCanvas = null;
    Light m_glowLight;
    AlwaysFaceCamera m_glowPush;
    ConfigurableJoint m_holdJoint; public ConfigurableJoint HoldJoint => m_holdJoint;
    GameObject m_holder; public GameObject Holder => m_holder;
    void CheckVisibility()
    {
        if (m_powerCanvas == null)
            m_powerCanvas = GetComponentInChildren<MAPowerUI>();
        if (m_handVisible != (m_handInstance != null))
        {
            if (m_handVisible)
            {
                m_handInstance = new GameObject("Hand");
                m_handInstance.transform.SetParent(transform);
                var go = Instantiate(m_handPrefab, m_handInstance.transform);
                go.transform.localPosition = m_cursor3DOffset;
                go.SetLayerRecursively(GameManager.c_layerDistrict);
                m_handRenderer = go.transform.FindRecursiveByName<SkinnedMeshRenderer>("SK_Hand");
                SetMaterialValues();
                m_handAnimator = go.GetComponentInChildren<Animator>();
                CheckGripWidth();
                SetNewScheme();
                ShowHand(true, true);
                
                m_bracelet = go.GetComponentInChildren<Bracelet>();
                
                GenerateBlends();
                
                var canvasParent = m_handInstance.transform.FindRecursiveByName<Transform>(m_tipOfFingerBone);
                var canvasHolder = new GameObject("GlowHolder");
                canvasHolder.transform.SetParent(canvasParent);
                canvasHolder.transform.localPosition = Vector3.zero;
                m_pointer = canvasHolder.transform;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
                var canvasObj = new GameObject("Glow");
                canvasObj.transform.SetParent(canvasHolder.transform);
                var canvas = canvasObj.AddComponent<Canvas>();
                canvas.renderMode = RenderMode.WorldSpace;
                var rt = canvas.transform as RectTransform;
                rt.sizeDelta = Vector2.one * m_glowSize;
                var sprite = canvasObj.AddComponent<UnityEngine.UI.Image>();
                sprite.material = m_fingerGlowMaterial; 
                sprite.raycastTarget = false;
                m_pointerImage = sprite;
                var afc = canvasObj.AddComponent<AlwaysFaceCamera>();
                afc.m_pushForward = 1.5f;
                afc.m_freezeXZ = false;
                m_glowPush = afc;
                
                var glowLight = new GameObject("GlowLight");
                glowLight.transform.SetParent(canvasHolder.transform);
                glowLight.transform.localPosition = Vector3.zero;
                var light = glowLight.AddComponent<Light>();
                light.type = LightType.Point;
                light.color = new Color(.8f, .6f, .3f);
                light.range = 32;
                light.intensity = 1;
                light.renderingLayerMask = -1;
                light.shadows = LightShadows.Soft;
                var hd = glowLight.AddComponent<HDAdditionalLightData>();
                glowLight.AddComponent<UniversalAdditionalLightData>();
                hd.SetIntensity(1, UnityEngine.Rendering.LightUnit.Lux);
                hd.luxAtDistance = 1;
                m_glowLight = light;
#endif

                m_powerCanvasIs2D = true;
                if (m_powerCanvas.GetComponent<Canvas>().renderMode == RenderMode.WorldSpace)
                {
                    var uiHolder = new GameObject("UI");
                    uiHolder.transform.SetParent(canvasHolder.transform, false);
                    uiHolder.transform.localPosition = new Vector3(0, 0, -.75f);
                    m_powerCanvas.transform.SetParent(uiHolder.transform, false);
                    m_powerCanvas.transform.localPosition = Vector3.zero;
                    m_powerCanvas.transform.localScale = Vector3.one * .01f;
                    m_powerCanvasIs2D = false;
                }
                RefreshPowerUI();
                
                
                var pointerObj = canvasParent.gameObject;
                var physicsHolder = new GameObject("PhysicsHolder");
                physicsHolder.transform.SetParent(pointerObj.transform, false);
                physicsHolder.transform.localPosition = Vector3.zero;
                var body = physicsHolder.AddComponent<Rigidbody>();
                body.isKinematic = true;
                var joint = physicsHolder.AddComponent<ConfigurableJoint>();
                joint.connectedBody = null;
                joint.autoConfigureConnectedAnchor = false;
                joint.anchor = Vector3.zero;
                joint.connectedAnchor = Vector3.zero;
                joint.axis = Vector3.right;
                joint.secondaryAxis = Vector3.up;
                joint.xMotion = ConfigurableJointMotion.Locked;
                joint.yMotion = ConfigurableJointMotion.Locked;
                joint.zMotion = ConfigurableJointMotion.Locked;
                joint.angularXMotion = ConfigurableJointMotion.Free;
                joint.angularYMotion = ConfigurableJointMotion.Locked;
                joint.angularZMotion = ConfigurableJointMotion.Free;
                m_holdJoint = joint;
                m_holder = physicsHolder;
                
                canvasHolder.transform.localScale = Vector3.one * .01f; // set scale last so it applies to all children
            }
            else
            {
                m_powerCanvas.transform.SetParent(transform, false);
                Destroy(m_handInstance);
                m_handInstance = null;
                m_handRenderer = null;
                m_handAnimator = null;
                m_glowLight = null;
            }
        }
    }

    private static List<(Vector3, Quaternion)> s_transformBackup = new(); 
    public static void TwistHierarchy(Transform _root, float _twistAdd, float _twist = 0)
    {
        s_transformBackup.Clear();
        for (int i = 0; i < _root.childCount; ++i)
        {
            var child = _root.GetChild(i);
            s_transformBackup.Add((child.position, child.rotation));
        }
        _root.localRotation *= Quaternion.Euler(0, _twist, 0);
        for (int i = 0; i < _root.childCount; ++i)
        {
            var child = _root.GetChild(i);
            (child.position, child.rotation) = s_transformBackup[i];
            TwistHierarchy(child, _twistAdd, _twist + _twistAdd);
        }
    }
    
#if true
    public GameObject m_towelPrefab;
    public float m_towelScale = 10;
    GameObject m_whip;
    private void AddWhip()
    {
        if (m_whip != null) return;
        m_whip = Instantiate(m_towelPrefab, m_holder.transform);
        m_whip.transform.localPosition = Vector3.zero;
        m_whip.transform.localRotation = Quaternion.identity;
        m_whip.transform.localScale = Vector3.one * (m_towelScale / m_holder.transform.lossyScale.x);
        m_attachedObject = m_whip;
        m_towelTwist = 0;
        m_towelTwistDirection = 0;
        m_towelLastAngle = 0;
        m_towelPayloadPosition = m_whip.transform.position;
        m_towelPayloadVelocity = Vector3.zero;
    }

    private void RemoveWhip()
    {
        if (m_whip == null) return;
        Destroy(m_whip);
        m_whip = null;
    }

    public string m_snapTowelTrigger = "Snap";
    public void SnapWhipAt(Transform _target, float _uncoilTime, float _whipTime, Action _onHit, Action _onFinish)
    {
        var whipAnimator = m_whip.GetComponentInChildren<Animator>();
        if (whipAnimator == null) return;
        //if (_target != null)
        //{
        //var toTarget = _target.position - m_pointer.position;
        //var toTargetXZ = toTarget.GetXZNorm();
        //var toSideXZ = new Vector3(toTargetXZ.z, 0, -toTargetXZ.x);
        //m_whip.transform.LookAt(m_whip.transform.position + toSideXZ, Vector3.up);
        //}
        whipAnimator.SetTrigger(m_snapTowelTrigger);
        this.DoAfter(_uncoilTime + _whipTime, () => {
            _onHit?.Invoke();
            this.DoAfter(0.25f, _onFinish);
        });
    }

    void Spring2D(Vector2 _movement, float _maxSpeed, ref Vector2 _pendulum, ref Vector2 _lastVelocity, float _firmness, float _dampening)
    {
        var velocity = _movement / Time.deltaTime;
        if (velocity.sqrMagnitude > _maxSpeed * _maxSpeed)
            velocity = velocity.normalized * _maxSpeed;
        var counterVelocity = -_pendulum * _firmness;
        _lastVelocity = _lastVelocity * (1 - _dampening) + counterVelocity + velocity;
        _pendulum += _lastVelocity * Time.deltaTime;
    }

    public float m_towelSpringStiffness = 100;
    public float m_towelSpringDamping = .1f;
    private Vector3 m_towelPayloadPosition, m_towelPayloadVelocity;
    GameObject m_towelDragDebug, m_towelDragDebug2;
    public bool m_towelDragShowDebug = false;
    public float m_towelDragDebugDrop = -4;
    public float m_towelDragDebugScale = -10;
    private float m_towelLastAngle = 0;
    private float m_towelSmoothAngleDelta = 0;
    private float m_towelTwistDirection = 0;
    public float m_towelTwistRate = .2f;
    public float m_towelMaximumTwist = 120;
    public float m_towelTwistDecay = .005f;

    private float m_towelTwist = 0;
    void UpdateWhipAnimation()
    {
        var whipPos = m_whip.transform.position;
        var camXform = Camera.main.transform;
        var camSide = camXform.right.GetXZNorm();
        var camFwd = camXform.forward.GetXZNorm();
        var anim = m_whip.GetComponentInChildren<Animator>();

        Vector3 acceleration = (whipPos - m_towelPayloadPosition) * m_towelSpringStiffness * Time.deltaTime;
        m_towelPayloadVelocity = (m_towelPayloadVelocity + acceleration) * (1 - m_towelSpringDamping);
        m_towelPayloadPosition += m_towelPayloadVelocity * Time.deltaTime;
        var tipToPayload = m_towelPayloadPosition - whipPos;
        Vector2 dragFinal;
        dragFinal.x = Vector3.Dot(tipToPayload, camSide) * -.1f;
        dragFinal.y = Vector3.Dot(tipToPayload, camFwd) * -.1f;
        anim.SetFloat("XSpeed", dragFinal.x);
        anim.SetFloat("ZSpeed", dragFinal.y);
#if UNITY_EDITOR
        if (m_towelDragShowDebug)
        {
            if (m_towelDragDebug == null)
            {
                m_towelDragDebug = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                m_towelDragDebug.transform.localScale = Vector3.one * .5f;
                m_towelDragDebug.GetComponent<Renderer>().material.color = Color.red;
                m_towelDragDebug2 = GameObject.CreatePrimitive(PrimitiveType.Sphere);
                m_towelDragDebug2.transform.localScale = Vector3.one * .5f;
                m_towelDragDebug2.GetComponent<Renderer>().material.color = Color.blue;
            }
            m_towelDragDebug.transform.position = m_whip.transform.position + camSide * (dragFinal.x * m_towelDragDebugScale) + camFwd * (dragFinal.y * m_towelDragDebugScale) + Vector3.up * m_towelDragDebugDrop;
            m_towelDragDebug2.transform.position = m_towelPayloadPosition + Vector3.up * m_towelDragDebugDrop;
        }
        else if (m_towelDragDebug != null) 
        {
            Destroy(m_towelDragDebug);
            Destroy(m_towelDragDebug2);
            m_towelDragDebug = null;
        }
#endif

        float deltaAngle = 0;
        float angle = m_towelLastAngle;
        if (dragFinal.sqrMagnitude > .1f * .1f)
        {
            angle = Mathf.Atan2(dragFinal.y, dragFinal.x) * Mathf.Rad2Deg;
            deltaAngle = Mathf.DeltaAngle(angle, m_towelLastAngle);
            m_towelLastAngle = angle;
        }
        float towelTwistDirection = deltaAngle * deltaAngle > 2 * 2 ? Mathf.Sign(deltaAngle) : 0;
        if (towelTwistDirection * m_towelTwistDirection < 0) 
        {
            m_towelSmoothAngleDelta = 0;
            m_towelTwistDirection = 0;
        }
        m_towelTwistDirection = Mathf.Lerp(m_towelTwistDirection, towelTwistDirection, .4f);
        m_towelSmoothAngleDelta = m_towelSmoothAngleDelta * .5f + deltaAngle * m_towelTwistDirection * m_towelTwistDirection;
        
        m_towelTwist -= m_towelSmoothAngleDelta * m_towelTwistRate;
        m_towelTwist *= (1 - m_towelTwistDecay);
        m_towelTwist = Mathf.Clamp(m_towelTwist, -m_towelMaximumTwist, m_towelMaximumTwist);
        var rootBone = m_whip.transform.FindRecursiveByName<Transform>(GlobalData.c_avatarRootName);
        var firstBone = rootBone.GetChild(0);
        TwistHierarchy(firstBone, m_towelTwist);
    }
#elif true
    public GameObject m_towelPrefab;
    GameObject m_whip;
    PhysicsAnimationBlend m_whipAnimBlend;
    private void AddWhip()
    {
        if (m_whip != null) return;
        m_whip = Instantiate(m_towelPrefab);
        var body = m_whip.GetComponentInChildren<Rigidbody>();
        m_whipAnimBlend = m_whip.GetComponentInChildren<PhysicsAnimationBlend>();
        AttachToHand(body, m_whipAnimBlend);

        //m_holdJoint.angularYMotion = ConfigurableJointMotion.Locked;
        /*m_attachDragMax = 2;
        m_attachDragMin = 0;
        m_attachScale = new Vector2(0.0f, 0.0f);
        m_attachRootDistance = 0.1f;
        m_attachRootOffset = Vector3.zero;*/

        var diff = Fingertip.position - m_attachedBody.transform.position;
        m_attachedObject.transform.position += diff;
    }

    private void RemoveWhip()
    {
        if (m_whip == null) return;
        DetachFromHand(m_whip.GetComponentInChildren<Rigidbody>());
        Destroy(m_whip);
        m_whip = null;
    }

    public string m_snapTowelTrigger = "Snap";
    public float m_snapTowelTimeToHit = .5f;
    public float m_snapTowelTimeToFinish = 1;
    public void SnapWhipAt(Transform _target, float _uncoilTime, float _whipTime, Action _onHit, Action _onFinish)
    {
        var whipAnimator = m_whip.GetComponentInChildren<Animator>();
        if (whipAnimator == null) return;
        var toTarget = _target.position - m_pointer.position;
        var toTargetXZ = toTarget.GetXZNorm();
        var toSideXZ = new Vector3(toTargetXZ.z, 0, -toTargetXZ.x);
        m_attachedBody.transform.LookAt(m_attachedBody.transform.position + toSideXZ, Vector3.up);
        whipAnimator.SetTrigger(m_snapTowelTrigger);
        m_whipAnimBlend.SetPhysicsWeight(0);
        this.DoAfter(m_snapTowelTimeToHit, () => {
            _onHit?.Invoke();
            this.DoAfter(m_snapTowelTimeToFinish - m_snapTowelTimeToHit, () => {
                _onFinish?.Invoke();
                m_whipAnimBlend.SetPhysicsWeight(1);
            });
        });
    }
    void UpdateWhipAnimation() {}
#else
    private WhipController m_whip = null;
    private void AddWhip()
    {
        if (m_whip != null) return;

        var whipPrefab = Resources.Load<GameObject>("Whip");
        var whipController = WhipController.CreateWhipController(whipPrefab, m_pointer);
        m_whip = whipController;
        AttachToHand<DynamicRope>(m_whip.Whip.FirstBody, m_whip.Whip);

        m_holdJoint.angularYMotion = ConfigurableJointMotion.Locked;
        m_attachDragMax = 0.0f;
        m_attachDragMin = 0.0f;
        m_attachScale = new Vector2(1.0f, 1.0f);
        m_attachRootDistance = 0.1f;
        m_attachRootOffset = Vector3.zero;

        var diff = Fingertip.position - m_attachedBody.transform.position;
        m_attachedObject.transform.position += diff;
    }
    private void RemoveWhip()
    {
        if (m_whip != null)
        {
            DetachFromHand(m_whip.Whip.FirstBody);
            if (false) // enable to keep whip in scene for debugging
            {
                foreach (var rb in m_whip.GetComponentsInChildren<Rigidbody>())
                    rb.isKinematic = true;
                m_whip = null;
                return;
            }
            Destroy(m_whip.gameObject);
            m_whip = null;
        }
    }

    public void SnapWhipAt(Transform _target, float _uncoilTime, float _whipTime, Action _onHit, Action _onFinish)
    {
        m_whip.SnapWhipAt(_target, _uncoilTime, _whipTime, _onHit, _onFinish);
    }
    void UpdateWhipAnimation() {}
#endif

    // Vector3 GetWhipPosAlongEye(Vector3 _target)
    // {
    //     var whipLength = m_whip.Whip.m_length;
    //     var eye = Camera.main.transform.position;
    //     var eyeForward = (_target - eye).normalized;
    //     var pointer = m_pointer.position;
    //     // eye + eyeForward * k - pointer = whipLength
    //     var pointerToEye = eye - pointer;
    //     const float a = 1;
    //     var b = 2 * Vector3.Dot(pointerToEye, eyeForward);
    //     var c = pointerToEye.sqrMagnitude - whipLength * whipLength;
    //     float d = 0.0f;
    //     if (b * b > 4 * a * c)
    //         d = Mathf.Sqrt(b * b - 4 * a * c);
    //     var k = (-b + d) / (2 * a);
    //     return eye + eyeForward * k;
    // }

    public static bool IsClickableElement(GameObject ui)
    {
        if (ui == null) return false;
        if (ui.GetComponentInParent<UnityEngine.UI.Button>() != null) return true;
        if (ui.GetComponentInParent<DragBase>() != null) return true;
        return false;
    }

    public static bool IsClickableObject(GameObject _o)
    {
        if (_o.GetComponentInParent<DragBase>() != null) return true;
        if (_o.GetComponentInParent<NGMovingObject>() != null) return true;
        if (_o.GetComponent<TreeHolder>() != null) return true;
        if (_o.GetComponent<Button3D>() != null) return true;
        if (_o.GetComponentInParent<ClickToAnimate>() != null) return true;
        if (_o.GetComponent<PathHandle>() != null) return true;
        if (_o.GetComponent<MAHelperSign>() != null) return true;
        return false;
    }

    public static bool Is2DElement(GameObject ui)
    {
        if (ui == null) return false;
        if (ui.GetComponentInParent<Canvas>().renderMode == RenderMode.WorldSpace) return false;
        return true;
    }


    public static List<Transform> GetNearestObjects(bool _includeWorkers, bool _includeEnemies, bool _includeNonEnemies, bool _includeTourists, bool _includeHeroes, bool _includeAnimals, bool _includeQuestMovingObjects, int _numberToFind, float _maxDistance = 50, Vector3 _forcePosition = default, Transform _exclude = null, Vector3 _direction = default, bool _isConical = false, float _coneAngle = 0, float _coneAngleScreenSpace = 0, bool _onlyInFrustum = false, string _debugLabel = null, bool _ignoreCanBeTargetted = true)
    {
        return GetNearestObjectsAndStatics(_includeWorkers, _includeEnemies, _includeNonEnemies, _includeTourists, _includeHeroes, _includeAnimals, _includeQuestMovingObjects, false, _numberToFind, _maxDistance, _forcePosition, _exclude, _direction, _isConical, _coneAngle, _coneAngleScreenSpace, _onlyInFrustum, _debugLabel, _ignoreCanBeTargetted); 
    }

    public static List<Transform> GetNearestObjectsAndStatics(bool _includeWorkers, bool _includeEnemies, bool _includeNonEnemies, bool _includeTourists, bool _includeHeroes, bool _includeAnimals, bool _includeQuestMovingObjects, bool _includeStatics, int _numberToFind, float _maxDistance = 50, Vector3 _forcePosition = default, Transform _exclude = null, Vector3 _direction = default, bool _isConical = false, float _coneAngle = 0, float _coneAngleScreenSpace = 0, bool _onlyInFrustum = false, string _debugLabel = null, bool _ignoreCanBeTargetted = true) 
    {
        var finds = new List<Transform>();
        var distSqrds = new List<float>();
        var handPosition = _forcePosition.sqrMagnitude > .01f * .01f ? _forcePosition : Me.Fingertip.position;
        bool useDirection = _direction.sqrMagnitude > .01f * .01f;
        
        var coneCos = _coneAngle > 0 ? Mathf.Cos(_coneAngle * Mathf.Deg2Rad) : 999;
        var coneCosSqrd = coneCos * coneCos;
        var coneCosSS = _coneAngleScreenSpace > 0 ? Mathf.Cos(_coneAngleScreenSpace * Mathf.Deg2Rad) : 999;
        var coneCosSSSqrd = coneCosSS * coneCosSS;
        var cam = Camera.main;
        var handPosSS = cam.WorldToScreenPoint(handPosition); handPosSS.z = 0;
        var velFwdSS = cam.WorldToScreenPoint(handPosition + _direction.GetXZ()); velFwdSS.z = 0;
        var handToVelSS = velFwdSS - handPosSS;

        if (_debugLabel != null)
        {
            GameManager.Me.ClearGizmos(_debugLabel);
            if (_coneAngleScreenSpace > 0)
            {
                GameManager.Me.AddGizmoCone(_debugLabel, handPosition, handPosition + _direction * _maxDistance, _coneAngleScreenSpace, new Color(1, .5f, .5f, .5f));
            }
        }
        
        const bool c_debugLogs = false;

        void TestAndInsert(NGLegacyBase _chr)
        {
            if (_chr == null) return;
            var chrTransform = _chr.transform;
            if (chrTransform == _exclude) return;
            if (_chr is NGMovingObject mo && (mo.Health <= 0 || (mo.CanBeTargetted == false && _ignoreCanBeTargetted == false))) return;
            var chrGameObject = _chr.gameObject;
            if (chrGameObject.activeSelf == false) return;
            if (chrGameObject == GameManager.Me.PossessedObject) return;
            if (_onlyInFrustum && GameManager.Me.IsVisible(chrTransform.position, Vector3.one) == false) return;
            var toChr = chrTransform.position - handPosition;
            var dot = Vector3.Dot(_direction, toChr);
            var dotXZ = Vector3.Dot(_direction.GetXZ(), toChr.GetXZ());
            if (useDirection && dotXZ < 0)
            {
                if (c_debugLogs) Debug.LogError($"Considered {_chr.name} but rejected due to dot {dotXZ} {_direction} {toChr}");
                if (_debugLabel != null) GameManager.Me.AddGizmoPoint(_debugLabel, chrTransform.position, 1, new Color(1, 0, 0, .5f));
                return;
            }
            if (_coneAngleScreenSpace > 0)
            {
                var chrPosSS = cam.WorldToScreenPoint(chrTransform.position); chrPosSS.z = 0;
                var handToChrSS = chrPosSS - handPosSS;
                var dotSS = Vector3.Dot(handToChrSS, handToVelSS);
                if (dotSS * dotSS < coneCosSSSqrd * handToChrSS.sqrMagnitude * handToVelSS.sqrMagnitude)
                {
                    if (c_debugLogs) Debug.LogError($"Considered {_chr.name} but rejected due to ss cone angle - dot {dotSS / (handToChrSS.magnitude * handToVelSS.magnitude)} angle {Mathf.Acos(dotSS / (handToChrSS.magnitude * handToVelSS.magnitude)) * Mathf.Rad2Deg} : {_coneAngleScreenSpace}  hit {dotSS * dotSS >= coneCosSS * coneCosSS * handToChrSS.sqrMagnitude * handToVelSS.sqrMagnitude} -- HP: {handPosSS} CP: {chrPosSS} VP: {velFwdSS} H2C: {handToChrSS} H2V: {handToVelSS}");
                    if (_debugLabel != null) GameManager.Me.AddGizmoPoint(_debugLabel, chrTransform.position, 1, new Color(0, 0, 1, .5f));                    
                    return;
                }
            }
            if (coneCos <= 1 && dotXZ * dotXZ < coneCosSqrd * toChr.xzSqrMagnitude())
            {
                if (c_debugLogs) Debug.LogError($"Considered {_chr.name} but rejected due to cone angle - angle {Mathf.Acos(Vector3.Dot(_direction.GetXZ(), toChr.GetXZNorm())) * Mathf.Rad2Deg} - dist {toChr.magnitude} / {_maxDistance}  Dir {_direction.GetXZ()}.{toChr.GetXZNorm()} - {Vector3.Dot(_direction.GetXZ(), toChr.GetXZNorm())} - hit {Vector3.Dot(_direction.GetXZ(), toChr.GetXZNorm()) >= coneCos} [{dotXZ * dotXZ >= coneCosSqrd * toChr.xzSqrMagnitude()}]");
                if (_debugLabel != null) GameManager.Me.AddGizmoPoint(_debugLabel, chrTransform.position, 1, new Color(1, 0, 1, .5f));
                return;
            }
            if (_isConical) toChr -= _direction * (dot * .85f);
            var dSqrd = toChr.sqrMagnitude;
            if (dSqrd > _maxDistance * _maxDistance)
            {
                if (c_debugLogs) Debug.LogError($"Considered {_chr.name} but rejected due to distance {Mathf.Sqrt(dSqrd)} {_maxDistance}");
                if (_debugLabel != null) GameManager.Me.AddGizmoPoint(_debugLabel, chrTransform.position, 1, new Color(1, 1, 0, .5f));
                return;
            }
            if (_debugLabel != null) GameManager.Me.AddGizmoPoint(_debugLabel, chrTransform.position, 1, new Color(0, 1, 0, .5f));
            for (int i = 0; i < distSqrds.Count; ++i)
            {
                if (dSqrd < distSqrds[i])
                {
                    distSqrds.Insert(i, dSqrd);
                    finds.Insert(i, chrTransform);
                    if (distSqrds.Count > _numberToFind)
                    {
                        distSqrds.RemoveAt(distSqrds.Count - 1);
                        finds.RemoveAt(finds.Count - 1);
                    }
                    return;
                }
            }
            if (distSqrds.Count < _numberToFind)
            {
                distSqrds.Add(dSqrd);
                finds.Add(chrTransform);
            }
        }
        if (_includeEnemies || _includeNonEnemies)
        {
            foreach (var chr in NGManager.Me.m_MACreatureList)
            {
                if ((_includeEnemies && chr.IsEnemy) || (_includeNonEnemies && !chr.IsEnemy))
                    TestAndInsert(chr);
            }
        }
        if (_includeHeroes)
            foreach (var chr in NGManager.Me.m_MAHeroList)
                TestAndInsert(chr);
        if (_includeWorkers)
            foreach (var chr in NGManager.Me.m_MAWorkerList)
                TestAndInsert(chr);
        if (_includeTourists)
            foreach (var chr in MATouristManager.Me.m_tourists)
                TestAndInsert(chr);
        if (_includeAnimals)
            foreach (var chr in NGManager.Me.m_MAAnimalList)
                TestAndInsert(chr);
        if (_includeStatics)
        {
            foreach (var dec in GameManager.Me.m_state.m_decorations)
                if (dec.Decoration != null && dec.Decoration.m_canPickupAndThrow == false)
                    TestAndInsert(dec.Decoration);
            foreach (var ngc in NGManager.Me.m_maBuildings)
                if (ngc.Name != "Fake")
                    TestAndInsert(ngc);
            foreach (var wb in GameManager.Me.m_state.m_wildBlocks)
                TestAndInsert(wb.WildBlock);
            var trees = TerrainPopulation.Me.InstancesInRange(handPosition + _direction * (_maxDistance * .5f), _maxDistance * .5f);
            foreach (var tree in trees)
            {
                var th = tree.GetComponent<TreeHolder>();
                if (th == null) continue;
                if (th.m_treeType != "Trees") continue;
                TestAndInsert(th);
            }
        }
        if (_includeQuestMovingObjects)
            foreach (var chr in MAQuestManager.Me.RequiredMovingObjects())
                TestAndInsert(chr);
        return finds;
    }
    
    public bool IsConsuming => string.IsNullOrEmpty(m_currentPowerType) == false;

    public bool PowerActive(string _type) => m_currentPowerType == _type;
    public bool AnyPowerActive => string.IsNullOrEmpty(m_currentPowerType) == false;
    public bool CanPowerPickUp => AnyPowerActive == false || m_currentPowerType == c_powerTypeUproot;

    private Color BlendAlignmentColour(Color _base, Color _evil, Color _good)
    {
        if (m_alignmentBlend < 0)
            return Color.Lerp(_base, _evil, Mathf.Min(1, -m_alignmentBlend));
        else if (m_alignmentBlend > 0)
            return Color.Lerp(_base, _good, Mathf.Min(1, m_alignmentBlend));
        return _base;
    }
    
    public Color PowerLightColour()
    {
        var visuals = GetPowerVisuals();
        if (visuals != null)
        {
            if (AnyPowerActive == false)
            {
                return BlendAlignmentColour(visuals.m_lightColour, m_evilAlignmentLightColour, m_goodAlignmentLightColour);
            }
            return visuals.m_lightColour;
        }
        return new Color(.8f, .6f, .3f);        
    }

    public bool PowerUnlocked(string _type)
    {
        if (s_debugPowerLevel > 0) return true;
        if (MAUnlocks.Me == null) return false;
        if (_type == "Any")
        {
            return MAUnlocks.Me.m_handPowerLightning || MAUnlocks.Me.m_handPowerFlamethrower || MAUnlocks.Me.m_handPowerFireball
                   || MAUnlocks.Me.m_handPowerWaterSpout || MAUnlocks.Me.m_handPowerWaterBlob || MAUnlocks.Me.m_handPowerWhip
                   || MAUnlocks.Me.m_handPowerDig || MAUnlocks.Me.m_handPowerUproot || MAUnlocks.Me.m_handPowerCuddle;
        }
        switch (_type)
        {
            case "": return true;
            case c_powerTypeLightning: return MAUnlocks.Me.m_handPowerLightning;
            case c_powerTypeFlamethrower: return MAUnlocks.Me.m_handPowerFlamethrower;
            case c_powerTypeFireball: return MAUnlocks.Me.m_handPowerFireball;
            case c_powerTypeWaterSpout: return MAUnlocks.Me.m_handPowerWaterSpout;
            case c_powerTypeWaterBlob: return MAUnlocks.Me.m_handPowerWaterBlob;
            case c_powerTypeWhip: return MAUnlocks.Me.m_handPowerWhip;
            case c_powerTypeDig: return MAUnlocks.Me.m_handPowerDig;
            case c_powerTypeUproot: return MAUnlocks.Me.m_handPowerUproot;
            case c_powerTypeCuddle: return MAUnlocks.Me.m_handPowerCuddle;
            default: return false;
        }
    }

    public int PowerLevel(string _type)
    {
        if (s_debugPowerLevel > 0) return s_debugPowerLevel;
        if (MAUnlocks.Me == null) return 0;
        switch (_type)
        {
            case c_powerTypeLightning: return MAUnlocks.Me.m_handPowerLightning ? (int)MAUnlocks.Me.m_handPowerLightningLevel : 0;
            case c_powerTypeFlamethrower: return MAUnlocks.Me.m_handPowerFlamethrower ? (int)MAUnlocks.Me.m_handPowerFlamethrowerLevel : 0;
            case c_powerTypeFireball: return MAUnlocks.Me.m_handPowerFireball ? (int) MAUnlocks.Me.m_handPowerFireballLevel : 0;
            case c_powerTypeWaterSpout: return MAUnlocks.Me.m_handPowerWaterSpout ? (int)MAUnlocks.Me.m_handPowerWaterSpoutLevel : 0;
            case c_powerTypeWaterBlob: return MAUnlocks.Me.m_handPowerWaterBlob ? (int)MAUnlocks.Me.m_handPowerWaterBlobLevel : 0;
            case c_powerTypeWhip: return MAUnlocks.Me.m_handPowerWhip ? (int) MAUnlocks.Me.m_handPowerWhipLevel : 0;
            case c_powerTypeDig: return MAUnlocks.Me.m_handPowerDig ? (int)MAUnlocks.Me.m_handPowerDigLevel : 0;
            case c_powerTypeUproot: return MAUnlocks.Me.m_handPowerUproot ? 1 : 0;
            case c_powerTypeCuddle: return MAUnlocks.Me.m_handPowerCuddle ? 1 : 0;
            default: return 0;
        }
    }    

    public bool PowerShowing(string _type)
    {
        if (string.IsNullOrEmpty(m_currentPowerType))
        {
            // only show powers if there's someone around to attack or you have a power active
            if (GetNearestObjects(false, true, false, false, false, false, false, 1, 50).Count == 0)
            {
                return false;
            }
        }
        return PowerUnlocked(_type);
    }

    static DebugConsole.Command s_spawntreasure = new ("treasure", _s => {
        var pos = Me.Hand.position;
        MATreasurePit.Create(pos, 0, _s);
    });
    static DebugConsole.Command s_spawnchestcmd = new("chest", _s =>
    {
        var pos = Me.Hand.position;
        TreasureChestInteraction.Create(pos, 0, _s);
    });
    static DebugConsole.Command s_spawnmanaball = new("manaball", _s =>
    {
        float size = 100;
        if (string.IsNullOrEmpty(_s) == false) size = float.Parse(_s);
        MAManaBall.Create(Me.Hand.transform, size);
    });
    static DebugConsole.Command s_spawngrotesque = new("grotesque", _s =>
    {
        var pos = Me.Hand.position;
        MAGrotesque.Create(pos, 0, _s);
    });

    private static bool s_indescriminate = false; public static bool Indescriminate => s_indescriminate;
    private static DebugConsole.Command s_toggleindescriminate = new("indescriminate", _s => Utility.SetOrToggle(ref s_indescriminate, _s));
    
    private static DebugConsole.Command s_givemana = new ("mana", _s => {
        var amount = MAUnlocks.Me.MaxPowerMana;
        if (!string.IsNullOrEmpty(_s) && floatinv.TryParse(_s, out var f))
            amount = f;
        GameManager.Me.m_state.m_powerMana = amount;
    });
#if UNITY_EDITOR || DEVELOPMENT_BUILD
    private static int s_debugPowerLevel = 0;
#else
    private static int s_debugPowerLevel => 0;
#endif
    public static void DemoUnlockHandPowers(string _s)
    {
        var bits = _s.Split(':');
        int level = bits.Length > 1 ? int.Parse(bits[1]) : 1;
#if UNITY_EDITOR || DEVELOPMENT_BUILD
        s_debugPowerLevel = level;
#endif
        KeyboardShortcutManager.Me.Refresh();
    }
    private static DebugConsole.Command s_unlockpower = new ("unlockpower", _s => DemoUnlockHandPowers(_s));
    
    public const string c_powerTypeLightning = "lightning";
    public const string c_powerTypeFlamethrower = "flamethrower";
    public const string c_powerTypeFireball = "fireball";
    public const string c_powerTypeWaterSpout = "waterspout";
    public const string c_powerTypeWaterBlob = "waterblob";
    public const string c_powerTypeWhip = "whip";
    public const string c_powerTypeDig = "dig";
    public const string c_powerTypeUproot = "uproot";
    public const string c_powerTypeCuddle = "cuddle";

    public static string[] c_allPowers =
    {
        "",
        c_powerTypeLightning,
        c_powerTypeFlamethrower,
        c_powerTypeFireball,
        c_powerTypeWaterSpout,
        c_powerTypeWaterBlob,
        c_powerTypeWhip,
        c_powerTypeDig,
        c_powerTypeUproot,
        c_powerTypeCuddle,
    };
    
    public static int PowerIndex(string _power) => Array.IndexOf(c_allPowers, _power);
    public Sprite PowerIcon(string _power)
    {
        var index = PowerIndex(_power);
        if (index <= 0) return null;
        return m_handPowerSprites[index];
    }

    public string m_currentPowerType = "";

    public void ActivatePower(string _type)
    {
        if (string.IsNullOrEmpty(_type) || PowerUnlocked(_type))
        {
            CancelActivePowerEffect();
            var previous = m_currentPowerType;
            m_currentPowerType = _type;
            KeyboardShortcutManager.Me.Refresh();
            RefreshPowerUI();
            RunPowerSpecificActivate(previous);
        }
    }

    public void ActivateNextPower(int _direction)
    {
        int index = System.Array.IndexOf(c_allPowers, m_currentPowerType);
        if (index < 0) index = 0;
        while (true)
        {
            index += _direction;
            if (index < 0) index = c_allPowers.Length - 1;
            if (index >= c_allPowers.Length) index = 0;
            if (index == 0 || PowerUnlocked(c_allPowers[index]))
            {
                ActivatePower(c_allPowers[index]);
                return;
            }
        }
    }

    void RunPowerSpecificActivate(string _previous)
    {
        if (m_currentPowerType == c_powerTypeUproot)
            TreeHolder.SetTreePickupMode(true);
        else if (_previous == c_powerTypeUproot)
            TreeHolder.SetTreePickupMode(false);
        if (m_currentPowerType == c_powerTypeWhip)
            AddWhip();
        else if (_previous == c_powerTypeWhip)
            RemoveWhip();
    }

    bool RunPowerSpecificUpdate(ref Ray _ray, ref Vector3 _pos, ref Vector3 _nrm)
    {
        if (m_currentPowerType == c_powerTypeWhip) return RunPowerSpecificUpdate_Whip(ref _ray, ref _pos, ref _nrm);
        if (m_currentPowerType == c_powerTypeCuddle) return RunPowerSpecificUpdate_Cuddle(ref _ray, ref _pos, ref _nrm);
        return false;
    }
    public float m_towelDragPlaneDistance = 45;
    public float m_towelDragPlaneTilt = 0;
    public float m_towelDragMinimumHeight = 12;
    bool RunPowerSpecificUpdate_Whip(ref Ray _ray, ref Vector3 _pos, ref Vector3 _nrm)
    {
        var cam = Camera.main;
        _ray = cam.ScreenPointToRay(Utility.mousePosition);
        var planeOrigin = cam.transform.position + cam.transform.forward * m_towelDragPlaneDistance;
        var planeNormal = cam.transform.forward + cam.transform.up * m_towelDragPlaneTilt;
        var plane = new Plane(planeNormal, planeOrigin);
        plane.Raycast(_ray, out var distance);
        _pos = _ray.GetPoint(distance).AboveGround(m_towelDragMinimumHeight);
        _nrm = Vector3.up;
        m_currentPointTarget = 1;
        UpdateWhipAnimation();
        return true;
    }

    private Vector3 m_cuddleDropPoint;
    private Transform m_cuddleDropTransform;
    private float m_cuddleDropPointLevel = 0;
    private System.Action m_cuddleDropPointCb;
    public void SetCuddleDropPoint(Vector3 _point, System.Action _cb)
    {
        m_cuddleDropPoint = _point;
        m_cuddleDropTransform = null;
        m_cuddleDropPointLevel = .001f;
        m_cuddleDropPointCb = _cb;
    }

    public void SetCuddleDropPoint(Transform _point, System.Action _cb)
    {
        m_cuddleDropTransform = _point;
        m_cuddleDropPointLevel = .001f;
        m_cuddleDropPointCb = _cb;
    }
    
    const float c_cuddleDragPlaneDistance = 30;
    bool RunPowerSpecificUpdate_Cuddle(ref Ray _ray, ref Vector3 _pos, ref Vector3 _nrm)
    {
        var cam = Camera.main;
        _ray = cam.ScreenPointToRay(Utility.mousePosition);
        var planeOrigin = cam.transform.position + cam.transform.forward * c_cuddleDragPlaneDistance;
        var planeNormal = cam.transform.forward;
        var plane = new Plane(planeNormal, planeOrigin);
        plane.Raycast(_ray, out var distance);
        _pos = _ray.GetPoint(distance).AboveGround(m_towelDragMinimumHeight);
        
        if (m_cuddleDropPointLevel > 0)
        {
            var previousLevel = m_cuddleDropPointLevel; 
            m_cuddleDropPointLevel += Time.deltaTime;
            var t = Mathf.Min(m_cuddleDropPointLevel, 2 - m_cuddleDropPointLevel);
            var lerpTo = m_cuddleDropTransform?.position ?? m_cuddleDropPoint;
            _pos = Vector3.Lerp(_pos, lerpTo, t);
            if (m_cuddleDropPointLevel >= 1 && previousLevel < 1)
                m_cuddleDropPointCb();
            else if (m_cuddleDropPointLevel >= 2)
                m_cuddleDropPointLevel = 0;
        }
        _nrm = Vector3.up;
        m_currentPointTarget = 1;
        return true;
    }

    float m_forcePowerUIDisplayTime = 0;
    public void ForcePowerUIDisplay()
    {
        m_forcePowerUIDisplayTime = Time.time;
    }

    private float m_overbrightTarget = 0, m_overbrightCurrent = 0;
    public void SetOverbright()
    {
        m_overbrightTarget = 1;
    }

    private void UpdateOverbright()
    {
        float was = m_overbrightCurrent;
        m_overbrightCurrent = Mathf.Lerp(m_overbrightCurrent, m_overbrightTarget, .3f.TCLerp());
        m_overbrightTarget = Mathf.Lerp(m_overbrightTarget, 0, .15f.TCLerp());
        if (was.Nearly(m_overbrightCurrent, .0001f) == false)
            PossessSequenceSuperBrightHand(m_overbrightCurrent);
    }

    void RefreshPowerUI()
    {
        RefreshPowerUIVisibility();
        m_powerCanvas.SetPowerLabel(m_currentPowerType);
    }

    void RefreshPowerUIVisibility()
    {
        float timeSinceForceUIDisplay = Time.time - m_forcePowerUIDisplayTime;
        bool isPowerActive = string.IsNullOrEmpty(m_currentPowerType) == false;
        bool isPossessing = GameManager.Me.IsPossessing;
        m_powerCanvas.Show((isPowerActive || timeSinceForceUIDisplay < 1) && isPossessing == false && m_unlockPowerSequenceLock < .001f);
    }

    void CheckPowerSwitch(KeyCode _key, string _type)
    {
        if (Input.GetKeyDown(_key))
            ActivatePower(_type);
    }
    
    static bool s_superNashwanMode = false;
    static DebugConsole.Command s_nashwan = new("nashwan", _s => Utility.SetOrToggle(ref s_superNashwanMode, _s));
    
    public void SetHandState(string _state, bool _value)
    {
        if (m_handAnimator == null) return;
        m_handAnimator.SetBool(_state, _value);
    }

    [Serializable]
    public class HandGesture
    {
        public string m_name;
        public int m_gestureId;
        public string[] m_characterResult;
        public string[] m_heroResult;

        public string Pick(bool _hero) => (_hero ? m_heroResult : m_characterResult).PickRandom();
    }
    public HandGesture[] m_gestures = {
        new () {m_name = "Hang Loose", m_gestureId = 1, m_characterResult = new[] {"WorkerWave"}, m_heroResult = new[] {"WorkerWave"}},
        new () {m_name = "Loser", m_gestureId = 2, m_characterResult = new[] {"WorkerWave"}, m_heroResult = new[] {"WorkerWave"}},
        new () {m_name = "Thumbs Up", m_gestureId = 3, m_characterResult = new[] {"WorkerWave"}, m_heroResult = new[] {"WorkerWave"}},
        new () {m_name = "Thumbs Down", m_gestureId = 4, m_characterResult = new[] {"WorkerWave"}, m_heroResult = new[] {"WorkerWave"}},
        new () {m_name = "Peace", m_gestureId = 5, m_characterResult = new[] {"WorkerWave"}, m_heroResult = new[] {"WorkerWave"}},
        new () {m_name = "F Off", m_gestureId = 6, m_characterResult = new[] {"WorkerWave"}, m_heroResult = new[] {"WorkerWave"}},
        new () {m_name = "A Hole", m_gestureId = 7, m_characterResult = new[] {"WorkerWave"}, m_heroResult = new[] {"WorkerWave"}},
        new () {m_name = "Wanker", m_gestureId = 8, m_characterResult = new[] {"WorkerWave"}, m_heroResult = new[] {"WorkerWave"}},
        new () {m_name = "Fingers Crossed", m_gestureId = 9, m_characterResult = new[] {"WorkerWave"}, m_heroResult = new[] {"WorkerWave"}},
    };

    private static DebugConsole.Command s_playhandgesturecmd = new ("handgesture", _s => Me.PlayHandGesture(_s));
    public void PlayHandGesture(string _name)
    {
        for (int i = 0; i < m_gestures.Length; ++i)
        {
            if (m_gestures[i].m_name == _name)
            {
                StartGesture(i);
                return;
            }
        }
    }

    public bool IsGestureUnlocked(int _n)
    {
        switch (_n)
        {
            case 0: return MAUnlocks.Me.m_handGestureHangLoose;
            case 1: return MAUnlocks.Me.m_handGestureLoser;
            case 2: return MAUnlocks.Me.m_handGestureThumbsUp;
            case 3: return MAUnlocks.Me.m_handGestureThumbsDown;
            case 4: return MAUnlocks.Me.m_handGesturePeace;
            case 5: return MAUnlocks.Me.m_handGestureFOff;
            case 6: return MAUnlocks.Me.m_handGestureAHole;
            case 7: return MAUnlocks.Me.m_handGestureWanker;
            case 8: return MAUnlocks.Me.m_handGestureCrossFingers;
            default: return false;
        }
    }

    private int m_currentGesture = 0;
    private bool m_currentGestureSeen = false;
    private float m_currentGestureTime = 0;
    private List<Transform> m_currentGestureResponders;

    private void StartGesture(int _index)
    {
        var gesture = m_gestures[_index];
        m_currentGesture = _index + 1;
        m_currentGestureSeen = false;
        m_currentGestureTime = 0;
        m_handAnimator.SetInteger("Gesture", m_currentGesture);
        m_handAnimator.SetTrigger("GestureTrigger");
        m_currentGestureResponders = GetNearestObjects(true, false, false, true, false, false, true, 10, 50);
        for (int i = 0; i < m_currentGestureResponders.Count; ++i)
        {
            var chr = m_currentGestureResponders[i].GetComponent<NGMovingObject>();
            var tracker = m_currentGestureResponders[i].GetComponentInChildren<HeadTracker>();
            Utility.After(UnityEngine.Random.Range(.1f, .8f), () => {
                string gesturePauseLabel = $"Gesture{Time.frameCount}";
                chr.m_nav.PushPause(gesturePauseLabel, _freeze: true);
                var faceTransform = Camera.main.transform;
                tracker.Override(faceTransform);
                var toFace = (faceTransform.position - chr.m_transform.position).GetXZNorm();
                StartCoroutine(Co_TurnToFaceThenPlay(chr.gameObject, toFace, gesture.Pick(chr is MAHeroBase), (b) => {
                    chr.m_nav.PopPause(gesturePauseLabel);
                    tracker.Override(null);
                }));
            });
        }
    }

    public static IEnumerator Co_TurnToFaceThenPlay(GameObject _who, Vector3 _facing, string _anim, Action<bool> _cb)
    {
        var anim = _who.GetComponentInChildren<Animator>();
        var whoTransform = _who.transform;
        var dirQuat = Quaternion.LookRotation(_facing, Vector3.up);
        while (true)
        {
            if (Vector3.Dot(whoTransform.forward, _facing) > .99f)
            {
                whoTransform.rotation = dirQuat;
                break;
            }
            whoTransform.rotation = Quaternion.Slerp(whoTransform.rotation, dirQuat, .2f);
            yield return null;
        }
        AnimationOverride.PlayClip(anim.gameObject, _anim, _cb);
    }
    
    private string m_buildingSpecificAnimation = null;
    public void StartBuildingSpecificAnimation(string _name)
    {
        if (string.IsNullOrEmpty(_name)) return;
        EndBuildingSpecificAnimation();
        m_buildingSpecificAnimation = _name;
        SetBuildingSpecificAnimationSpeed(0);
        m_handAnimator.SetBool(m_buildingSpecificAnimation, true);
    }

    public void SetBuildingSpecificAnimationSpeed(float _speed)
    {
        if (string.IsNullOrEmpty(m_buildingSpecificAnimation)) return;
        m_handAnimator.SetFloat("AnimSpeed", _speed);
    }

    public void EndBuildingSpecificAnimation()
    {
        if (string.IsNullOrEmpty(m_buildingSpecificAnimation)) return;
        m_handAnimator.SetBool(m_buildingSpecificAnimation, false);
        m_buildingSpecificAnimation = null;
    }

    public enum ERadialType { None, Gesture, HandPower };
    private ERadialType m_radialMenuActive = ERadialType.None; public bool RadialMenuActive => m_radialMenuActive != ERadialType.None;

    public static Sprite ResRadialSprite(string _name) => Resources.Load<Sprite>($"_Art/Sprites/RadialMenu/Gesture_{_name}");
    private Sprite[] m_gestureSprites;
    private Sprite[] m_handPowerSprites;

    private void LoadSpriteArrays()
    {
        if (m_gestureSprites != null) return;
        m_gestureSprites = new[] {
            ResRadialSprite("G_2_Horns"),
            ResRadialSprite("G_3_L"),
            ResRadialSprite("G_4_Thumbup"),
            ResRadialSprite("G_5_Thumbdown"),
            ResRadialSprite("G_6_Victory"),
            ResRadialSprite("G_7_Birdie"),
            ResRadialSprite("G_1_OK"),
            ResRadialSprite("G_12_Wanker"),
            ResRadialSprite("G_11_Crossed"),
        };
        m_handPowerSprites = new[] {
            ResRadialSprite("H_1_Interact"),
            ResRadialSprite("H_5_Lightning"),
            ResRadialSprite("H_3_Flame"),
            ResRadialSprite("H_4_Fireball"),
            ResRadialSprite("H_6_Waterspout"),
            ResRadialSprite("H_7_WaterBlob"),
            ResRadialSprite("H_9_Dicipline"),
            ResRadialSprite("H_2_Dig"),
            ResRadialSprite("H_11_Uproot"),
            ResRadialSprite("H_1_Interact"),
        };
    }

    void UpdateRadialMenus()
    {
        LoadSpriteArrays();
        if (GameManager.Me.IsTownMode == false || GameManager.Me.IsInTitleScreen()) return;
        if ((GameManager.InputConsuming && GameManager.Me.IsPossessing == false) || GameManager.KeyboardConsuming) return;
        if (ContextMenuManager.Me.ContextMenuOpen) return;
        if (IntroControl.Me.InIntroPossess) return;
        if (IsInPowerUnlockSequence) return;
        if (RadialMenuActive == false)
        {
            var handPowerKey = EKeyboardFunction.HandPowerMenu.Key();
            var gestureKey = EKeyboardFunction.GestureMenu.Key();
            if (Input.GetKeyDown(gestureKey) && GameManager.Me.IsPossessing == false)
            {
                m_radialMenuActive = ERadialType.Gesture;
                RadialPopup.Create(Input.mousePosition, m_gestureSprites, false, () => Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(gestureKey), (i) =>
                {
                    if (i == -1)
                    {
                        m_radialMenuActive = ERadialType.None;
                    }
                    else
                    {
                        QueueGesture(i);
                    }
                }, (n) => IsGestureUnlocked(n) ? RadialPopup.ELockState.Unlocked : RadialPopup.ELockState.Locked,
                (n) => KeyCode.Alpha0 + ((1 + n) % 10)
                    );
            }

            if (Input.GetKeyDown(handPowerKey) && GameManager.Me.IsPossessing == false)
            {
                m_radialMenuActive = ERadialType.HandPower;
                RadialPopup.Create(Input.mousePosition, m_handPowerSprites, true, () => Input.GetKeyDown(KeyCode.Return), (i) =>
                {
                    if (i == -1)
                    {
                        m_radialMenuActive = ERadialType.None;
                    }
                    else
                    {
                        AudioClipManager.Me.PlaySound(i == 0 ? "PlaySound_HandPowers_DEACTIVATE" : "PlaySound_HandPowers_ACTIVATE", GameManager.Me.gameObject);
                        ActivatePower(c_allPowers[i]);
                    }
                }, (n) => m_currentPowerType == c_allPowers[n] ? RadialPopup.ELockState.Active : (PowerUnlocked(c_allPowers[n]) ? RadialPopup.ELockState.Unlocked : RadialPopup.ELockState.Locked),
                    (n) => KeyCode.Alpha0 + ((1 + n) % 10), _shortcutKey: handPowerKey, _shortcutCb: () => TogglePower());
            }
        }
    }

    private List<int> m_gestureQueue = new();
    private void QueueGesture(int _n)
    {
        m_gestureQueue.Add(_n);
        var iconPrefab = UIManager.Me.m_gestureQueue.GetChild(0);
        var icon = Instantiate(iconPrefab, UIManager.Me.m_gestureQueue);
        var image = icon.GetComponent<UnityEngine.UI.Image>();
        image.sprite = m_gestureSprites[_n];
        icon.gameObject.SetActive(true);
    }

    private bool CheckGestureQueue()
    {
        if (RadialMenuActive || m_gestureQueue.Count == 0) return false;
        var n = m_gestureQueue[0];
        m_gestureQueue.RemoveAt(0);
        Destroy(UIManager.Me.m_gestureQueue.GetChild(1).gameObject);
        StartGesture(n);
        return true;
    }

    private void UpdateGestures()
    {
        UpdateRadialMenus();
        
        if (m_currentGesture > 0)
        {
            var gesturePath = $"Gestures.Gesture{m_currentGesture}";
            bool isInGesture = m_handAnimator.GetCurrentAnimatorStateInfo(0).IsName(gesturePath);
            if (m_currentGestureSeen == false && isInGesture) m_currentGestureSeen = true;
            else if (m_currentGestureSeen && isInGesture == false) m_currentGesture = 0;
            return;
        }
        
        if (CheckGestureQueue()) return;
        
        bool control = Utility.GetKey(KeyCode.LeftControl) || Utility.GetKey(KeyCode.RightControl);
        bool alt = Utility.GetKey(KeyCode.LeftAlt) || Utility.GetKey(KeyCode.RightAlt);
        bool shift = Utility.GetKey(KeyCode.LeftShift) || Utility.GetKey(KeyCode.RightShift);
 
        //     if (control == false && shift == false && alt)
        if (control == true && shift == false && alt) //Marcos Changed this as not good key combo for PC!
        {
            for (int i = 0; i < 9; ++i)
                if (Utility.GetKeyDown(KeyCode.Alpha1 + i))
                    StartGesture(i);
        }
    }

    [BurstCompile]
    public struct GetDistrictEdgeAroundPointJob : IJob
    {
        [ReadOnly] private NativeArray<byte> m_districtData;
        [ReadOnly] private int m_x, m_z, m_radiusCells;
        private NativeArray<UInt32> m_bm;
        private NativeArray<UInt32> m_bmDiff;
        private NativeList<int> m_choices;
        public GetDistrictEdgeAroundPointJob(Vector3 center, float radius)
        {
            m_districtData = DistrictManager.Me.m_textureData;
            m_x = GlobalData.TerrainX(center.x);
            m_z = GlobalData.TerrainZ(center.z);
            var div = GlobalData.c_heightmapW / GlobalData.c_districtMapRes;
            m_x /= div;
            m_z /= div;
            m_radiusCells = (int) (radius * GlobalData.c_terrainXZScale / div);
            if (m_radiusCells > 15) m_radiusCells = 15;
            m_bm = new NativeArray<UInt32>(32, Allocator.TempJob);
            m_bmDiff = new NativeArray<UInt32>(32, Allocator.TempJob);
            m_choices = new NativeList<int>(Allocator.TempJob);
        }

        public List<int> GetResult(out int _x, out int _z)
        {
            _x = m_x; _z = m_z;
            var result = new List<int>(m_choices.Length);
            for (int i = 0; i < m_choices.Length; ++i)
                result.Add(m_choices[i]);
            m_bm.Dispose();
            m_bmDiff.Dispose();
            m_choices.Dispose();
            return result;
        }

        public void Execute()
        {
            for (int dz = -m_radiusCells; dz <= m_radiusCells; ++dz)
            {
                var zz = m_z + dz;
                var inZ = zz >= 0 && zz < GlobalData.c_districtMapRes;
                UInt32 bmZ = 0;
                for (int dx = -m_radiusCells; dx <= m_radiusCells; ++dx)
                {
                    var xx = m_x + dx;
                    bool inDistrict = false;
                    if (inZ && xx >= 0 && xx < GlobalData.c_districtMapRes)
                        inDistrict = m_districtData[(xx + zz * GlobalData.c_districtMapRes) * 2 + 1] >= 128;
                    bmZ |= (UInt32) (inDistrict ? 1 : 0) << (dx + 16);
                }
                m_bm[dz + 16] = bmZ;
            }
            if (m_radiusCells > 14) m_radiusCells = 14;
            for (int dz = -m_radiusCells; dz <= m_radiusCells; ++dz)
                m_bmDiff[dz + 16] = (m_bm[dz + 16] ^ m_bm[dz + 16 + 1]) | (m_bm[dz + 16] ^ m_bm[dz + 16 - 1]) | (m_bm[dz + 16] ^ (m_bm[dz + 16] >> 1)) | (m_bm[dz + 16] ^ (m_bm[dz + 16] << 1));

            --m_radiusCells;
            for (int dz = -m_radiusCells; dz <= m_radiusCells; ++dz)
            {
                var bmZ = m_bmDiff[dz + 16];
                for (int dx = -m_radiusCells; dx <= m_radiusCells; ++dx)
                {
                    if (dx * dx + dz * dz > m_radiusCells * m_radiusCells) continue;
                    var isChanged = (bmZ & (1 << (dx + 16))) != 0;
                    if (isChanged) m_choices.Add((dx + 16) + (dz + 16) * 32);
                }
            }
        }
    }

    private Transform m_handCenter;
    private ParticleSystem m_handVFX;
    private float m_handVFXNext = 0;
    private float m_handVFXTimeBetweenParticles = 0.02f;
    private void UpdateVFX()
    {
        if (m_handVFX == null)
            m_handVFX = m_handInstance.GetComponentInChildren<ParticleSystem>();
        if (m_handVFX == null) return;
        
        if (HandShowing == false) return;
        
        m_handVFXNext -= Time.deltaTime;
        if (m_handVFXNext >= 0) return;
        
        return; // removing this for now - if replaced disable when unlocking power

        if (m_handVFX.emission.rateOverTime.constant > 0)
        {
            m_handVFXTimeBetweenParticles = 1 / m_handVFX.emission.rateOverTime.constant;
            var emission = m_handVFX.emission;
            emission.rateOverTime = 0;
        }
        int count = Mathf.CeilToInt(-m_handVFXNext / m_handVFXTimeBetweenParticles);
        m_handVFXNext += m_handVFXTimeBetweenParticles * count;

        const float c_handBaseSize = .3f;

        if (m_handCenter == null) m_handCenter = m_handInstance.transform.FindRecursiveByName<Transform>("Bone.Middle.Base");
        var job = new GetDistrictEdgeAroundPointJob(m_handCenter.position, c_handBaseSize * m_handInstance.transform.lossyScale.x);
        job.Schedule().Complete();
        var choices = job.GetResult(out var x, out var z);
        
        if (choices.Count == 0) return;

        var div = GlobalData.c_heightmapW / GlobalData.c_districtMapRes;
        var divHalf = div >> 1;

        m_handVFX.Emit(count);
        var particles = new ParticleSystem.Particle[m_handVFX.particleCount];
        m_handVFX.GetParticles(particles);
        for (int i = 0; i < count; ++i)
        {
            var choice = choices.PickRandom();
            int cdx = (choice & 31) - 16, cdz = (choice >> 5) - 16;
            var cpos = new Vector3((float)
                ((x + cdx) * div + divHalf) / GlobalData.c_terrainXZScale * TerrainBlock.GlobalScale / GlobalData.c_navScale + GlobalData.c_terrainOriginX,
                m_handCenter.position.y + UnityEngine.Random.Range(.1f, .3f),
                ((z + cdz) * div + divHalf) / GlobalData.c_terrainXZScale * TerrainBlock.GlobalScale / GlobalData.c_navScale + GlobalData.c_terrainOriginZ);
            particles[particles.Length - 1 - i].position = cpos;
        }
        m_handVFX.SetParticles(particles);
    }

    private void UpdatePowers()
    {
        if (GameManager.Me.IsTownMode == false && string.IsNullOrEmpty(m_currentPowerType) == false)
            ActivatePower("");
            
        RefreshPowerUIVisibility();
        if (GameManager.KeyboardConsuming == false && GameManager.InputConsuming == false && false)
        {
            CheckPowerSwitch(KeyCode.F1, "");
            CheckPowerSwitch(KeyCode.F2, c_powerTypeLightning);
            CheckPowerSwitch(KeyCode.F3, c_powerTypeFlamethrower);
            CheckPowerSwitch(KeyCode.F4, c_powerTypeFireball);
            CheckPowerSwitch(KeyCode.F5, c_powerTypeWaterSpout);
            CheckPowerSwitch(KeyCode.F6, c_powerTypeWaterBlob);
            CheckPowerSwitch(KeyCode.F8, c_powerTypeWhip);
            CheckPowerSwitch(KeyCode.F9, c_powerTypeDig);
            if (PowerUnlocked("Any"))
            {
                if (Input.GetKeyDown(KeyCode.R))
                    ActivateNextPower(-1);
                if (Input.GetKeyDown(KeyCode.T))
                    ActivateNextPower(1);
            }
        }
        var mouseButton = 0;//GameManager.Me.IsPossessing ? 1 : 0;
        if (Input.GetMouseButtonDown(mouseButton) && string.IsNullOrEmpty(m_currentPowerType) == false && Utility.IsMouseOverUI() == false)
        {
            if (s_superNashwanMode)
            {
                foreach (var power in c_allPowers)
                    TriggerPower(power, mouseButton, 8);
            }
            else
                TriggerPower(m_currentPowerType, mouseButton);
        }

        SetPowerAnimation(m_currentPowerType);
    }

    private MAHandPowerInfo m_fallbackPowerInfo = new() {
        m_description = "Missing Power Info Fallback",
        m_level = 1,
        m_name = "Fallback",
        m_title = "Fallback",
        m_aoeRadius = 10,
        m_attackLength = 20,
        m_attackWidth = 5,
        m_baseDamage = 0,
        m_manaCost = 0,
        m_minDamage = 0,
        m_baseCooldownTime = 1,
        m_bonusAttackChance = 0,
        m_numOfHits = 1,
        m_tickSpeed = 0,
    };
    Dictionary<string, float> m_powerCooldowns = new();

    string m_lastActivePower = "";
    void TogglePower()
    {
        if (AnyPowerActive)
        {
            m_lastActivePower = m_currentPowerType;
            ActivatePower("");
        }
        else
        {
            if (string.IsNullOrEmpty(m_lastActivePower))
                ActivateNextPower(1);
            else
                ActivatePower(m_lastActivePower);
        }
    }

    bool PowerTargetSearchConical()
    {
        if (m_currentPowerType == c_powerTypeWhip) return true;
        return false;
    }

    bool PowerAffectsFallback() // this power will fall back on affecting -every- type if it fails on its primary target
    {
        if (m_currentPowerType == c_powerTypeLightning) return true;
        return false;
    }

    bool PowerAffectsWorkers()
    {
        if (m_currentPowerType == c_powerTypeWhip) return true;
        if (m_currentPowerType == c_powerTypeCuddle) return true;
        return s_indescriminate;
    }

    bool PowerAffectsTourists()
    {
        if (m_currentPowerType == c_powerTypeWhip) return true;
        if (m_currentPowerType == c_powerTypeCuddle) return false;
        return s_indescriminate;
    }

    bool PowerAffectsHeroes()
    {
        if (m_currentPowerType == c_powerTypeWhip) return true;
        if (m_currentPowerType == c_powerTypeCuddle) return true;
        return s_indescriminate;
    }

    bool PowerAffectsEnemies()
    {
        if (m_currentPowerType != c_powerTypeWhip) return true;
        if (m_currentPowerType == c_powerTypeCuddle) return false;
        return s_indescriminate;
    }

    bool PowerAffectsNonEnemies()
    {
        if (m_currentPowerType == c_powerTypeWhip) return false;
        if (m_currentPowerType == c_powerTypeCuddle) return false;
        return s_indescriminate;
    }
    
    public void RegisterPowerTrigger(string _type)
    {
        m_powerCooldowns[_type] = Time.time;
    }

    private float m_lastPowerCancelTime; public float LastPowerCancelTime => m_lastPowerCancelTime;
    private int m_lastPowerCancelFrame; public int LastPowerCancelFrame => m_lastPowerCancelFrame;
    private void CancelActivePowerEffect()
    {
        m_lastPowerCancelTime = Time.unscaledTime;
        m_lastPowerCancelFrame = Time.frameCount;
    }

    float m_handPowerVisualBlend = 0;
    void UpdateCooldownBrightness()
    {
        if (GameManager.Me.LoadComplete == false) return;
        var anyPowerActive = AnyPowerActive;
        m_handPowerVisualBlend = Mathf.Lerp(m_handPowerVisualBlend, anyPowerActive ? 1 : 0, .1f);
        bool inCooldown = false;
        if (anyPowerActive)
        {
            var info = MAHandPowerInfo.GetInfoByLevel(m_currentPowerType, PowerLevel(m_currentPowerType));
            if (info == null) info = m_fallbackPowerInfo;
            if (info == null) return;
            inCooldown = m_powerCooldowns.TryGetValue(m_currentPowerType, out var lastTime) && Time.time - lastTime <= info.m_baseCooldownTime;
            if (m_handPowerActive || m_handPowerWaitingForActive) inCooldown = false;
            var state = GameManager.Me.m_state;
            if (state.m_powerMana < info.m_manaCost * 1f) inCooldown = true;
        }
        ApplyPowerVisualBrightness(m_currentPowerType, inCooldown);
    }

    bool ThreatsInRadius(Vector3 _pos, float _radius)
    {
        return GetNearestObjects(false, true, false, false, false, false, false, 1, _radius, _pos).Count != 0;
    }

    void ShowHandPowerOutOfDistrictWarning()
    {
        // TODO - show a warning that the player is trying to use a hand power outside of unlocked districts
    }

    void TriggerPower(string _power, int _mouseButton, int _levelOverride = -1)
    {
        if (IsInPowerUnlockSequence) return;
        if (string.IsNullOrEmpty(_power)) return;
        if (NGManager.Me.m_canUseHandPowersOutOfDistrict == false && DistrictManager.Me.IsWithinDistrictBounds(m_pointer.position) == false)
        {
            ShowHandPowerOutOfDistrictWarning();
            return;
        }
        int level = PowerLevel(_power);
        if (_levelOverride >= 0) level = _levelOverride;
        if (level > 0)
        {
            var maxDistance = 30f + level * 10f;
            var source = m_pointer;
            var sourcePos = source.position;
            var direction = Camera.main.transform.forward.GetXZNorm();
            var isConical = PowerTargetSearchConical();
            var possessed = GameManager.Me.PossessedObject;
            if (possessed != null)
            {
                source = possessed.GetComponentInChildren<AnimationHandler>()?.m_leftHandAttach ?? source;
                sourcePos = source.position;
                direction = possessed.transform.forward;
            }
            else
            {
                GameManager.Me.RaycastAtPoint(Utility.mousePosition, out var hit, GameManager.c_layerTerrainBit);
                sourcePos = hit.point;
            }
            var info = MAHandPowerInfo.GetInfoByLevel(_power, level);
            if (info == null) info = m_fallbackPowerInfo;
            if (info == null)
            {
                Debug.LogError($"No MAHandPowerInfo found for type {_power} level {level}");
            }
            else
            {
                if (m_powerCooldowns.TryGetValue(_power, out var lastTime) == false || Time.time - lastTime > info.m_baseCooldownTime)
                {
                    if (info.m_attackLength > 0) maxDistance = info.m_attackLength;
                    m_handPowerWaitingForActive = true;
                    int count = info.m_numOfHits;
                    /*int count = 1;
                    for (int i = 0; i < info.m_numOfHits; ++i)
                        if (UnityEngine.Random.Range(0.0f, 1.0f) < info.m_bonusAttackChance)
                            ++count;*/
                    var targets = GetNearestObjects(PowerAffectsWorkers(), PowerAffectsEnemies(), PowerAffectsNonEnemies(), PowerAffectsTourists(), PowerAffectsHeroes(), false, false, count, maxDistance, sourcePos, possessed?.transform, direction, isConical);
                    if (targets.Count == 0 && PowerAffectsFallback())
                    {
                        if (MAUnlocks.Me.m_handPowerTargetFriendlies && ThreatsInRadius(sourcePos, NGManager.Me.m_threatZoneRadius) == false)
                            targets = GetNearestObjects(true, true, true, true, true, true, true, count, maxDistance, sourcePos, possessed?.transform, direction, isConical, _ignoreCanBeTargetted:false);
                        if (targets.Count == 0)
                        {
                            // look for static targets such as buildings and decorations
                            targets = GetNearestObjectsAndStatics(false, false, false, false, false, false, false, true, count, maxDistance, sourcePos, possessed?.transform, direction, isConical, _ignoreCanBeTargetted: false);
                        }
                    }
                    if (targets.Count > 0 && targets.Count < count)
                        for (int i = targets.Count; i < count; ++i)
                            targets.Add(targets[UnityEngine.Random.Range(0, targets.Count)]);
                    if (possessed != null)
                        m_castAnimOverride = AnimationOverride.PlayClip(possessed.GetComponentInChildren<Animator>().gameObject,
                            "HeroCastInto", (b) => MAPowerEffectBase.Create(_power, info, source, targets, _mouseButton, level),
                            "HeroCastLoop", null, "HeroCastOut", null);
                    else
                        MAPowerEffectBase.Create(_power, info, source, targets, _mouseButton, level);
                }
            }
        }
    }
    
    const float c_unscaledFrontToBackLength = 1.5f;
    const float c_unscaledRaiseToBackLength = .5f;

    private Vector3 m_smoothPos = Vector3.zero;
    private Vector3 m_smoothNrm = Vector3.up;


    // == attach
    
    Rigidbody m_attachedBody = null;
    List<Rigidbody> m_attachedChildBodies = null;
    GameObject m_attachedObject = null;
    float m_attachDragMaxVar = 0f;
    Vector2 m_attachScaleVar = Vector2.zero;
    float m_attachRootDistanceVar = 0f;
    
    float m_attachDragMax = 1.5f;
    float m_attachDragMin = 0f;
    Vector2 m_attachScale = new(0.1f, 0.96f);
    float m_attachRootDistance = 1f;
    Vector3 m_attachRootOffset = Vector3.down * 3f;
    float m_attachHipsHeight = 3f;

    public void AttachToHand<AttachedObjectType>(Rigidbody _body, AttachedObjectType attachedObject)
        where AttachedObjectType : MonoBehaviour
    {
        m_attachedBody = _body;
        _body.transform.rotation = Quaternion.identity;
        _body.transform.localEulerAngles = Vector3.zero;
        m_holdJoint.connectedBody = _body;

        m_attachedObject = attachedObject?.gameObject;
        m_attachedChildBodies = new List<Rigidbody>();
        m_attachedChildBodies.AddRange(m_attachedObject.GetComponentsInChildren<Rigidbody>(true));
        var rc = m_attachedObject.GetComponentInChildren<RagdollController>();
        if (rc != null)
            m_attachedChildBodies.AddRange(rc.BoneHipsRagdoll.GetComponentsInChildren<Rigidbody>(true));
        
        m_holdJoint.angularYMotion = ConfigurableJointMotion.Free;
        forceSnapTimer = 0f;

        var ch = attachedObject as MACharacterBase;
        if (ch != null)
        {
            var cs = ch.CharacterSettings;
            m_attachDragMax = cs.m_attachDragMax;
            m_attachDragMin = cs.m_attachDragMin;
            m_attachScale = cs.m_attachScale;
            m_attachRootDistance = cs.m_attachRootDistance;
            m_attachRootOffset = cs.m_attachRootOffset;
            m_attachHipsHeight = cs.m_attachHipsHeight;
        }
        m_attachDragMaxVar = m_attachDragMax;
        m_attachScaleVar = m_attachScale;
        m_attachRootDistanceVar = m_attachRootDistance;
    }

    public Rigidbody Attached => m_attachedBody;

    public void DetachFromHand(Rigidbody _body)
    {
        if (m_attachedBody == _body)
        {
            m_holdJoint.connectedBody = null;
            m_attachedBody = null;
            m_attachedChildBodies = null;
        }
    }

    public static void ZeroRigidbodyVelocities(Rigidbody _root, int _traverse = 3)
    {
        var rb = _root;
        for (int i = 0; i < _traverse; ++i)
        {
            if (rb == null) break;
            rb.linearVelocity = Vector3.zero;
            rb.angularVelocity = Vector3.zero;
            rb = rb.transform.parent.GetComponent<Rigidbody>();
        }
    }

    private float forceSnapTimer = 0f;
    void UpdateAttach()
    {
        if ((m_attachedObject == null) || (Attached == null)) return;
        
        var attachedRoot = m_attachedObject.transform;
        var rc = attachedRoot.GetComponentInChildren<RagdollController>();

        var pointerPos = m_pointer.position;
        if (GlobalData.IsInTerrain(pointerPos) && (pointerPos.y < GlobalData.c_seaLevel))
            pointerPos.y = Mathf.Max(pointerPos.y, GlobalData.c_seaLevel);
        var pos = pointerPos + m_attachRootOffset;
        var delta = pos - attachedRoot.position;
        float deltaDist = delta.sqrMagnitude;
        float distanceFactor = 0.0f;
        if (m_attachRootDistanceVar > 0.0f)
        {
            distanceFactor = Mathf.Clamp01(deltaDist / (m_attachRootDistanceVar * m_attachRootDistanceVar));
        }
        if ((m_attachedBody.linearVelocity.sqrMagnitude > 5f * 5f) && (m_attachDragMaxVar < 0.6f))
            m_attachDragMaxVar = m_attachDragMax;
        var deltaScale = Mathf.Lerp(m_attachScaleVar.x, m_attachScaleVar.y, distanceFactor);
        var teleportDelta = delta * deltaScale;
        var rootPos = attachedRoot.position + teleportDelta;
        attachedRoot.position = rootPos.AboveGround(0f);

        if (rc != null)
        {
            var hips = rc.BoneHipsRagdoll;
            var hipsPos = hips.position;
            if (m_attachDragMaxVar >= 0.5f)
            {
                hipsPos += pointerPos - m_attachedBody.transform.position;
            }
            else
            {
                hipsPos += teleportDelta;
            }
            hips.position = hipsPos.AboveGround(m_attachHipsHeight);

            float startY = pointerPos.y - 150f;
            float t = Mathf.Clamp01(startY / 200f);
            if (forceSnapTimer > 0f)
            {
                t = 1f;
                forceSnapTimer -= Time.deltaTime;
            }
            if (deltaDist > 60f * 60f)
            {
                t = 1f;
                forceSnapTimer = 0.5f;
            }
            m_attachRootDistanceVar = Mathf.Lerp(m_attachRootDistance, 0.5f, t);
            m_attachScaleVar = new Vector2(m_attachScale.x, Mathf.Lerp(m_attachScale.y, 1f, t));
        }
        
        m_attachDragMaxVar = Mathf.Lerp(m_attachDragMaxVar, m_attachDragMin, 0.1f);
        float dampFactor = 1.0f - Mathf.Min(1.0f, m_attachDragMaxVar);
        foreach (var rb in m_attachedChildBodies)
        {
            if (rb == null || rb.isKinematic)
            {
                continue;
            }
            
            rb.linearVelocity *= dampFactor;
            rb.angularVelocity *= dampFactor;
        }
        
        m_attachedBody.transform.position = pointerPos;
    }
    
    // == /attach

    void TriggerMouseClick()
    {
        if (m_currentPointTarget > .5f && IsVisible)
            m_handAnimator.SetTrigger("Click");
    }

    void SetPowerAnimation(string _power)
    {
        bool isLightning = false, isFireball = false, isFlamethrower = false, isWaterSpout = false, isWaterBlob = false, isWhip = false, isDig = false, isCuddle = false;
        switch (_power)
        {
            case c_powerTypeLightning: isLightning = true; break;
            case c_powerTypeFlamethrower: isFlamethrower = true; break;
            case c_powerTypeFireball: isFireball = true; break;
            case c_powerTypeWaterSpout: isWaterSpout = true; break;
            case c_powerTypeWaterBlob: isWaterBlob = true; break;
            case c_powerTypeWhip: isWhip = true; break;
            case c_powerTypeDig: isDig = true; break;
            case c_powerTypeCuddle: isCuddle = true; break;
        }
        m_handAnimator.SetBool("PowerLightning", isLightning);
        m_handAnimator.SetBool("PowerFlamethrower", isFlamethrower);
        m_handAnimator.SetBool("PowerFireball", isFireball);
        m_handAnimator.SetBool("PowerWaterstream", isWaterSpout);
        m_handAnimator.SetBool("PowerWaterball", isWaterBlob);
        m_handAnimator.SetBool("PowerWhip", isWhip);
        m_handAnimator.SetBool("PowerCuddle", isCuddle);
    }

    public void SetAnimatorValue(string _name, float _value)
    {
        m_handAnimator.SetFloat(_name, _value);
    }

    private bool m_handPowerWaitingForActive = false;
    private bool m_handPowerActive = false;
    public void SetPowerActivateAnimationState(bool _activated)
    {
        m_handPowerActive = _activated;
        if (_activated) m_handPowerWaitingForActive = false;
        m_handAnimator.SetBool("PowerTrigger", _activated);
    }

    float m_freezeHandTime = 0;
    Vector3 m_freezeHandPos;
    Quaternion m_freezeHandRot;
    Vector3 m_freezeHandScale;
    Plane m_freezeDragPlane;
    string m_lastHoldingType = null;
    KeyboardShortcutManager.EShortcutType? m_lastShortcut;
    const float c_freezeHandDuration = .8f;
    const float c_freezeHandHoldMultiplier = 4;

    const string c_gripWidthParameter = "GripWidth";
    const float c_gripWidthDefault = 1;
    void CheckGripWidth()
    {
        m_handAnimatorHasGrip = m_handAnimator.HasParameter(c_gripWidthParameter);
    }

    void SetGripWidth(HoldTransform _ht)
    {
        if (m_handAnimatorHasGrip)
            m_handAnimator.SetFloat(c_gripWidthParameter, _ht?.m_gripWidth ?? c_gripWidthDefault);
    }

    void UpdateHold(GameObject _holding)
    {
        bool isHolding = _holding != null;
        string holdingType = isHolding ? _holding.GetComponent<Pickup>()?.m_handAnimationEvent : null;
        var wasHolding = m_lastHoldingType != null && m_handAnimator.GetBool(m_lastHoldingType);
        if (holdingType != null)
            m_handAnimator.SetBool(holdingType, true);
        else if (m_lastHoldingType != null)
            m_handAnimator.SetBool(m_lastHoldingType, false);
        m_lastHoldingType = holdingType;
        
        if (wasHolding && !isHolding)
        {
            if (m_lastShortcut != null) KeyboardShortcutManager.Me.PopShortcuts(m_lastShortcut.Value);
            m_lastShortcut = null;
            //m_freezeHandTime = c_freezeHandDuration;
        }
        else if (isHolding)
        {
            var dragBase = _holding.GetComponent<DragBase>();
            if (dragBase != null)
                m_freezeDragPlane.SetNormalAndPosition(dragBase.DragPlaneNormal, dragBase.DragPlaneOrigin);
            else
                m_freezeDragPlane.SetNormalAndPosition(Camera.main.transform.forward, m_pointer.transform.position);
            m_freezeHandPos = m_handInstance.transform.position;
            m_freezeHandRot = m_handInstance.transform.rotation;
            m_freezeHandScale = m_handInstance.transform.localScale;
            
            var objectBase = _holding.GetComponentInChildren<NGLegacyBase>();
            
            var nextShortcut = objectBase == null ? KeyboardShortcutManager.EShortcutType.HeldObjects : objectBase.KeyboardShortcut; 
            
            if (wasHolding == false || nextShortcut != m_lastShortcut)
            {
                // Remove old
                if(m_lastShortcut != null) KeyboardShortcutManager.Me.PopShortcuts(m_lastShortcut.Value);
                    
                // Add new
                m_lastShortcut = nextShortcut;
                KeyboardShortcutManager.Me.PushShortcuts(nextShortcut);
            }
        }
    }

    bool UpdateFreeze()
    {
        if (m_freezeHandTime > 0)
        {
            var t = Mathf.Min(1, c_freezeHandHoldMultiplier * m_freezeHandTime / c_freezeHandDuration);
            m_handInstance.transform.position = Vector3.Lerp(m_handInstance.transform.position, m_freezeHandPos, t);
            m_handInstance.transform.rotation = Quaternion.Slerp(m_handInstance.transform.rotation, m_freezeHandRot, t);
            m_handInstance.transform.localScale = Vector3.Lerp(m_handInstance.transform.localScale, m_freezeHandScale, t);
            m_freezeHandTime -= Time.deltaTime;
            return true;
        }
        return false;
    }

    private string m_bestHitName = "";
    private bool m_debugBestHitName = false;
    public string BestHit => m_bestHitName;
    private static DebugConsole.Command s_debughandcmd = new ("debughand", _s => {
        Utility.SetOrToggle(ref Me.m_debugBestHitName, _s);
        if (Me.m_debugBestHitName) GameManager.SetConsoleDisplay(() => Me.m_bestHitName);
        else GameManager.SetConsoleDisplay(null);
    });

    byte[] m_unlockLevels = new byte[c_allPowers.Length];
    void UpdateBracelet()
    {
        UpdateGeneralSequence();
        
        if (m_bracelet == null) return;
        if (GameManager.Me.IsInTownView == false) return;

        for (int i = 1; i < c_allPowers.Length; ++i)
        {
            int powerLevel = PowerLevel(c_allPowers[i]);
            if (m_unlockLevels[i] < powerLevel && GameManager.Me.LoadComplete)
            {
                UnlockPowerUpdate(i);
                return;
            }
            m_unlockLevels[i] = (byte)powerLevel;
        }
        for (int i = 1; i < c_allPowers.Length; ++i)
            m_bracelet.ShowGem(i - 1, PowerUnlocked(c_allPowers[i]));
        m_bracelet.SetIcon(m_currentPowerType);
        m_bracelet.SetManaBar(GameManager.Me.m_state.m_powerMana / MAUnlocks.Me.MaxPowerMana);
    }

    public bool m_highlightWithBrightness = false;
    [ColorUsage(false, true)] public Color m_highlightColour = new Color(.0f, .2f, .5f);
    [ColorUsage(false, true)] public Color m_highlightColourNegative = new Color(.0f, 0, 0);
    private void HighlightObject(GameObject _root, bool _highlight)
    {
        var dragCard = _root.GetComponent<DragCard>();
        if (dragCard != null)
            dragCard.Highlight(_highlight);
        else if (m_highlightWithBrightness)
            Utility.HighlightRenderers(_root, _highlight, true);
        else
            Utility.ColourHighlightRenderers(_root, _highlight, (Vector4)m_highlightColour - (Vector4)m_highlightColourNegative, true);
    }
    
    private GameObject m_currentHighlightedObject = null;
    private GameObject m_lastHoverObject = null;
    private float m_hoverTime;
    private float m_hoverTipInhibitTime = 0;
    private Vector3 m_hoverTipStartPos;

    private void UpdateHoverHighlight(GameObject _obj, bool _isDragging)
    {
        var (targetObject, targetText) = GetHoverTarget(_obj, _isDragging);    
        
        if(targetObject != m_lastHoverObject)
        {
            m_lastHoverObject = targetObject;
            m_hoverTime = 0;
            m_hoverTipInhibitTime = 0;
            m_hoverTipStartPos = Vector3.zero;
        }
        
        if(m_currentHighlightedObject != null && targetObject != m_currentHighlightedObject)
        {
            HighlightObject(m_currentHighlightedObject, false);
            m_currentHighlightedObject = null;
        }
        
        if(targetObject != null)
        {
            m_hoverTime += Time.deltaTime;
            
            if(m_currentHighlightedObject == null && m_hoverTime >= NGManager.Me.m_timeBeforeHighlight)
            {
                HighlightObject(targetObject, true);
                m_currentHighlightedObject = targetObject;
            }
            const float c_toolTipDelay = 1f;
            if(m_hoverTime > c_toolTipDelay)
            {
                float moveAllowance = 0.05f * Screen.dpi;
                if(Utility.GetMouseButton(0) || Utility.GetMouseButton(1))
                {
                    m_hoverTipInhibitTime = c_toolTipDelay*2f;
                }
                else if(m_hoverTipInhibitTime > 0)
                {
                    m_hoverTipInhibitTime -= Time.deltaTime;
                }
                else if(targetText.IsNullOrWhiteSpace() == false)
                {
                    if(m_hoverTipStartPos.sqrMagnitude <= 0.001f)
                    {
                        m_hoverTipStartPos = Utility.mousePosition;
                    }
                    else if((m_hoverTipStartPos - Utility.mousePosition).sqrMagnitude > moveAllowance*moveAllowance)
                    {
                        m_hoverTipStartPos = Vector3.zero;
                        m_hoverTipInhibitTime = c_toolTipDelay*2f;
                    }
                    else
                    {
                        UIInfoMessage.Create(UIInfoMessage.Location.Hand, _obj, targetText, null, null, 0.1f);   
                    }
                }
            }
        }
    }

    private (GameObject, string) GetHoverTarget(GameObject _obj, bool _isDragging)
    {
        GameObject owner = null;
        string text = null;
        bool doDistrictBoundsCheck = true;
        
        if (_isDragging)
        {
            // dragging, drop highlight
        }
        else if (_obj != null && _obj.GetComponentInParent<DragBase>() is { } db)
        {
            if (db is DTDragBlock && (GameManager.Me.IsDesignTable || (db as DTDragBlock).IsWildBlock))
            {
                doDistrictBoundsCheck = DesignTableManager.Me.IsInDesignInPlaceActively == false; // Only if we're not on design table
                owner = db.gameObject;
                var block = (db as DTDragBlock).GetBlock();
                if(block != null && block.BlockInfo != null)
                {
                    text = block.BlockInfo.m_displayName;
                }
            }
            else if (_obj.GetComponentInParent<MABuilding>() is {} building)
            {
                if (building.IsHighlightable())
                {
                    owner = building.gameObject;
                    text = building.GetBuildingPopupText(m_hoverTime);
                }
            }
            else if (db is Pickup)
            {
                // Pickup on a building, check we can drag from it
                if (db.GetComponent<NGLegacyBase>() is { } lbp)
                {
                    if (lbp.IsHighlightable())
                    {
                        owner = lbp.gameObject;
                        text = lbp.DisplayName;
                    }
                }
                else
                    owner = db.gameObject;
            }
            else if (db is not DTDragDrawer && db is not DragToRotate)
            {
                doDistrictBoundsCheck = false;
                var dp = db.GetComponentInParent<DTDragPalette>();
                if(dp != null)
                {
                    text = dp.GetName();
                }
                owner = db.gameObject;
            }
        }
        else if (_obj != null && _obj.GetComponentInParent<NGLegacyBase>() is { } lb)
        {
            if (lb.IsHighlightable())
            {
                owner = lb.gameObject;
                text = lb.DisplayName;
            }
        }
        if (doDistrictBoundsCheck && owner != null && DistrictManager.Me.IsWithinDistrictBounds(owner.transform.position) == false)
        {
            owner = null;
            text = null;
        }
        return (owner,text);
    }

    public Vector3 m_powerUnlockOffsetInFrontOfCamera = new Vector3(-.75f, 2, 8);
    public Vector3 m_powerUnlockRotationInFrontOfCamera = new Vector3(0, 225, 0);
    public Vector3 m_possessSequenceOffsetInFrontOfCamera = new (-.75f, -1, 8);
    public Vector3 m_possessSequenceRotationInFrontOfCamera = new Vector3(0, 225, 0);
    
    private RaycastHit[] m_raycastHits = new RaycastHit[128];
    private Vector3 m_lastInputPosition;
    private Vector3 m_smoothDrag = Vector3.zero;
#if UNITY_EDITOR
    public bool showCharacterDebugHelper = false;
#endif
    void LateUpdate()
    {
        if (PauseManager.IsPaused)
        {
            ShowHand(false, false);
            return;
        }

        UpdateBlends();
        UpdateOverbright();
        
        var currentDragObject = InputUtilities.GetCurrentDragObject();
        var pickup = currentDragObject == null ? null : currentDragObject.GetComponent<Pickup>();
        bool needSyncTransforms = pickup != null && pickup.m_dragHandledByPhysics;
        
        if (needSyncTransforms || GameManager.Me.IsDesignTable) Physics.SyncTransforms();
        
        SetMaterialValues();
        UpdateScarAmount();

        if (PauseManager.IsPaused)
        {
            if (Cursor.visible != true || Cursor.lockState != CursorLockMode.None)
            {
                Cursor.visible = true;
                Cursor.lockState = CursorLockMode.None;
            }
            return;
        }
        
        if (Input.GetMouseButtonDown(0))
            TriggerMouseClick();

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        UpdateBracelet();
  
        var regenRate = DayNight.Me.m_isFullDay ? NGManager.Me.m_manaRegeneratePerSecondDay : NGManager.Me.m_manaRegeneratePerSecondNight;
        regenRate *= DayNight.Me.TimePassingSpeedMultiplier;
        GameManager.Me.m_state.m_powerMana = Mathf.Clamp(GameManager.Me.m_state.m_powerMana + Time.deltaTime * regenRate, 0, MAUnlocks.Me.MaxPowerMana);
#endif //!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        
        CheckVisibility();
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        UpdatePowers();
        UpdateGestures();
        UpdateVFX();
#else
        RefreshPowerUIVisibility();
#endif
        
        if (m_handVisible == false)
        {
            if (Cursor.visible == false)
            {
                Cursor.visible = true;
                m_lastCursor2D = null;
                Cursor.SetCursor(null, Vector2.zero, CursorMode.Auto);
            }
            return;
        }

        m_infoMessageHolder.transform.position = Utility.mousePosition;

        const bool c_showGizmos = false;
        if (c_showGizmos) GameManager.Me.ClearGizmos("Hand");

        m_isOver2D = false;

        var inputPosition = Utility.mousePosition;
        var inputMove = (inputPosition - m_lastInputPosition) * (800.0f / Screen.height);
        inputMove.x *= -1;
        m_lastInputPosition = inputPosition;
        m_smoothDrag = inputMove = Vector3.Lerp(m_smoothDrag, inputMove, .2f);
        
        GameObject bestHighlight = null;
        
        int hits = 0;
        Vector3 nrm = Vector3.zero, pos = Vector3.zero;
        float stepSize = Screen.height * .01f;
        bool isOverUI = false, isOverClickable = false, isOverWorldUI = false, isOverDesignHarness = false;
        Ray ray = default;
        var cam = Camera.main;
        var camTransform = cam.transform;
        HoldTransform currentHoldTransform = null;
        if (!IsTrapped)
        {
            UpdateHold(currentDragObject);
        }
        bool isConstantScreenSize = m_handConstantScreenSize || GameManager.Me.IsInterior || UndergroundManager.IsActive || (GameManager.Me.IsPossessing && IsInPossessSequence == false) || GameManager.Me.IsCharacterSelectionScene;
        GameObject closestObj = null;
        if (m_debugBestHitName) m_bestHitName = "";
        
        var dragRoll = currentDragObject != null ? inputMove : Vector3.zero;

        UpdateCooldownBrightness();
        
        bool shouldUpdateAttach = false;
        bool applyRaise = true, ignoreGround = false, snapDragObjectRotation = false;
        if (RunPowerSpecificUpdate(ref ray, ref pos, ref nrm))
        {
            hits = 1;
            if (m_debugBestHitName) m_bestHitName = "PowerSpecific";

            shouldUpdateAttach = true;

            var uiPwr = Utility.GetUIObjectAt(Utility.mousePosition, out var uiPosPwr, out var uiNrmPwr);
            if (uiPwr != null)
            {
                isOverClickable = IsClickableElement(uiPwr);
                if (Is2DElement(uiPwr))
                {
                    m_isOver2D = true;
                    isOverUI = true;
                }
            }
        }
        else if (currentDragObject != null)
        {
            if (pickup == null)// || pickup.m_dragHandledByPhysics == false)
            {
                ray = cam.ScreenPointToRay(Utility.mousePosition);
                var holdTransform = HoldTransform.Get(currentDragObject);
                currentHoldTransform = holdTransform;
                if (holdTransform != null) pos = holdTransform.transform.position;
                else pos = currentDragObject.transform.position;
                applyRaise = false;
                snapDragObjectRotation = true;
            }
            else
            {
                ray = new Ray(Vector3.zero, Vector3.zero);
                pos = m_pointer.position;
                applyRaise = pickup.m_dragHandledByPhysics;
            }
            const float c_minimumDragAboveGround = .8f;
            var groundPos = pos.GroundPosition(c_minimumDragAboveGround);
            if (pos.y < groundPos.y) pos.y = groundPos.y;
            if (m_debugBestHitName) m_bestHitName = "Drag";
            hits = 1;
            nrm = Vector3.up;
            //isConstantScreenSize = true;
        }
        else
        {
            for (int i = 0; i < 5; ++i)
            {
                var spos = Utility.mousePosition;
                if (i != 0)
                {
                    float dx = (((i - 1) >> 0) & 1) * 2 - 1;
                    float dy = (((i - 1) >> 1) & 1) * 2 - 1;
                    spos.x += dx * stepSize;
                    spos.y += dy * stepSize;
                }
                spos = Utility.ClampToScreen(spos);
                ray = cam.ScreenPointToRay(spos);

                var closest = 1e23f;
                var hitPos = Vector3.zero;
                var hitNrm = Vector3.zero;
                var hitIsWorldUI = false;
                
                var ui = Utility.GetUIObjectAt(spos, out var uiPos, out var uiNrm);
                if (c_showGizmos) GameManager.Me.AddGizmoLine("Hand", ray.origin, ray.origin + ray.direction * 100, ui == null ? Color.red : Color.blue);
                if (ui != null)
                {
                    isOverClickable = IsClickableElement(ui);
                    if (Is2DElement(ui))
                    {
                        var cg = ui.GetComponentInParent<CanvasGroup>();
                        if (cg == null || cg.alpha > .2f)
                        {
                            m_isOver2D = true;
                            isOverUI = true;
                            closest = 0;
                            if (m_debugBestHitName) m_bestHitName = $"[2D] {ui.transform.Path()}";
                        }
                    }
                    else
                    {
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
                        if (ui.transform.IsChildOf(DesignHarness.Me.transform)) isOverDesignHarness = true;
#endif
                        var plane = new Plane(ui.transform.forward, ui.transform.position);
                        plane.Raycast(ray, out var distance);
                        uiNrm = -ui.transform.forward;
                        uiPos = ray.GetPoint(distance * .95f);
                        //var distance = (cam.transform.position - uiPos).magnitude;
                        if (distance < closest)
                        {
                            closest = distance;
                            hitPos = uiPos;
                            hitNrm = uiNrm;
                            hitIsWorldUI = true;
                            if (m_debugBestHitName) m_bestHitName = $"[3D] {ui.transform.Path()}";
                            bestHighlight = ui;
                        }
                    }
                }
                int hitCount = Physics.RaycastNonAlloc(ray, m_raycastHits, 1e23f, ~(GameManager.c_layerDistrictBit | GameManager.c_layerIgnoreRaycastBit | GameManager.c_layerBodyToBodyBit | GameManager.c_layerCollideWithNothingBit));
                for (int hit = 0; hit < hitCount; ++hit)
                {
                    var ph = m_raycastHits[hit];
                    //if (ph.collider.isTrigger) continue;
                    var go = ph.collider.gameObject;
                    if (m_whip != null && go.transform.IsChildOf(m_whip.transform)) continue;
                    if (IsClickableObject(go)) isOverClickable = true;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
                    if (go.transform.IsChildOf(DesignHarness.Me.transform)) isOverDesignHarness = true;
#endif
                    if (c_showGizmos)
                    {
                        GameManager.Me.AddGizmoPoint("Hand", ph.point, .0001f, Color.cyan);
                        GameManager.Me.AddGizmoLabel("Hand", ph.point, go.name, Color.white);
                        var c = ph.collider.transform.position;
                        if (ph.collider is BoxCollider box)
                        {
                            c = box.transform.TransformPoint(box.center);
                            GameManager.Me.AddGizmoOCube("Hand", c, box.transform.right * box.size.x * .5f * box.transform.lossyScale.x, box.transform.up * box.size.y * .5f * box.transform.lossyScale.y, box.transform.forward * box.size.z * .5f * box.transform.lossyScale.z, new Color(.3f, 1f, 1f, .2f), true);
                        }
                        GameManager.Me.AddGizmoLabel("Hand", c, $"O {go.name}", Color.white);
                    }
#if UNITY_EDITOR
                    if (showCharacterDebugHelper)
                    {
                        var character = go.GetComponentInChildren<MACharacterBase>();
                        if (character != null)
                            character.CreateDebugHelperIfNeeded();
                    }
#endif
                    if (ph.distance < closest)
                    {
                        closest = ph.distance;
                        hitIsWorldUI = false;
                        hitPos = ph.point;
                        hitNrm = ph.normal;
                        closestObj = ph.collider.gameObject;
                        if (m_debugBestHitName) m_bestHitName = $"[W] {closestObj.transform.Path()} {hitNrm}";
                        bestHighlight = closestObj;
                    }
                }
                if (GameManager.Me.IsInTownView && (closest > 1e22f || hitPos.y < GlobalData.c_seaLevel))
                {
                    if (GameManager.s_seaPlane.Raycast(ray, out var hitSea))
                    {
                        hitPos = ray.GetPoint(hitSea);
                        hitNrm = Vector3.up;
                        closest = hitSea;
                    }
                }

                if (closest < 1e22f)
                {
                    isOverWorldUI |= hitIsWorldUI;
                    nrm += hitNrm;
                    pos += hitPos;
                    ++hits;
                }
            }
        }
        if (m_debugBestHitName) m_bestHitName = $"{m_bestHitName}\n{(bestHighlight==null ? "<none>" : bestHighlight.transform.Path(5))}\nScreen: {Utility.mousePosition}\nWorld: {m_pointer.position:n1}";
        if (hits > 0)
        {
            if (m_currentGesture == 0)
                nrm.Normalize();
            else
                nrm = -camTransform.forward;
            pos /= hits;
            m_smoothPos = pos;
            m_smoothNrm = Vector3.Slerp(m_smoothNrm, nrm, .3f);

            if (m_unlockPowerSequenceLock > 0)
            {
                Vector3 lockPosLocal = default, lockRotLocal = default;
                
                switch (m_unlockPowerSequenceLockType)
                {
                    case 0: lockPosLocal = m_powerUnlockOffsetInFrontOfCamera; lockRotLocal = m_powerUnlockRotationInFrontOfCamera; break;
                    case 1: lockPosLocal = m_possessSequenceOffsetInFrontOfCamera; lockRotLocal = m_possessSequenceRotationInFrontOfCamera; break; 
                }
                var lockPos = camTransform.TransformPoint(lockPosLocal);
                var lockNrm = camTransform.rotation * Quaternion.Euler(lockRotLocal + Vector3.up * m_unlockPowerSequenceLockRock) * Vector3.forward;//camTransform.forward;
                m_smoothPos = Vector3.Lerp(m_smoothPos, lockPos, m_unlockPowerSequenceLock);
                m_smoothNrm = Vector3.Slerp(m_smoothNrm, lockNrm, m_unlockPowerSequenceLock);
                float fadeDirection = 1 - Mathf.Min(1, m_unlockPowerSequenceLock * (1.0f / .3f));
                ray.direction *= fadeDirection; // reduce raise
            }

            pos = m_smoothPos;
            nrm = m_smoothNrm;
            var fwd = camTransform.up;
            var camFwd = camTransform.forward;
            var camUp = camTransform.up;
            var camRight = camTransform.right;

            float distance = (pos - camTransform.position).magnitude;

            if (GameManager.Me.IsDesignTable && isOverUI == false)
            {
                var xzFwd = camTransform.forward.GetXZNorm();
                var pushBack = c_unscaledFrontToBackLength * 4;
                pushBack *= distance * (1.0f / 50.0f); // always treat this test as constant screen size since the drawer section -is-
                var basePos = pos - xzFwd * pushBack;
                var rayOrigin = camTransform.position;
                var rayFwd = (basePos - rayOrigin).normalized;
                var baseRay = new Ray(rayOrigin, rayFwd);
                int hitCount = Physics.RaycastNonAlloc(baseRay, m_raycastHits);
                for (int i = 0; i < hitCount; ++i)
                {
                    var baseHit = m_raycastHits[i];
                    if(baseHit.transform.parent == null)
                        continue;
                    if (baseHit.transform.parent.GetComponent<DTDragDrawer>() != null)
                    {
                        if (baseHit.point.y > basePos.y)
                        {
                            var baseHolder = baseHit.transform.parent;
                            if (m_debugBestHitName) m_bestHitName = $"[B] drawer {baseHit.transform.name} - {baseHolder.transform.name} {baseHit.transform.up} {baseHolder.transform.up}";                            
                            var plane = new Plane(baseHolder.transform.up, baseHolder.transform.position + baseHolder.transform.up * .02f);
                            var finalRay = cam.ScreenPointToRay(Utility.mousePosition);
                            var finalHit = plane.Raycast(finalRay, out var finalDistance);
                            pos = finalRay.GetPoint(finalDistance);
                            nrm = baseHolder.transform.up;
                            isOverUI = false;
                            isOverDesignHarness = true;
                            //applyRaise = false;
                            ignoreGround = true;
                            distance = (pos - camTransform.position).magnitude;
                            break;
                        }
                    }
                }
            }
            
            SetHandXrayLerpFromDistance(distance);
            //var side = Vector3.Cross(nrm, fwd).normalized;
            var side = Vector3.Cross(nrm, fwd);
            side -= camFwd * Vector3.Dot(side, camFwd);
            side.Normalize();
            fwd = Vector3.Cross(side, nrm);
            
            if (dragRoll.sqrMagnitude > 0)
            {
                var dragRollRot = Quaternion.AngleAxis(dragRoll.x, camUp) * Quaternion.AngleAxis(dragRoll.y, camRight);
                nrm = dragRollRot * nrm;
                fwd = dragRollRot * fwd;
                side = dragRollRot * side;
            }

            float pointValueTarget = 0;
            if (closestObj != null)
            {
                if (InputUtilities.GetCurrentDragObject() != null || IsClickableObject(closestObj))
                    pointValueTarget = 1;
                var stroke = closestObj.GetComponentInParent<StrokeResponder>();
                if (stroke != null)
                    stroke.Stroke();
            }
            else if (isOverClickable)
                pointValueTarget = 1;

            if (IsTrapped) pointValueTarget = 0;
            m_currentPointTarget = pointValueTarget; 
            m_handAnimator.SetFloat("PointLevel", Mathf.Lerp(m_handAnimator.GetFloat("PointLevel"), pointValueTarget, .2f));

            float scale = m_handBaseScale;
            isConstantScreenSize |= isOverDesignHarness;
            UpdateHandDistrictFilter(isConstantScreenSize);
            float lightScale;
            if (isConstantScreenSize)
            {
                scale *= distance;
                lightScale = .04f;
            }
            else
            {
                scale *= 50;
                lightScale = 1.5f;
                const float c_scaleWhenCloserThan = 20;
                const float c_scaleWhenFurtherThan = 150;
                var overscale = 1.0f;
                if (distance < c_scaleWhenCloserThan && currentDragObject == null && m_unlockPowerSequenceLock < .001f) // don't clamp max size when dragging, it interferes with the visuals of the hold
                    overscale = distance * (1.0f / c_scaleWhenCloserThan); 
                else if (distance > c_scaleWhenFurtherThan)
                    overscale = distance * (1.0f / c_scaleWhenFurtherThan);
                scale *= overscale;
                lightScale *= overscale;
            }
            m_handLightEnabledScale = Mathf.Lerp(m_handLightEnabledScale, AnyPowerActive ? 0 : 1, .1f);
            if (m_forceHideHand) m_handLightEnabledScale = 0; // make light come on slowly after hiding hand
            float dayNightScale = FingerLightScale();
            lightScale *= dayNightScale;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
            m_glowLight.color = Color.Lerp(m_glowLight.color, PowerLightColour(), .2f);
            m_glowLight.SetShadowIntensity(m_handLightEnabledScale);
            m_glowLight.SetRadius(lightScale);
            m_glowLight.SetIntensity(lightScale * lightScale * .3f);
            m_glowPush.m_pushForward = lightScale * .3f;
            m_glowLight.transform.localPosition = Vector3.up * (lightScale * .1f);
#endif
            m_handInstance.transform.localScale = Vector3.one * scale;
            const float c_raise = .3f;
            if (applyRaise)
                pos += ray.direction * (c_raise * -scale);
            var handRaise = UpdateHandRaise(); 
            pos += Vector3.up * handRaise;
            m_handInstance.transform.LookAt(m_handInstance.transform.position + fwd, nrm);
            m_handInstance.transform.position += pos - m_pointer.position; // force m_pointer to be exactly at the target position
            if (s_lockHandPosition)
                m_handInstance.transform.position += s_lockedHandPosition - m_pointer.position;

            bool keepHandAboveGround = m_keepHandAboveGround && UndergroundManager.IsActive == false && ignoreGround == false;
            if (GameManager.Me.IsCharacterSelectionScene || GameManager.Me.IsFrescoScene)
                keepHandAboveGround = false;
            if (GlobalData.IsInTerrain(pos) == false)
                keepHandAboveGround = false;
            if (keepHandAboveGround)
            {
                // make sure the hand doesn't go underground
                var backPos = m_pointer.position - m_handInstance.transform.forward * (scale * c_unscaledFrontToBackLength) + m_handInstance.transform.up * (scale * c_unscaledRaiseToBackLength);
                var groundPos = backPos.GroundPosition(scale * .25f);
                if (groundPos.y > backPos.y)
                {
                    if (m_currentGesture != 0)
                    {
                        var raise = groundPos.y - scale * .25f * 2 - backPos.y;
                        if (raise > 0)
                            m_handInstance.transform.position += Vector3.up * raise;
                    }
                    else
                    {
                        Vector3 toBack = pos - backPos, toGround = pos - groundPos;
                        var raiseAngle = Vector3.Angle(toBack, toGround) * Mathf.Deg2Rad; // this is only rough but good enough
                        var fwdRot = fwd.RotateAbout(side, raiseAngle);
                        var nrmRot = nrm.RotateAbout(side, raiseAngle);
                        m_handInstance.transform.LookAt(m_handInstance.transform.position + fwdRot, nrmRot);
                        m_handInstance.transform.position += pos - m_pointer.position; // force m_pointer to be exactly at the target position
                    }
                }
                if (pos.y < GlobalData.c_seaLevel)
                {
                    pos.y = Mathf.Max(pos.y, GlobalData.c_seaLevel);
                    m_handInstance.transform.position += pos - m_pointer.position;
                }
            }
            var lightLayer = isOverDesignHarness ? 1u << 7 : 1u << 0;
            foreach (var r in m_handInstance.GetComponentsInChildren<Renderer>())
                r.renderingLayerMask = lightLayer;
        }

        if (currentDragObject != null)
        {
            if (pickup != null && pickup.m_dragHandledByPhysics == false)
            {
                var holdTransform = HoldTransform.Get(currentDragObject);
                currentHoldTransform = holdTransform;
                Vector3 snapPos;
                if (holdTransform != null) snapPos = holdTransform.transform.position;
                else snapPos = currentDragObject.transform.position;
                currentDragObject.transform.position += pos - snapPos;
                applyRaise = false;
                snapDragObjectRotation = true;
            }
        }

        if (snapDragObjectRotation)
        {
            if (currentHoldTransform != null)
                currentDragObject.transform.rotation = m_holder.transform.rotation * currentHoldTransform.transform.localRotation;
            else
                currentDragObject.transform.rotation = m_holder.transform.rotation;
        }

        if (!IsTrapped)
        {
            SetGripWidth(currentHoldTransform);
        }
        
        bool isFrozen = UpdateFreeze();
        bool isTrapped = UpdateTrapped();
        bool showHand = !isOverUI || isFrozen || isTrapped;
        var handIsShowing = ShowHand(showHand);

        if (handIsShowing == false) bestHighlight = null;
        if (AnyPowerActive) bestHighlight = null;
        //if (GameManager.Me.IsDesignTable) bestHighlight = null;
        UpdateHoverHighlight(bestHighlight, currentDragObject != null);
        
        var cursor2D = isOverUI ? (isOverClickable ? m_cursor2DPoint : m_cursor2DNormal) : null;
        if (cursor2D != m_lastCursor2D)
        {
            m_lastCursor2D = cursor2D;
            // GL - not using custom 2D cursors, just the system cursor
            //Cursor.SetCursor(m_lastCursor2D, c_cursor2DHotspot * m_cursor2DPoint.width, CursorMode.Auto);
        }

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        Color pointerColour;
        if (isOverWorldUI)
        {
            pointerColour = isOverClickable ? m_glowUIClickable : m_glowUI;
        }
        else
        {
            pointerColour = isOverClickable ? m_glowClickable : m_glowNormal;
        }
        m_pointerImage.color = pointerColour;
#endif
        
        if (shouldUpdateAttach)
        {
            UpdateAttach();
        }
        
        UpdatePowerUIPosition();
        
        if (needSyncTransforms) Physics.SyncTransforms();
    }

    private bool m_powerCanvasIs2D;
    public Vector3 m_powerUIOffset2D = new Vector3(0, -.05f, 0);
    void UpdatePowerUIPosition()
    {
        if (m_powerCanvasIs2D == false) return;
        var ui = m_powerCanvas.transform.GetChild(0);
        var offset = new Vector3(m_powerUIOffset2D.x * Screen.width, m_powerUIOffset2D.y * Screen.height, 0);
        ui.position = Camera.main.WorldToScreenPoint(m_bracelet.transform.position) + offset;
    }

    private bool m_isHandIgnoreDistrictFilterFlagSet = false;
    void UpdateHandDistrictFilter(bool _disableDistrictFilter)
    {
        if (m_isHandIgnoreDistrictFilterFlagSet != _disableDistrictFilter)
        {
            m_isHandIgnoreDistrictFilterFlagSet = _disableDistrictFilter;
            m_handInstance.IgnoreDistrictFilter(_disableDistrictFilter);
        }
    }

    void SetHandXrayLerpFromDistance(float _distance)
    {
        /*if (m_xrayRenderer == null) return;
        const float c_xrayDefaultDistance = 75;
        const float c_xrayMaxDistance = 200;
        const float c_xrayMaxLerp = .25f;
        var factor = Mathf.Clamp01((_distance - c_xrayDefaultDistance) / (c_xrayMaxDistance - c_xrayDefaultDistance)) * c_xrayMaxLerp;
        foreach (var m in m_xrayRenderer.materials)
            m.SetFloat("_ColorLerp", factor);*/
    }

    float m_handLightEnabledScale = 0;

    float FingerLightScale()
    {
        const float c_dayLightScale = 1;
        const float c_nightLightScale = 2; //Marcos Tinkered with this, was 6 before
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        float dark = DayNight.Me.Darkness();
#else
				float dark = 0f;
#endif //!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        float lerp = dark * dark * m_handLightEnabledScale; // using powers dims the hand light to day levels
        return Mathf.Lerp(c_dayLightScale, c_nightLightScale, lerp);
    }
    
    private bool m_forceHideCursor = false; // something else is hiding the cursor

    public void ForceHideCursor(bool _forceHide)
    {
        m_forceHideCursor = _forceHide;
    }

    bool IsVisible => m_handRenderer?.enabled ?? false;
    
    public bool ShowCursor()
    {
        if(RadialMenuActive) return true;
        if(MAQuestMessageDialog.s_current != null) return true;
        if(TownInfoUIController.Showing) return true;
        if (MAChoiceGUIManager.Me && MAChoiceGUIManager.Me.m_showing) return true;
        if (NGBaseInfoGUI.s_infoShowing != null) return true;
        return false;
    }
    
    bool ShowHand(bool _showHand, bool _force = false)
    {
        bool unlockCursor = true;
        var showCursor = !_showHand || m_debugShowSystemCursor;
        if (m_forceHideCursor) showCursor = false;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        if ((GameManager.Me.IsPossessingWithHiddenHand || GameManager.Me.IsPossessingBuilding || IntroControl.Me.InCrypt || MAQuestCutscene.s_isAnyActive) && GameManager.Me.m_possessionRotateRequiresButton == false && ShowCursor() == false && PauseManager.IsPaused == false)
#else
        if ((GameManager.Me.IsPossessingWithHiddenHand || GameManager.Me.IsPossessingBuilding) && GameManager.Me.m_possessionRotateRequiresButton == false && ShowCursor() == false)
#endif
            showCursor = _showHand = unlockCursor = false;
        if (Input.mousePosition.x < 0 || Input.mousePosition.y < 0 || Input.mousePosition.x >= Screen.width || Input.mousePosition.y >= Screen.height)
            showCursor = true; 
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        if (GameManager.Me.IsFrescoScene || GameManager.Me.IsCharacterSelectionScene || DistrictManager.Me.IsEditing || FailSequenceController.Me.IsActive || RoadManager.Me.InDebugEdit || GameManager.Me.IsInTitleScreen())
        {
            showCursor = true;
            _showHand = false;
            unlockCursor = true;
        }
#endif
        if (m_forceHideHand)
            _showHand = false;
        if (Utility.IsVideoRecording)
            showCursor = true;
        if (m_unlockPowerSequenceLock > 0 || IsTrapped)
        {
            _showHand = true;
            showCursor = false;
        }
        var lockState = unlockCursor ? CursorLockMode.None : CursorLockMode.Locked;
        if (Cursor.visible != showCursor || Cursor.lockState != lockState)
        {
            Cursor.visible = showCursor;
            Cursor.lockState = lockState;
        }
        if (m_handRenderer != null && (m_handRenderer.enabled != _showHand || _force))
            m_handRenderer.enabled = _showHand;
        if (m_bracelet != null && m_bracelet.gameObject.activeSelf != _showHand)
                m_bracelet.gameObject.SetActive(_showHand);
        if (m_glowLight != null && m_glowLight.enabled != _showHand)
            m_glowLight.enabled = _showHand;
        if (m_powerCanvas != null && m_powerCanvas.m_canvas.enabled != _showHand)
            m_powerCanvas.m_canvas.enabled = _showHand;

        ShowRings(_showHand);
        return _showHand;
    }

    private List<UnityEngine.UI.Image> m_debugImages = new();
    public UnityEngine.UI.Image ShowDebugRect(string _name, bool _show)
    {
        if(_show)
        {
            int i = m_debugImages.FindIndex(x => x.name == "DebugRect " + _name);
            if(i == -1)
            {
                GameObject newDebugImageGO = new GameObject("DebugRect " + _name);
                newDebugImageGO.AddComponent<RectTransform>();
                UnityEngine.UI.Image newDebugImage = newDebugImageGO.AddComponent<UnityEngine.UI.Image>();
                newDebugImageGO.transform.parent = GameManager.Me.m_fullScreenCanvas;
                m_debugImages.Add(newDebugImage);
                i = m_debugImages.Count - 1;
                m_debugImages[i].transform.localScale = Vector3.one;
            }

            for(int j = 0; j < m_debugImages.Count; ++j)
            {
                Color col = Color.HSVToRGB((1 / (float)m_debugImages.Count) * j, 1, 1);
                col.a = 0.4f;
                m_debugImages[j].color = col;
            }

            return m_debugImages[i];
        }
        else
        {
            int i = m_debugImages.FindIndex(x => x.name == "DebugRect " + _name);
            if(i != -1)
            {
                Destroy(m_debugImages[i].gameObject);
                m_debugImages.RemoveAt(i);
            }
        }
        return null;
    }

    // Rings
    public enum RingType
    {
        Evil,
        Neutral,
        Good
    }

    [Serializable]
    public class RingInfo
    {
        public RingType m_type;
        public GameObject m_prefab;
        public string m_bone;
    }

    public class RingRenderer
    {
        public GameObject m_gameObject;
        public Renderer[] m_renderers;

        public RingRenderer(GameObject _gameObject)
        {
            m_gameObject = _gameObject;
            m_renderers = _gameObject.GetComponentsInChildren<Renderer>();
        }

        public bool enabled
        {
            get
            {
                if(m_renderers.Length > 0)
                {
                    return m_renderers[0].enabled;
                }
                return false;
            }
            set
            {
                foreach(var renderer in m_renderers)
                {
                    renderer.enabled = value;
                }
            }
        }
    }

    public List<RingInfo> m_ringInfos = new List<RingInfo>();
    public List<RingType> OwnedRings => GameManager.Me.m_state.m_ringsState.m_ownedRings;
    public List<RingType> EquippedRings => GameManager.Me.m_state.m_ringsState.m_equippedRings;

    private Dictionary<RingType, RingInfo> m_ringInfoDict = null;    
    private Dictionary<RingType, RingRenderer> m_ringRenderers = new Dictionary<RingType, RingRenderer>();
    private bool m_showRings = false;

    public void UnlockRing(RingType _ringType)
    {
        if(!OwnedRings.Contains(_ringType))
        {
            OwnedRings.Add(_ringType);
        }
    }

    public void EquipRing(RingType _ringType, bool _playAnimation)
    {
        if (OwnedRings.Contains(_ringType))
        {
            if (!EquippedRings.Contains(_ringType))
            {
                RingInfo ringInfo = GetRingInfo(_ringType);

                if (ringInfo != null)
                {
                    UnequipFromBone(ringInfo.m_bone);
                    EquippedRings.Add(_ringType);

                    if (m_handInstance != null)
                    {
                        if (_playAnimation)
                        {
                            StartCoroutine(Co_AddRing());
                        }
                        else
                        {
                            ShowRings(m_showRings);
                        }
                    }
                }
            }      
        }
    }

    public void UnequipRing(RingType _ringType)
    {
        if(EquippedRings.Contains(_ringType))
        {
            EquippedRings.Remove(_ringType);

            if(m_ringRenderers.ContainsKey(_ringType))
            {
                Destroy(m_ringRenderers[_ringType].m_gameObject);
                m_ringRenderers.Remove(_ringType);
            }
        }
    }

    private void UnequipFromBone(string _bone)
    {
        for (int i = EquippedRings.Count - 1; i >= 0; i--)
        {
            RingInfo ringInfo = GetRingInfo(EquippedRings[i]);

            if (ringInfo != null && ringInfo.m_bone == _bone)
            {
                UnequipRing(ringInfo.m_type);
            }
        }       
    }

    private const float m_addRingSpeed = 2.0f;
    private IEnumerator Co_AddRing()
    {
        var handTarget = new GameObject("HandTarget");
        handTarget.transform.SetParent(Camera.main.transform);
        handTarget.transform.localPosition = new Vector3(-0.3f, 1.5f, 2.2f);
        handTarget.transform.localRotation = Quaternion.Euler(-77.0f, -15.5f, 10);
        handTarget.transform.localScale = Vector3.one;

        m_trappedTransform = handTarget.transform;
        m_trappedTransformWeight = 0.0f;

        while(m_trappedTransformWeight < 1.0f)
        {
            m_trappedTransformWeight += m_addRingSpeed * Time.deltaTime;
            m_trappedTransformWeight = Mathf.Clamp01(m_trappedTransformWeight);
            yield return null;
        }

        PlayHandGesture("Hang Loose");

        yield return new WaitForSeconds(1.0f);

        ShowRings(m_showRings);

        yield return new WaitForSeconds(1.0f);

        while (m_trappedTransformWeight > 0.0f)
        {
            m_trappedTransformWeight -= m_addRingSpeed * Time.deltaTime;
            m_trappedTransformWeight = Mathf.Clamp01(m_trappedTransformWeight);
            yield return null;
        }

        m_trappedTransform = null;

        Destroy(handTarget);

        yield return null;
    }

    private void ShowRings(bool _showRings)
    {
        if (GameManager.Me.LoadComplete == false) return;

        foreach (var ringType in EquippedRings)
        {
            if (!m_ringRenderers.ContainsKey(ringType))
            {
                SpawnRing(ringType);
            }
        }

        foreach (var ring in m_ringRenderers)
        {
            if (ring.Value.enabled != _showRings)
            {
                ring.Value.enabled =_showRings;
            }
        }

        m_showRings = _showRings;
    }

    private void SpawnRing(RingType _ringType)
    {
        RingInfo ringInfo = GetRingInfo(_ringType);

        if (ringInfo != null)
        {
            if(ringInfo.m_prefab != null && m_handInstance != null)
            {
                Transform finger = m_handInstance.transform.FindChildRecursiveByName(ringInfo.m_bone);

                if(finger != null)
                {
                    GameObject gameObject = Instantiate(ringInfo.m_prefab, finger);
                    gameObject.transform.localPosition = Vector3.zero;
                    gameObject.transform.localRotation = Quaternion.identity;
                    m_ringRenderers[_ringType] = new RingRenderer(gameObject);
                }
            }
        }
    }

    private RingInfo GetRingInfo(RingType _ringType)
    {
        InitRingInfoDict();

        if (m_ringInfoDict.ContainsKey(_ringType))
        {
            return m_ringInfoDict[_ringType];
        }

        return null;
    }

    private void InitRingInfoDict()
    {
        if(m_ringInfoDict == null)
        {
            m_ringInfoDict = new Dictionary<RingType, RingInfo>();

            foreach(var ringInfo in m_ringInfos)
            {
                m_ringInfoDict[ringInfo.m_type] = ringInfo;
            }
        }
    }

    private void ClearRings()
    {
        foreach(var ring in m_ringRenderers)
        {
            Destroy(ring.Value.m_gameObject);
        }

        m_ringRenderers.Clear();
        GameManager.Me.m_state.m_ringsState.Clear();
    }

    private Transform m_trappedTransform = null;
    private GameObject m_trappedHeldObject = null;
    private int m_stigmataID, m_stigmataRandomID, m_stigmataRandomTriggerID, m_stigmataPartialReleaseID;
    private int m_lastStigmataRandomAnim = -1;
    private bool m_isInStigmataAnim = false;   
    private float m_stigmataRandomDelay;
    private float m_stigmataClickCooldown;
    private float[] m_stigmataRandomWeights = { 0.2f, 1.0f, 1.0f, 1.0f };
    private const float STIGMATA_RANDOM_DELAY_MIN = 4.0f;
    private const float STIGMATA_RANDOM_DELAY_MAX = 8.0f;
    private const float STIGMATA_CLICK_COOLDOWN = 3.5f;
    private const int STIGMATA_CLICK_INDEX = 0;
    public bool IsTrapped => m_trappedTransform != null;
    public float m_trappedTransformWeight = 1.0f;

    public void SetTrapped(Transform _transform, GameObject _heldObject = null)
    {
        m_trappedTransform = _transform;
        m_trappedHeldObject = _heldObject;
        m_stigmataRandomDelay = UnityEngine.Random.Range(STIGMATA_RANDOM_DELAY_MIN, STIGMATA_RANDOM_DELAY_MAX);
        m_stigmataClickCooldown = STIGMATA_CLICK_COOLDOWN;
        SetInStigmataAnim(IsTrapped);
        m_trappedTransformWeight = IsTrapped ? 1.0f : 0.0f;
    }

    public void TriggerPartialReleaseAnim()
    {
        m_handAnimator.SetTrigger(m_stigmataPartialReleaseID);
    }

    public void SetInStigmataAnim(bool _isInStigmataAnim)
    {
        m_isInStigmataAnim = _isInStigmataAnim;
    }

    public void SetScarAmount(float _scarAmount, float _scarColourAmount)
    {
        _scarAmount = Mathf.Clamp01(_scarAmount);
        _scarColourAmount = Mathf.Clamp01(_scarColourAmount);

        GameManager.Me.m_state.m_handScarAmount = _scarAmount;
        GameManager.Me.m_state.m_handScarColourAmount = _scarColourAmount;

        if (m_handRenderer != null)
        {
            var mat = HandMainMaterial;

            if (mat != null)
            {
                mat.SetFloat("_ScarBlendAmount", _scarAmount);
                mat.SetFloat("_WoundColourAmount", _scarColourAmount);
            }
        }
    }

    private const float MIN_SCAR_FADE_VALUE = 0.0f;
    private const float SCAR_FADE_SPEED = 0.0001f;

    private void UpdateScarAmount()
    {
        if (GameManager.Me.m_state.m_handScarColourAmount > MIN_SCAR_FADE_VALUE && !IsTrapped)
        {
            GameManager.Me.m_state.m_handScarColourAmount -= SCAR_FADE_SPEED * Time.deltaTime;
            GameManager.Me.m_state.m_handScarColourAmount = Mathf.Clamp(GameManager.Me.m_state.m_handScarColourAmount, MIN_SCAR_FADE_VALUE, 1.0f);
        }

        SetScarAmount(GameManager.Me.m_state.m_handScarAmount, GameManager.Me.m_state.m_handScarColourAmount);
    }

    private bool UpdateTrapped()
    {
        if(m_trappedTransform != null)
        {
            if (m_trappedTransformWeight >= 1.0f)
            {
                m_handInstance.transform.position = m_trappedTransform.position;
                m_handInstance.transform.rotation = m_trappedTransform.rotation;
                m_handInstance.transform.localScale = m_trappedTransform.localScale;
            }
            else if(m_trappedTransformWeight > 0.0f)
            {
                m_handInstance.transform.position = Vector3.Lerp(m_handInstance.transform.position, m_trappedTransform.position, m_trappedTransformWeight);
                m_handInstance.transform.rotation = Quaternion.Lerp(m_handInstance.transform.rotation, m_trappedTransform.rotation, m_trappedTransformWeight);
                m_handInstance.transform.localScale = Vector3.Lerp(m_handInstance.transform.localScale, m_trappedTransform.localScale, m_trappedTransformWeight);
            }

            string holdingType = m_trappedHeldObject != null ? m_trappedHeldObject.GetComponent<Pickup>()?.m_handAnimationEvent : null;

            if (holdingType != null)
            {
                m_handAnimator.SetBool(holdingType, true);
                SetGripWidth(HoldTransform.Get(m_trappedHeldObject));
            }
            else
            {
                if (m_lastHoldingType != null)
                {
                    m_handAnimator.SetBool(m_lastHoldingType, false);
                }

                m_handAnimator.SetBool(m_stigmataID, m_isInStigmataAnim);

                if (m_isInStigmataAnim)
                {
                    m_stigmataRandomDelay -= Time.deltaTime;
                    m_stigmataClickCooldown -= Time.deltaTime;

                    bool isClicked = GameManager.GetMouseButtonDown(0);

                    if ((isClicked && m_stigmataClickCooldown <= 0.0f) || m_stigmataRandomDelay <= 0.0f)
                    {
                        m_stigmataRandomDelay = UnityEngine.Random.Range(STIGMATA_RANDOM_DELAY_MIN, STIGMATA_RANDOM_DELAY_MAX);
                        m_stigmataClickCooldown = STIGMATA_CLICK_COOLDOWN;

                        if(isClicked)
                        {
                            m_stigmataRandomDelay += STIGMATA_CLICK_COOLDOWN;
                        }

                        int randomAnim = isClicked ? STIGMATA_CLICK_INDEX : GetRandomStigmataAnim(UnityEngine.Random.Range(0.0f, 1.0f));

                        while(!isClicked && randomAnim == m_lastStigmataRandomAnim)
                        {
                            randomAnim = GetRandomStigmataAnim(UnityEngine.Random.Range(0.0f, 1.0f));
                        }

                        m_handAnimator.SetInteger(m_stigmataRandomID, randomAnim);
                        m_handAnimator.SetTrigger(m_stigmataRandomTriggerID);
                        m_lastStigmataRandomAnim = randomAnim;
                    }
                }
            }

            m_lastHoldingType = holdingType;

            return true;
        }

        m_handAnimator.SetBool(m_stigmataID, false);

        return false;
    }

    private void InitStigmataRandomWeights()
    {
        float totalWeight = 0.0f;

        foreach(var weight in m_stigmataRandomWeights)
        {
            totalWeight += weight;
        }

        float currentWeight = 0.0f;

        for(int i = 0; i < m_stigmataRandomWeights.Length; i++)
        {
            currentWeight += m_stigmataRandomWeights[i] / totalWeight; // do not set all weights to zero
            m_stigmataRandomWeights[i] = currentWeight;
        }
    }

    private int GetRandomStigmataAnim(float _random)
    {
        for (int i = 0; i < m_stigmataRandomWeights.Length; i++)
        {
            if(_random <= m_stigmataRandomWeights[i])
            {
                return i;
            }
        }

        return 0;
    }
}
