using System.Collections;
using UnityEngine;
using UnityEngine.Playables;

public class CryptManager : SubSceneManager<CryptManager>
{
    override public string SceneName => "Crypt";
    public Transform m_characterSpawn;
    public Transform m_characterReEnterSpawn;
    public Camera m_frescoCamera;
    public Transform m_introTapestry;
    public PlayableDirector[] m_frescos;
    public string[] m_frescoNames = {"Chapter_1_Intro"};
    private PlayableDirector m_currentFresco;
    private bool m_isInFrescoMode = false;
    private float m_frescoEndHold = 0;
    private string m_currentFrescoName = "";
    public PlayableDirector m_throneSequence;
    public GameObject m_fakeCharacter;
    public Transform m_failSequenceStart;
    public Transform m_failSequenceTarget;
    public UnityEngine.Video.VideoPlayer m_helmetSequence;
    
    private DebugConsole.Command s_startHelmetSequenceCmd = new ("playhelmetsequence", _s => Me.StartCoroutine(Me.Co_PlayHelmetSequence(null)));
    public IEnumerator Co_PlayHelmetSequence(System.Action _aboutToFadeOutCb)
    {
        if (m_helmetSequence.targetTexture != null) yield break; // already playing
        m_helmetSequence.url = System.IO.Path.Combine(Application.streamingAssetsPath, "Video/God_Helm_Cut_Scene_Concept.mp4");
        var rt = new RenderTexture(Screen.width, Screen.height, 0);
        m_helmetSequence.targetTexture = rt;
        m_helmetSequence.GetComponentInChildren<UnityEngine.UI.RawImage>().texture = rt;
        var cg = m_helmetSequence.GetComponentInChildren<CanvasGroup>();
        cg.alpha = 0;
        m_helmetSequence.gameObject.SetActive(true);
        bool fail = false;
        m_helmetSequence.errorReceived += (vp, error) => {
            Debug.LogError("Video error: " + error);
            fail = true;
        };
        m_helmetSequence.Play();

        const string c_skipLabel = "HelmetSequence";
        bool abort = false, abortExecuted = false;
        SkipManager.Me.EnableSkip(c_skipLabel, true, () => abort = true);
        
        const float c_fadeInDuration = 1;
        const float c_fadeOutDuration = 1;
        while ((m_helmetSequence.isPlaying == false || m_helmetSequence.length < .001) && fail == false)
            yield return null;
        var duration = (float) m_helmetSequence.length;
        float previousTime = 0;
        while (m_helmetSequence.frame < (long)m_helmetSequence.frameCount - 1)
        {
            if (m_helmetSequence.audioOutputMode != UnityEngine.Video.VideoAudioOutputMode.None)
                m_helmetSequence.SetDirectAudioVolume(0, AudioClipManager.Me.GetVOVolume());
            if (abort && abortExecuted == false && m_helmetSequence.time < duration - c_fadeOutDuration)
            {
                m_helmetSequence.time = duration - c_fadeOutDuration;
                abortExecuted = true; // seems that setting time doesn't update the time property immediately
            }
            var t = (float)m_helmetSequence.time;
            if ((t >= duration - c_fadeOutDuration && previousTime < duration - c_fadeOutDuration) || fail)
                _aboutToFadeOutCb?.Invoke();
            if (fail) break;
            var alpha = 1f;
            if (t < c_fadeInDuration) alpha = t / c_fadeInDuration;
            else if (t > duration - c_fadeOutDuration) alpha = (duration - t) / c_fadeOutDuration;
            cg.alpha = alpha;
            previousTime = t;
            yield return null;
        }
        SkipManager.Me.DebugEnableSkip(c_skipLabel, false);
        m_helmetSequence.gameObject.SetActive(false);
        Destroy(rt);
    }

    private DebugConsole.Command s_startCryptCmd = new("startfresco", _s => {
        if (int.TryParse(_s, out var index))
            Me.StartFresco(index);
        else
            Me.StartFresco(_s);
    });

    void Start()
    {
        gameObject.IgnoreDistrictFilterShared();
    }

    public static string Sanitise(string _s)
    {
        return _s.Replace("\x201c", "").Replace("\x201d", "");
    }

    private int LookupFrescoByName(string _s)
    {
        _s = Sanitise(_s);
        var index = System.Array.IndexOf(m_frescoNames, _s);
        if (index == -1)
        {
            Debug.LogError("Fresco not found: " + _s);
            index = 0;
        }
        return index;
    }
    
    public string CurrentFrescoName => m_currentFrescoName;

    public void StartFresco(string _name)
    {
        StartFresco(LookupFrescoByName(_name));
    }

    public void StartFresco(int _index)
    {
        if (m_isInFrescoMode) return;
        m_currentFrescoName = m_frescoNames[_index];
        StartFrescoMode();
        m_currentFresco = m_frescos[_index];
        m_frescoEndHold = 0;
        SkipManager.Me.EnableSkip(SceneName, true, SkipFresco);
        GameManager.Me.CloseSettings();
        Activate();
    }
    
    override public  bool ActivateComplete()
    {
        base.ActivateComplete();
        m_currentFresco.Play();
        return true;
    }

    void Update()
    {
        if (m_currentFresco != null)
        {
            if (m_currentFresco.time > m_currentFresco.duration - .1f)
            {
                const float c_frescoEndHoldTime = 1f;
                if (m_frescoEndHold < c_frescoEndHoldTime)
                {
                    m_frescoEndHold += Time.deltaTime;
                    if (m_frescoEndHold >= c_frescoEndHoldTime)
                        EndFresco();
                }
            }
        }
    }

    public void ShowCharacterOnThrone()
    {
        m_throneSequence.Play();
        m_throneSequence.time = m_throneSequence.duration - .5f;
        m_throneSequence.extrapolationMode = DirectorWrapMode.Hold;
    }

    const float c_skipToEnd = .5f; // show last n seconds on skip

    public void SkipFresco()
    {
        if (m_currentFresco.time < m_currentFresco.duration - c_skipToEnd)
            m_currentFresco.time = m_currentFresco.duration - c_skipToEnd;
        AlignmentManager.Me.ApplyAction("SkipFresco", -0.001f, m_currentFrescoName);
    }


    public bool IsFrescoActive() => m_isInFrescoMode && IsActiveOrFadingNonExit && m_frescoEndHold == 0;
    public bool IsFrescoActiveOrLeaving => m_isInFrescoMode && IsActiveOrFading;

    public void EndFresco()
    {
        bool isIntro = m_currentFresco == m_frescos[0];
        
        m_currentFresco.Stop();
        m_currentFresco = null;
        SkipManager.Me.EnableSkip(SceneName, false);

        if (isIntro)
            GlobalData.Me.StartCoroutine(Co_DeactivateIntro());
        else
            Deactivate();
    }
    
    IEnumerator Co_DeactivateIntro()
    {
        GameManager.Me.m_possessModeFreeze = 1.0f;
        var tapestry = m_introTapestry;
        var originalParent = tapestry.parent;
        var cam = Camera.main.transform;
        var camDest = GameManager.Me.m_camera.transform;
        tapestry.SetParent(cam, true);
        tapestry.SetParent(camDest, false);
        DeactivateScene(true);
        const float c_fadeSpeed = 1.0f / 4.0f;
        var rnds = tapestry.GetComponentsInChildren<Renderer>();
        for (float t = -.1f; t <= 1; )
        {
            t += Time.deltaTime * c_fadeSpeed;
            var alpha = Mathf.Clamp01(1 - t);
            GameManager.Me.m_possessModeFreeze = Mathf.Min(1.0f, alpha * 3);
            alpha = alpha * alpha * (3 - alpha - alpha);
            foreach (var rnd in rnds)
                foreach (var mat in rnd.materials)
                    mat.SetFloat("_AlphaMultiply", alpha);
            yield return null;
        }
        GameManager.Me.m_possessModeFreeze = 0.0f;
        tapestry.SetParent(cam, false);
        tapestry.SetParent(originalParent, true);
        foreach (var rnd in rnds)
            foreach (var mat in rnd.materials)
                mat.SetFloat("_AlphaMultiply", 1);
        EndFrescoMode();
    }

    void StartFrescoMode()
    {
        m_isInFrescoMode = true;
        AudioClipManager.Me.PlaySound("PlaySound_Fresco_ENTER", GameManager.Me.gameObject);
        AudioClipManager.Me.PlaySound($"PlaySound_{m_currentFrescoName}", GameManager.Me.gameObject);
    }

    void EndFrescoMode()
    {
        m_isInFrescoMode = false;
        AudioClipManager.Me.PlaySound("PlaySound_Fresco_EXIT", GameManager.Me.gameObject);
    }

    override public void DeactivateFinished()
    {
        EndFrescoMode();
    }

    private void EnableNonFresco(bool _enable)
    {
        m_fakeCharacter.SetActive(false);
        m_isInFrescoMode = false;
        m_frescoCamera.GetComponent<CameraRenderSettings>().enabled = !_enable;
        m_frescoCamera.GetComponent<AudioListener>().enabled = !_enable;
        m_frescoCamera.enabled = !_enable;
        m_root.SetActive(_enable);
    }

    public void EnterNonFresco() => EnableNonFresco(true);

    public void ExitNonFresco() => EnableNonFresco(false);
}
