using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Crossfade : MonoSingleton<Crossfade> {
	CanvasGroup m_canvasGroup;
	protected override void _Awake() {
		m_canvasGroup = GetComponent<CanvasGroup>();
		gameObject.SetActive(false);
	}

	private bool m_fadeInProgress = false;
	private bool m_fadeActive = false;
	private bool m_fadeQueued = false;
	public void Fade(System.Func<bool> _fadedOut, System.Action _fadeComplete) {
		gameObject.SetActive(true);
		StartCoroutine(Co_Fade(_fadedOut, _fadeComplete));
	}
	
	public float DT => Mathf.Min(.1f, Time.unscaledDeltaTime);
	
	const float c_fadeTime = .25f;
	IEnumerator Co_Fade(System.Func<bool> _fadedOut, System.Action _fadeComplete) {
		float t = 0;
		m_fadeActive = true;
		m_fadeQueued = true;
		var fadeIn = m_fadeInProgress == false;
		while (m_fadeInProgress) yield return null;
		m_fadeInProgress = true;
		m_fadeQueued = false;
		if (fadeIn && m_canvasGroup.alpha < .5f)
		{
			while (t < c_fadeTime)
			{
				t += DT;
				yield return null;
				m_canvasGroup.alpha = t / c_fadeTime;
			}
		}
		m_canvasGroup.alpha = 1f;
		for (int i = 0; i < 5; ++i) yield return null;
		if (m_fadeOverrideLock == false && m_fadeOverride > 0)
			EndFadeOverride();
		if (_fadedOut != null) while (_fadedOut() == false) yield return null;
		t = 0;
		m_fadeInProgress = false;
		if (m_fadeQueued)
		{
			_fadeComplete();
			m_fadeActive = false;
			yield break;
		}
		while (t < c_fadeTime)
		{
			if (m_fadeInProgress)
			{
				_fadeComplete();
				yield break;
			}
			t += DT;
			yield return null;
			m_canvasGroup.alpha = Mathf.Max(m_fadeOverrideValue, 1f - t / c_fadeTime);
		}
		_fadeComplete();
		if (m_fadeInProgress == false && m_fadeOverride < .001f)
			gameObject.SetActive(false);
		m_fadeActive = false;
	}

	private float m_fadeOverride = 0, m_fadeOverrideValue = 0;
	private bool m_fadeOverrideLock = false;
	public void StartFadeOverride(bool _instant = false, bool _lock = true)
	{
		m_fadeOverrideLock = _lock;
		m_fadeOverride = 1;
		if (_instant) m_fadeOverrideValue = 1;
		gameObject.SetActive(true);
		StartCoroutine(Co_FadeOverride());
	}
	public void EndFadeOverride()
	{
		StartCoroutine(Co_EndFadeOverride());
	}

	private IEnumerator Co_EndFadeOverride()
	{
		for (int i = 0; i < 4; ++i) yield return null;
		while (m_fadeOverride > .01f)
		{
			m_fadeOverride *= .9f;
			yield return null;
		}
		m_fadeOverride = 0;
	}

	private IEnumerator Co_FadeOverride()
	{
		while (true)
		{
			m_fadeOverrideValue = Mathf.Lerp(m_fadeOverrideValue, m_fadeOverride, .1f);
			if (m_fadeOverride < .1f && m_fadeOverrideValue < .001f)
				break;
			if (m_fadeActive == false) m_canvasGroup.alpha = m_fadeOverride;
			yield return null;
		}
		if (m_fadeActive == false)
			gameObject.SetActive(false);
	}

	public void SetFade(float _fade)
	{
		_fade = Mathf.Clamp01(_fade);
		m_canvasGroup.alpha = Mathf.Max(m_fadeOverrideValue, _fade);
		gameObject.SetActive(m_canvasGroup.alpha > 0.001f);
	}

	public void Unfade(float _speed = 2f)
	{
		if (gameObject.activeSelf)
		{
			m_canvasGroup.alpha = Mathf.Max(m_fadeOverrideValue, Mathf.Max(0, m_canvasGroup.alpha - Time.deltaTime * _speed));
			if (m_canvasGroup.alpha <= .001f)
				gameObject.SetActive(false);
		}
	}
}
