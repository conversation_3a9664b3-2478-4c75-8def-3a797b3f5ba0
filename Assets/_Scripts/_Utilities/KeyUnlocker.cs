using System.Collections.Generic;
using UnityEngine;

public class KeyUnlocker
{
	static Dictionary<string, List<KeyUnlockable>> s_keyUnlockables = new Dictionary<string, List<KeyUnlockable>>();

	static List<string> UnlockedKeys => GameManager.Me.m_state.m_keyUnlockableUnlockedKeys;

	public static void UnlockKey(string _key)
	{
		if (!_key.IsNullOrWhiteSpace() && !UnlockedKeys.Contains(_key))
		{
			UnlockedKeys.Add(_key);
			if (s_keyUnlockables.ContainsKey(_key))
			{
				var toUnlock = s_keyUnlockables[_key];
				for (int i = 0; i<toUnlock.Count; i++)
				{
					toUnlock[i].UnlockWithKey();
				}
			}
		}
	}

	public static bool IsKeyUnlocked(string _key)
	{
		if (_key.IsNullOrWhiteSpace())
		{
			return true;
		}

		return UnlockedKeys.Contains(_key);
	}

	public static void AddKeyUnlockabale(KeyUnlockable _ku)
	{
		if (!s_keyUnlockables.ContainsKey(_ku.m_unlockKey))
		{
			s_keyUnlockables.Add(_ku.m_unlockKey, new List<KeyUnlockable>());
		}
		s_keyUnlockables[_ku.m_unlockKey].Add(_ku);
	}

	public static void RemoveKeyUnlockabale(KeyUnlockable _ku)
	{
		s_keyUnlockables[_ku.m_unlockKey].Remove(_ku);
	}
}

public abstract class KeyUnlockable : MonoBehaviour
{
	void Awake()
	{
		KeyUnlocker.AddKeyUnlockabale(this);
	}

	void OnDestroy()
	{
		KeyUnlocker.RemoveKeyUnlockabale(this);
	}

	public string m_unlockKey;

	public abstract void UnlockWithKey();
}