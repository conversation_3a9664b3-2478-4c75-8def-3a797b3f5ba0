using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Animations;
using System.Collections;

[System.Serializable]
public class SyncedAnimations
{
    [System.Serializable]
    public class SyncedAnim
    {
        public Animator m_animator;
        public AnimationClip m_clip;
    }

    public SyncedAnim[] m_syncedAnims;
    public float m_fadeInTime = 0.5f;
    public float m_fadeOutTime = 0.5f;

    private class PlayableInfo
    {
        public PlayableGraph m_graph;
        public AnimationLayerMixerPlayable m_layerMixer;
        public AnimationClipPlayable m_clip;
        public bool m_isLooping;
        public double m_duration;
        public bool m_isStopping;

        public bool IsFinished()
        {
            return !m_isLooping && m_clip.GetTime() >= m_duration;
        }

        public void Stop()
        {
            m_isStopping = true;
        }
    }

    private PlayableInfo[] m_playableInfos = null;
    private int m_coroutineCount = 0;

    public void Play(MonoBehaviour _mono, bool _forceClear = false)
    {
        if (_forceClear)
        {
            Clear();
        }
        else if(m_coroutineCount > 0)
        {
            return;
        }

        m_coroutineCount = 0;
        m_playableInfos = new PlayableInfo[m_syncedAnims.Length];

        for (int i = 0; i < m_syncedAnims.Length; i++)
        {
            m_playableInfos[i] = new PlayableInfo();

            m_playableInfos[i].m_graph = PlayableGraph.Create($"Graph_{i}");
            m_playableInfos[i].m_graph.SetTimeUpdateMode(DirectorUpdateMode.GameTime);

            m_playableInfos[i].m_layerMixer = AnimationLayerMixerPlayable.Create(m_playableInfos[i].m_graph, 2);

            var playableOutput = AnimationPlayableOutput.Create(m_playableInfos[i].m_graph, $"Output_{i}", m_syncedAnims[i].m_animator);
            playableOutput.SetSourcePlayable(m_playableInfos[i].m_layerMixer);

            Playable animatorPlayable = AnimatorControllerPlayable.Create(m_playableInfos[i].m_graph, m_syncedAnims[i].m_animator.runtimeAnimatorController);
            m_playableInfos[i].m_graph.Connect(animatorPlayable, 0, m_playableInfos[i].m_layerMixer, 0);
            m_playableInfos[i].m_layerMixer.SetInputWeight(0, 1f);

            m_playableInfos[i].m_clip = AnimationClipPlayable.Create(m_playableInfos[i].m_graph, m_syncedAnims[i].m_clip);
            m_playableInfos[i].m_graph.Connect(m_playableInfos[i].m_clip, 0, m_playableInfos[i].m_layerMixer, 1);
            m_playableInfos[i].m_layerMixer.SetInputWeight(1, 0f);

            m_playableInfos[i].m_isLooping = m_syncedAnims[i].m_clip.isLooping;
            m_playableInfos[i].m_duration = m_syncedAnims[i].m_clip.length;

            m_playableInfos[i].m_graph.Play();

            _mono.StartCoroutine(Co_Update(m_playableInfos[i]));

            m_coroutineCount++;
        }
    }

    public void Stop()
    {
        if (m_playableInfos != null)
        {
            foreach (var playableInfo in m_playableInfos)
            {
                playableInfo.Stop();
            }
        }
    }

    private void Clear()
    {
        if (m_playableInfos != null)
        {
            foreach (var playableInfo in m_playableInfos)
            {
                if (playableInfo.m_graph.IsValid())
                {
                    playableInfo.m_graph.Stop();
                    playableInfo.m_graph.Destroy();
                }
            }
        }
    }

    private IEnumerator Co_Update(PlayableInfo _playableInfo)
    {
        float time = 0f;

        while (!_playableInfo.m_isStopping && time < m_fadeInTime)
        {
            float weight = time / m_fadeInTime;
            _playableInfo.m_layerMixer.SetInputWeight(0, 1f - weight); // Animator Controller layer
            _playableInfo.m_layerMixer.SetInputWeight(1, weight);      // Playable Clip layer
            time += Time.deltaTime;
            yield return null;
        }

        _playableInfo.m_layerMixer.SetInputWeight(0, 0f);
        _playableInfo.m_layerMixer.SetInputWeight(1, 1f);

        while (!_playableInfo.m_isStopping)
        {
            if (_playableInfo.IsFinished())
            {
                _playableInfo.Stop();  
            }

            yield return null;
        }

        time = 0f;

        while (time < m_fadeOutTime)
        {
            float weight = time / m_fadeOutTime;
            _playableInfo.m_layerMixer.SetInputWeight(0, weight);       // Animator Controller layer
            _playableInfo.m_layerMixer.SetInputWeight(1, 1f - weight);  // Playable Clip layer
            time += Time.deltaTime;
            yield return null;
        }

        _playableInfo.m_layerMixer.SetInputWeight(0, 1f);
        _playableInfo.m_layerMixer.SetInputWeight(1, 0f);

        if (_playableInfo.m_graph.IsValid())
        {
            _playableInfo.m_graph.Stop();
            _playableInfo.m_graph.Destroy();
        }

        m_coroutineCount--;
    }
}

