using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class OnBoardingChoiceHolderMgr : MonoSingleton<OnBoardingChoiceHolderMgr>
{
    public Transform[] m_holders;
    private int m_count = 0;

    private void Start()
    {
        m_count = 0;
    }

    public void ChooseRandom()
    {
        var choices = GetComponentsInChildren<NGTutorialChoice>();
        var choice = choices.PickRandom();
        if (choice != null) choice.ClickedButton();
    }
    
    void Update()
    {
        foreach(Transform child in transform)
        {
            if (child.name.Contains("ChoiceBalloonHolder"))
                continue;
            child.parent = m_holders[m_count];
            child.localPosition = Vector3.zero;
            m_count++;
        }
    }
}
