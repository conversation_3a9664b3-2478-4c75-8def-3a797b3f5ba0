using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class CSGCuboid {
	public Vector3 m_center;
	public Vector4 m_extents1, m_extents2, m_extents3;
}

public class CSGCuboids : MonoBehaviour {
	public Shader ReplacementShader { get; set; }
	Renderer[] m_renderers;
	Material[][] m_oldMaterials;
	Material[][] m_newMaterials;
	List<CSGCuboid> m_cuboidCutters = new List<CSGCuboid>();
	void Start() {
		Initialise();
	}
	void Initialise() {
		if (m_renderers != null) return;
		m_renderers = GetComponentsInChildren<Renderer>(true);
		Replace();
	}
	void OnDestroy() {
		Restore();
	}
	void Update() {
		SetCuboids();
	}

	public void SetCuboid(int _index, Vector3 _center, Vector4 _extent1, Vector4 _extent2, Vector4 _extent3) {
		while (_index >= m_cuboidCutters.Count) m_cuboidCutters.Add(new CSGCuboid());
		m_cuboidCutters[_index].m_center = _center;
		m_cuboidCutters[_index].m_extents1 = _extent1;
		m_cuboidCutters[_index].m_extents2 = _extent2;
		m_cuboidCutters[_index].m_extents3 = _extent3;
	}
	public void SetCuboid(int _index, Vector3 _center, Vector3 _dir1, float _extent1, Vector3 _dir2, float _extent2, Vector3 _dir3, float _extent3) {
		var extDir1 = new Vector4(_dir1.x,_dir1.y,_dir1.z, _extent1*_extent1);
		var extDir2 = new Vector4(_dir2.x,_dir2.y,_dir2.z, _extent2*_extent2);
		var extDir3 = new Vector4(_dir3.x,_dir3.y,_dir3.z, _extent3*_extent3);
		SetCuboid(_index, _center, extDir1, extDir2, extDir3);
	}

	//==========================
	
	private static string c_replacementKeyword = "CSGCUBOID_ADDITION"; 
	void Replace() {
		m_oldMaterials = new Material[m_renderers.Length][];
		m_newMaterials = new Material[m_renderers.Length][];
		for (int i = 0; i < m_renderers.Length; ++i) {
			var mats = m_renderers[i].sharedMaterials;
			var newMats = new Material[mats.Length];
			m_oldMaterials[i] = mats;
			for (int j = 0; j < mats.Length; ++j) {
				if (mats[j] == null) continue;
				newMats[j] = new Material(mats[j]);
				if (c_replacementKeyword != null && newMats[j].HasProperty("_cuboidCenter1")) newMats[j].EnableKeyword(c_replacementKeyword);
				else newMats[j].shader = ReplacementShader;
			}
			m_newMaterials[i] = newMats;
			m_renderers[i].materials = newMats;
		}
	}
	void Restore() {
		for (int i = 0; i < m_renderers.Length; ++i) {
			if (m_oldMaterials[i] == null) continue;
			if (m_renderers[i] == null) continue;
			//m_renderers[i].materials = null;
			m_renderers[i].materials = m_oldMaterials[i];
			m_renderers[i].sharedMaterials = m_oldMaterials[i];
		}
	}

	public void RestoreForBlock(GameObject _block)
	{
		foreach (var rnd in _block.GetComponentsInChildren<Renderer>(true))
		{
			int index = System.Array.IndexOf(m_renderers, rnd);
			if (index == -1) continue;
			if (m_oldMaterials[index] == null) continue;
			rnd.materials = m_oldMaterials[index];
			rnd.sharedMaterials = m_oldMaterials[index];
		}
	}

	public void SetCuboids() {
		for (int i = 0; i < m_renderers.Length; ++i) {
			for (int j = 0; j < m_newMaterials[i].Length; ++j) {
				for (int k = 0; k < m_cuboidCutters.Count; k ++) {
					var m = m_newMaterials[i][j];
					if (m == null) continue;
					m.SetVector("_cuboidCenter" + (k + 1), m_cuboidCutters[k].m_center);
					m.SetVector("_cuboidExtents" + (k + 1) + "a", m_cuboidCutters[k].m_extents1);
					m.SetVector("_cuboidExtents" + (k + 1) + "b", m_cuboidCutters[k].m_extents2);
					m.SetVector("_cuboidExtents" + (k + 1) + "c", m_cuboidCutters[k].m_extents3);
					m.SetFloat("_ClipCoord", -1000f);
					m.SetInt("_Cull", 0);
				}
			}
		}
	}

	//==========================

	public static CSGCuboids CreateCutter(GameObject _root) {
		var cutter = _root.AddComponent<CSGCuboids>();
		if(GlobalData.Me)
			cutter.ReplacementShader = GlobalData.Me.m_CSGCuboidShader;
		cutter.Initialise();
		return cutter;
	}

	public static CSGCuboids CreateCutter(GameObject _root, Shader _shader)
	{
		var cutter = _root.AddComponent<CSGCuboids>();
		cutter.ReplacementShader = _shader;
		cutter.Initialise();
		return cutter;
	}
}
