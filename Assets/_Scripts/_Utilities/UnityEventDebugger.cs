using UnityEngine;

public class UnityEventDebugger : MonoBehaviour
{
    public string m_label;
    void Awake() => Debug.LogError($"UED {m_label} Awake");
    void Start() => Debug.LogError($"UED {m_label} Start");
    void OnEnable() => Debug.LogError($"UED {m_label} OnEnable");
    void OnDisable() => Debug.LogError($"UED {m_label} OnDisable");
    void OnDestroy() => Debug.LogError($"UED {m_label} OnDestroy");
}
