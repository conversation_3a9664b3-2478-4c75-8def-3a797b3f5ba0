using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Spinner3D : MonoBehaviour
{
    public float m_spinSpeed;
    private Vector3 m_eulers;

    public void Show(Vector3 _initialEulers, float _spinSpeed)
    {
        m_spinSpeed = _spinSpeed;
        m_eulers = _initialEulers;
    }
    void Update()
    {
        float v = m_eulers.z;
        v += m_spinSpeed * Time.deltaTime;
        if (v > 180) v -= 360;
        else if (v < -180) v += 360;
        m_eulers.z = v;
        transform.localEulerAngles = m_eulers;
    }

    public static Spinner3D Create(Transform _parent, Vector3 _offset, Vector3 _initialEulers, float _speed, float _localScale)
    {
        var go = Instantiate(GlobalData.Me.m_spinner3DPrefab, _parent);
        var s = go.GetComponent<Spinner3D>();
        go.transform.localPosition = _offset;
        go.transform.localScale = Vector3.one * _localScale;
        s.Show(_initialEulers, _speed);
        return s;
    }
}
