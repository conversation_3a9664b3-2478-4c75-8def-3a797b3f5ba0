using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEngine;
using UnityEditor;
[CustomEditor(typeof(DebugBC))]
public class DebugBCEditor : Editor
{
	public static void ShowBCs(Transform _t)
	{
		GUIStyle style = new GUIStyle(GUI.skin.label);
		style.richText = true;
		GUILayout.Label("\n<color=#ffff80><b>BCs in object:</b></color>", style);
		List<(string, int)> toOutput = new();
		int lastDepth = 0;
		List<(Transform, int)> stack = new();
		int numNonTriggers = 0;
		stack.Add((_t, 0));
		while (stack.Count > 0)
		{
			var (t, depth) = stack[^1];
			stack.RemoveAt(stack.Count - 1);
			var bcs = t.gameObject.GetComponents<BCBase>();
			if (bcs.Length == 0)
			{
				while (toOutput.Count > 0 && depth < toOutput[^1].Item2)
					toOutput.RemoveAt(toOutput.Count - 1);
				toOutput.Add((t.name, depth));
			}
			else
			{
				for (int i = 0; i < toOutput.Count; ++i)
				{
					var (to, deptho) = toOutput[i];
					GUILayout.Label($"{new string(' ', deptho * 2)} {to}", style);
				}
				toOutput.Clear();
				var s = new string(' ', depth * 2);
				s += $"{t.name}  ";
				foreach (var bc in bcs)
				{
					var enabled = bc.enabled && bc.gameObject.activeInHierarchy;
					s += enabled ? "<color=#8080ff>" : "<color=#ff8080>";
					s += bc.GetType().Name;
					s += " </color>";
				}
				if (GUILayout.Button(s, style)) Selection.activeGameObject = t.gameObject;
			}
			for (int i = t.childCount - 1; i >= 0; --i)
				stack.Add((t.GetChild(i), depth + 1));
		}
	}

	public override void OnInspectorGUI()
    {
        if (target is MonoBehaviour m)
            ShowBCs(m.transform);
    }
}
public class DebugBC : MonoBehaviour
{
}
#endif
