using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Playables;
using UnityEngine.Timeline;

// Attach this component to the PlayableDirector in order to correctly setup the pauses
[RequireComponent(typeof(PlayableDirector))]
public class TimelinePausesManager : MonoBehaviour
{
    public SignalAsset m_pauseSignalAsset;
    private PlayableDirector m_director;
    private List<SignalEmitter> m_markers;

    private void Awake()
    {
        m_director = GetComponent<PlayableDirector>();

        StartCoroutine(Co_LateInitialise());
    }

    private IEnumerator Co_LateInitialise()
    {
        // Wait for the graph to be built
        yield return new WaitUntil(() => m_director.playableGraph.IsValid() && m_director.playableGraph.GetRootPlayable(0).IsValid());

        m_markers = GetAllMarkers();

        //Debug.Log($"TimelinePausesManager: Co_LateInitialise() _markers.Count: {m_markers.Count}");

        if (m_markers.Count > 0)
            SetupPauses();
    }

    private void SetupPauses()
    {
        // Update timeline duration now and on any play/pause events
        //UpdateTimelineDuration();
        m_director.played += _ => UpdateTimelineDuration();
        m_director.paused += _ => UpdateTimelineDuration();

        // Make sure that the signal receiver exists
        var signalReceiver = GetComponent<SignalReceiver>();
        if (signalReceiver == null)
            signalReceiver = gameObject.AddComponent<SignalReceiver>();

        // Pause the timeline automatically on each pause marker
        foreach (var marker in m_markers)
        {
            var reaction = signalReceiver.GetReaction(marker.asset);
            if (reaction == null)
            {
                reaction = new UnityEvent();
                signalReceiver.AddReaction(marker.asset, reaction);
                reaction.AddListener(m_director.Pause);
            }
        }
    }

    private void UpdateTimelineDuration()
    {
        // Find the next pause marker that will be hit
        var nextMarker = m_markers
            .Where(m => m.time > m_director.time)
            .FirstOrDefault();

        // Force the timeline duration to end exactly on that marker
        // (or to the original duration if there are no pause markers ahead)
        var nextMarkerTime = nextMarker != null ? nextMarker.time : m_director.duration;
        //Debug.Log($"TimelinePausesManager: nextMarkerTime: {nextMarkerTime + 0.0001f}");
        //Debug.Log($"TimelinePausesManager: Before SetDuration: {m_director.playableGraph.GetRootPlayable(0).GetDuration()}");
        m_director.playableGraph.GetRootPlayable(0).SetDuration(nextMarkerTime + 0.0001f);//Make sure it plays slightly past next marker
        //Debug.Log($"TimelinePausesManager: After SetDuration: {m_director.playableGraph.GetRootPlayable(0).GetDuration()}");
    }

    /// Find all markers of a specified type
    private List<SignalEmitter> GetAllMarkers()
    {
        List<SignalEmitter> markers = new();

        var timeline = m_director.playableAsset as TimelineAsset;

        if (timeline == null)
        {
            Debug.Log($"TimelinePausesManager: GetAllMarkers timeline is null");
            return markers;
        }

        //Debug.Log($"TimelinePausesManager: GetAllMarkers: MarkerCount: {timeline.markerTrack.GetMarkerCount()}");

        foreach (var track in timeline.GetOutputTracks())
        {
            if (track is MarkerTrack markerTrack)
            {
                //Debug.Log($"TimelinePausesManager: Found marker track: {track.name}");
                foreach (var marker in markerTrack.GetMarkers())
                {
                    //Debug.Log($"TimelinePausesManager: Marker: {marker.GetType().Name}");

                    if (marker is SignalEmitter tMarker)
                    {
                        if (tMarker.asset == m_pauseSignalAsset)
                        {
                            //Debug.Log($"TimelinePausesManager: GetAllMarkers marker added");
                            markers.Add(tMarker);
                        }
                        //else
                        //{
                        //    Debug.Log($"TimelinePausesManager: GetAllMarkers not pause signal asset");
                        //}
                    }
                    //else
                    //{
                    //    Debug.Log($"TimelinePausesManager: GetAllMarkers marker is not correct type");
                    //}
                }
            }
        }

        return markers;
    }
}
