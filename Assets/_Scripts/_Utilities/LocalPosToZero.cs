using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class LocalPosToZero : MonoBehaviour//rename to clamp
{
    private Vector3 m_constantLocalPos = Vector3.zero;
    private Quaternion m_constantLocalRotation = Quaternion.identity;
    private Transform m_transform = null;
    
    private void Awake()
    {
        m_transform = transform;    
        m_constantLocalPos = m_transform.localPosition;
        m_constantLocalRotation = m_transform.localRotation;
    }

    private void Update()
    {
        m_transform.localPosition = m_constantLocalPos;
        m_transform.localRotation = m_constantLocalRotation;
    }
    
    private void LateUpdate()
    {
        m_transform.localPosition = m_constantLocalPos;
        m_transform.localRotation = m_constantLocalRotation;
    }
}
