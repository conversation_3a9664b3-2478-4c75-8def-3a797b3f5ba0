using UnityEngine;

public class DisplayCharAnimEventReceiver : Mono<PERSON>ehaviour
{
    public MADisplayCreature m_character;
    public Animator m_animator;

    private void OnEnable()
    {
        m_character = GetComponentInParent<MADisplayCreature>();
        m_animator = GetComponent<Animator>();
    }

		public void PlayVFX(AnimationEvent _event)
		{
			//m_character.PlayAnimVFX(_event.stringParameter, _event.objectReferenceParameter as GameObject);
		}

    public void AttackImpact(AnimationEvent _event)
    {
        // RW-04-JUN-25: If we're in the testing room scene, m_character will be null because they're not proper characters.
        // I did consider removing the anim events from the anims instead, but I figured it'd allow more flexibility if I
        // just null-checked here.
       /* if (m_character == null)
        {
            return;
        }

        m_character.m_onCollisionWithTarget?.Invoke();
		if (_event.objectReferenceParameter != null)
		{
			m_character.PlayAnimVFX(_event.stringParameter, _event.objectReferenceParameter as GameObject);
		}

		// RW-04-FEB-25: I'm stopping the weapon glow here because that works for the only 
		// attack we've currently got that uses it. If more flexibility is required, it could
		// have its own anim event.
		m_character.StopWeaponGlow();*/
    }
	public void ShowProjectile()
	{
		//m_character.m_onShowProjectile?.Invoke();
	}

	public void FireProjectile(AnimationEvent _event)
    {
        //m_character.m_onRangeAttackFired?.Invoke();
    }

	public void StartWeaponTrail(AnimationEvent _event)
	{
		// RW-14-MAR-25: If the attack is interrupted, we explicitly stop the weapon trail. However, there's
		// a bit of a vulnerability, since while the anim's blending out, StartWeaponTrail could still be called _after_
		// the explicit stop has been done. If we're blending, don't start the weapon trail. Of course, we could be blending 
		// _into_ the attack anim and this would be <1, but it would be desirable to start the trail. However, I don't think
		// we currently have any anims that trigger the trail that early. We'll cross that bridge when we come to it.
		/*if (_event.animatorClipInfo.weight < 1)
		{
			return;
		}

		m_character.StartWeaponTrail(_event.stringParameter);*/
	}

	public void StopWeaponTrail(string _boneName)
	{
		//m_character.StopWeaponTrail(_boneName);
	}

	public void TurnEnded(int direction)
	{
		/*var actualEnd = m_animator.GetFloat("TurnSpeed").Sign() == direction;
		
		if (actualEnd)
			m_character.OnTurnFinished();*/
	}
	
	public void ApplyCameraShake()
	{
		/*if (GameManager.Me.PossessedCharacter == m_character)
		{
			GameManager.Me.PlayCameraShake();
		}*/
	}

	public void StopActionCam()
	{
		/*if (GameManager.Me.PossessedCharacter == m_character)
		{
			GameManager.Me.ToggleActionCamTracking(false);
		}*/
	}

	public void StartSlowmo(float _duration)
	{
		/*if (GameManager.Me.PossessedCharacter == m_character)
		{
			GameManager.Me.PlayBigAttackSlowMotion(_duration);
		}*/
	}
}
