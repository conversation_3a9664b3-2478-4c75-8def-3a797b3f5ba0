
using System;
public class LocalEditorSettings : MonoSingleton<LocalEditorSettings>
{
#if UNITY_EDITOR
    // Important: all members must be matched with an equivalent in the #else case, ideally with arrow notation so it drops out at compile-time
    public bool m_testTransactionPayload = false;
    public bool m_transactionPayloadTrackOperations = false;
    public bool m_measureNetworkApiRate = false;
#else
    public bool m_testTransactionPayload => false;
    public bool m_transactionPayloadTrackOperations => false;
    public bool m_measureNetworkApiRate => false;
#endif
}
