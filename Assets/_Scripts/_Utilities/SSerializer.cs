using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using System.Reflection;

/*
 SSerializer
 A simple serializer designed to produce results that can be safely embedded in Json and other formats
 Individual fields can be given custom import/export functions
 Fields or whole classes can be marked for serialization
 Encodes JSon control characters and non-ascii characters in strings to make sure strings can be embedded
 Supports Lists, Arrays and Dictionaries
 
 Usage:
 * Mark classes to be completely serialized with [SaveAll]
 * Mark fields to be serialized in non-SaveAll classes with [Save]
 * Mark fields to be omitted in SaveAll classes with [NoSave]
 * Mark fields with custom serialization behaviour with [SaveTransform("XYZ")]
   - then add functions ImportXYZ and ExportXYZ
   - functions can be static (in which case ImportXYZ takes a string and returns a value, ExportXYZ takes a value and returns a string)
    or non-static (in which case ImportXYZ takes a string and fills the value, ExportXYZ takes no parameter and returns a string
 * You can also set global data transforms using SSerialize.AddGlobalTypeTransform(type, class, function) with static ImportXX and ExportXX functions
 * Alternatively, use [Serializable] as normal to mark that a class should serialize all public or [SerializeField] fields 
 * To Serialize simply call SSerializer.Serialize(objectToSerialize)
 * To Deserialize over an existing object call SSerializer.DeserializeInPlace(object objectToReplace, string _serializedData)
 * To Deserialize to a new object call SSerializer.Deserialize<T>(string _serializedData) where T is the type of the data to be deserialized
 * There is also a string extension str.PrettySS() which converts the serialized output into a more readable format
     (this function takes an optional "compactness" value, roughly the max length of lines allowed to be combined onto a single line) 
 * You can check if a string is an SS serialized object with the static function SSerializer.IsObject(str)
 * You can check if a string is an SS serialized array of objects with the static function SSerializer.IsArray(str)
 * You can check if a string is SS serialized with the static function SSerializer.IsSSerialized(str)
 */

public class SBString
{
   private System.Text.StringBuilder m_builder = new();

   public static implicit operator SBString(String _s)
   {
      return new SBString() + _s;
   }

   public static implicit operator String(SBString _s)
   {
      return _s.ToString();
   }

   public static SBString operator +(SBString _this, String _s)
   {
      _this.m_builder.Append(_s);
      return _this;
   }

   public static SBString operator +(SBString _this, char _c)
   {
      _this.m_builder.Append(_c);
      return _this;
   }

   internal int Length => m_builder.Length;
   internal char this[int index] => m_builder[index];

   public override String ToString() => m_builder.ToString();
}

[AttributeUsage(AttributeTargets.Class)]
public class SaveAllAttribute : PropertyAttribute { }
[Flags]
public enum ESaveFlags
{
   None = 0x0,
   NoNulls = 0x1, // for list/dictionary/array, don't add null values
}
public class SaveAttribute : PropertyAttribute
{
   public ESaveFlags Flags;

   public SaveAttribute()
   {
      Flags = ESaveFlags.None;
   }

   public SaveAttribute(ESaveFlags _flags)
   {
      Flags = _flags;
   }
}
public class NoSaveAttribute : PropertyAttribute { }
public class SaveTransformAttribute : PropertyAttribute
{
   public string Function, Class;

   public SaveTransformAttribute(string _fn, string _class = null)
   {
      Function = _fn;
      Class = _class;
   }

   public object Apply(bool _isImport, FieldInfo _field, object _obj, object[] _params)
   {
      string prefix = _isImport ? "Import" : "Export";
      var rootType = _obj.GetType();
      if (Class != null)
         rootType = Type.GetType(Class);
      var method = rootType.GetMethod($"{prefix}{Function}", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);
      if (method == null) return null;
      if (method.IsStatic && _isImport == false)
         _params = new object[] {_obj};
      var res = method.Invoke(_obj, _params);
      if (method.IsStatic && _isImport)
         _field.SetValue(_obj, res);
      return res;
   }

   public object Apply(bool _isImport, object _obj, object[] _params, Type _type = default)
   {
      string prefix = _isImport ? "Import" : "Export";
      var rootType = _obj == null ? _type : _obj.GetType();
      if (Class != null)
         rootType = Type.GetType(Class);
      var method = rootType.GetMethod($"{prefix}{Function}", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);
      if (method == null) return null;
      if (method.IsStatic && _isImport == false)
         _params = new object[] {_obj};
      var res = method.Invoke(_obj, _params);
      return res;
   }
   
   public object ApplyStaticImport(string _s)
   {
      var rootType = Type.GetType(Class);
      var method = rootType.GetMethod($"Import{Function}", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);
      if (method == null) return null;
      return method.Invoke(null, new object[] { _s });
   }

   public string ApplyStaticExport(object _o)
   {
      var rootType = Type.GetType(Class);
      var method = rootType.GetMethod($"Export{Function}", BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Static);
      if (method == null) return null;
      return method.Invoke(null, new object[] {_o}) as string;
   }
}

public static class SSerializerExt
{
   public static bool HasStringAt(this string _this, int _at, string _str)
   {
      var thisLen = _this.Length;
      var len = _str.Length;
      for (int i = 0; i < len; ++i)
      {
         if (_at + i >= thisLen)
            return false;
         if (_this[_at + i] != _str[i])
            return false;
      }
      return true;
   }

   public static string PrettySS(this string _this, int _maxSingleLineContent = 48)
   {
      var bits = _this.Split(':', StringSplitOptions.RemoveEmptyEntries);
      SBString s = "";
      string indent = "", nextIndent = "";
      int keepOnLineUntil = -1;
      var squares = new List<int>();
      var curlies = new List<int>();
      var bitStates = new int[bits.Length];
      for (int i = 0; i < bits.Length; ++i)
      {
         var isOnLine = i <= keepOnLineUntil;
         var cr = isOnLine ? " " : "\n";

         if (keepOnLineUntil < i && bits[i] == "<" || bits[i] == "[")
         {
            string match = bits[i];
            string endMatch = (match == "<") ? ">" : "]";
            int numOpens = 1, totalLength = 0;
            for (int j = i + 1; j < bits.Length; ++j)
            {
               if (bits[j] == match)
               {
                  ++numOpens;
               }
               else if (bits[j] == endMatch)
               {
                  --numOpens;
                  if (numOpens == 0)
                  {
                     if (totalLength < _maxSingleLineContent)
                     {
                        keepOnLineUntil = j;
                        isOnLine = true;
                        cr = "";
                     }
                     break;
                  }
               }
               totalLength += bits[j].Length;
            }
         }
         
         switch (bits[i])
         {
            case "<":
               if (isOnLine == false)
                  s += $"{cr}{indent}";
               s += $"{{{cr}";
               indent += "  ";
               nextIndent = indent;
               bitStates[i] = 1 | (isOnLine ? 2 : 0);
               break;
            case ">":
               if (string.IsNullOrEmpty(nextIndent) == false) s += cr;
               indent = indent[0..^2];
               if (isOnLine == false) s += indent;
               s += $"}}{cr}";
               nextIndent = indent;
               break;
            case "=":
               s += " = ";
               break;
            case ".":
               nextIndent = cr + indent;
               break;
            case "[":
               s += "[";
               indent += "  ";
               if (isOnLine == false)
                  nextIndent = cr + indent;
               squares.Add(i);
               bitStates[i] = 1 | (isOnLine ? 2 : 0);
               break;
            case "]":
               indent = indent[0..^2];
               if (isOnLine == false && bitStates[squares[^1] + 1] != 3)
               {
                  if (s[^1] != '\n') s += "\n";
                  s += indent;
               }
               s += "]";
               squares.RemoveAt(squares.Count - 1);
               break;
            default:
               if (isOnLine == false)
                  s += nextIndent;
               s += SSerializer.Inst.DecodeString(bits[i]);
               nextIndent = "";
               break;
         }
      }
      return s;
   }
   
}

public class SSerializer
{
   public static SSerializer Inst = new();
   
   const string c_objectStart = ":<:";
   const string c_objectType = ":T:";
   const string c_objectEnd = ":>:";
   const string c_fieldSep = ":=:";
   const string c_fieldEnd = ":.:";
   const string c_arrayStart = ":[:";
   const string c_arrayEnd = ":]:";
   
   public static bool IsSSerialized(string _s) => _s.IsNullOrWhiteSpace() == false && (IsObject(_s) || IsArray(_s));
   public static bool IsObject(string _s) => _s.StartsWith(c_objectStart) || _s.StartsWith(c_objectType);
   public static bool IsArray(string _s) => _s.StartsWith(c_arrayStart);

   void EncodeChr(char _c, System.Text.StringBuilder _out)
   {
      if ((_c >= 'A' && _c <= 'Z') || (_c >= 'a' && _c <= 'z') || (_c >= '0' && _c <= '9') || _c == '_' || _c == ' ')
         _out.Append(_c);
      else
      {
         var unicode = (int) _c;
         _out.Append('[');
         AppendInt(unicode, _out);
         _out.Append(']');
      }
   }

   void EncodeString(string _s, System.Text.StringBuilder _out)
   {
      for (int i = 0; i < _s.Length; ++i)
         EncodeChr(_s[i], _out);
   }

   public string DecodeString(string _s)
   {
      SBString res = "";
      for (int i = 0; i < _s.Length; ++i)
      {
         char c = _s[i];
         if (c != '[')
            res += c;
         else
         {
            int index = _s.IndexOf(']', i + 1);
            int unicode = StringToInt(_s[(i + 1)..(index)]);
            res += (char) unicode;
            i = index;
         }
      }
      return res;
   }

   void EncodeArray(IList _list, System.Text.StringBuilder _sb, bool _forceSerializeAll)
   {
      _sb.Append(c_arrayStart);
      int count = _list.Count;
      for (int i = 0; i < count; ++i)
         SerializeValueWithTransform(_list[i], _sb, true, _forceSerializeAll);
      //foreach (var obj in _list)
      //   SerializeValueWithTransform(obj, _sb, true, _forceSerializeAll);
      _sb.Append(c_arrayEnd);
   }

   object SafeCreateInstance(Type _type, object _onFail = null)
   {
      if (_type.IsSubclassOf(typeof(UnityEngine.MonoBehaviour))) return _onFail;
      if (_type == typeof(string)) return "";
      try
      {
         var obj = Activator.CreateInstance(_type);
         if (obj == null) return _onFail;
         if (obj.GetHashCode() == 0 && obj.ToString() == "null") return _onFail;
         return obj;
      }
      catch (Exception _e)
      {
         return _onFail;
      }
   }

   IList DecodeList(Type _listType, string _s, ref int _next, ESaveFlags _flags)
   {
      var list = SafeCreateInstance(_listType) as IList;
      _next += 3;
      if (_s.HasStringAt(_next-3, c_fieldEnd))
         return list;
      var entryType = _listType.GetGenericArguments()[0];
      while (_s[_next + 1] != c_arrayEnd[1])
      {
         var o = DeserializeValueWithTransform(entryType, _s, ref _next, ESaveFlags.None);
         if (o != null || (_flags & ESaveFlags.NoNulls) != ESaveFlags.NoNulls)
            list.Add(o);
      }
      _next += 3 + 3;
      return list;
   }

   object DecodeArray(Type _type, string _s, ref int _next, ESaveFlags _flags)
   {
      var memberType = _type.GetElementType();
      var entries = new List<object>();
      _next += 3;
      while (_s[_next + 1] != c_arrayEnd[1])
      {
         var o = DeserializeValueWithTransform(memberType, _s, ref _next, ESaveFlags.None);
         if (o != null || (_flags & ESaveFlags.NoNulls) != ESaveFlags.NoNulls)
            entries.Add(o);
      }
      _next += 3 + 3; // end array, end sep

      var array = Array.CreateInstance(memberType, entries.Count);
      for (int i = 0; i < entries.Count; ++i)
         array.SetValue(entries[i], i);
      return array;
   }

   void EncodeDictionary(IDictionary _dic, System.Text.StringBuilder _sb, bool _forceSerializeAll)
   {
      _sb.Append(c_arrayStart);
      foreach (var key in _dic.Keys)
      {
         SerializeValueWithTransform(key, _sb, true, _forceSerializeAll);
         _sb.Append(c_fieldSep);
         SerializeValueWithTransform(_dic[key], _sb, true, _forceSerializeAll);
         _sb.Append(c_fieldEnd);
      }
      _sb.Append(c_arrayEnd);
   }

   IDictionary DecodeDictionary(Type _dictType, string _s, ref int _next, ESaveFlags _flags)
   {
      var dic = SafeCreateInstance(_dictType) as IDictionary;
      _next += 3;
      var entryType1 = _dictType.GetGenericArguments()[0];
      var entryType2 = _dictType.GetGenericArguments()[1];
      while (_s[_next + 1] != c_arrayEnd[1])
      {
         var ok = DeserializeValueWithTransform(entryType1, _s, ref _next, ESaveFlags.None);
         _next += 3;
         var ov = DeserializeValueWithTransform(entryType2, _s, ref _next, ESaveFlags.None);
         _next += 3;
         if ((ok != null && ov != null) || (_flags & ESaveFlags.NoNulls) != ESaveFlags.NoNulls)
            dic[ok] = ov;
      }
      _next += 3 + 3;
      return dic;
   }

   bool ApplyExportTransform(FieldInfo _field, object _obj, System.Text.StringBuilder _sb)
   {
	  if (_obj == null)
		return false;
      var res = ApplyTransform(false, _field, _obj);
      if (res == null) return false;
      var export = res as string;
      if (export == null) return false;
      EncodeString(export, _sb);
      _sb.Append(c_fieldEnd);
      return true;
   }

   bool ApplyImportTransform(FieldInfo _field, object _obj, string _s, ref int _next)
   {
      int peekNext = _next;
      var value = DecodeString(NextFieldString(_s, ref peekNext));
      var res = ApplyTransform(true, _field, _obj, new object[] { value });
      if (res == null) return false;
      _next = peekNext;
      return true;
   }

   bool ApplyExportTransform(object _obj, System.Text.StringBuilder _sb)
   {
      var res = ApplyTransform(false, _obj);
      if (res == null) return false;
      var export = res as string;
      if (export == null) return false;
      EncodeString(export, _sb);
      _sb.Append(c_fieldEnd);
      return true;
   }

   bool ApplyImportTransform(ref object _obj, string _s, ref int _next, Type _type)
   {
      int peekNext = _next;
      var value = DecodeString(NextFieldString(_s, ref peekNext));
      var res = ApplyTransform(true, _obj, new object[] {value}, _type);
      if (res == null) return false;
      _next = peekNext;
      _obj = res;
      return true;
   }
   
   Dictionary<Type, SaveTransformAttribute> s_globalTransforms = new();
   HashSet<Type> s_noGlobalTransforms = new();

   public SaveTransformAttribute GetGlobalTypeTransform(Type _type, bool _allowBaseClasses = true)
   {
		Type type = _type;
      if (s_noGlobalTransforms.Contains(_type))
         return null;
		while (type != null)
		{
         if (s_globalTransforms.TryGetValue(type, out var trans))
         {
            if (type != _type)
               s_globalTransforms[_type] = trans; // found a base class transform, add it to the lookup for the subclass for faster lookup next time
            return trans;
         }
         if (_allowBaseClasses == false)
            break;
         type = type.BaseType;
		}
      s_noGlobalTransforms.Add(_type);
      return null;
   }
   public bool HasGlobalTypeTransform(Type _type, bool _allowBaseClasses) => GetGlobalTypeTransform(_type, _allowBaseClasses) != null;
   public static Dictionary<Type, SaveTransformAttribute> GlobalTypeTransforms => Inst.s_globalTransforms;

   public static void ClearGlobalTypeTransforms()
   {
      Inst.s_globalTransforms.Clear();
      Inst.s_noGlobalTransforms.Clear();
   }

   public static void AddGlobalTypeTransform(Type _type, string _class, string _function)
   {
      if (Inst.HasGlobalTypeTransform(_type, false))
      {
         Debug.LogError($"Adding a global type transform for type {_type.Name} with an existing transform");
         return;
      }
      Inst.s_globalTransforms[_type] = new SaveTransformAttribute(_function, _class);
      if (Inst.s_noGlobalTransforms.Contains(_type))
         Inst.s_noGlobalTransforms.Remove(_type);
   }

   object ApplyTransform(bool _isImport, FieldInfo _field, object _obj, object[] _params = null)
   {
      var transform = LookupSaveTransformAttribute(_field);
		if (transform == null)
		{
			transform = GetGlobalTypeTransform(_field.FieldType);
			if (transform == null)
				return null;
		}
		return transform.Apply(_isImport, _field, _obj, _params);
   }

   object ApplyTransform(bool _isImport, object _obj, object[] _params = null, Type _type = default)
   {
      //if (_obj == null) return null;
      var type = _obj == null ? _type : _obj.GetType();
      var transform = LookupSaveTransformAttribute(type);
		if (transform == null)
		{
			transform = GetGlobalTypeTransform(type);
			if (transform == null)
				return null;
		}
      return transform.Apply(_isImport, _obj, _params, _type);
   }

#if false
   Dictionary<Type, SaveAllAttribute> s_saveAllLookup = new();
   Dictionary<Type, SerializableAttribute> s_serializableLookup = new();
   Dictionary<FieldInfo, SerializableAttribute> s_serializableFieldLookup = new();
   Dictionary<FieldInfo, UnityEngine.SerializeField> s_serializeFieldFieldLookup = new();
   Dictionary<Type, SaveAttribute> s_saveLookup = new();
   Dictionary<FieldInfo, SaveAttribute> s_saveFieldLookup = new();
   Dictionary<FieldInfo, NoSaveAttribute> s_noSaveLookup = new();
   Dictionary<Type, SaveTransformAttribute> s_saveTransformLookup = new();
   Dictionary<FieldInfo, SaveTransformAttribute> s_saveTransformFieldLookup = new();
   
   public void DumpStats()
   {
      Debug.LogError($"SS Stats: all:{s_saveAllLookup.Count} ser:{s_serializableLookup.Count} serf:{s_serializableFieldLookup.Count} sff:{s_serializeFieldFieldLookup.Count} save:{s_saveLookup.Count} savef:{s_saveFieldLookup.Count} nosave:{s_noSaveLookup.Count} savet:{s_saveTransformLookup.Count} savetf:{s_saveTransformFieldLookup.Count}");
   }
   
   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   T TypeLookup<T>(Type _type, Dictionary<Type, T> _dict) where T : System.Attribute
   {
      if (!_dict.TryGetValue(_type, out var attr))
      {
         attr = _type.GetCustomAttribute<T>();
         _dict[_type] = attr;
      }
      return attr;
   }

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   T TypeLookup<T>(FieldInfo _type, Dictionary<FieldInfo, T> _dict) where T : System.Attribute
   {
      if (!_dict.TryGetValue(_type, out var attr))
      {
         attr = _type.GetCustomAttribute<T>();
         _dict[_type] = attr;
      }
      return attr;
   }

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SaveAllAttribute LookupSaveAllAttribute(Type _type) => TypeLookup(_type, s_saveAllLookup);

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SerializableAttribute LookupSerializableAttribute(Type _type) => TypeLookup(_type, s_serializableLookup);

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SerializableAttribute LookupSerializableAttribute(FieldInfo _type) => TypeLookup(_type, s_serializableFieldLookup);

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SerializeField LookupSerializeFieldAttribute(FieldInfo _type) => TypeLookup(_type, s_serializeFieldFieldLookup);

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SaveAttribute LookupSaveAttribute(Type _type) => TypeLookup(_type, s_saveLookup);

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SaveAttribute LookupSaveAttribute(FieldInfo _type) => TypeLookup(_type, s_saveFieldLookup);

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   NoSaveAttribute LookupNoSaveAttribute(FieldInfo _type) => TypeLookup(_type, s_noSaveLookup);

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SaveTransformAttribute LookupSaveTransformAttribute(Type _type) => TypeLookup(_type, s_saveTransformLookup);

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SaveTransformAttribute LookupSaveTransformAttribute(FieldInfo _type) => TypeLookup(_type, s_saveTransformFieldLookup);
#else   
   Dictionary<Type, int> s_typeLookup = new();
   Type s_lastTypeLookupType;
   int s_lastTypeLookupIndex = -1;
   Dictionary<FieldInfo, int> s_fieldLookup = new();
   FieldInfo s_lastFieldInfoLookupType;
   int s_lastFieldInfoLookupIndex = -1;
   
   public class SSTypeOrFieldInfo
   {
      public SaveAllAttribute m_saveAll;
      public SerializableAttribute m_serializable;
      public SerializableAttribute m_serializableField;
      public SerializeField m_serializeField;
      public SaveAttribute m_save;
      public SaveAttribute m_saveField;
      public NoSaveAttribute m_noSave;
      public SaveTransformAttribute m_saveTransform;
      public SaveTransformAttribute m_saveTransformField;

      public SSTypeOrFieldInfo(Type _type)
      {
         m_saveAll = _type.GetCustomAttribute<SaveAllAttribute>();
         m_serializable = _type.GetCustomAttribute<SerializableAttribute>();
         m_serializeField = _type.GetCustomAttribute<SerializeField>();
         m_save = _type.GetCustomAttribute<SaveAttribute>();
         m_saveTransform = _type.GetCustomAttribute<SaveTransformAttribute>();
      }

      public SSTypeOrFieldInfo(FieldInfo _type)
      {
         m_serializableField = _type.GetCustomAttribute<SerializableAttribute>();
         m_serializeField = _type.GetCustomAttribute<SerializeField>();
         m_saveField = _type.GetCustomAttribute<SaveAttribute>();
         m_noSave = _type.GetCustomAttribute<NoSaveAttribute>();
         m_saveTransformField = _type.GetCustomAttribute<SaveTransformAttribute>();
      }
   }
   List<SSTypeOrFieldInfo> s_typeOrFieldInfo = new();
   
   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SSTypeOrFieldInfo GetSSTypeOrFieldInfo(Type _type)
   {
      var id = _type;
      if (id == s_lastTypeLookupType) return s_typeOrFieldInfo[s_lastTypeLookupIndex];
      if (s_typeLookup.TryGetValue(id, out var index) == false)
      {
         index = s_typeOrFieldInfo.Count;
         s_typeLookup[id] = index;
         s_typeOrFieldInfo.Add(new SSTypeOrFieldInfo(_type));
      }
      s_lastTypeLookupType = id;
      s_lastTypeLookupIndex = index;
      return s_typeOrFieldInfo[index];
   }

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SSTypeOrFieldInfo GetSSTypeOrFieldInfo(FieldInfo _type)
   {
      var id = _type;
      if (id == s_lastFieldInfoLookupType) return s_typeOrFieldInfo[s_lastFieldInfoLookupIndex];
      if (s_fieldLookup.TryGetValue(id, out var index) == false)
      {
         index = s_typeOrFieldInfo.Count;
         s_fieldLookup[id] = index;
         s_typeOrFieldInfo.Add(new SSTypeOrFieldInfo(_type));
      }
      s_lastFieldInfoLookupType = id;
      s_lastFieldInfoLookupIndex = index;
      return s_typeOrFieldInfo[index];
   }

   public void DumpStats()
   {
      Debug.LogError($"SS Stats: types {s_typeOrFieldInfo.Count}");
   }

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SaveAllAttribute LookupSaveAllAttribute(Type _type) => GetSSTypeOrFieldInfo(_type).m_saveAll;

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SerializableAttribute LookupSerializableAttribute(Type _type) => GetSSTypeOrFieldInfo(_type).m_serializable;
   
   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SaveTransformAttribute LookupSaveTransformAttribute(Type _type) => GetSSTypeOrFieldInfo(_type).m_saveTransform;

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SerializableAttribute LookupSerializableAttribute(FieldInfo _type) => GetSSTypeOrFieldInfo(_type).m_serializableField;

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SerializeField LookupSerializeFieldAttribute(FieldInfo _type) => GetSSTypeOrFieldInfo(_type).m_serializeField;

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SaveAttribute LookupSaveAttribute(FieldInfo _type) => GetSSTypeOrFieldInfo(_type).m_saveField;

   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   NoSaveAttribute LookupNoSaveAttribute(FieldInfo _type) => GetSSTypeOrFieldInfo(_type).m_noSave;
   
   [System.Runtime.CompilerServices.MethodImpl(System.Runtime.CompilerServices.MethodImplOptions.AggressiveInlining)]
   SaveTransformAttribute LookupSaveTransformAttribute(FieldInfo _type) => GetSSTypeOrFieldInfo(_type).m_saveTransformField;   
#endif

   private void TestFloat(float _f)
   {
      var sb = m_builder;
      sb.Clear();
      AppendFloat(_f, sb);
      var s1 = sb.ToString();
      sb.Clear();
      sb.Append(_f.ToString("R", System.Globalization.CultureInfo.InvariantCulture));
      var s2 = sb.ToString();
      if (s1 != s2)
         Debug.LogError($"Float mismatch {_f} {s1} {s2}");
      //else
      //   Debug.LogError($"Float match {s1}");
   }

   public void TestFloats()
   {
      TestFloat(1.234f);
      TestFloat(12.234f);
      TestFloat(123.234f);
      TestFloat(0.0123f);
      TestFloat(0.00123f);
      TestFloat(0.000123f);
      TestFloat(0.000099999f);
      TestFloat(0.0000123f);
      TestFloat(0.00000123f);
      TestFloat(1000.000001f);
      TestFloat(1.234e5f);
      TestFloat(1.234e6f);
      TestFloat(9.234e6f);
      TestFloat(9.999e6f);
      TestFloat(1.0e7f);
      TestFloat(1.234e7f);
      TestFloat(1.234e8f);
      TestFloat(1.234e12f);
      TestFloat(1.234e38f);
   }

   Dictionary<Type, FieldInfo[]> s_fieldInfoLookup = new();
   FieldInfo[] GetTypeFields(Type _type)
   {
      if (!s_fieldInfoLookup.TryGetValue(_type, out var fields))
      {
         var fieldList = _type.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance).ToList();
         ExtractBaseTypes(_type.BaseType, fieldList);
         fields = fieldList.ToArray();
         s_fieldInfoLookup[_type] = fields;
      }
      return fields;
   }
   
   private void ExtractBaseTypes(Type _type, List<FieldInfo> _fieldList)
   {
      if(_type == null || _type == typeof(MonoBehaviour) || _type == typeof(UnityEngine.Object) || 
         _type == typeof(System.Object) || _type == typeof(Behaviour) || _type == typeof(Component)) 
         return;
      
      var baseTypes = _type.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.DeclaredOnly);
      
      for (int i = 0; i < baseTypes.Length; ++i)
      {
         var pbt = baseTypes[i];
         if(_fieldList.Contains(pbt) == false)
            _fieldList.Add(pbt);
      }
      
      ExtractBaseTypes(_type.BaseType, _fieldList);
   }

   private Dictionary<Type, bool> s_isAlwaysSerializeNamespace = new();
   private bool IsAlwaysSerializeNamespace(Type _type)
   {
      if (!s_isAlwaysSerializeNamespace.TryGetValue(_type, out var isAlwaysSerializeNamespace))
      {
         var nspace = _type.Namespace;
         isAlwaysSerializeNamespace = nspace != null && s_alwaysSerializeNamespace.Contains(nspace);
         s_isAlwaysSerializeNamespace[_type] = isAlwaysSerializeNamespace;
         if (nspace != null && nspace.StartsWith("System") == false && nspace.StartsWith("TMPro") == false && isAlwaysSerializeNamespace == false) Debug.LogError($"Namespace consideration {nspace}");
      }
      return isAlwaysSerializeNamespace;
   }

   HashSet<string> s_alwaysSerializeNamespace = new();
   public static void ClearAlwaysSerializeNamespaces() => Inst.s_alwaysSerializeNamespace.Clear();
   public static void AddAlwaysSerializeNamespace(string _s) => Inst.s_alwaysSerializeNamespace.Add(_s);
   
   bool IsSubclass(Type _type)
   {
      // for some reasonable value of "subclass"
      var baseType = _type.BaseType;
      if (baseType == null) return false;
      if (baseType == typeof(ValueType)) return false;
      if (baseType == typeof(System.Object)) return false;
      if (baseType == typeof(UnityEngine.Object)) return false;
      if (HasPublicValuelessConstructor(baseType) == false) return false;
      return true;
   }

   /*[UnityEditor.MenuItem("22Cans/Test")]
   void Test()
   {
      //var test = new ConfigurableJointFixerProperties();
      var test = Camera.main.gameObject;
      Debug.LogError($"Test {Serialize(test, true).PrettySS()}");
   }*/

   void SerializeObject(object _o, System.Text.StringBuilder _sb, bool _forceSerialize, bool _forceSerializeAll)
   {
      var type = _o.GetType();
      var fields = GetTypeFields(type);
      bool includeAll = _forceSerialize || _forceSerializeAll || LookupSaveAllAttribute(_o.GetType()) != null || IsAlwaysSerializeNamespace(type);
      bool includeSerializable = LookupSerializableAttribute(_o.GetType()) != null;

      if (IsSubclass(type))
      {
         _sb.Append(c_objectType);
         _sb.Append(type.FullName);
      }
      _sb.Append(c_objectStart);

      for (int i = 0; i < fields.Length; ++i)
      {
         var f = fields[i];
         if (f.IsLiteral || f.IsStatic) continue;
         bool hasSaveAttribute = LookupSaveAttribute(f) != null || LookupSaveTransformAttribute(f) != null;
         if (includeSerializable)
            if (LookupSerializeFieldAttribute(f) == null && LookupSerializableAttribute(f) == null && f.IsPublic == false && hasSaveAttribute == false)
               continue;
         if (includeSerializable == false)
         {
            if (includeAll == false && hasSaveAttribute == false) continue;
            if (includeAll == true && LookupNoSaveAttribute(f) != null) continue;
         }

         var value = f.GetValue(_o);
         if (_forceSerializeAll || HasPublicValuelessConstructor(f.FieldType))
         {
            _sb.Append(f.Name); _sb.Append(c_fieldSep);
            bool isAlwaysSerializeNamespace = IsAlwaysSerializeNamespace(f.FieldType);
            SerializeValue(f, value, _sb, hasSaveAttribute && isAlwaysSerializeNamespace, _forceSerializeAll);
         }
      }
      _sb.Append(c_objectEnd);
   }

   void SerializeValue(FieldInfo _typeInfo, object _value, System.Text.StringBuilder _sb, bool _forceSerializeChildren, bool _forceSerializeAll)
   {
      if (ApplyExportTransform(_typeInfo, _value, _sb) == false)
         SerializeValue(_value, _sb, _forceSerializeChildren, _forceSerializeAll);
   }

   void SerializeValueWithTransform(object _value, System.Text.StringBuilder _sb, bool _forceSerializeChildren, bool _forceSerializeAll)
   {
      if (ApplyExportTransform(_value, _sb) == false)
         SerializeValue(_value, _sb, _forceSerializeChildren, _forceSerializeAll);
   }
   

   Dictionary<Type, bool> s_hasValuelessConstructorLookup = new();
   bool HasPublicValuelessConstructor(Type _type)
   {
      if (_type.IsArray) return true;
      if (!s_hasValuelessConstructorLookup.TryGetValue(_type, out var b))
      {
         b = _type == typeof(string) || _type.IsValueType || _type.GetConstructor(Type.EmptyTypes) != null;
         s_hasValuelessConstructorLookup[_type] = b;
      }
      return b;
   }

   bool HasPublicOrPrivateValuelessConstructor(Type _type)
   {
      return _type == typeof(string) || _type.IsValueType || _type.GetConstructor(BindingFlags.Instance | BindingFlags.Public | BindingFlags.NonPublic, null, Type.EmptyTypes, null) != null;
   }

   void AppendInt(long _v, System.Text.StringBuilder _sb)
   {
      if (_v < 0)
      {
         _sb.Append('-');
         _v = -_v;
      }
      AppendUInt((ulong)_v, _sb);
   }

   char[] m_intBuffer = new char[64];
   void AppendUInt(ulong _v, System.Text.StringBuilder _sb)
   {
      if (_v == 0)
      {
         _sb.Append('0');
         return;
      }
      int digits = 0;
      while (_v != 0)
      {
         m_intBuffer[digits++] = (char)('0' + (_v % 10));
         _v /= 10;
      }
      for (int i = digits - 1; i >= 0; --i)
         _sb.Append(m_intBuffer[i]);
   }

   void AppendFloat(double _v, System.Text.StringBuilder _sb)
   {
      const double c_epsilon = .00001;
      if (_v < 0)
      {
         _sb.Append('-');
         _v = -_v;
      }
      int exp = 0;
      if (_v >= 1e7)
      {
         while (_v >= 10)
         {
            ++exp;
            _v *= .1;
         }
      }
      else if (_v > 0 && _v < 1e-4)
      {
         while (_v < 1)
         {
            --exp;
            _v *= 10;
         }
      }
      int digits = 1;
      while (_v >= 10)
      {
         _v *= .1;
         ++digits;
      }
      int nextIndex = 0;
      int postZeroDigits = 0;
      bool hadNonZero = false;
      const int c_maxSignificantDigits = 6;
      bool finished = false, haveDot = false;
      while (digits > 0 || (!finished &&_v * _v >= c_epsilon * c_epsilon))
      {
         if (digits-- == 0)
         {
            m_intBuffer[nextIndex++] = '.'; //_sb.Append('.');
            haveDot = true;
         }
         int thisDigit = (int)_v;
         _v -= thisDigit;
         _v *= 10;
         if (thisDigit > 0) hadNonZero = true;
         if (hadNonZero) ++postZeroDigits;
         if (haveDot && postZeroDigits > c_maxSignificantDigits)
         {
            finished = true;
            if (_v >= 5)
            {
               ++thisDigit;
               if (thisDigit == 10)
               {
                  thisDigit = 0;
                  for (int j = nextIndex - 1; j >= 0; --j)
                  {
                     if (m_intBuffer[j] == '.') continue;
                     var c = (char) (m_intBuffer[j] + 1);
                     if (c > '9')
                     {
                        c = '0';
                        if (j == 0) _sb.Append('1');
                        m_intBuffer[j] = c;
                     }
                     else
                     {
                        m_intBuffer[j] = c;
                        break;
                     }
                  }
               }
            }
         }
         char outVal = (char) ('0' + thisDigit);
         m_intBuffer[nextIndex++] = outVal;//_sb.Append(outVal);
      }
      if (haveDot)
      {
         while (m_intBuffer[nextIndex - 1] == '0')
            --nextIndex;
         if (m_intBuffer[nextIndex - 1] == '.')
            --nextIndex;
      }
      for (int i = 0; i < nextIndex; ++i)
         _sb.Append(m_intBuffer[i]);
      if (exp != 0)
      {
         _sb.Append('E');
         if (exp > 0)
            _sb.Append('+');
         else
         {
            _sb.Append("-");
            exp = -exp;
         }
         if (exp < 10) _sb.Append('0');
         AppendInt(exp, _sb);
      }
   }

   void SerializeValue(object _value, System.Text.StringBuilder _sb, bool _forceSaveChildren = false, bool _forceSerializeAll = false)
   {
      if (_value == null)
         ;
      else if (_value is int ivalue)
         AppendInt(ivalue, _sb);
      else if (_value is long lvalue)
         AppendInt(lvalue, _sb);
      else if (_value is short svalue)
         AppendInt(svalue, _sb);
      else if (_value is sbyte sbvalue)
         AppendInt(sbvalue, _sb);
      else if (_value is uint uivalue)
         AppendUInt(uivalue, _sb);
      else if (_value is ulong ulvalue)
         AppendUInt(ulvalue, _sb);
      else if (_value is ushort usvalue)
         AppendUInt(usvalue, _sb);
      else if (_value is byte ubvalue)
         AppendUInt(ubvalue, _sb);
      else if (_value is bool bvalue)
         _sb.Append(bvalue ? "True" : "False");
      else if (_value is string str)
         EncodeString(str, _sb);
      else if (_value is float fval)
         AppendFloat(fval, _sb);//_sb.Append(fval.ToString("R", System.Globalization.CultureInfo.InvariantCulture));
      else if (_value is double dval)
         AppendFloat(dval, _sb); //_sb.Append(Convert.ToDouble(_value).ToString("R", System.Globalization.CultureInfo.InvariantCulture));
      else if (_value is decimal dcval)
         AppendFloat((double)dcval, _sb); //_sb.Append(Convert.ToDouble(_value).ToString("R", System.Globalization.CultureInfo.InvariantCulture));
      else if (_value is IList lst)
         EncodeArray(lst, _sb, _forceSerializeAll);
      else if (_value is IDictionary dic)
         EncodeDictionary(dic, _sb, _forceSerializeAll);
      else if (_value is char vchr)
         EncodeChr(vchr, _sb);
      else if (_value is Enum)
         _sb.Append($"{_value}");
      else if (_forceSerializeAll || HasPublicValuelessConstructor(_value.GetType())) // only serialize objects that have a valueless constructor otherwise we'll throw an exception on deserialize 
         SerializeObject(_value, _sb, _forceSaveChildren, _forceSerializeAll);
      else
         Debug.LogError($"Skipping element {_value} of type {_value.GetType().Name} since it has no valueless constructor");
      _sb.Append(c_fieldEnd);
   }

   System.Text.StringBuilder m_builder = new(4096);

   public static string Serialize(object _o, bool _forceSerializeEverything = false)
   {
      return Inst.SerializeInternal(_o, _forceSerializeEverything);
   }

   private string SerializeInternal(object _o, bool _forceSerializeEverything = false)
   {
      m_builder.Clear();
      SerializeValueWithTransform(_o, m_builder, false, _forceSerializeEverything);
      var res = m_builder.ToString();
      m_builder.Clear();
      return res;
   }

   public float StringToFloat(string _s, float _default = 0)
   {
      if (float.TryParse(_s, System.Globalization.NumberStyles.Float, System.Globalization.CultureInfo.InvariantCulture, out var value)) return value;
      return _default;
      //return _s.ToFloatInv(_default);
   }

   public int StringToInt(string _s, int _default = 0)
   {
      return int.TryParse(_s, out var v) ? v : _default;
   }

   string NextFieldString(string _s, ref int _next, bool _peek = false)
   {
      var endSep = _s.IndexOf(':', _next);
      var valString = _s[_next..endSep];
      _next = endSep + 3;
      return valString;
   }

   public static void DeserializeInPlace(object _o, string _s)
   {
      int next = 0;
      Inst.DeserializeObject(_o, _s, ref next);
   }

   public static T Deserialize<T>(string _s)
   {
      int next = 0;
      return (T)Inst.DeserializeValueWithTransform(typeof(T), _s, ref next, ESaveFlags.None);
   }

   private object DeserializeObject(object _o, string _s, ref int _next)
   {
      var nextOnEntry = _next;
      var typeOverride = ExtractObjectType(_s, ref _next);
      if (typeOverride != null)
         _o = SafeCreateInstance(Type.GetType(typeOverride), _o);
      _next += 3;
      while (_s.HasStringAt(_next, c_objectEnd) == false)
      {
         int nextSep = _s.IndexOf(':', _next);
         var field = _s[_next..nextSep];
         _next = nextSep + 3;
         
         FieldInfo fieldInfo = null;
         var fields = GetTypeFields(_o.GetType());
         for (int i = 0; i < fields.Length; ++i)
         {
            var f = fields[i];
            if(f.Name.Equals(field) == false) continue;
            fieldInfo = f;
            break;
         }
         
         if (fieldInfo == null)
         {
            // couldn't find this field, attempt to recover by skipping past the data
			   Debug.LogError($"Field {field} could not be found in class {_o?.GetType()}, this will lead to problems loading save data");
            SkipPastField(_s, ref _next);
         }
         else
         {
            var fieldType = fieldInfo?.FieldType;
            var flags = LookupSaveAttribute(fieldInfo)?.Flags ?? ESaveFlags.None;
            fieldInfo.SetValue(_o, DeserializeValueWithTransform(fieldType, _s, ref _next, flags));
         }
      }
      _next += 3 + 3;
      return _o;
   }

   void SkipPastField(string _s, ref int _next)
   {
      int arrayCount = 0, objCount = 0;
      while (true)
      {
         _next = _s.IndexOf(':', _next);
         if (_next == -1) break; // error! things are going to go bad
         char type = _s[_next + 1];
         _next += 3;
         if (type == c_fieldEnd[1])
         {
            if (arrayCount == 0 && objCount == 0)
               break;
         }
         else if (type == c_arrayStart[1])
            ++arrayCount;
         else if (type == c_arrayEnd[1])
            --arrayCount;
         else if (type == c_objectStart[1])
            ++objCount;
         else if (type == c_objectEnd[1])
            --objCount;
      }
   }
   
   object DeserializeValueWithTransform(Type _type, string _s, ref int _next, ESaveFlags _flags)
   {
      object obj = null;
      if (ApplyImportTransform(ref obj, _s, ref _next, _type) == false)
      {
         bool isArray = _type.IsArray;
         if (isArray)
         {
            var elementType = _type.GetElementType();
            _type = typeof(List<>).MakeGenericType(elementType);
         }
         obj = SafeCreateInstance(_type);
         if (obj == null)
         {
            SkipPastField(_s, ref _next);
            return null;
         }
         obj = DeserializeValue(_type, _s, ref _next, _flags);
         if (isArray)
         {
            var toArrayMethod = _type.GetMethod("ToArray");
            obj = toArrayMethod.Invoke(obj, null);
         }
      }
      return obj;
   }

   string ExtractObjectType(string _s, ref int _next)
   {
      if (_s.HasStringAt(_next, c_objectType))
      {
         _next += 3;
         var typeEnd = _s.IndexOf(c_objectStart, _next);
         var typeOverride = _s[_next..typeEnd];
         _next = typeEnd;
         return typeOverride;
      }
      return null;
   }

   object DecodeEnum(Type _type, string _s)
   {
      return Enum.Parse(_type, _s);
   }
   object DeserializeValue(Type _type, string _s, ref int _next, ESaveFlags _flags)
   {
      var typeOverride = ExtractObjectType(_s, ref _next);
      if (typeOverride != null) _type = Type.GetType(typeOverride) ?? _type;
      if (_type.IsGenericType && _type.GetGenericTypeDefinition() == typeof(List<>))
         return DecodeList(_type, _s, ref _next, _flags);
      if (_type.IsArray)
         return DecodeArray(_type, _s, ref _next, _flags);
      if (_type.IsGenericType && _type.GetGenericTypeDefinition() == typeof(Dictionary<,>))
         return DecodeDictionary(_type, _s, ref _next, _flags);
      if (_s.HasStringAt(_next, c_objectStart))
         return DeserializeObject(SafeCreateInstance(_type), _s, ref _next);
      var valString = NextFieldString(_s, ref _next);
      if (_type == typeof(float) || _type == typeof(double) || _type == typeof(decimal))
         return StringToFloat(valString);
      if (_type == typeof(int) || _type == typeof(short) || _type == typeof(long) || _type == typeof(sbyte) || _type == typeof(ushort) || _type == typeof(uint) || _type == typeof(ulong) || _type == typeof(byte))
         return StringToInt(valString);
      if (_type == typeof(string))
         return DecodeString(valString);
      if (_type == typeof(char))
         return DecodeString(valString)[0];
      if (_type == typeof(bool))
         return valString == "True";
      if (_type.IsEnum)
         return DecodeEnum(_type, valString);
      return null;
   }
}
