using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(DynamicRope))]
public class WhipController : MonoBehaviour
{
    private enum WhipState
    {
        None = 0,
        Coil,
        SoftenRope,
        Uncoil,
        Snap
    }

    private DynamicRope whip = null;
    public DynamicRope Whip
    {
        get { return whip; }
    }

    private List<Rigidbody> kinematicBodies = null;
    private List<Rigidbody> softBodies = null;

    private WhipState currentState = WhipState.None;
    private WhipState nextState = WhipState.None;

    [SerializeField]
    private float uncoilDuration = 3.0f;
    private float uncoilElapsedTime = 0.0f;

    private float snapDuration = 0.0f;
    private float snapElapsedTime = 0.0f;
    private Transform snapTarget = null;
    private Action onSnapHit = null;
    private Action onSnapFinish = null;

    private Transform ropeHolder = null;

    private float snapStep1 = 0.0f;
    private float snapStep2 = 0.0f;
    private float snapStep3 = 0.0f;
    
    [SerializeField]
    private float softenRopeDuration = 0.25f;
    private float softenRopeElapsedTime = 0.0f;

    private Vector3 snapUpDir = Vector3.zero;
    private Vector3 snapBackDir = Vector3.zero;

    public static WhipController CreateWhipController(GameObject prefab, Transform holder)
    {
        var rope = DynamicRope.Create(prefab.GetComponent<DynamicRope>(), null, null, holder.position);
        var whipController = rope.gameObject.GetComponent<WhipController>();
        whipController.SetupWhip(rope, holder);

        return whipController;
    }

    public void SetupWhip(DynamicRope rope, Transform holder)
    {
        whip = rope;
        whip.FirstBody.isKinematic = false;
        whip.LastBody.isKinematic = false;
        
        ropeHolder = holder;
        transform.rotation = holder.rotation;
        transform.position = holder.position;
        nextState = WhipState.Coil;
    }

    public void FixedUpdate()
    {
        bool hasChanged = nextState != currentState;
        if (hasChanged)
        {
            currentState = nextState;
        }

        if (currentState == WhipState.Coil)
        {
            if (hasChanged)
            {
                foreach (var body in whip.RopeBodies)
                {
                    body.linearVelocity = Vector3.zero;
                    body.angularVelocity = Vector3.zero;
                }
            }
            
            CoilWhip();

            nextState = WhipState.SoftenRope;
        }
        else if (currentState == WhipState.SoftenRope)
        {
            if (hasChanged)
            {
                softenRopeElapsedTime = 0.0f;
            }

            if (softenRopeElapsedTime >= softenRopeDuration)
            {
                SoftenWhip();

                nextState = WhipState.None;
            }
            else
            {
                softenRopeElapsedTime += Time.fixedDeltaTime;
            }
        }
        else if (currentState == WhipState.Uncoil)
        {
            if (hasChanged)
            {
                uncoilElapsedTime = 0.0f;
            }

            UncoilWhip();

            if (uncoilElapsedTime >= uncoilDuration)
            {
                nextState = WhipState.Snap;
            }
            else
            {
                uncoilElapsedTime += Time.fixedDeltaTime;
            }
        }
        else if (currentState == WhipState.Snap)
        {
            if (hasChanged)
            {
                snapElapsedTime = 0.0f;
                SetupSnap();
            }

            bool snapCompleted = SnapWhip();
            if (snapCompleted)
            {
                snapTarget = null;
                onSnapHit = null;
                onSnapFinish = null;

                nextState = WhipState.Coil;
            }
        }
    }

    public void LateUpdate()
    {
        if (ropeHolder == null)
        {
            return;
        }

        transform.position = ropeHolder.position;

        Quaternion rotation = transform.localRotation;
        Vector3 forward = ropeHolder.forward;
        if (snapTarget != null)
        {
            forward = snapTarget.position - transform.position;
        }
        rotation.SetLookRotation(forward.normalized, transform.up);
        transform.localEulerAngles = new Vector3(transform.localEulerAngles.x, rotation.eulerAngles.y, transform.localEulerAngles.z);
    }
    
    public void CoilWhip()
    {
        if (whip == null)
        {
            return;
        }

        kinematicBodies = new List<Rigidbody>();
        softBodies = new List<Rigidbody>();

        Rigidbody[] ropeBodies = whip.RopeBodies;
        int totalSegments = whip.SegmentsCount;
        int bendSegments = 2;
        float segmentWidth = whip.segmentRadius * 2.0f;
        float segmentLength = whip.SegmentLength;

        int horizontalSegments = Mathf.FloorToInt(whip.m_bonesPerSegment) + 1;
        int startIndex = totalSegments - 1;
        int endIndex = startIndex - horizontalSegments;
        float startAngle = -90.0f;
        //-->
        for (int i = startIndex; (i >= 0) && (i >= endIndex); --i)
        {
            var body = ropeBodies[i];
            body.isKinematic = true;

            body.transform.localRotation = Quaternion.Euler(new Vector3(startAngle, 0.0f, 0.0f));
            body.transform.localPosition = new Vector3(0.0f, -(segmentWidth * 0.0f), segmentLength * (startIndex - i));
        }
        // |
        //\ /
        int verticalSegments = 3;
        startIndex = endIndex - bendSegments - 1;
        endIndex = startIndex - verticalSegments;
        startAngle += 90.0f;
        for (int i = startIndex; (i >= 0) && (i >= endIndex); --i)
        {
            var body = ropeBodies[i];
            softBodies.Add(body);
            body.isKinematic = true;

            body.transform.localRotation = Quaternion.Euler(new Vector3(startAngle, 0.0f, 0.0f));
            body.transform.localPosition = new Vector3(0.0f,
                                            (-segmentLength * (startIndex - i)) - (segmentLength * bendSegments),
                                            segmentLength * (horizontalSegments + (bendSegments * 0.8f)));
        }
        //<--
        horizontalSegments = 2;
        startIndex = endIndex - bendSegments - 1;
        endIndex = startIndex - horizontalSegments;
        startAngle += 90.0f;
        for (int i = startIndex; (i >= 0) && (i >= endIndex); --i)
        {
            var body = ropeBodies[i];
            softBodies.Add(body);
            body.isKinematic = true;

            body.transform.localRotation = Quaternion.Euler(new Vector3(startAngle, 0.0f, 0.0f));
            body.transform.localPosition = new Vector3(0.0f,
                                            -segmentLength * (verticalSegments + (bendSegments * 1.7f)),
                                            (-segmentLength * (startIndex - i)) + (segmentLength * 4.2f));
        }
        // / \
        //  |
        --verticalSegments;
        startIndex = endIndex - bendSegments - 1;
        endIndex = startIndex - verticalSegments;
        startAngle += 90.0f;
        for (int i = startIndex; (i >= 0) && (i >= endIndex); --i)
        {
            var body = ropeBodies[i];
            softBodies.Add(body);
            body.isKinematic = true;

            body.transform.localRotation = Quaternion.Euler(new Vector3(startAngle, 0.0f, 0.0f));
            body.transform.localPosition = new Vector3(0.0f,
                                            (segmentLength * (startIndex - i)) - (segmentLength * (verticalSegments + (bendSegments * 1.0f))),
                                            segmentLength * 0.7f);
        }
        //-->
        --bendSegments;
        --horizontalSegments;
        startIndex = endIndex - bendSegments - 1;
        endIndex = startIndex - horizontalSegments;
        startAngle += 90.0f;
        for (int i = startIndex; (i >= 0) && (i >= endIndex); --i)
        {
            var body = ropeBodies[i];
            kinematicBodies.Add(body);
            body.isKinematic = true;

            body.transform.localRotation = Quaternion.Euler(new Vector3(startAngle, 0.0f, 0.0f));
            body.transform.localPosition = new Vector3(0.0f, -segmentWidth, (segmentLength * (startIndex - i)) + (segmentLength * 3.0f));
        }
        // |
        //\ /
        startIndex = endIndex - bendSegments - 1;
        endIndex = startIndex - verticalSegments;
        startAngle += 90.0f;
        for (int i = startIndex; (i >= 0) && (i >= endIndex); --i)
        {
            var body = ropeBodies[i];
            softBodies.Add(body);
            body.isKinematic = true;

            body.transform.localRotation = Quaternion.Euler(new Vector3(startAngle, 0.0f, 0.0f));
            body.transform.localPosition = new Vector3(0.0f,
                                            (-segmentLength * (startIndex - i)) - (segmentLength * 2.5f),
                                            segmentLength * (horizontalSegments + 4 + (bendSegments * 0.7f)));
        }
        //<--
        startIndex = endIndex - bendSegments - 1;
        endIndex = startIndex - horizontalSegments;
        startAngle += 90.0f;
        for (int i = startIndex; (i >= 0) && (i >= endIndex); --i)
        {
            var body = ropeBodies[i];
            softBodies.Add(body);
            body.isKinematic = true;

            body.transform.localRotation = Quaternion.Euler(new Vector3(startAngle, 0.0f, 0.0f));
            body.transform.localPosition = new Vector3(0.0f,
                                            -segmentLength * (verticalSegments + 2.7f + (bendSegments * 0.7f)),
                                            (-segmentLength * (startIndex - i)) + (segmentLength * (horizontalSegments + 2.7f)));
        }
        // / \
        //  |
        --verticalSegments;
        startIndex = endIndex - bendSegments - 1;
        endIndex = startIndex - verticalSegments;
        startAngle += 90.0f;
        for (int i = startIndex; (i >= 0) && (i >= endIndex); --i)
        {
            var body = ropeBodies[i];
            softBodies.Add(body);
            body.isKinematic = true;

            body.transform.localRotation = Quaternion.Euler(new Vector3(startAngle, 0.0f, 0.0f));
            body.transform.localPosition = new Vector3(0.0f,
                                            (segmentLength * (startIndex - i)) - (segmentLength * (verticalSegments + 1.7f + (bendSegments * 0.7f))),
                                            segmentLength * (horizontalSegments + 0.7f));
        }
        //-->
        startIndex = endIndex - bendSegments - 3;
        endIndex = startIndex - horizontalSegments;
        startAngle += 90.0f;
        for (int i = startIndex; (i >= 0) && (i >= endIndex); --i)
        {
            var body = ropeBodies[i];
            kinematicBodies.Add(body);
            body.isKinematic = true;

            body.transform.localRotation = Quaternion.Euler(new Vector3(startAngle, 0.0f, 0.0f));
            body.transform.localPosition = new Vector3(0.0f, -(segmentWidth * 2.0f), (segmentLength * (startIndex - i)) + (segmentLength * 3.5f));
        }
    }
    
    public void UncoilWhip()
    {
        foreach (var body in kinematicBodies)
        {
            body.isKinematic = false;
        }
    }
    
    public void SoftenWhip()
    {
        foreach (var body in softBodies)
        {
            body.isKinematic = false;
        }
    }

    private void SetupSnap()
    {
        if ((whip == null) || (snapTarget == null))
        {
            return;
        }

        snapStep1 = snapDuration * 0.5f;
        snapStep2 = snapDuration * 0.6f;
        snapStep3 = snapDuration;

        float length = whip.m_length - 3.0f;
        var upPos = whip.transform.position + (whip.transform.forward * (length * 0.5f)) + (whip.transform.up * (length * 0.5f));
        snapUpDir = (upPos - whip.LastBody.transform.position).normalized;
        snapBackDir = (whip.transform.position - snapTarget.position).normalized;
    }

    public void SnapWhipAt(Transform target, float uncoilTime, float snapTime, Action onHit, Action onFinish)
    {
        snapTarget = target;
        uncoilDuration = uncoilTime;
        snapDuration = snapTime;
        onSnapHit = onHit;
        onSnapFinish = onFinish;

        nextState = WhipState.Uncoil;
    }
    
    private bool SnapWhip()
    {
        if (whip == null)
        {
            return true;
        }
        
        var nextElapsedTime = snapElapsedTime + Time.fixedDeltaTime;
        if (snapElapsedTime <= snapStep1)
        {
            foreach (var body in whip.RopeBodies)
            {
                body.AddForce(snapUpDir * (4000.0f / snapDuration) * Time.fixedDeltaTime, ForceMode.Acceleration);
            }
            transform.localEulerAngles = new Vector3(Mathf.Lerp(0.0f, -90.0f, snapElapsedTime / snapStep1),
                                            transform.localEulerAngles.y,
                                            transform.localEulerAngles.z);
        }
        else if (snapElapsedTime <= snapStep2)
        {
            transform.localEulerAngles = new Vector3(Mathf.Lerp(-90.0f, 0.0f, (snapElapsedTime - snapStep1) / (snapStep2 - snapStep1)),
                                            transform.localEulerAngles.y,
                                            transform.localEulerAngles.z);

            float snapStep1_5 = snapStep1 + ((snapStep2 - snapStep1) * 0.6f);
            if (snapElapsedTime >= snapStep1_5)
            {
                float damping = 15.0f / snapStep1_5;
                foreach (var body in whip.RopeBodies)
                {
                    body.linearVelocity *= damping * Time.fixedDeltaTime;
                    body.angularVelocity *= damping * Time.fixedDeltaTime;
                    body.AddForce(snapBackDir * (10000.0f / snapDuration) * Time.fixedDeltaTime, ForceMode.Acceleration);
                }
            }

            if ((onSnapHit != null) && (nextElapsedTime > snapStep2))
                onSnapHit();
        }
        else if (snapElapsedTime <= snapStep3)
        {
            whip.LastBody.AddForce(snapBackDir * (20000.0f / snapDuration) * Time.fixedDeltaTime, ForceMode.Acceleration);

            if ((onSnapFinish != null) && (nextElapsedTime > snapStep3))
                onSnapFinish();
        }
        else
        {
            return true;
        }

        snapElapsedTime = nextElapsedTime;

        return false;
    }
}
