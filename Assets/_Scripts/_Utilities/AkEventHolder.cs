using UnityEngine;

#if UNITY_EDITOR
[UnityEditor.CustomPropertyDrawer(typeof(AkEventHolder))]
public class EventDrawer : AK.Wwise.Editor.BaseTypeDrawer
{
    protected override WwiseObjectType WwiseObjectType
    {
        get { return WwiseObjectType.Event; }
    }
}
#endif

[System.Serializable]
public class AkEventHolder : AK.Wwise.Event
{
    public enum EBus { SFX, Music, Voice };
    public string Event(string _default) => string.IsNullOrEmpty(Name) ? _default : Name;
    public static string SafeEvent(AkEventHolder _holder, string _default) => _holder == null ? _default : _holder.Event(_default);
    public int Play(GameObject _o, EBus _bus = EBus.SFX, bool _allowPreLoad = false)
    {
        var name = Name;
        if (string.IsNullOrEmpty(name))
            return 0;
        switch (_bus)
        {
            default: case EBus.SFX: return AudioClipManager.Me.PlaySound(name, _o, _allowPreLoad);
            case EBus.Music: return AudioClipManager.Me.PlayMusic(name);
            case EBus.Voice: return AudioClipManager.Me.PlayVO(name);
        }
    }
}
