using UnityEngine;

public class InteractionSequenceNode_Navigate : InteractionSequenceNode_CharacterBase
{
    public float m_walkSpeedMultiplier = 1;
    public bool m_matchRotation = false;
    public bool m_navigateDirect = false;
    public bool m_waitUntilFinished = true;
    private bool m_hasPath = false;
    private bool m_hasBegun = false;

    protected override void Begin()
    {
        if (Chr == null)
        {
            m_hasBegun = true;
            return;
        }
        m_hasPath = false;
        m_hasBegun = false;
        m_root.QueueNavNode(m_characterIndex, this);
    }
    
    protected override void BeginFromQueue()
    {
        m_hasBegun = true;
        Chr.m_nav.Speed = Chr.GetDesiredSpeed() * m_walkSpeedMultiplier;
        if (m_navigateDirect)
            Chr.m_nav.NavigateToDirect(transform.position);
        else
            Chr.m_nav.NavigateTo(transform.position);
    }

    public bool HasStarted => m_hasBegun;
    public bool HasEnded => Check();

    private bool Check()
    {
        if (m_hasBegun == false) return false;
        if (Chr == null) return true;
         if (Chr.m_nav.PathPending) return false;
         if (Chr.m_nav.PathExists) return false;
         if (m_matchRotation)
         {
             Chr.transform.rotation = Quaternion.Slerp(Chr.transform.rotation, transform.rotation, .2f);
             if (Chr.transform.rotation.AlmostEquals(transform.rotation) == false) return false;
         }
         return true;
    }

    protected override bool Tick()
    {
        if (m_waitUntilFinished == false) return true;
        return HasEnded;
    }
}
