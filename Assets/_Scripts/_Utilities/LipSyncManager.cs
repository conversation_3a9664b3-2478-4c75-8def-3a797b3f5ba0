using System.Collections.Generic;
using UnityEngine;

public class LipSyncManager : Mon<PERSON><PERSON><PERSON><PERSON><LipSyncManager>
{
    public class LipSyncer
    {
        private string m_typeInfo;
        private MACharacterBase m_character;

        public LipSyncer(string _typeInfo)
        {
            TypeInfo = _typeInfo;
        }

        public string TypeInfo
        {
            get => m_typeInfo;
            set
            {
                m_typeInfo = value;
                m_character = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == m_typeInfo);
            }
        }

        public MACharacterBase Character
        {
            get
            {
                if(m_character == null)
                {
                    m_character = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == m_typeInfo);
                }

                return m_character;
            }
            set
            {
                m_character = value;

                if (m_character != null)
                {
                    m_typeInfo = m_character.GetTypeInfo();
                }
            }
        }
    }

    private Dictionary<string, LipSyncer> m_lipSyncers = new Dictionary<string, LipSyncer>();

    public void AddLipSyncer(string _advisor, MACharacterBase _character, float _deltaRot = 0.0f)
    {
        if(!m_lipSyncers.ContainsKey(_advisor))
        {
            m_lipSyncers[_advisor] = new LipSyncer(_character.GetTypeInfo());
        }

        m_lipSyncers[_advisor].Character = _character;

        if (_deltaRot != 0.0f)
        {
            var ls = _character.SetLipSync(false);
            ls.m_deltaRot = _deltaRot;
        }
    }

    public void SetGestureSet(string _advisor, string _gestureSet)
    {
        var lipSync = GetOrAddLipSyncComponent(_advisor);

        if (lipSync != null)
        {
            lipSync.SetGestureSet(_gestureSet);
        }
    }
    
    public void UnsetGestureSet(string _advisor)
    {
        var lipSync = GetOrAddLipSyncComponent(_advisor);

        if (lipSync != null)
        {
            lipSync.UnsetGestureSet();
        }
    }

    public void AddGesture(string _advisor, string _gestureSet, string _animation, bool _disableHeadTracking = false)
    {
        var lipSync = GetOrAddLipSyncComponent(_advisor);

        if (lipSync != null)
        {
            lipSync.AddGesture(_gestureSet, _animation, _disableHeadTracking);
        }
    }

    public void OnDisplayMessage(MAMessage _message)
    {
        if(_message.m_type.m_type == "QuestDialog")
        {
            return;
        }

        MACharacterBase lipSyncer = GetLipSyncer(_message.m_advisor);

        if (lipSyncer != null)
        {
            AudioSource debugAudioSource = null;

            if (!_message.IsUsingAudioID)
            {
                debugAudioSource = MAMessageManager.Me.GetAudioSource(_message.m_advisor);
            }

            lipSyncer.SetLipSync(true, debugAudioSource);
        }
    }

    public void OnDestroyMessage(MAMessage _message)
    {
        MACharacterBase lipSyncer = GetLipSyncer(_message.m_advisor);

        if (lipSyncer != null)
        {
            lipSyncer.SetLipSync(false);
        }
    }

    private MACharacterBase GetLipSyncer(string _advisor)
    {
        if (m_lipSyncers.ContainsKey(_advisor))
        {
            return m_lipSyncers[_advisor].Character;
        }

        return null;
    }

    private LipSync GetOrAddLipSyncComponent(string _advisor)
    {
        var character = GetLipSyncer(_advisor);

        if (character != null)
        {
            LipSync lipSync = character.GetComponent<LipSync>();

            if (lipSync == null)
            {
                lipSync = character.SetLipSync(false);
            }

            return lipSync;
        }

        return null;
    }

    public class SaveLipSyncer
    {
        [Save] public string m_advisor;
        [Save] public string m_typeInfo;
        [Save] public LipSync.LipSyncSaveState m_lipSyncSaveState;
    }

    public class SaveLipSyncers
    {
        [Save] public List<SaveLipSyncer> m_lipSyncers = new();
    }

    public string SaveAll()
    {
        var lipSyncersSave = new SaveLipSyncers();

        foreach (var lipSyncer in m_lipSyncers)
        {
            var lipSyncerSave = new SaveLipSyncer();
            lipSyncerSave.m_advisor = lipSyncer.Key;
            lipSyncerSave.m_typeInfo = lipSyncer.Value.TypeInfo;

            var character = GetLipSyncer(lipSyncerSave.m_advisor);

            if (character != null)
            {
                LipSync lipSync = character.GetComponent<LipSync>();

                if(lipSync != null)
                {
                    lipSyncerSave.m_lipSyncSaveState = lipSync.GetSaveState();
                }
            }

            lipSyncersSave.m_lipSyncers.Add(lipSyncerSave);
        }

        var serialise = SSerializer.Serialize(lipSyncersSave);
        return serialise;
    }

    public void LoadAll(string _data)
    {
        if (_data.IsNullOrWhiteSpace()) return;

        var lipSyncersSave = SSerializer.Deserialize<SaveLipSyncers>(_data);

        m_lipSyncers.Clear();

        foreach (var lipSyncerSave in lipSyncersSave.m_lipSyncers)
        {
            if (lipSyncerSave == null) continue;

            m_lipSyncers[lipSyncerSave.m_advisor] = new LipSyncer(lipSyncerSave.m_typeInfo);

            if (lipSyncerSave.m_lipSyncSaveState != null)
            {
                var lipSync = GetOrAddLipSyncComponent(lipSyncerSave.m_advisor);

                if (lipSync != null)
                {
                    lipSync.SetSaveState(lipSyncerSave.m_lipSyncSaveState);
                }
            }
        }
    }
}
