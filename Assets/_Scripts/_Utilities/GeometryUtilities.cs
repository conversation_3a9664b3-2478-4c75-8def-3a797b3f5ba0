using System;
using System.Linq;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GeometryUtilities 
{
    // =========================================================================
    // Structs

    public class Polygon2D
    {
        public List<Vector2> Vertices;
        public List<Edge> Edges;

        public Polygon2D(List<Vector2> _verts, List<Edge> _edges)
        {
            Vertices = _verts;
            Edges = _edges;
        }


    }

    // -------------------------------------------------------------------------

    public class Edge
    {
        public int VertexIndexA;
        public int VertexIndexB;

        public Edge(int _vertexA, int _vertexB)
        {
            VertexIndexA = _vertexA;
            VertexIndexB = _vertexB;
        }

        public static bool operator ==(Edge e1, Edge e2)
        {
            return (e1.VertexIndexA == e2.VertexIndexA && e1.VertexIndexB == e2.VertexIndexB) ||
                    (e1.VertexIndexA == e2.VertexIndexB && e1.VertexIndexB == e2.VertexIndexA);
        }

        public static bool operator !=(Edge e1, Edge e2)
        {
            return !(e1 == e2);
        }

        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        public override bool Equals(object obj)
        {
            Edge e1 = this;
            Edge e2 = obj as Edge;
            if(e2 == null) return false;

            return e1 == e2;
        }
    }

    // =========================================================================
    // Constants

    public const float EPSILON = 0.0001f;
    // =============================== Lines ===================================

    public static bool LinesParallel(Vector2 _startA, Vector2 _endA, Vector2 _startB, Vector2 _endB)
    {
        Vector2 lineA =  _endA - _startA;
        Vector2 lineB = _endB - _startB;

        float c = lineA.x * lineB.y - lineA.y * lineB.x;

        return Mathf.Abs(c) < EPSILON;
    }

    public static bool LineSegmentIntersectXZ(Vector3 _lineAStart, Vector3 _lineAEnd, Vector3 _lineBStart, Vector3 _lineBEnd, out Vector3 _intersectionPoint, float _epsilon = 0)
    {
        Vector2 intersectionPointV2;
        bool success = LineSegmentIntersectXZ(new Vector2(_lineAStart.x, _lineAStart.z),
                                      new Vector2(_lineAEnd.x, _lineAEnd.z),
                                      new Vector2(_lineBStart.x, _lineBStart.z),
                                      new Vector2(_lineBEnd.x, _lineBEnd.z),
                                      out intersectionPointV2,
                                      _epsilon);

        _intersectionPoint = new Vector3(intersectionPointV2.x, 0, intersectionPointV2.y);

        return success;
    }

    // -------------------------------------------------------------------------

    public static bool LineSegmentIntersectXZ(Vector3 _lineAStart, Vector3 _lineAEnd, Vector3 _lineBStart, Vector3 _lineBEnd, float _epsilon = 0)
    {
        return LineSegmentIntersect2D(new Vector2(_lineAStart.x, _lineAStart.z),
                                      new Vector2(_lineAEnd.x, _lineAEnd.z),
                                      new Vector2(_lineBStart.x, _lineBStart.z),
                                      new Vector2(_lineBEnd.x, _lineBEnd.z),
                                      _epsilon);
    }

    // -------------------------------------------------------------------------

    public static bool LineSegmentIntersectXZ(Vector2 _lineAStart, Vector2 _lineAEnd, Vector2 _lineBStart, Vector2 _lineBEnd, out Vector2 _intersectionPoint, float _epsilon = 0)
    {
        Vector2 s1, s2;

        s1 = _lineAEnd - _lineAStart;
        s2 = _lineBEnd - _lineBStart;

        float s, t;
        s = (-s1.y * (_lineAStart.x - _lineBStart.x) + s1.x * (_lineAStart.y - _lineBStart.y)) / (-s2.x * s1.y + s1.x * s2.y);
        t = (s2.x * (_lineAStart.y - _lineBStart.y) - s2.y * (_lineAStart.x - _lineBStart.x)) / (-s2.x * s1.y + s1.x * s2.y);

        _intersectionPoint = _lineAStart + s1 * t;
        //_lineBStart + s2 * s;

        if (s >= 0 + _epsilon && s <= 1 - _epsilon && t >= 0 + _epsilon && t <= 1 - _epsilon)
        {
            // Collision detected           
            return true;
        }

        return false; // No collision
    }

    // -------------------------------------------------------------------------
	/// <summary>
	/// Returns true if 2 lines intersect. Epsilon is percentage tolerance from the end of the line.
	/// Use a NEGATIVE epsilon to be MORE tolerant of error.
	/// </summary>
	/// <param name="_lineAStart"></param>
	/// <param name="_lineAEnd"></param>
	/// <param name="_lineBStart"></param>
	/// <param name="_lineBEnd"></param>
	/// <param name="_epsilon"></param>
	/// <returns></returns>
	public static bool LineSegmentIntersect2D(Vector2 _lineAStart, Vector2 _lineAEnd, Vector2 _lineBStart, Vector2 _lineBEnd, float _epsilon = 0)
	{
		Vector2 s1, s2;

		s1 = _lineAEnd - _lineAStart;
		s2 = _lineBEnd - _lineBStart;

		float s, t;
		s = (-s1.y * (_lineAStart.x - _lineBStart.x) + s1.x * (_lineAStart.y - _lineBStart.y)) / (-s2.x * s1.y + s1.x * s2.y);
		t = (s2.x * (_lineAStart.y - _lineBStart.y) - s2.y * (_lineAStart.x - _lineBStart.x)) / (-s2.x * s1.y + s1.x * s2.y);

		if (s >= 0 + _epsilon && s <= 1 - _epsilon && t >= 0 + _epsilon && t <= 1 - _epsilon)
		{
			// Collision detected           
			return true;
		}

		return false; // No collision
	}

    

	// -------------------------------------------------------------------------

	public static bool LineSegmentIntersectXZZero(Vector2 _lineAStart, Vector2 _lineAEnd, Vector2 _lineBStart, Vector2 _lineBEnd, float _epsilon = 0)
	{
		// Assuming _lineAStart.y == _lineAEnd and hence s1.y = 0
		Vector2 s1, s2;

		s1 = _lineAEnd - _lineAStart;
		s2 = _lineBEnd - _lineBStart;

		float s, t;
		s = (s1.x * (_lineAStart.y - _lineBStart.y)) / (s1.x * s2.y);
		t = (s2.x * (_lineAStart.y - _lineBStart.y) - s2.y * (_lineAStart.x - _lineBStart.x)) / (s1.x * s2.y);

		if (s >= 0 + _epsilon && s <= 1 - _epsilon && t >= 0 + _epsilon && t <= 1 - _epsilon)
		{
			// Collision detected           
			return true;
		}

		return false; // No collision
	}

	// -------------------------------------------------------------------------
    // From Real-time collision detection book
    public static float LineLineClosestPoint(Vector3 _lineAStart, Vector3 _lineAEnd, Vector3 _lineBStart, Vector3 _lineBEnd, out Vector3 _result0, out Vector3 _result1, float _epsilon = 0.01f) {
        float t = 0.0f;
        float s = 0.0f;
        Vector3 d1 = _lineAEnd - _lineAStart; // Direction Vector3 of segment S1
        Vector3 d2 = _lineBEnd - _lineBStart; // Direction Vector3 of segment S2
        Vector3 r = _lineAStart - _lineBStart;
        float a = Vector3.Dot(d1, d1); // Squared length of segment S1, always nonnegative
        float e = Vector3.Dot(d2, d2); // Squared length of segment S2, always nonnegative
        float f = Vector3.Dot(d2, r);
        // Check if either or both segments degenerate into points
        if (a <= _epsilon && e <= _epsilon) {
            // Both segments degenerate into points
            s=t= 0.0f;
            _result0 = _lineAStart;
            _result1 = _lineBStart;
            return Vector3.Dot(_result0 - _result1, _result0 - _result1);
        }
        if (a <= _epsilon) {
            // First segment degenerates into a point
            s = 0.0f;
            t = f / e; // s = 0 => t = (b*s + f) / e = f / e
            t = Mathf.Clamp(t, 0.0f, 1.0f);
        } else {
            float c = Vector3.Dot(d1, r);
            if (e <= _epsilon) {
            // Second segment degenerates into a point
                t = 0.0f;
                s = Mathf.Clamp(-c / a, 0.0f, 1.0f); // t = 0 => s = (b*t - c) / a = -c / a
            } else {
                // The general nondegenerate case starts here
                float b = Vector3.Dot(d1, d2);
                float denom = a*e-b*b; // Always nonnegative
                // If segments not parallel, compute closest point on L1 to L2 and
                // Mathf.clamp to segment S1. Else pick arbitrary s (here 0)
                if (denom != 0.0f) {
                    s = Mathf.Clamp((b*f - c*e) / denom, 0.0f, 1.0f);
                } else s = 0.0f;
                // Compute point on L2 closest to S1(s) using
                // t = Vector3.Dot((_lineAStart + D1*s) - _lineBStart,D2) / Vector3.Dot(D2,D2) = (b*s + f) / e
                t = (b*s + f) / e;
                // If t in [0,1] done. Else Mathf.clamp t, recompute s for the new value
                // of t using s = Vector3.Dot((_lineBStart + D2*t) - _lineAStart,D1) / Vector3.Dot(D1,D1)= (t*b - c) / a
                // and Mathf.clamp s to [0, 1]
                if (t < 0.0f) {
                    t = 0.0f;
                    s = Mathf.Clamp(-c / a, 0.0f, 1.0f);
                } else if (t > 1.0f) {
                    t = 1.0f;
                    s = Mathf.Clamp((b - c) / a, 0.0f, 1.0f);
                }
            }
        }
        _result0 = _lineAStart + d1 * s;
        _result1 = _lineBStart + d2 * t;
        return Vector3.Dot(_result0 - _result1, _result0 - _result1);
    }
    
    // -------------------------------------------------------------------------

    // This is for infinite lines, returns false if parallel
	public static bool LineIntersectionPoint(Vector2 ps1, Vector2 pe1, Vector2 ps2, Vector2 pe2, out Vector2 _intersectionPoint)
	{
		_intersectionPoint = Vector2.zero;
		// Get A,B,C of first line - points : ps1 to pe1
		float A1 = pe1.y - ps1.y;
		float B1 = ps1.x - pe1.x;
		float C1 = A1 * ps1.x + B1 * ps1.y;

		// Get A,B,C of second line - points : ps2 to pe2
		float A2 = pe2.y - ps2.y;
		float B2 = ps2.x - pe2.x;
		float C2 = A2 * ps2.x + B2 * ps2.y;

		// Get delta and check if the lines are parallel
		float delta = A1 * B2 - A2 * B1;
        if (Mathf.Abs(delta) < EPSILON)
			return false;

		// now return the Vector2 intersection point
		_intersectionPoint = new Vector2(
			(B2 * C1 - B1 * C2) / delta,
			(A1 * C2 - A2 * C1) / delta
		);

		return true;
	}

    // -------------------------------------------------------------------------

	public static bool IsPointOnLine(Vector3 _point, Vector3 _lineStart, Vector3 _lineEnd)
	{
		float cross = (_point.z - _lineStart.z) * (_lineEnd.x - _lineStart.x) - (_point.x - _lineStart.x) * (_lineEnd.z - _lineStart.z);
		if (cross > 0.0001f)
			return false;

		float dot = (_point.x - _lineStart.x) * (_lineEnd.x - _lineStart.x) + (_point.z - _lineStart.z) * (_lineEnd.z - _lineStart.z);
		if (dot < 0)
			return false;

		float sqrLength = (_lineEnd.x - _lineStart.x) * (_lineEnd.x - _lineStart.x) + (_lineEnd.z - _lineStart.z) * (_lineEnd.z - _lineStart.z);
		if (dot > sqrLength)
			return false;

		return true;
	}

    // -------------------------------------------------------------------------
    /// <summary>
    /// /
    /// </summary>
    /// <returns>The side of line the point is on. Where -1 is below the line, +1 is above the line and 0 is on the line exactly.</returns>
    /// <param name="_point">Point.</param>
    /// <param name="_lineStart">Line start.</param>
    /// <param name="_lineEnd">Line end.</param>
    public static int IsPointAboveLine(Vector2 _point, Vector2 _lineStart, Vector2 _lineEnd)
    {
        float d = (_point.x - _lineStart.x) * (_lineEnd.y - _lineStart.y) - (_point.y - _lineStart.y) * (_lineEnd.x - _lineStart.x);

        if(d == 0)
            return 0;

        return (d < 0) ? -1 : 1;
    }

    // -------------------------------------------------------------------------

    public static Vector3 FindClosestPointOnLine(Vector3 _point, Vector3 _lineStart, Vector3 _lineEnd)
    {
        // Get closest point on line
        Vector3 v = _lineEnd - _lineStart;

        float k = Vector3.Dot(v, _point - _lineStart) / Vector3.Dot(v, v);
        k = Mathf.Clamp01(k);

        Vector3 point = _lineStart + v * k;

        return point;
    }

	// -------------------------------------------------------------------------

	public static Vector3 LineCircleIntersection(Vector3 circleCenter, Vector3 linePoint1, Vector3 linePoint2, float radius)
	{
		Vector3 lineVector = linePoint2 - linePoint1;
		float segmentLength = lineVector.magnitude;
		var normalizedDirection = lineVector.normalized;
		var dirToStart = linePoint1 - circleCenter;

		var dot = Vector3.Dot(dirToStart, normalizedDirection);
		var discriminant = dot * dot - (dirToStart.sqrMagnitude - radius * radius);

		if (discriminant < 0)
		{
			// No intersection, pick closest point on segment
			discriminant = 0;
		}

		var t = -dot + Mathf.Sqrt(discriminant);
		// Note: the default value of 1 is important for the PathInterpolator.MoveToCircleIntersection2D
		// method to work properly. Maybe find some better abstraction where this default value is more obvious.
		t = segmentLength > 0.00001f ? t / segmentLength : 1f;

		return linePoint1 + t * lineVector;
	}

    // =========================================================================
    // Polygons

    public static bool IsPointInPolygon(List<Vector2> _vertices, List<Edge> _edges, Vector2 _point)
    {

        List<Vector2> vertices = _vertices;
        List<Edge> edges = _edges;

        // Create line from outside polygon to point
        Vector2 lineStart = new Vector2(-99999f, -99999f);
        Vector2 lineEnd = _point;
		
        List<int> cornersHit = new List<int>();
		int hitCount = 0;
        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Check number of intersection with lines

        for(int i = 0; i < edges.Count; i++)
        {
            Vector2 intersect;
            Vector2 start = vertices[edges[i].VertexIndexA];
            Vector2 end = vertices[edges[i].VertexIndexB];
            if(LineSegmentIntersectXZ(lineStart, lineEnd,start, end, out intersect))
            {
                // Special case handling if line intersects a vertex below
                if((start - intersect).sqrMagnitude < EPSILON)
                {
                    cornersHit.Add(edges[i].VertexIndexA);
                }
                else if((end - intersect).sqrMagnitude < EPSILON)
                {
                    cornersHit.Add(edges[i].VertexIndexB);
                }
                else
                {
                    hitCount++;
                }
            }
        }

        // Handling if corners have been hit
        for(int i = 0; i < vertices.Count; i++)
        {
            int hits = 0;
            for(int j = 0; j < cornersHit.Count; j++)
            {
                if(i == cornersHit[j])
                    hits++;
            }

            if(hits == 0){}
            else if(hits == 1)
            {
                // One hit is as normal
                hitCount++;
            }
            else if(hits == 2)
            {
                // Check if end points of connected edges are above or below test line
                List<int> connectedVertices = new List<int>();
                for(int j = 0; j < edges.Count; j++)
                {
                    if(edges[j].VertexIndexA == i)
                        connectedVertices.Add(edges[j].VertexIndexB);
                    else if(edges[j].VertexIndexB == i)
                        connectedVertices.Add(edges[j].VertexIndexA);
                }

                // Should be exactly 2
                Vector2 nodeA = vertices[connectedVertices[0]];
                Vector2 nodeB = vertices[connectedVertices[1]];

                // count only those above the line
                if(IsPointAboveLine(nodeA, lineStart, lineEnd) == 1)
                {
                    hitCount++;
                }

                if(IsPointAboveLine(nodeB, lineStart, lineEnd) == 1)
                {
                    hitCount++;
                }
            }
            /*else
                Debug.LogError("Hit corner more than twice with single line - THIS SHOULD NEVER HAPPEN! " + hits + "-" + i);*/
        }

        // Odd means we're inside the polygon, even is outside
        return (hitCount % 2) == 1;
    }

    // -------------------------------------------------------------------------

    public static List<Vector2> ScaleAndGiftwrap(List<Polygon2D> _shapes, float _scale = 1f)
    {
        List<Vector2> vertices = new List<Vector2>();
        List<Edge> edges = new List<Edge>();

        foreach (var s in _shapes)
        {
            Vector2 min = Vector2.one * 1e23f, max = Vector2.one * -1e23f;
            foreach (var v in s.Vertices)
            {
                min = Vector2.Min(min, v);
                max = Vector2.Max(max, v);
            }
            var center = (min + max) * 0.5f;
            foreach (var v in s.Vertices)
            {
                vertices.Add(center + (v - center) * _scale);
            }
        }
        return vertices;
    }

	// -- Gift-Wrapping Algorithm -- //
	/*public static List<Vector3> GiftWrap( List<Vector3> _junctions )
	{
		float minX = float.MaxValue;
		int startvertexIndex = 0;
		for (int l = 0; l < _junctions.Count; l++) // loop again to remove all dependencies from one vertex on the block to another on the same block
		{
			if (_junctions[l].x < minX) // find an extreme to start merging
			{
				minX = _junctions[l].x;
				startvertexIndex = l;
			}
		}
		var finalVerts = new List<Vector3>();
		int currentIndex = startvertexIndex;   // start with first vertex of array and find a neighbor to link to that offers greatest angle
		List<int> visitedIndex = new List<int>(); // to avoid going back on old values
		int _best = (startvertexIndex == 0) ? 1 : 0;

		Vector3 lastV = Vector3.forward;
		do
		{
			float bestDiffAngle = 360.0f;
			Vector3 bestV = Vector3.zero;

			for (int h = 0; h < _junctions.Count; h++)
			{
				if (h != currentIndex && !visitedIndex.Contains(h)) // skip current index and any already visited values
				{
					var thisV = _junctions[h] - _junctions[currentIndex];

					var diffAngle = Vector3.Angle(lastV, thisV);

					if (diffAngle < bestDiffAngle)
					{
						bestDiffAngle = diffAngle;
						_best = h;
						bestV = thisV;
					}
				}
			}
			lastV = bestV;

			finalVerts.Add(_junctions[_best].NewY(0));

			if (currentIndex != startvertexIndex)
				visitedIndex.Add(currentIndex);

			currentIndex = _best; // move on to the next vertex
		} while(currentIndex != startvertexIndex);

		// -- Remove vert doubles (aka vertices that don't bring anything more to the shape) -- //
		if (finalVerts.Count > 2) // GL - 051018 - if we have no visuals we may pass in a degenerate (zero area) cage; this is benign
		{
			List<int> _toRemove = new List<int>();
			for (int x = 0; x < finalVerts.Count; x++)
			{
				int _start = (x - 1 < 0) ? finalVerts.Count - 1 : x - 1, _end = (x + 1) % finalVerts.Count;

				while (_toRemove.Contains(_start)) _start = (_start - 1 < 0) ? finalVerts.Count - 1 : _start - 1;
				while (_toRemove.Contains(_end)) _end = (_end + 1) % finalVerts.Count;

				if (CheckonSegment(finalVerts[x], finalVerts[_start], finalVerts[_end], 0.01f)) _toRemove.Add(x);

				if (_toRemove.Count == finalVerts.Count) {
					Debug.LogError("Vert remover failed! All verts removed");
					break;
				}
				if (_start == _end) {
					Debug.LogError("Vert remover failed! Start==End");
					break;
				}
			}
			for (int x = _toRemove.Count - 1; x >= 0; x--) finalVerts.RemoveAt(_toRemove[x]);
		}

		return finalVerts;
	}

	public static bool CheckonSegment(Vector3 pt, Vector3 ptA, Vector3 ptB, float tolerance)
	{
		if (Vector2.Dot((pt - ptA).GetXZVector2(), (pt - ptB).GetXZVector2()) > 0) return false;

		var edge = ptB - ptA;
		var tan = new Vector3(edge.z, edge.y, -edge.x);

		var dot = Vector3.Dot(tan, pt - ptA);
		return dot * dot < tolerance * tolerance * tan.sqrMagnitude;
	}*/

    /// <summary>
    /// Given a set of polygons. 
    /// This function returns a set of points and edges representing a single polygon that is the outline of those shapes combined.
    /// Can optionally pass in a float to scale the outputted shape.
    /// </summary>
    /// <returns>The and extrude shapes.</returns>
    /// <param name="_shapePoints">Shape points.</param>
    /// <param name="_edges">Edges.</param>
    /// <param name="_scale">Scale.</param>
    public static Polygon2D MergeAndScaleShapes(List<Polygon2D> _shapes, float _scale = 1f, float _threshold = 0.1f)
    {
        // Create lists that will make the new polygon
        List<Vector2> vertices = new List<Vector2>();
        List<Edge> edges = new List<Edge>();

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

        // Create initial polygon with all vertices and edges
        int vertexIndexMod = 0;
        for(int i = 0; i < _shapes.Count; i++)
        {
            vertices.AddRange(_shapes[i].Vertices) ;
            for(int j = 0; j < _shapes[i].Edges.Count; j++)
            {
                edges.Add(new Edge(_shapes[i].Edges[j].VertexIndexA + vertexIndexMod,
                                   _shapes[i].Edges[j].VertexIndexB + vertexIndexMod));
            }

            vertexIndexMod += _shapes[i].Vertices.Count;
        }

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // VERTEX PASS
		RemoveDuplicateVertices(ref vertices, ref edges, _threshold);

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // INTERIOR EDGE PASS
        //RemoveInteriorEdges(ref vertices, ref edges);

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // EXTERIOR EDGE PASS
        //MergeExteriorEdges(ref vertices, ref edges);

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // SCALE PASS
        /*if(_scale != 1f)
        {
            ScalePolygon(ref vertices, ref edges, _scale);
        }*/
        
        var poly = new Polygon2D(vertices, edges);

        return poly;
    }

    // -------------------------------------------------------------------------

    static void RemoveDuplicateVertices(ref List<Vector2> _vertices, ref List<Edge> _edges, float _epsilon)
    {
        // VERTEX PASS
        // Remove any vertices that are the same
        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Variables

        int[] newIndices = new int[_vertices.Count];
        List<int> verticesToCheck = new List<int>();
        List<Vector2> newVectices = new List<Vector2>();

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Initialise all vertices to check

        for(int i = 0; i < _vertices.Count; i++)
        {
            newIndices[i] = i;
            verticesToCheck.Add(i);
        }

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        //  Check all vertices

        int idx = 0;
        while(verticesToCheck.Count > 0)
        {
            int currentVertex = verticesToCheck[0];
            newVectices.Add(_vertices[currentVertex]);
            verticesToCheck.RemoveAt(0);
            newIndices[currentVertex] = idx;

            List<int> verticesToRemove = new List<int>();
            for(int i = 0; i < verticesToCheck.Count; i++)
            {
                int checkVertex = verticesToCheck[i];
				if((_vertices[currentVertex] - _vertices[checkVertex]).sqrMagnitude < _epsilon*_epsilon)
                {
                    newIndices[checkVertex] = idx;
                    verticesToRemove.Add(checkVertex);
                }
            }

            for(int i = 0; i < verticesToRemove.Count; i++)
            {
                verticesToCheck.Remove(verticesToRemove[i]);
            }

            idx++;
        }

        _vertices = newVectices;

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Update edges with new vertex indices
        for(int i = 0; i < _edges.Count; i++)
        {
            _edges[i].VertexIndexA = newIndices[_edges[i].VertexIndexA];
            _edges[i].VertexIndexB = newIndices[_edges[i].VertexIndexB];
        }
    }

    // -------------------------------------------------------------------------

    static void RemoveInteriorEdges(ref List<Vector2> _vertices, ref List<Edge> _edges)
    {
        // INTERIOR EDGE PASS
        // Remove any interior edges

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Check for duplicate edges and schedule to remove
        List<int> edgesToRemove = new List<int>();
        for(int i = _edges.Count - 1; i >= 0 ; i--)
        {
            for(int j = i - 1; j >= 0 ; j--)
            {
                if(_edges[i] == _edges[j])
                {
                    edgesToRemove.Add(i);
                    edgesToRemove.Add(j);
                    break;
                }
            }
        }

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Remove in inverse order so as to not mess up indices
        edgesToRemove.Sort();
		int lastRemove = -1;
        for(int i = edgesToRemove.Count - 1; i >= 0; i--)
        {
            int removeIdx = edgesToRemove[i];
			if (removeIdx == lastRemove) continue; // in case we had the same edge twice
			lastRemove = removeIdx;
			if (removeIdx < 0 || removeIdx >= _edges.Count)
				Debug.LogWarningFormat("RemoveInteriorEdges: index out of range {0} ({1})", removeIdx, _edges.Count);
			else
            	_edges.RemoveAt(removeIdx);
        }

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Remove any vertices that are not part of any edge
        List<int> verticesToDelete = Enumerable.Range(0, _vertices.Count).ToList();
        for(int i = 0; i < _edges.Count; i++)
        {
            int vertA = _edges[i].VertexIndexA;
            int vertB = _edges[i].VertexIndexB;

            if(verticesToDelete.Contains(vertA))
                verticesToDelete.Remove(vertA);

            if(verticesToDelete.Contains(vertB))
                verticesToDelete.Remove(vertB);
        }

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Remove unused vertices
        int[] decrementArray = new int[_vertices.Count];
        for(int i = 0; i < _vertices.Count; i++)
        {
            decrementArray[i] = 0;
        }

        for(int i = 0; i < verticesToDelete.Count; i++)
        {
            int index = verticesToDelete[i];
            for(int j = index + 1; j < decrementArray.Length; j++)
            {
                decrementArray[j]--;
            }
        }

        verticesToDelete.Sort();
        for(int i = verticesToDelete.Count -1; i >=0; i--)
        {
            _vertices.RemoveAt(verticesToDelete[i]);
        }

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Remap edges

        for(int i = 0; i < _edges.Count; i++)
        {
            _edges[i].VertexIndexA += decrementArray[_edges[i].VertexIndexA];
            _edges[i].VertexIndexB += decrementArray[_edges[i].VertexIndexB];
        }
    }

    // -------------------------------------------------------------------------

    static void MergeExteriorEdges(ref List<Vector2> _vertices, ref List<Edge> _edges)
    {
        // EXTERIOR EDGE PASS
        // Merge continuous edges
        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Schedule edges to be merged
        List<int> verticesToDelete = new List<int>();
        for(int i = 0; i < _edges.Count; i++)
        {
            for(int j = i + 1; j < _edges.Count; j++)
            {
                Vector2 startA = _vertices[_edges[i].VertexIndexA];
                Vector2 endA   = _vertices[_edges[i].VertexIndexB];

                Vector2 startB = _vertices[_edges[j].VertexIndexA];
                Vector2 endB   = _vertices[_edges[j].VertexIndexB];

				// If two edges share a vertex and are parallel
                if(LinesParallel(startA, endA, startB, endB))
                { 
                    int nodeToRemove = -1;
                    if(_edges[i].VertexIndexA == _edges[j].VertexIndexA)
                    {
                        nodeToRemove = _edges[i].VertexIndexA;
                        _edges[i].VertexIndexA = _edges[j].VertexIndexB;
                    }

                    else if(_edges[i].VertexIndexA == _edges[j].VertexIndexB)
                    {
                        nodeToRemove = _edges[i].VertexIndexA;
                        _edges[i].VertexIndexA = _edges[j].VertexIndexA;
                    }

                    else if(_edges[i].VertexIndexB == _edges[j].VertexIndexA)
                    {
                        nodeToRemove = _edges[i].VertexIndexB;
                        _edges[i].VertexIndexB = _edges[j].VertexIndexB;
                    }

                    else if(_edges[i].VertexIndexB == _edges[j].VertexIndexB)
                    {
                        nodeToRemove = _edges[i].VertexIndexB;
                        _edges[i].VertexIndexB = _edges[j].VertexIndexA;
                    }


                    if(nodeToRemove != -1)
                    {
                        verticesToDelete.Add(nodeToRemove);
                        _edges.RemoveAt(j);
                        j = i;
                    }
                }
            }
        }


		for(int i = _edges.Count-1; i >= 0; i--)
		{
			if (verticesToDelete.Contains(_edges[i].VertexIndexA) || verticesToDelete.Contains(_edges[i].VertexIndexB)) {
				Debug.LogWarningFormat("Ext Edge containing deleted vert Edge: {0} - {1} {2} ({3} {4})", i, _edges[i].VertexIndexA, _edges[i].VertexIndexB, verticesToDelete.Contains(_edges[i].VertexIndexA), verticesToDelete.Contains(_edges[i].VertexIndexB));
				_edges.RemoveAt(i);
			}
		}

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Remove unused vertices
        int[] swapArray = new int[_vertices.Count];
        for(int i = 0; i < _vertices.Count; i++)
        {
            swapArray[i] = 0;
        }

        for(int i = 0; i < verticesToDelete.Count; i++)
        {
            int index = verticesToDelete[i];
            for(int j = index + 1; j < swapArray.Length; j++)
            {
                swapArray[j]--;
            }
        }

        verticesToDelete.Sort();
        for(int i = verticesToDelete.Count -1; i >=0; i--)
        {
            _vertices.RemoveAt(verticesToDelete[i]);
        }

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Remap edges

        for(int i = 0; i < _edges.Count; i++)
        {
            _edges[i].VertexIndexA += swapArray[_edges[i].VertexIndexA];
            _edges[i].VertexIndexB += swapArray[_edges[i].VertexIndexB];
        }
    }

    // -------------------------------------------------------------------------

    static void ScalePolygon(ref List<Vector2> _vertices, ref List<Edge> _edges, float _scale)
    {
        // SCALE PASS
        // Scale edges

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Get adjacent edges
        List<Edge>[] adjacentEdges = new List<Edge>[_vertices.Count];
        for(int i = 0; i < _vertices.Count; i++)
        {
            adjacentEdges[i] = new List<Edge>();
        }

        for(int i = 0; i < _edges.Count; i++)
        {
            adjacentEdges[_edges[i].VertexIndexA].Add(_edges[i]);
            adjacentEdges[_edges[i].VertexIndexB].Add(_edges[i]);
        }

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Calculate normal
        Vector2[] normalVectors = new Vector2[_vertices.Count];
        for(int i = 0; i < adjacentEdges.Length; i++)
        {
            int targetVertex = i;
			if (adjacentEdges[i].Count < 2) {
				Debug.LogWarningFormat("Vertex unused in all edges {0}", i);
				continue;
			}

            int vertexA = (adjacentEdges[i][0].VertexIndexA == i) ? adjacentEdges[i][0].VertexIndexB : adjacentEdges[i][0].VertexIndexA;
            int vertexB = (adjacentEdges[i][1].VertexIndexA == i) ? adjacentEdges[i][1].VertexIndexB : adjacentEdges[i][1].VertexIndexA;

            Vector2 vectorA = (_vertices[targetVertex] - _vertices[vertexA]).normalized;
            Vector2 vectorB = (_vertices[targetVertex] - _vertices[vertexB]).normalized;

            Vector2 normalVector = (vectorA + vectorB).normalized;
            normalVectors[i] = normalVector;
        }

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Calculate direction

        for(int i = 0 ; i < normalVectors.Length; i++)
        {
            // Point is small amount in vector direction
            Vector2 testPoint = _vertices[i] + normalVectors[i] * 0.1f;

            if(IsPointInPolygon(_vertices, _edges, testPoint))
            {
                normalVectors[i] = -1 * normalVectors[i];
            }
        }

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
        // Scale in direction
        for(int i = 0; i < _vertices.Count; i++)
        {
            _vertices[i] = _vertices[i] + normalVectors[i] * _scale;
        }

        // - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
    }

    // =========================================================================
    // Triangles

    public static bool TriangleRayIntersect(Vector3 p1, Vector3 p2, Vector3 p3, Ray ray, out Vector3 _intersectionPoint)
    {
		//CM: All the manual operations in this function are intentional.
		// Because this function can be run many times, and Vector3 operations are slow when performed frequently. (because structs are copied and a new one is made).
		_intersectionPoint.x = 0.0f;
		_intersectionPoint.y = 0.0f;
		_intersectionPoint.z = 0.0f;

		// Vectors from p1 to p2/p3 (edges)
		Vector3 e1, e2, ro, rd;  

        Vector3 p, q, t;
        float det, invDet, u, v, w;

		ro = ray.origin;
		rd = ray.direction;

		//Find vectors for two edges sharing vertex/point p1
		e1.x = p2.x - p1.x;
		e1.y = p2.y - p1.y;
		e1.z = p2.z - p1.z;

		e2.x = p3.x - p1.x;
		e2.y = p3.y - p1.y;
		e2.z = p3.z - p1.z;

		// calculating determinant 
		p.x = rd.y * e2.z - rd.z * e2.y;
		p.y = rd.z * e2.x - rd.x * e2.z;
		p.z = rd.x * e2.y - rd.y * e2.x;

		//Calculate determinat
		det = e1.x * p.x + e1.y * p.y + e1.z * p.z;

		//if determinant is near zero, ray lies in plane of triangle otherwise not
		if (det > -Mathf.Epsilon && det < Mathf.Epsilon) { return false; }
        invDet = 1.0f / det;

		//calculate distance from p1 to ray origin
		t.x = ro.x - p1.x;
		t.y = ro.y - p1.y;
		t.z = ro.z - p1.z;

		//Calculate u parameter
		u = (t.x * p.x + t.y * p.y + t.z * p.z) * invDet;

		//Check for ray hit
		if (u < 0 || u > 1) { return false; }

        //Prepare to test v parameter
		q.x = t.y * e1.z - t.z * e1.y;
		q.y = t.z * e1.x - t.x * e1.z;
		q.z = t.x * e1.y - t.y * e1.x;

		//Calculate v parameter
		v = (rd.x * q.x + rd.y * q.y + rd.z * q.z) * invDet;

		//Check for ray hit
		if (v < 0 || u + v > 1) { return false; }

		w = (e2.x * q.x + e2.y * q.y + e2.z * q.z) * invDet;
		if (w > Mathf.Epsilon)
        { 
            //ray does intersect
            _intersectionPoint = p1 + (e1 * u) + (e2 * v);

            return true;
        }

        // No hit at all
        return false;
    }

    // =========================================================================

	public static Mesh CreateUnitQuad()
	{
		var quad = new Mesh();
		quad.name = "UnitQuad";
		quad.vertices = new[]
		{
			new Vector3(-0.5f, 0f, -0.5f), 
			new Vector3( 0.5f, 0f, -0.5f),
			new Vector3(-0.5f, 0f,  0.5f),
			new Vector3( 0.5f, 0f,  0.5f)
		};
		quad.triangles = new[] {0, 2, 1, 1, 2, 3, 0, 1, 2, 2, 1, 3};
		quad.RecalculateNormals();
		return quad;
	}
	/// <summary>
	/// Gets the point on circle.
	/// </summary>
	/// <returns>The point on circle.</returns>
	/// <param name="_angle">Angle.</param>
	/// <param name="_radius">Radius.</param>
	public	static	Vector3		GetPointOnCircle(float _angle, float _radius)
	{
		var rad = _angle*Mathf.Deg2Rad;
		return	new Vector3(Mathf.Cos(rad), 0f, Mathf.Sin(rad)) * _radius;
	}

	// =========================================================================
	/// <summary>
	/// _object must have meshes inside of it
	/// </summary>
	/// <param name="_object"></param>
	/// <param name="_bounds"></param>
	/*public static void FitObjectInsideBounds(GameObject _object, BoxCollider _bounds)
	{
		var boundsCentre = _bounds.transform.TransformPoint(_bounds.center);
		var boundExtents = Vector3.Scale(_bounds.size, _bounds.transform.lossyScale) * 0.5f;
		var boundBottom = boundsCentre - _bounds.transform.up * boundExtents.y;

		_object.transform.position = Vector3.zero;
		_object.transform.rotation = Quaternion.identity;
		_object.transform.localScale = Vector3.one;

		var visualBounds = _object.transform.GetMeshBoundsInChildren();
		var visualCentre = visualBounds.center;
		var visualExtents = visualBounds.size * 0.5f;

		if (Mathf.Approximately(visualExtents.sqrMagnitude, 0f))
			Debug.LogErrorFormat("No mesh bounds found in {0}", _object.name);

		float targetScale = Mathf.Min(
			boundExtents.x / visualExtents.x,
			boundExtents.y / visualExtents.y,
			boundExtents.z / visualExtents.z);

		_object.transform.localScale = Vector3.one * targetScale;

		//size and wrap box
		visualExtents *= targetScale;

		//position the visual inside the box
		visualCentre = (visualCentre - _object.transform.position) * targetScale + _object.transform.position;
		var visualBottomPoint = visualCentre - _object.transform.up * visualExtents.y;

		_object.transform.localPosition = Vector3.zero;
		_object.transform.position -= visualBottomPoint + Vector3.up * boundExtents.y;

		//turn visual towards parent
		_object.transform.forward = _bounds.transform.forward;
	}*/

	public static bool IsPointInPolygon(Vector3[] _polyVertices, Vector3 _testPoint)
	{    
		bool result = false;
		int j = _polyVertices.Length - 1;
		for (int i = 0; i < _polyVertices.Length; i++)
		{
			var a = _polyVertices[i];
			var b = _polyVertices[j];
			if (a.z < _testPoint.z && b.z >= _testPoint.z || b.z < _testPoint.z && a.z >= _testPoint.z)
			{
				if (a.x + (_testPoint.z - a.z) / (b.z - a.z) * (b.x - a.x) < _testPoint.x)
				{
					result = !result;
				}
			}
			j = i;
		}
		return result;
	}

	// Take a line of two points, and extrapolate a quad. (NOT bottom left or top right vectors)
	public static List<Vector3> CreateQuadFromLine( Vector3 _from, Vector3 _to, float _paddingLength = 0.5f, float _paddingWidth = 0.25f )
	{
		// Each link has a start and end point.
		// We're going to stretch that into a rectangular quad.

		// TODO: do this without a normalization.

		// stretchN
		Vector3 edgeDir = (_from - _to).normalized;      
		// 90 degreesN
		Vector3 perp = new Vector3( -edgeDir.z, 0.0f, edgeDir.x ) * _paddingWidth;

		edgeDir *= _paddingLength;

		var quad = new List<Vector3>(4);

		quad.Add( _from + (edgeDir + perp) );
		quad.Add( _from + (edgeDir - perp) );
		quad.Add( _to - (edgeDir + perp) );
		quad.Add( _to - (edgeDir - perp) );

		// CM: If this breaks, maybe check orientation?

		return quad;
	}

	public static Vector3 GetClosestPointOnLineSegment(Vector3 AA, Vector3 BB, Vector3 PP)
	{
		// this method will return the nearest perpendicular point to the point PP on the segment AABB
		// if this point cannot be found on the segment, the default return value will be an infinite Vector3 (so that it cannot be chosen as the best candidate)
		Vector2 A = new Vector2(AA.x, AA.z);
		Vector2 B = new Vector2(BB.x, BB.z);
		Vector2 P = new Vector2(PP.x, PP.z);
		Vector2 p = B - A;
		float dAB = p.x * p.x + p.y * p.y;
		if (Mathf.Abs(dAB) < 0.0001f) // if A = B
			return AA;
		float u = ((P.x - A.x) * p.x + (P.y - A.y) * p.y) / dAB;
		if (u < 0 || u > 1) // if the point is not on the segment we are currently looking at
			return new Vector3(float.MaxValue, float.MaxValue, float.MaxValue); // return infinite vector3
		float x = A.x + u * p.x, y = A.y + u * p.y;
		return new Vector3(x, 0, y);
	}

	public static Vector3 ClosestPointToInfiniteLine(Vector3 _origin, Vector3 _direction, Vector3 _point)
	{
		float k = Vector3.Dot(_point - _origin, _direction);
		return _origin + _direction * k;
	}

	public static bool CheckSegmentIntersectionXZ(Vector3 A, Vector3 B, Vector3 C, Vector3 D, ref Vector3 _intersectPt)
	{
		float s1x = B.x - A.x, s1y = B.z - A.z, s2x = D.x - C.x, s2y = D.z - C.z;
		float ACx = A.x - C.x, ACz = A.z - C.z;
		float det = -s2x * s1y + s1x * s2y;
		float s = -s1y * ACx + s1x * ACz;
		float t = s2x * ACz - s2y * ACx;

		// RW-27-SEP-18: Return false for colinear line segments.
		if (det * det < 0.00001f)
		{
			return false;
		}

		// check that s and t are between 0 and det
		if (s * (s - det) <= 0 && t * (t - det) <= 0)
		{
			t /= det;
			_intersectPt.x = A.x + (t * s1x);
			_intersectPt.y = A.y;
			_intersectPt.z = A.z + (t * s1y);
			return true;
		}
		return false;
	}
	
	public static bool PointInTriangleXZ(Vector3 _p, Vector3 _v1, Vector3 _v2, Vector3 _v3)
	{
		float Sign(Vector3 p1, Vector3 p2, Vector3 p3) => (p1.x - p3.x) * (p2.z - p3.z) - (p2.x - p3.x) * (p1.z - p3.z);
		float d1 = Sign(_p, _v1, _v2);
		float d2 = Sign(_p, _v2, _v3);
		float d3 = Sign(_p, _v3, _v1);
		int s1 = d1 < 0 ? 1 : 0;
		int s2 = d2 < 0 ? 1 : 0;
		int s3 = d3 < 0 ? 1 : 0;
		int sum = s1 + s2 + s3;
		return sum == 0 || sum == 3;
	}

	public static bool IsPointInTriangle( Vector2 P, Vector2 A, Vector2 B, Vector2 C )
	{
		// Compute vectors        
		var v0 = C - A;
		var v1 = B - A;
		var v2 = P - A;

		// Compute dot products
		var dot00 = Vector2.Dot(v0, v0);
		var dot01 = Vector2.Dot(v0, v1);
		var dot02 = Vector2.Dot(v0, v2);
		var dot11 = Vector2.Dot(v1, v1);
		var dot12 = Vector2.Dot(v1, v2);

		// Compute barycentric coordinates
		var denom = dot00 * dot11 - dot01 * dot01;
		if (denom * denom < 0.0001f * 0.0001f) return false; // degenerate triangle
		var invDenom = 1 / denom;
		var u = (dot11 * dot02 - dot01 * dot12) * invDenom;
		var v = (dot00 * dot12 - dot01 * dot02) * invDenom;

		// Check if point is in triangle
		return (u >= 0) && (v >= 0) && (u + v < 1);
	}

	public struct OrientedBoundingBox2D
	{
		public Vector2 m_center;
		public Vector2 m_extents;
		public float m_angle; // Rotation angle in degrees relative to the X axis.
		private Vector3 m_cacheMin, m_cacheMax;
		private Vector2 m_cacheCenter, m_cacheExtents;
		private float m_cacheAngle;

		public (Vector3, Vector3) CalculateMinMax(float _margin)
		{
			if ((m_center - m_cacheCenter).sqrMagnitude > .001f * .001f || (m_extents - m_cacheExtents).sqrMagnitude > .001f * .001f || m_angle.Nearly(m_cacheAngle) == false)
			{
				m_cacheCenter = m_center;
				m_cacheExtents = m_extents;
				m_cacheAngle = m_angle;
				var right = new Vector3(Mathf.Cos(m_angle * Mathf.Deg2Rad), 0, Mathf.Sin(m_angle * Mathf.Deg2Rad));
				var fwd = new Vector3(-right.z, 0, right.x);
				var absRight = new Vector2(Mathf.Abs(right.x), Mathf.Abs(right.z));
				var absFwd = new Vector2(Mathf.Abs(fwd.x), Mathf.Abs(fwd.z));
				var min = m_center - absRight * m_extents.x - absFwd * m_extents.y - Vector2.one * _margin;
				var max = m_center + absRight * m_extents.x + absFwd * m_extents.y + Vector2.one * _margin;
				m_cacheMin = min.V3XZ();
				m_cacheMax = max.V3XZ();
			}
			return (m_cacheMin, m_cacheMax);
		}
	}

	public static OrientedBoundingBox2D Find2DBoundsFromOBB(Vector3 _center, Vector3 _extents, Quaternion _rotation) => 
		Find2DBoundsFromOBB(_center, _extents, _rotation * Vector3.right, _rotation * Vector3.up, _rotation * Vector3.forward);

	public static OrientedBoundingBox2D Find2DBoundsFromOBB(Vector3 _center, Vector3 _extents, Vector3 _localX, Vector3 _localY, Vector3 _localZ)
	{
		Vector2 center2D = new Vector2(_center.x, _center.z);
		var localX2D = new Vector2(_localX.x, _localX.z);
		var localY2D = new Vector2(_localY.x, _localY.z);
		var localZ2D = new Vector2(_localZ.x, _localZ.z);

		OrientedBoundingBox2D bestBox = new OrientedBoundingBox2D();
		var bestAreaMetric = 1e23f;
		var bestAxis = Vector2.zero;
	
		void ConsiderAxis(Vector2 _axis)
		{
			const float c_minMag = .001f;
			if (_axis.sqrMagnitude < c_minMag * c_minMag) return;
			_axis = _axis.normalized;
			var perp = new Vector2(-_axis.y, _axis.x);

			var halfExtentAxis = 0f;
			var halfExtentPerp = 0f;

			halfExtentAxis += Mathf.Abs(Vector2.Dot(localX2D, _axis)) * _extents.x;
			halfExtentPerp += Mathf.Abs(Vector2.Dot(localX2D, perp)) * _extents.x;

			halfExtentAxis += Mathf.Abs(Vector2.Dot(localY2D, _axis)) * _extents.y;
			halfExtentPerp += Mathf.Abs(Vector2.Dot(localY2D, perp)) * _extents.y;

			halfExtentAxis += Mathf.Abs(Vector2.Dot(localZ2D, _axis)) * _extents.z;
			halfExtentPerp += Mathf.Abs(Vector2.Dot(localZ2D, perp)) * _extents.z;

			var areaMetric = halfExtentAxis * halfExtentPerp; // this will be quarter of the actual area, but we're only using it for comparison
			if (areaMetric < bestAreaMetric)
			{
				bestAreaMetric = areaMetric;
				bestBox.m_center = center2D;
				bestBox.m_extents = new Vector2(halfExtentAxis, halfExtentPerp);
				bestAxis = _axis;
			}
		}

		ConsiderAxis(localX2D);
		ConsiderAxis(localY2D);
		ConsiderAxis(localZ2D);
		bestBox.m_angle = Mathf.Atan2(bestAxis.y, bestAxis.x) * Mathf.Rad2Deg;
		return bestBox;
	}
}
