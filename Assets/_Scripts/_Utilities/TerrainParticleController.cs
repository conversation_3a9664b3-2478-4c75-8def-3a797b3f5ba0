using System.Collections.Generic;
using UnityEngine;

[RequireComponent(typeof(ParticleSystem))]
public class TerrainParticleController : MonoBehaviour
{
    public Vector4 m_presence1Mask;
    public Vector4 m_presence2Mask;
    public float m_timeAppear24h = 0, m_timeDisappear24h = 24;
    private ParticleSystem m_particleSystem;
    private float m_timeBetweenParticles = 1;
    private float m_particleSystemNext = 0;
    void Start()
    {
        m_particleSystem = GetComponent<ParticleSystem>();
    }

    void Update()
    {
        if (GameManager.Me.m_state.m_subSceneState.m_current != null) return;
        var time = DayNight.Me.CurrentTime24h;
        if ((m_timeAppear24h <= m_timeDisappear24h && (time < m_timeAppear24h || time > m_timeDisappear24h)) ||
            (m_timeAppear24h > m_timeDisappear24h && (time < m_timeAppear24h && time > m_timeDisappear24h)))
            return;
        
        var newPos = GameManager.Me.RaycastTerrain(.5f, .35f);
        if (newPos != null) transform.position = newPos.Value;
        
        if (m_particleSystem.emission.rateOverTime.constant > 0)
        {
            m_timeBetweenParticles = 1 / m_particleSystem.emission.rateOverTime.constant;
            var emission = m_particleSystem.emission;
            emission.rateOverTime = 0;
        }
        m_particleSystemNext -= Time.deltaTime;
        if (m_particleSystemNext >= 0) return;
        int count = Mathf.CeilToInt(-m_particleSystemNext / m_timeBetweenParticles);
        m_particleSystemNext += m_timeBetweenParticles * count;

        var emit = new List<Vector3>();
        for (int i = 0; i < count; ++i)
        {
            var emissionShape = m_particleSystem.shape;
            Vector3 pos = Vector3.zero;
            switch (emissionShape.shapeType)
            {
                case ParticleSystemShapeType.Sphere:
                    pos = Random.insideUnitSphere * emissionShape.radius;
                    break;
                case ParticleSystemShapeType.Box:
                    pos = Random.Range(-emissionShape.scale.x, emissionShape.scale.x) * transform.right +
                          Random.Range(0, emissionShape.scale.y) * transform.up +
                          Random.Range(-emissionShape.scale.z, emissionShape.scale.z) * transform.forward;
                    break;
            }
            if (pos.sqrMagnitude > 0)
            {
                var wpos = transform.position + pos;
                if (DistrictManager.Me.IsWithinDistrictBounds(wpos) == false) continue;
                var splat = CameraRenderSettings.Me.GetStrongestSplatPoint(wpos);
                var presence = CameraRenderSettings.Me.m_grassColoursAndPresence[splat];
                var match = Vector4.Dot(presence.m_presence, m_presence1Mask) + Vector4.Dot(presence.m_presence2, m_presence2Mask);
                if (match > 0)
                    emit.Add(wpos.GroundPosition(pos.y));
            }
        }
        if (emit.Count > 0)
        {
            var oldCount = m_particleSystem.particleCount;
            m_particleSystem.Emit(emit.Count);
            var newCount = m_particleSystem.particleCount;
            var toEmit = newCount - oldCount;
            if (toEmit <= 0) return;
            var particles = new ParticleSystem.Particle[newCount];
            m_particleSystem.GetParticles(particles);
            for (int i = 0; i < toEmit; ++i)
                particles[particles.Length - 1 - i].position = emit[i];
            m_particleSystem.SetParticles(particles);
        }
    }
}
