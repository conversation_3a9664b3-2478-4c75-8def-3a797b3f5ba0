using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(LayoutElement))]
public class PlatformLayoutModifier : MonoBehaviour
{
    void Start()
    {
#if UNITY_IOS || UNITY_ANDROID
        var le = GetComponent<LayoutElement>();
        if (Utility.IsTablet) le.preferredHeight = GlobalData.Me.m_tabletPreferredHeightOverride;
        else le.preferredHeight = GlobalData.Me.m_phonePreferredHeightOverride;
#endif
    }
}
