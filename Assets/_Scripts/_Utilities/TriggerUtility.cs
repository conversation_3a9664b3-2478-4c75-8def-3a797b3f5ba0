using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

public class TriggerUtility : MonoBehaviour
{
    private List<string> creatures = null;
    public List<string> Creatures
    {
        get
        {
            if (creatures == null)
            {
                creatures = new List<string>();
                foreach (var creature in MACreatureInfo.GetList)
                {
                    Creatures.Add(creature.m_name);
                }
            }

            return creatures;
        }
    }
    
    public int creatureCount = 0;

    [HideInInspector]
    public int creatureIndex = 0;
    private string Creature
    {
        get
        {
            return Creatures[creatureIndex];
        }
    }

    public string namedPointSpawn = "";
    public string buildingSpawn = "";
    private GameObject spawn = null;
    private GameObject Spawn
    {
        get
        {
            if (spawn == null)
            {
                spawn = new GameObject("SpawnPos");
                spawn.transform.SetParent(transform);
                spawn.transform.position = transform.position;
            }

            return spawn;
        }
    }
    private GameObject destination = null;
    private GameObject Destination
    {
        get
        {
            if (destination == null)
            {
                destination = new GameObject("Destination");
                destination.transform.SetParent(transform);
                destination.transform.position = transform.position;
            }

            return destination;
        }
    }

    public string SerialiseTrigger()
    {
        var sp = Spawn.transform.position;
        var dp = Destination.transform.position;

        string spawnStr = $"Pos[{sp.x},{sp.z}]";
        string rotStr = ",Rot[0]";
        if (!string.IsNullOrEmpty(namedPointSpawn))
        {
            spawnStr = $"NamedPoint[{namedPointSpawn}]";
            rotStr = "";
        }
        else if (!string.IsNullOrEmpty(buildingSpawn))
        {
            spawnStr = $"Building[{buildingSpawn}]";
            rotStr = "";
        }
        if (creatureCount > 1)
            return $"SpawnCreatures({creatureCount},{Creature},CreatureInfo[{Creature}],{spawnStr},Pos[{dp.x},{dp.z}]{rotStr})";
        
        return $"SpawnCreature({Creature},CreatureInfo[{Creature}],{spawnStr},Pos[{dp.x},{dp.z}]{rotStr})";
    }

    public void DeserialiseTrigger(string trigger)
    {
        string spawnStr = "";
        string destinationStr = "";
        if (trigger.StartsWith("SpawnCreature("))
        {
            var functions = MAParserSupport.ParseString(trigger);
            var args = functions[0].m_args;
            creatureIndex = MACreatureInfo.GetList.FindIndex(x => x.m_name == args[0]);
            spawnStr = args[2];
            destinationStr = args[3];
        }
        else if (trigger.StartsWith("SpawnCreatures("))
        {
            var functions = MAParserSupport.ParseString(trigger);
            var args = functions[0].m_args;
            creatureCount = int.Parse(args[0]);
            creatureIndex = MACreatureInfo.GetList.FindIndex(x => x.m_name == args[1]);
            spawnStr = args[3];
            destinationStr = args[4];
        }

        if (spawnStr.StartsWith("NamedPoint"))
        {
            var np = (NamedPoint)MAParserSupport.ConvertNamedPoint(spawnStr.Split('[', ']'));
            Spawn.transform.position = np.transform.position;
            namedPointSpawn = spawnStr.Substring(11, spawnStr.Length - 11 - 1);
        }
        else if (spawnStr.StartsWith("Building"))
        {
            var b = (MABuilding)MAParserSupport.ConvertBuilding(spawnStr.Split('[', ']'));
            Spawn.transform.position = b.DoorPosOuter;
            namedPointSpawn = spawnStr.Substring(9, spawnStr.Length - 9 - 1);
        }
        else
        {
            Spawn.transform.position = (Vector3)MAParserSupport.ConvertPos(spawnStr.Split('[', ']'));
            namedPointSpawn = "";
        }
        Destination.transform.position = (Vector3)MAParserSupport.ConvertPos(destinationStr.Split('[', ']'));
    }
}

// Waypoint(Pos[-156.08,-149.35],SpawnCreature(Zombie,CreatureInfo[Zombie],Pos[-156.08,-149.35],Pos[-186.08,-149.35],Rot[0]))
// Waypoint(Pos[-188.28,-152.2])
// Waypoint(Pos[-217.9,-143.2])
// Waypoint(Pos[-195.8,-93])
// Waypoint(Pos[-192.7,-46.9])
// Waypoint(Pos[-216.2,-0.6])
// Waypoint(Pos[-192.7,60.1])
// Waypoint(NamedPoint[EasternTunnel])

// Waypoint(Pos[-156.08,-149.35],ListStr{SpawnCreatures(3,Zombie,CreatureInfo[Zombie],NamedPoint[EasternTunnel],Pos[-186.08,-149.35]);;SpawnCreature(Zombie,CreatureInfo[Zombie],Pos[-150.08,-149.35],Pos[-180.08,-149.35],Rot[0])})
// Waypoint(Pos[-188.28,-152.2])
// Waypoint(Pos[-217.9,-143.2])
// Waypoint(Pos[-195.8,-93])
// Waypoint(Pos[-192.7,-46.9])
// Waypoint(Pos[-216.2,-0.6])
// Waypoint(Pos[-192.7,60.1])
// Waypoint(NamedPoint[EasternTunnel])


#if UNITY_EDITOR
[CanEditMultipleObjects]
[CustomEditor(typeof(TriggerUtility))]
public class TriggerUtilityEditor : Editor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();

		TriggerUtility tu = (TriggerUtility)target;

		if (Application.isPlaying)
        {
            tu.creatureIndex = EditorGUILayout.Popup(tu.creatureIndex, tu.Creatures.ToArray());
		}
	}
}
#endif
