using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

public class WaypointPathUtility : MonoBehaviour
{
    [SerializeField]
    [Multiline(10)]
    private string waypoints = "";

    public void SerialiseWaypoints()
    {
        string path = "";
        for (int i = 0; i < transform.childCount; ++i)
        {
            var waypoint = transform.GetChild(i);
            if (waypoint == transform)
                continue;

            List<string> triggers = new List<string>();
            for (int j = 0; j < waypoint.childCount; ++j)
            {
                var trigger = waypoint.GetChild(j);
                if (trigger == waypoint)
                    continue;

                var tu = trigger.GetComponent<TriggerUtility>();
                triggers.Add(tu.SerialiseTrigger());
            }

            string triggersStr = "";
            if (triggers.Count == 1)
                triggersStr += "," + triggers[0];
            else if (triggers.Count > 1)
            {
                triggersStr += ",ListStr{" + triggers[0];
                for (int t = 1; t < triggers.Count; ++t)
                {
                    triggersStr += ";;" + triggers[t];
                }
                triggersStr += "}";
            }
            path += $"Waypoint(Pos[{waypoint.position.x},{waypoint.position.z}]{triggersStr})\n";
        }

        waypoints = path;
        Debug.LogError(path);
    }

    public void DeserialiseWaypoints()
    {
        transform.DestroyChildrenImmediately();

        int id = MAWaypointPathController.DecodeWaypoints(waypoints);
        var path = MAWaypointPathController.GetWaypointPath(id);
        for (int i = 0; i < path.Count; ++i)
        {
            var waypoint = path.GetWaypointItem(i);
            if (waypoint == null)
                return;
            
            var w = new GameObject($"Waypoint_{i}");
            var wt = w.transform;
            wt.SetParent(transform);
            wt.position = new Vector3(waypoint.m_pos.x, 0f, waypoint.m_pos.z);
            wt.position = wt.position.GroundPosition();

            if (waypoint.m_triggers != null && waypoint.m_triggers.Count > 0)
            {
                for (int j = 0; j < waypoint.m_triggers.Count; ++j)
                {
                    var t = new GameObject($"Trigger_{j}");
                    t.transform.SetParent(wt);
                    t.transform.position = wt.position;
                    var trigger = t.AddComponent<TriggerUtility>();
                    trigger.DeserialiseTrigger(waypoint.m_triggers[j]);
                }
            }
        }
    }
}


#if UNITY_EDITOR
[CanEditMultipleObjects]
[CustomEditor(typeof(WaypointPathUtility))]
public class WaypointUtilityEditor : Editor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();

		WaypointPathUtility wu = (WaypointPathUtility)target;

		if (Application.isPlaying)
		{
            if (GUILayout.Button($"Serialise Waypoints"))
            {
                wu.SerialiseWaypoints();
            }
            if (GUILayout.Button($"Deserialise Waypoints"))
            {
                wu.DeserialiseWaypoints();
            }
		}
	}
}
#endif
