using UnityEngine;
using System.Collections.Generic;

public static class RandomNameGenerator
{
    private static List<string> s_maleFirstNames = new List<string>
    {
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Easton", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
        "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
    };
    
    private static <PERSON><string> s_female<PERSON><PERSON>t<PERSON><PERSON>s = new <PERSON><string>
    {
        "Abigail", "Addison", "Adele", "Alexa", "Alice", "Alyssa", "Amelia", "Amy", "Anastasia", "Angela",
        "Anna", "Ariana", "Aubrey", "Aurora", "Autumn", "Ava", "Bailey", "Bella", "Bethany", "Brianna",
        "Brooke", "Camila", "Caroline", "Catherine", "Charlotte", "Chloe", "Claire", "Clara", "Daisy", "Danielle",
        "Delilah", "Destiny", "Diana", "Eleanor", "Elena", "Eliza", "Elizabeth", "Ella", "Ellie", "Emily",
        "Emma", "Erin", "Evelyn", "Faith", "Felicity", "Gabriella", "Genesis", "Georgia", "Grace", "Hailey",
        "Hannah", "Harper", "Hazel", "Isabella", "Isla", "Ivy", "Jade", "Jasmine", "Jessica", "Josephine",
        "Julia", "Juliana", "Kaitlyn", "Katherine", "Kayla", "Keira", "Kylie", "Laila", "Lauren", "Layla",
        "Leah", "Lillian", "Lily", "Lucy", "Luna", "Madeline", "Madison", "Maria", "Margaret", "Maya",
        "Melanie", "Mia", "Mila", "Miranda", "Naomi", "Natalie", "Nicole", "Nina", "Olivia", "Paige",
        "Penelope", "Peyton", "Quinn", "Rachel", "Rebecca", "Rose", "Ruby", "Samantha", "Savannah", "Scarlett",
        "Serena", "Sophia", "Stella", "Tessa", "Trinity", "Valentina", "Valerie", "Vanessa", "Victoria", "Zoe"
    };
    
    private static List<string> s_lastNames = new List<string>
    {
        "Adams", "Allen", "Anderson", "Bailey", "Baker", "Barnes", "Bell", "Bennett", "Brooks", "Brown",
        "Bryant", "Butler", "Campbell", "Carter", "Clark", "Collins", "Cook", "Cooper", "Cox", "Davis",
        "Diaz", "Edwards", "Evans", "Flores", "Foster", "Garcia", "Gonzalez", "Gray", "Green", "Hall",
        "Harris", "Hernandez", "Hill", "Howard", "Hughes", "Jackson", "James", "Jenkins", "Johnson", "Jones",
        "Kelly", "King", "Lee", "Lewis", "Long", "Lopez", "Martin", "Martinez", "Miller", "Mitchell",
        "Moore", "Morales", "Morgan", "Morris", "Murphy", "Nelson", "Nguyen", "Parker", "Patterson", "Perez",
        "Perry", "Peterson", "Phillips", "Powell", "Price", "Ramirez", "Reed", "Reyes", "Richardson", "Rivera",
        "Roberts", "Robinson", "Rodriguez", "Rogers", "Ross", "Russell", "Sanchez", "Sanders", "Scott", "Smith",
        "Stewart", "Sullivan", "Taylor", "Thomas", "Thompson", "Torres", "Turner", "Walker", "Ward", "Washington",
        "Watson", "White", "Williams", "Wilson", "Wood", "Wright", "Young", "Andrews", "Armstrong", "Bradley",
        "Chapman", "Craig", "Duncan", "Harrison", "Henderson", "Lane"
    };
    
    public static string GenerateRandomMaleName(int _seed = -1)
    {
        if(_seed > -1) Random.InitState(_seed);
        return $"{s_maleFirstNames[Random.Range(0, s_maleFirstNames.Count)]} {s_lastNames[Random.Range(0, s_lastNames.Count)]}";
    }
    
    public static string GenerateRandomFemaleName(int _seed = -1)
    {
        if(_seed > -1) Random.InitState(_seed);
        return $"{s_femaleFirstNames[Random.Range(0, s_femaleFirstNames.Count)]} {s_lastNames[Random.Range(0, s_lastNames.Count)]}";
    }
}
