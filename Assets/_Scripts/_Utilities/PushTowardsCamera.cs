using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PushTowardsCamera : MonoBehaviour
{
	public Vector3 m_initialPosition;
	public bool m_freezeXZ = false;
	public float m_pushForward = 0;

	public void Start()
	{
		m_initialPosition = transform.localPosition;
		FaceCamera();
	}

	private void LateUpdate()
    {
		FaceCamera();
	}
	
	private void FaceCamera()
	{
		var cam = Camera.main; // can't cache this atm, HDRP often returns SceneCamera
		if (cam == null) 
			return;
		var toCam = cam.transform.position - transform.position;
		if (m_freezeXZ)
			toCam.y = 0f;
		toCam.Normalize();

		transform.localPosition = m_initialPosition;		
		transform.position += toCam * m_pushForward;
	}
}