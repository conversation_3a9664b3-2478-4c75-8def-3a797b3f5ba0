using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PSysOutline : MonoBehaviour
{
    private List<ParticleSystem> m_particles;
    public void Initialise(Color _colour, float _size)
    {
        var rnds = GetComponentsInChildren<MeshRenderer>();
        m_particles = new List<ParticleSystem>();
        foreach (var r in rnds)
        {
            if (r.name == "Balloon") continue;
            var psysObj = Instantiate(GlobalData.Me.m_psysOutlinePrefab);
            psysObj.transform.SetParent(r.transform, false);
            var psys = psysObj.GetComponent<ParticleSystem>();
            var psysMain = psys.main;
            psysMain.startSize = _size;
            var psysShape = psys.shape;
            psysShape.meshRenderer = r;
            var psysColor = psys.colorOverLifetime;
            Gradient grad = new Gradient();
            grad.SetKeys( 
                new GradientColorKey[]
                {
                    new GradientColorKey(_colour, 0.0f),
                    new GradientColorKey(_colour, 1.0f)
                }, 
                new GradientAlphaKey[]
                {
                    new GradientAlphaKey(0.0f, 0.0f),
                    new GradientAlphaKey(1.0f, 0.25f),
                    new GradientAlphaKey(1.0f, 0.75f),
                    new GradientAlphaKey(0.0f, 1.0f)
                } );
            psysColor.color = grad;
            m_particles.Add(psys);
        }
    }

    void ShutDown()
    {
        foreach (var p in m_particles) Destroy(p.gameObject);
        m_particles = null;
    }

    public void Stop()
    {
        StartCoroutine(Co_Stop());
    }

    IEnumerator Co_Stop()
    {
        foreach (var p in m_particles) p.Stop();
        while (true)
        {
            bool stillGoing = false;
            foreach (var p in m_particles)
            {
                if (p.particleCount > 0) stillGoing = true;
            }
            if (!stillGoing) break;
            yield return null;
        }
        Destroy(this);
    }

    void OnDestroy() { ShutDown(); }
    public static PSysOutline Create(GameObject _root, Color _colour, float _size = 1)
    {
        var outline = _root.AddComponent<PSysOutline>();
        outline.Initialise(_colour, _size);
        return outline;
    }
    public static void Remove(PSysOutline _on)
    {
        if (_on == null) return;
        Destroy(_on);
    }
    public static void Remove(GameObject _on)
    {
        Remove(_on.GetComponent<PSysOutline>());
    }
}
