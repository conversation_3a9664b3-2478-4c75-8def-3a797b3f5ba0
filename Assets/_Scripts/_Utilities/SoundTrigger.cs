using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

public class SoundTrigger : MonoBehaviour
{
    public AkEventHolder m_onAwakeEvent;
    public string m_onAwakeSound;
    public AkEventHolder[] m_intervalEvents;
    public string[] m_intervalSounds;
    public float m_soundIntervalMinimum;
    public float m_soundIntervalMaximum;
    public float m_onlyInRange = GameManager.c_maxTreeSoundRange;
    public bool m_isTree = true;
    public int m_uniqueSoundSets = 10;
    public bool m_rememberPlayPosition = false;
    private int m_onAwakeSoundHandle;
    private float m_nextSoundTime;
#if UNITY_EDITOR
    public string m_debug;
    public void DDebug(string _msg) => m_debug = _msg;
#else
    public void DDebug(string _msg) { }
#endif

    IEnumerator Start()
    {
        if (m_onlyInRange > 0)
        {
            if (m_isTree) enabled = false;
            yield break;
        }
        while (GameManager.Me == null || GameManager.Me.LoadComplete == false)
            yield return null;
        if (enabled) // check if this was disabled while waiting for load to complete
            TriggerSound();
    }
    
    public void CopyDataFrom(SoundTrigger _other)
    {
        m_onAwakeEvent = _other.m_onAwakeEvent;
        m_intervalEvents = _other.m_intervalEvents;
        m_onAwakeSound = _other.m_onAwakeSound;
        m_intervalSounds = _other.m_intervalSounds;
        m_soundIntervalMinimum = _other.m_soundIntervalMinimum;
        m_soundIntervalMaximum = _other.m_soundIntervalMaximum;
    }

    public void CheckRange()
    {
        if (m_onlyInRange <= 0) return;
        if (gameObject.activeInHierarchy == false) return; // don't do anything if this is disabled
        var distanceSqrd = (Camera.main.transform.position - transform.position).sqrMagnitude;
        if (distanceSqrd < m_onlyInRange * m_onlyInRange)
        {
            if (m_onAwakeSoundHandle <= 0)
            {
                if (m_isTree) enabled = true;
                TriggerSound();
            }
        }
        else
        {
            if (m_onAwakeSoundHandle > 0)
            {
                if (m_isTree) enabled = false;
                ClearSound();
            }
        }
    }

    void Update()
    {
        CheckRange();
        if (m_nextSoundTime < Time.time)
        {
            if (m_intervalEvents != null && m_intervalEvents.Length > 0)
                m_intervalEvents.PickRandom().Play(gameObject);
            else if (m_intervalSounds.Length > 0)
                AudioClipManager.Me.PlaySound(m_intervalSounds.PickRandom(), gameObject);
            else return;
            m_nextSoundTime = Time.time + Random.Range(m_soundIntervalMinimum, m_soundIntervalMaximum);
        }
    }

    void OnDestroy() => ClearSound();
    void OnDisable() => ClearSound();
    void OnEnable() => TriggerSound();

    bool EnableAk(bool _enable)
    {
        var ak = GetComponent<AkGameObj>();
        if (ak != null)
        {
            ak.enabled = _enable;
            return true;
        }
        return false;
    }

    int m_lastPlayPosition = 0;
    void TriggerSound()
    {
        if (m_onAwakeSoundHandle > 0) return;
        if (AudioClipManager.Me == null) return; // starting up
        if (m_onAwakeEvent.IsValid() || string.IsNullOrEmpty(m_onAwakeSound) == false)
        {
            bool setSoundSet = (m_isTree && EnableAk(true) == false) || (m_isTree == false);
            if (setSoundSet && m_uniqueSoundSets > 0)
                AudioClipManager.Me.SetSoundset(gameObject, (int)((uint)gameObject.GetInstanceID() / 77) % m_uniqueSoundSets);
            m_onAwakeSoundHandle = AudioClipManager.Me.PlaySound(m_onAwakeEvent?.Event(m_onAwakeSound) ?? m_onAwakeSound, gameObject, _allowGetSoundPosition:m_rememberPlayPosition);
            DDebug($"Handle {m_onAwakeSoundHandle} started at play position {m_lastPlayPosition}");
            if (m_lastPlayPosition != 0)
                AudioClipManager.Me.Seek(m_onAwakeSoundHandle, m_lastPlayPosition, gameObject);
        }
    }

    void ClearSound()
    {
        if (AudioClipManager.Me == null) return; // shutting down
        if (m_onAwakeSoundHandle > 0)
        {
            if (m_rememberPlayPosition)
                m_lastPlayPosition = AudioClipManager.Me.GetPlayPosition(m_onAwakeSoundHandle);
            DDebug($"Handle {m_onAwakeSoundHandle} stopped at play position {m_lastPlayPosition}");
            AudioClipManager.Me.StopSound(m_onAwakeSoundHandle, gameObject);
            m_onAwakeSoundHandle = 0;
            if (m_isTree) EnableAk(false);
        }
    }
}
