using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class HideByPlatform : MonoBehaviour {
	public bool m_hideOniOS = false;
	public bool m_hideOnAndroid = false;
	public bool m_hideOnWebGL = false;
	public bool m_hideOnWindows = false;
	public bool m_hideOnMac = false;
	public bool m_hideOnEditor = false;
	void Awake() {
#if UNITY_EDITOR
		bool hide = m_hideOnEditor;
#elif UNITY_IOS
		bool hide = m_hideOniOS;
#elif UNITY_ANDROID
		bool hide = m_hideOnAndroid;
#elif UNITY_WEBGL
		bool hide = m_hideOnWebGL;
#elif UNITY_MACOS
		bool hide = m_hideOnMac;
#elif UNITY_WINDOWS
		bool hide = m_hideOnWindows;
#else
		bool hide = true;
#endif
		if (hide) gameObject.SetActive(false);
	}
}
