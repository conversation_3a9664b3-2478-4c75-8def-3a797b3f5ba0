using System.Collections;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(TriggeredAnimationState))]
public class TriggeredAnimationStateEditor : Editor {
	public override void OnInspectorGUI() {
		DrawDefaultInspector();
		TriggeredAnimationState t = target as TriggeredAnimationState;
		GUILayout.Label("Contained items:");
		if (t.Tracked.Count == 0)
			EditorGUILayout.TextField("   ", "<none>");
		else
			foreach (var i in t.Tracked) {
				EditorGUILayout.TextField("   ", i.transform.Path());
			}
	}
}
#endif

	public class TriggeredAnimationState : MonoBehaviour {
	public Animator m_animator;
	public string m_emptyTrigger;
	public string m_nonEmptyTrigger;
	public string m_triggerOnSoundHook = "PlaySound_DoorOpen";
	public string m_triggerOffSoundHook = "PlaySound_DoorClose";
	private List<GameObject> m_tracked = new List<GameObject>(); public List<GameObject> Tracked { get { return m_tracked; } }
	private bool m_errorShown = false;
	private void SetTriggerOrBool(bool _empty) {
		if (transform.IsChildOf(DesignHarness.Me.transform)) return;
		var block = GetComponentInParent<Block>();
		AudioClipManager.Me.PlaySound(_empty ? m_triggerOffSoundHook : m_triggerOnSoundHook, block?.transform ?? transform);
		if (m_animator == null) {
			m_animator = GetComponent<Animator>();
			if (m_animator == null) {
				if (!m_errorShown) {
					m_errorShown = true;
					Debug.LogError($"TriggeredAnimationState with null animator", gameObject);
				}
				return;
			}
		}
		if (m_emptyTrigger == m_nonEmptyTrigger) {
			m_animator.SetBool(m_emptyTrigger, _empty);
		} else {
			m_animator.SetTrigger(_empty ? m_emptyTrigger : m_nonEmptyTrigger);
		}
	}

	private void Awake()
	{
		if (gameObject.GetComponent<CameraSoundOcclusion>() == null)
		{
			gameObject.AddComponent<CameraSoundOcclusion>();
		}
	}

	bool IsValidTrackedObject(GameObject _go)
	{
        if (_go == null) return false;
		if (_go.GetComponent<Terrain>() != null) return false;
		if (_go.GetComponent<UnityEngine.Rendering.Volume>() != null) return false;
		var myCmd = GetComponentInParent<NGCommanderBase>();
		if (myCmd == null)
		{
			if(GetComponentInParent<MAWildBlock>() != null)
				return false;
			return true;
		}
		var theirCmd = _go.GetComponentInParent<NGCommanderBase>();
		if (theirCmd != null) return myCmd != theirCmd;
		var theirChr = _go.GetComponentInParent<MACharacterBase>();
		if (theirChr != null)
		{
			// If _go is a worker then only allow the target if it's destination is near the door
			// Alternatively we could use dot product of directions to determine if the worker is moving towards the door
			var theirDest = theirChr.m_nav.LastPathPos;
			var doorPosInner = myCmd.DoorPosInner;
			const float c_maxNavDistance = 0.5f;
			if (theirDest != null && (theirDest.Value - doorPosInner).xzSqrMagnitude() <
			    c_maxNavDistance * c_maxNavDistance)
				return true;
			var theirSrc = theirChr.m_nav.FirstPathPos;
			if (theirSrc != null && (theirSrc.Value - doorPosInner).xzSqrMagnitude() < c_maxNavDistance * c_maxNavDistance)
				return true;
			if ((theirChr.transform.position - doorPosInner).xzSqrMagnitude() <
			    c_maxNavDistance * c_maxNavDistance)
				return true;
			return false;
		}
		return true;
	}

	void Track(GameObject _go) {
		if (!IsValidTrackedObject(_go)) return;
		m_tracked.Add(_go);
	}
	void Untrack(GameObject _go) {
		m_tracked.Remove(_go);
	}
	private void OnTriggerEnter(Collider other) {
		Track(other.gameObject);
	}
	private void OnTriggerExit(Collider other) {
		Untrack(other.gameObject);
	}

	int m_lastTrackedCount = 0;
	private void LateUpdate()
	{
		for (int i = m_tracked.Count - 1; i >= 0; i--)
			if (m_tracked[i] == null || !m_tracked[i].activeInHierarchy)
				Untrack(m_tracked[i]);
		
		bool wasEmpty = m_lastTrackedCount == 0;
		bool isEmpty = m_tracked.Count == 0;
		if (wasEmpty != isEmpty)
			SetTriggerOrBool(isEmpty);
		m_lastTrackedCount = m_tracked.Count;
	}

	public void SlidingDoorClose()
	{
		AudioClipManager.Me.PlaySoundOld("PlaySound_SlidingDoor_Close", transform);
	}
	public void SlidingDoorOpen()
	{
		AudioClipManager.Me.PlaySoundOld("PlaySound_SlidingDoor_Open", transform);
	}
}
