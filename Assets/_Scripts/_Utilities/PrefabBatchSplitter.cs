using System.Collections.Generic;
using UnityEngine;

public class PrefabBatchSplitter : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IBatchPartitioner
{
    public float m_proximity = float.PositiveInfinity;
    
    public Component Component() => this;

    public List<List<Transform>> GetExcludedTransforms()
    {
        var groups = new List<List<Transform>>();
        foreach (var mR in transform.GetComponentsInChildren<MeshRenderer>())
        {
            var position = mR.transform.position;
            bool sorted = false;
            foreach (var group in groups)
            {
                bool fitsGroup = true;
                foreach (var item in group)
                {
                    if ((position - item.position).sqrMagnitude > m_proximity * m_proximity)
                    {
                        fitsGroup = false;
                        break;
                    }
                }

                if (fitsGroup)
                {
                    sorted = true;
                    group.Add(mR.transform);
                }
            }
            if (!sorted)
                groups.Add(new() { mR.transform });
        }
        return groups;
    }

    public void OnBatch(Dictionary<MeshRenderer, MeshRenderer> _replaced) { }
}
