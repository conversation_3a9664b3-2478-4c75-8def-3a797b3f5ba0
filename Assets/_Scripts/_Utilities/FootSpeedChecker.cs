using UnityEngine;

public class FootSpeedChecker : MonoBehaviour
{
	public Transform parent;
	Vector3 prevPos;
	public bool isAnimFwd = true;
	public bool restart = false;
	float totTime = 0f;
	float totDist = 0f;
	public float averageSpeed;

	private void Start()
	{
		parent = GetComponentInParent<Animator>().transform;
		prevPos = transform.position;
	}

	private void Update()
	{
		Vector3 pos = transform.position;
		Vector3 delta = pos - prevPos;
		prevPos = pos;
		if (pos.y > 0.05f)
			return;

		if (restart)
		{
			restart = false;
			totDist = 0f;
			totTime = 0f;
			return;
		}
		var fwd = isAnimFwd ? -parent.forward : parent.forward;
		var fwdDelta = Vector3.Dot(delta, fwd);
		totTime += Time.deltaTime;
		totDist += fwdDelta;
		averageSpeed = totDist / totTime;
	}
}
