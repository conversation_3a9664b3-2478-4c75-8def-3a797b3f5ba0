using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Skew : MonoBehaviour
{
    public float m_angleSkew = 0;
    public Transform m_child;

    const float c_root2 = 1.4142f;
    void UpdateSkew()
    {
        float angle = 90 + m_angleSkew;
        float angleRadians = angle * Mathf.Deg2Rad;
        float halfAngle = angle * .5f;
        float halfAngleRadians = halfAngle * Mathf.Deg2Rad;
        var parent = transform;
        var child = m_child ?? parent.GetChild(0);
        parent.localEulerAngles = Vector3.right * halfAngle;
        parent.localScale = new Vector3(1f, Mathf.Sin(halfAngleRadians), Mathf.Cos(halfAngleRadians));
        child.localEulerAngles = Vector3.right * -45;
        child.localScale = new Vector3(1f, c_root2 / Mathf.Sin(angleRadians), c_root2);
        var childPos = parent.position + new Vector3(0, 0, Mathf.Tan(m_angleSkew * Mathf.Deg2Rad) * .5f);
        if (parent.parent != null)
            child.position = parent.parent.localToWorldMatrix.MultiplyPoint(childPos);
        else
            child.position = childPos;
    }
    
#if UNITY_EDITOR
    void OnValidate() => UpdateSkew();
#endif
}
