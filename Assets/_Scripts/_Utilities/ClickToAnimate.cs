using UnityEngine;
using UnityEngine.EventSystems;

public class ClickToAnimate : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerClickHandler, ICharacterObjectInteract
{
    public Animator m_animator;
    public string m_triggerName;
    public string m_boolName;
    public AkEventHolder m_audioEvent;
    public bool m_supportsPossessed = true;
    public bool m_supportsHand = true;
    public void OnPointerClick(PointerEventData eventData) { if (eventData.button == PointerEventData.InputButton.Left) Click(); }
    public void Click()
    {
        if (m_supportsHand == false) return;
#if UNITY_EDITOR
        UnityEditor.Selection.activeGameObject = gameObject;
#endif
        if (m_animator != null && string.IsNullOrEmpty(m_triggerName) == false)
            m_animator.SetTrigger(m_triggerName);
        if (m_animator != null && string.IsNullOrEmpty(m_boolName) == false)
            m_animator.SetBool(m_boolName, !m_animator.GetBool(m_boolName));
        if (m_audioEvent != null)
            m_audioEvent.Play(gameObject);
    }
    
    private bool m_interactDisabled = false;

    public string InteractType => null;
    public bool CanInteract(NGMovingObject _chr) => _chr != null && m_interactDisabled == false && m_supportsPossessed &&
                                                    GameManager.Me.GlobalInteractCheck(_chr) && _chr is not MAAnimal &&
                                                    NGMovingObject.c_defaultInteractRange * NGMovingObject.c_defaultInteractRange >= transform.position.DistanceXZSq(_chr.transform.position);

    public string GetInteractLabel(NGMovingObject _chr) => "Interact";
    public void DoInteract(NGMovingObject _chr) => Click();
    public bool RunInteractSequence(System.Collections.Generic.List<FollowChild> _chr) => InteractionSequenceNode.AttemptInteraction(_chr, gameObject);
    public void EnableInteractionTriggers(bool _b) => m_interactDisabled = !_b;
    public float AutoInteractTime => 0;
}
