using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;

public class DepthOfFieldFromTransforms : MonoBehaviour
{
    public Transform m_fixedFarFocus;
    public float m_focusLength = .5f;
    private DepthOfField m_dof;
    private Volume m_volume;

    void Start()
    {
        var volume = GetComponent<Volume>();
        if (volume == null) return;
        var profile = volume.profile;
        if (profile == null) return;
        if (!profile.TryGet(out m_dof))
            m_dof = profile.Add<DepthOfField>();
        m_volume = volume;
    }

    void Update()
    {
        float focusDistance = (m_fixedFarFocus.transform.position - Camera.main.transform.position).magnitude;
        if (GameSettings.SRPOptions.IsURP == false)
        {
            if (!m_volume.profile.TryGet<UnityEngine.Rendering.HighDefinition.DepthOfField>(out var dof))
                dof = m_volume.profile.Add<UnityEngine.Rendering.HighDefinition.DepthOfField>(true);
            dof.farFocusStart.Override(focusDistance);
            dof.farFocusEnd.Override(focusDistance + m_focusLength);
        }
        else
        {
            m_dof.focusDistance.Override(focusDistance);
        }
    }
}
