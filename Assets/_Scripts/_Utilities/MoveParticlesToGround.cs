using UnityEngine;

[RequireComponent(typeof(ParticleSystem))]
public class MoveParticlesToGround : MonoBehaviour
{
    public float m_distanceAboveGround = 0;
    public float m_movementThreshold = .2f;
    public float m_riseTime = 0;
    public float m_riseDistance = 0;
    public float m_unriseTime = 0;
    private ParticleSystem m_particleSystem;
    void Start()
    {
        m_particleSystem = GetComponent<ParticleSystem>();
    }

    void LateUpdate()
    {
        var thresholdSqrd = m_movementThreshold * m_movementThreshold;
        var particles = new ParticleSystem.Particle[m_particleSystem.particleCount];
        m_particleSystem.GetParticles(particles);
        if (m_particleSystem.simulationSpace == ParticleSystemSimulationSpace.Local)
        {
            for (int i = 0; i < particles.Length; i++)
            {
                var pos = particles[i].position;
                var wpos = transform.TransformPoint(pos);
                var wposGround = wpos.GroundPosition(m_distanceAboveGround + ParticleRise(particles[i]));
                var newPos = transform.InverseTransformPoint(wposGround);
                if ((newPos - pos).sqrMagnitude > thresholdSqrd)
                    particles[i].position = newPos;
                particles[i].velocity = Vector3.zero;
                particles[i].angularVelocity3D = Vector3.zero;
            }
        }
        else
        {
            for (int i = 0; i < particles.Length; i++)
            {
                var pos = particles[i].position;
                var newPos = pos.GroundPosition(m_distanceAboveGround + ParticleRise(particles[i]));
                if ((newPos - pos).sqrMagnitude > thresholdSqrd)
                    particles[i].position = newPos;
                particles[i].velocity = Vector3.zero;
                particles[i].angularVelocity3D = Vector3.zero;
            }
        }
        m_particleSystem.SetParticles(particles, particles.Length);
    }

    private float ParticleRise(UnityEngine.ParticleSystem.Particle _particle)
    {
        if (m_riseDistance <= 0) return 0;
        var remainingTime = _particle.remainingLifetime;
        var particleTime = _particle.startLifetime - remainingTime;
        float riseProgress = 0;
        if (particleTime < m_riseTime)
            riseProgress = Mathf.Clamp01(particleTime / m_riseTime);
        else if (remainingTime < m_unriseTime)
            riseProgress = Mathf.Clamp01(remainingTime / m_unriseTime);
        else
            return 0;
        return (riseProgress - 1) * m_riseDistance; // -m_riseDistance to 0 over m_riseTime seconds
    }
}
