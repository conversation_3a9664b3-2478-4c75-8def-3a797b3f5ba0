using UnityEngine;

public class AkRTPCFromPositions : MonoBehaviour
{
    public AkRTPCHolder m_rtpc;
    public Transform m_referenceTransform;
    public enum EAxis { X, Y, Z }
    public EAxis m_axis = EAxis.Y;
    public float m_scale = 1;
    
    private float m_lastValue = 1e23f;

    void Update()
    {
        if (m_referenceTransform == null || m_rtpc == null) return;
        var delta = transform.position - m_referenceTransform.position;
        float value = 0;
        switch (m_axis)
        {
            case EAxis.X:
                value = delta.x * m_scale;
                break;
            case EAxis.Y:
                value = delta.y * m_scale;
                break;
            case EAxis.Z:
                value = delta.z * m_scale;
                break;
            default:
                value = 0;
                break;
        }
        if (value.Nearly(m_lastValue, .01f)) return;
        m_lastValue = value;
        m_rtpc.SetValue(gameObject, value);
    }
}
