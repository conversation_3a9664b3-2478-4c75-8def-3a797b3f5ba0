using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class EnableByName : MonoBehaviour
{
    [System.Serializable]
    public class EnableGroup
    {
        public string m_name;
        public List<GameObject> m_objects;

        public void Enable(bool _enable)
        {
            foreach (var o in m_objects) o.SetActive(_enable);
        }
    }
    public EnableGroup[] m_groups;

    public void Enable(string _groupName)
    {
        EnableGroup activeGroup = null;
        for (int i = 0; i < m_groups.Length; ++i)
        {
            var group = m_groups[i];
            if (group.m_name == _groupName)
                activeGroup = group;
            else
                group.Enable(false);
        }
        activeGroup?.Enable(true);
    }
}
