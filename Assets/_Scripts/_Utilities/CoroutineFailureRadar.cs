#if UNITY_EDITOR || DEVELOPMENT_BUILD
//#define CFR_ENABLED
#endif

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using UnityEngine;
using UnityEngine.SceneManagement;

[DefaultExecutionOrder(int.MinValue)]
public class CoroutineFailureRadar : MonoBehaviour
{
#if CFR_ENABLED
    const float WindowSeconds = 2.0f;
    static readonly Queue<Event> _events = new();
    static bool _installed;

    struct Event
    {
        public float t;
        public string type;
        public string who;
        public string scene;
        public string extra;
        public string stack;
    }

    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.AfterSceneLoad)]
    static void Install() =>
        new GameObject("CoroutineFailureRadar").AddComponent<CoroutineFailureRadar>();

    void Awake()
    {
        if (_installed) { Destroy(this); return; }
        _installed = true;
        DontDestroyOnLoad(gameObject);

        Application.logMessageReceived += OnLog;
        Application.logMessageReceivedThreaded += OnLog;

        SceneManager.sceneLoaded += (_, __) => SweepAttach();
        SceneManager.activeSceneChanged += (_, __) => SweepAttach();

        SweepAttach();
        InvokeRepeating(nameof(Prune), 0.5f, 0.25f);
        UnityEngine.Debug.Log("[CFR] CoroutineFailureRadar active. Will report near 'coroutine continue failure'.");

        StartLogTailer();
    }

    void OnDestroy()
    {
        Application.logMessageReceived -= OnLog;
        Application.logMessageReceivedThreaded -= OnLog;
        StopLogTailer();
        _installed = false;
    }

    // -------- detection ----------
    static void OnLog(string condition, string stacktrace, LogType type)
    {
        if (condition.StartsWith("[CFR]")) return; // ignore our own logs
        if (condition.IndexOf("coroutine continue failure", StringComparison.OrdinalIgnoreCase) >= 0)
            EmitReport();
    }

    void Update()
    {
        if (_pendingReport)
        {
            _pendingReport = false;
            EmitReport();
        }
    }

    static void EmitReport()
    {
        var now = Time.realtimeSinceStartup;
        var recent = _events.Where(e => now - e.t <= WindowSeconds).ToList();

        if (recent.Count == 0)
        {
            UnityEngine.Debug.LogWarning("[CFR] 'coroutine continue failure' but no recent host disable/destroy/stop in window.");
            return;
        }

        string Section(string key)
        {
            var list = recent.Where(e => e.type == key).OrderBy(e => e.t).ToList();
            if (list.Count == 0) return $"  {key}: (none)\n";
            var sb = new System.Text.StringBuilder().Append("  ").Append(key).Append(":\n");
            foreach (var e in list)
            {
                sb.Append($"    t-{(now - e.t):0.000}s  {e.who}  [{e.scene}]  {e.extra}\n");
                if (!string.IsNullOrEmpty(e.stack)) sb.Append("      at ").Append(e.stack).Append('\n');
            }
            return sb.ToString();
        }

        UnityEngine.Debug.LogWarning(
$"[CFR] Coroutine continue failure @ t={now:0.000}s — suspects in the last {WindowSeconds:0.0}s:\n" +
Section("Disable") + Section("Destroy") + Section("StopAll") + Section("StopOne"));
    }

    // -------- suspects ----------
    void Prune()
    {
        var now = Time.realtimeSinceStartup;
        while (_events.Count > 0 && now - _events.Peek().t > WindowSeconds)
            _events.Dequeue();
    }

    static void SweepAttach()
    {
        foreach (var mb in Resources.FindObjectsOfTypeAll<MonoBehaviour>())
        {
            if (!mb) continue;
            var go = mb.gameObject;
            if (!go || !go.scene.IsValid() || !go.activeInHierarchy) continue;
            if (!go.TryGetComponent<__CfrHostProbe>(out _))
                go.AddComponent<__CfrHostProbe>();
        }
    }

    private class __CfrHostProbe : MonoBehaviour
    {
        void OnDisable() => Push("Disable", gameObject, GetType().Name, null);
        void OnDestroy() => Push("Destroy", gameObject, GetType().Name, null);
    }

    static void Push(string type, GameObject go, string extra, string stack)
    {
        _events.Enqueue(new Event {
            t = Time.realtimeSinceStartup,
            type = type,
            who = go ? go.name : "(null)",
            scene = go && go.scene.IsValid() ? go.scene.name : "(no-scene)",
            extra = extra,
            stack = stack
        });
    }

    // -------- optional wrappers for StopCoroutine tracking ----------
    public static void StopAllCoroutinesTracked(MonoBehaviour host)
    {
        var st = new StackTrace(1, true).ToString();
        Push("StopAll", host ? host.gameObject : null, host ? host.GetType().Name : "(null)", st);
        host?.StopAllCoroutines();
    }
    public static void StopCoroutineTracked(MonoBehaviour host, Coroutine c)
    {
        var st = new StackTrace(1, true).ToString();
        Push("StopOne", host ? host.gameObject : null, host ? host.GetType().Name : "(null)", st);
        if (host != null && c != null) host.StopCoroutine(c);
    }

    // -------- log tailing fallback ----------
    class LogTailer : IDisposable
    {
        readonly string _path;
        readonly Action<string> _onLine;
        Thread _thread;
        volatile bool _run;

        public LogTailer(string path, Action<string> onLine)
        { _path = path; _onLine = onLine; }

        public void Start()
        {
            _run = true;
            _thread = new Thread(ThreadMain) { IsBackground = true, Name = "CFR-LogTailer" };
            _thread.Start();
        }

        void ThreadMain()
        {
            try
            {
                using var fs = new FileStream(_path, FileMode.Open, FileAccess.Read, FileShare.ReadWrite);
                using var sr = new StreamReader(fs);
                fs.Seek(0, SeekOrigin.End);
                while (_run)
                {
                    string line = sr.ReadLine();
                    if (line == null) { Thread.Sleep(50); continue; }
                    if (line.StartsWith("[CFR]")) continue; // skip our own lines
                    _onLine?.Invoke(line);
                }
            }
            catch { }
        }

        public void Dispose()
        {
            _run = false;
            try { _thread?.Join(250); } catch { }
        }
    }

    LogTailer _tailer;
    bool _pendingReport;

    void StartLogTailer()
    {
        var path = Application.consoleLogPath;
        if (string.IsNullOrEmpty(path)) return;
        if (!File.Exists(path))
        {
            StartCoroutine(WaitForLogAndTail());
            return;
        }
        _tailer = new LogTailer(path, line =>
        {
            if (line.IndexOf("coroutine continue failure", StringComparison.OrdinalIgnoreCase) >= 0)
                _pendingReport = true;
        });
        _tailer.Start();
    }

    System.Collections.IEnumerator WaitForLogAndTail()
    {
        var deadline = Time.realtimeSinceStartup + 10f;
        while (Time.realtimeSinceStartup < deadline && !File.Exists(Application.consoleLogPath))
            yield return null;
        if (File.Exists(Application.consoleLogPath)) StartLogTailer();
    }

    void StopLogTailer()
    {
        _tailer?.Dispose();
        _tailer = null;
    }
#endif
}
