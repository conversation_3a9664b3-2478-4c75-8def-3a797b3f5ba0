using System.Collections.Generic;
using UnityEngine;

public class SafeArea : MonoBehaviour
{
    public Transform m_target;
    public float m_radius = 30.0f;
    public float m_minTargetDistance = 40.0f;
    public float m_maxTargetDistance = 60.0f;

    public bool m_lockDrawGizmos = false;
    public bool m_debugClearArea = false;

    private Vector3 TargetPos => m_target != null ? m_target.position : transform.position;

    //private float m_checkTime;
    //private const float c_checkInterval = 10.0f;

    //private void Awake()
    //{
    //    m_checkTime = c_checkInterval;
    //}

    private void Update()
    {
        if(m_debugClearArea)
        {
            ClearArea();
            m_debugClearArea = false;
        }
        //m_checkTime -= Time.deltaTime;

        //if (m_checkTime <= 0.0f)
        //{
        //    ClearArea();
        //    m_checkTime = c_checkInterval;
        //}
    }

    public void ClearArea()
    {
        List<PickupAndThrowBehaviour> decorations = FindDecorationsInArea();

        foreach(var decoration in decorations)
        {
            SetInTargetArea(decoration.transform);
        }

        List<MAWildBlock> wildBlocks = FindWildBlocksInArea();

        foreach (var wildBlock in wildBlocks)
        {
            SetInTargetArea(wildBlock.transform);
        }

        List<MAQuestStone> questStones = FindQuestStonesInArea();

        foreach (var questStone in questStones)
        {
            SetInTargetArea(questStone.transform, Utility.ReseatType.GroundPosition, 1.0f);
        }
    }

    private List<PickupAndThrowBehaviour> FindDecorationsInArea()
    {
        List<PickupAndThrowBehaviour> decorations = new List<PickupAndThrowBehaviour>();

        var allDecorations = NGDecorationInfoManager.Me.m_decorationHolder.GetComponentsInChildren<PickupAndThrowBehaviour>();

        foreach(var decoration in allDecorations)
        {
            if(!decoration.Held)
            {
                if(IsInArea(decoration.transform))
                {
                    decorations.Add(decoration);
                }
            }
        }

        return decorations;
    }

    private List<MAWildBlock> FindWildBlocksInArea()
    {
        List<MAWildBlock> wildBlocks = new List<MAWildBlock>();

        var allWildBlocks = DesignTableManager.Me.m_wildBlockHolder.GetComponentsInChildren<MAWildBlock>();

        foreach (var wildBlock in allWildBlocks)
        {
            if (IsInArea(wildBlock.transform))
            {
                wildBlocks.Add(wildBlock);
            }
        }

        return wildBlocks;
    }

    private List<MAQuestStone> FindQuestStonesInArea()
    {
        List<MAQuestStone> questStones = new List<MAQuestStone>();

        var allQuestStones = MAQuestManager.Me.m_questHolder.GetComponentsInChildren<MAQuestStone>();

        foreach (var questStone in allQuestStones)
        {
            if (!questStone.IsBeingDragged)
            {
                if (IsInArea(questStone.transform))
                {
                    questStones.Add(questStone);
                }
            }
        }

        return questStones;
    }

    private void SetInTargetArea(Transform _transform, Utility.ReseatType _reseatType = Utility.ReseatType.PhysicsPosition, float _raise = 0f)
    {
        Vector3 targetPos = GetRandomTargetPos();
        targetPos = targetPos.GroundPosition(1.0f);
        _transform.position = targetPos;
        _transform.Reseat(_raise, _reseatType);
    }

    private Vector3 GetRandomTargetPos()
    {
        Vector3 targetPos = TargetPos;

        Vector3 randomDir = new Vector3(Random.Range(0f, 1f), 0f, Random.Range(0f, 1f));
        randomDir.Normalize();

        float randomDistance = Random.Range(m_minTargetDistance, m_maxTargetDistance);

        targetPos += randomDir * randomDistance;

        return targetPos;
    }

    private bool IsInArea(Transform _transform)
    {
        return _transform.position.DistanceXZSq(transform.position) <= m_radius * m_radius;
    }

#if UNITY_EDITOR
    private void OnDrawGizmos()
    {
        if (m_lockDrawGizmos)
        {
            DrawGizmos();
        }
    }

    private void OnDrawGizmosSelected()
    {
        if (!m_lockDrawGizmos)
        {
            DrawGizmos();
        }
    }

    private void DrawGizmos()
    {
        Vector3 pos = transform.position;
        Vector3 targetPos = TargetPos;

        SetGizmosColour(Color.green);
        Gizmos.DrawSphere(targetPos, m_maxTargetDistance);

        SetGizmosColour(Color.orange);
        Gizmos.DrawSphere(targetPos, m_minTargetDistance);

        SetGizmosColour(Color.red);
        Gizmos.DrawSphere(pos, m_radius);
    }

    private void SetGizmosColour(Color _colour, float _alpha = 0.5f)
    {
        _colour.a = _alpha;
        Gizmos.color = _colour;
    }
#endif
}
