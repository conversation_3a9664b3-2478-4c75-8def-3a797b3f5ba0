using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class MAFactionInfo
{
    public const string IconPath = "_art/Sprites/";
    private static List<MAFactionInfo> s_factionInfos = new List<MAFactionInfo>();
    public static List<MAFactionInfo> GetList => s_factionInfos;
    public string DebugDisplayName => m_name;
    public enum FactionType
    {
        None = -1,
        <PERSON>,
        Lords,
        Commoners,
        Mystic,
        Last,
    };

    public string id;
    public bool m_debugChanged;
    public string m_name;
    public int m_index;
    public string m_factionName;
    public string m_iconName;
    public int m_factionColour = 300;//magenta as error colour
    public int m_spriteAtlas;
    public string m_title;
    public string m_description;
    
    public Sprite m_iconSprite;

    public Color m_color;
    
    public static bool PostImport(MAFactionInfo _what)
    {
        if(_what.m_iconName.IsNullOrWhiteSpace() == false)
            _what.m_iconSprite = Resources.Load<Sprite>($"{IconPath}{_what.m_iconName}");
        if (GlobalData.Me)
        {
            Color factionColour = Color.HSVToRGB(
                _what.m_factionColour / 360f, 
                GlobalData.Me.m_orderCardFactionColourSaturation,
                GlobalData.Me.m_orderCardFactionColourValue);
            _what.m_color = factionColour;
    
        }
        return true;
    }
    
    public static List<MAFactionInfo> LoadInfo()  // Must be loaded after blocks
    {
        s_factionInfos = NGKnack.ImportKnackInto<MAFactionInfo>(PostImport);

        if(s_factionInfos.Count > 0)
        {
            foreach(var factionInfo in s_factionInfos)
            {
                s_factionInfoLookupByFactionName.Add(factionInfo.m_factionName.Trim().ToLower(), factionInfo);
                s_factionInfoLookup.Add(factionInfo.m_name.Trim().ToLower(), factionInfo);
            }
        }
        return s_factionInfos;
    }

    private static Dictionary<string, MAFactionInfo> s_factionInfoLookupByFactionName = new();
    private static Dictionary<string, MAFactionInfo> s_factionInfoLookup = new();
    public static string GetFactionSprite(FactionType _type)
    {
        return $"<sprite={(int) (_type + 1)}>";
    }

    
    public static MAFactionInfo GetInfo(string _faction)
    {
        if(s_factionInfoLookup.TryGetValue(_faction.Trim().ToLower(), out var info))
            return info;
        return null;
    }
    
    public static MAFactionInfo GetInfoByFactionName(string _factionName)
    {
        if(s_factionInfoLookupByFactionName.TryGetValue(_factionName.Trim().ToLower(), out var info))
            return info;
        return null;
    }
    public static MAFactionInfo GetInfoByName(string _name)
    {
        foreach(var i in s_factionInfos)
        {
            if(i.m_name == _name)
                return i;
        }
        return null;
    }
}
