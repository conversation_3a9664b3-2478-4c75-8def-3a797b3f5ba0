using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAResearchSceneManager : SubSceneManager<MAResearchSceneManager>
{
    public class MAResearchElement
    {
        public MAResearchElement(MAResearchInfo _element)
        {
            m_element = _element;
        }

        public MAResearchInfo m_element;

    } 
    override public string SceneName => "MAResearch";
    public Transform[] m_factionHolders = new Transform[(int) MAFactionInfo.FactionType.Last];
    public Sprite[] m_defaultFactionSprite = new Sprite[(int) MAFactionInfo.FactionType.Last];
    public Vector3 m_elementDistacePerLevel= new Vector3(0f,8f, 0f);
    public Vector3 m_elementDistace= new Vector3(8f,0f, 0f);
    public Camera m_camera;
    public float m_mouseScrollScale = 1f;
    private List<MAResearchElementDisplay> m_elementDisplays = new List<MAResearchElementDisplay>();
    private Dictionary<MAFactionInfo.FactionType, List<MAResearchElement>> m_elements = new Dictionary<MAFactionInfo.FactionType, List<MAResearchElement>>();

    private Dictionary<MAFactionInfo.FactionType, Dictionary<int, List<MAResearchElement>>> m_elementLines = new Dictionary<MAFactionInfo.FactionType, Dictionary<int, List<MAResearchElement>>>();
    private static DebugConsole.Command s_startProductTest = new("maresearch", _s =>
    {
        Create();
    });

    override public void Activate()
    {
        base.Activate();
        CreateResearchElements();
    }

    void CreateResearchElements()
    {
        return;
       /* DestroyElements();
        foreach (var faction in MAResearchInfo.m_elementLines)
        {
            foreach (var elementLine in faction.Value)
            {
                var holder = new GameObject($"Line {elementLine.Key}");
                holder.transform.SetParent(m_factionHolders[(int) faction.Key]);
                holder.transform.localPosition = elementLine.Key * m_elementDistacePerLevel;
                var elementList = elementLine.Value;
                var lineDimentions = (float)elementList.Count * m_elementDistace;
                var linePosition = (float) elementLine.Key * m_elementDistacePerLevel;
                for(int xi = 0; xi < elementList.Count; xi++)
                {
                    var element = elementList[xi];
                    var e = MAResearchElementDisplay.Create(element, holder.transform);
                    if (e)
                    {
                        e.transform.localPosition = m_elementDistace * (float)xi;
                        m_elementDisplays.Add(e);
                    }
                }
            }
        }

        var theBestScore = 0f;
        MAResearchElementDisplay bestElement = null;
        foreach (var e in m_elementDisplays)
        {
            if (e.IsAffordable() && e.m_element.m_info.m_bestScore > theBestScore)
            {
                theBestScore = e.m_element.m_info.m_bestScore;
                bestElement = e;
            }
            e.DrawConnections(m_elementDisplays);
        }

        if (bestElement)
        {
            bestElement.ToggleGoodChoice(true);
        }
        DebugElementDistance = m_elementDistace;
        DebugElementDistancePerLevel = m_elementDistacePerLevel;
*/
    }

    void DestroyElements()
    {
        for (var index = m_elementDisplays.Count-1; index >=0 ; index--)
        {
  //          m_elementDisplays[index].DestroyMe();
        }

        foreach (var fh in m_factionHolders)
        {
            fh.DestroyChildren();
        }
        m_elementDisplays.Clear();
    }

    private Vector3 DebugElementDistance;
    private Vector3 DebugElementDistancePerLevel;
    void Update()
    {
        if(m_elementDistace != DebugElementDistance || m_elementDistacePerLevel != DebugElementDistancePerLevel)
            CreateResearchElements();
        var pos = m_camera.transform.position;
        pos.y += Input.mouseScrollDelta.y * m_mouseScrollScale;
        m_camera.transform.position = pos;
    }



    public void ClickedClose()
    {
        Deactivate();
   //     DestroyMe();
    }
    override public bool ActivateComplete()
    {
        base.ActivateComplete();
        return true;
    }

    override public void DeactivateFinished()
    {
        base.DeactivateFinished();   
        DestroyMe();                                                                                                                                                                                                                                                                       
    }
    public static MAResearchSceneManager Create()
    {
        var go = Instantiate(NGManager.Me.m_researchSceneManagerPrefab.gameObject, NGManager.Me.m_reseaarchSceneManagerHolder);
        var rsm = go.GetComponent<MAResearchSceneManager>();
        rsm.Activate();
        return rsm;
    }
}
