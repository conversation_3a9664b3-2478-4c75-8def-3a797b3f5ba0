using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;

[CustomEditor(typeof(MADebugBuildingCreator))]
public class MADebugBuildingCreatorInspector : MonoEditorDebug.MonoBehaviourEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        var myScript = (MADebugBuildingCreator) target;
        if (GUILayout.Button($"Generate From Blocks"))
        {
            myScript.CreateFromBlocks();
        }
    }
}
#endif

public class MADebugBuildingCreator : MonoBehaviour
{
    [System.Serializable]
    public class Creator
    {
        public string m_block;
        public Vector3 m_pos;
    }

    public List<Creator> m_creators = new();

    public void Awake()
    {
  //      StartCoroutine(WaitForActivate());
    }
    
    IEnumerator WaitForActivate()
    {
        yield return new WaitUntil(() => MADebugShowAllBlocks.m_isLoaded || (GameManager.Me && GameManager.Me.LoadComplete));
        //Create();
    }

    public void Create()
    {
        var building = GetComponent<MABuilding>();
        if (building && building.m_blockHolder)
        {
            var name = building.Name;
            building.m_blockHolder.DestroyChildren();
            var createCount = m_creators.Count;
            foreach (var c in m_creators)
            {
                Block.Load(c.m_block, (_g) =>
                {
                    
                    var block = _g.GetComponent<Block>();
                    _g.transform.SetParent(building.m_blockHolder);
                    _g.transform.localPosition = c.m_pos;
                    block?.AddComponents();
                    
                    /*var rp = _g.GetComponentsInChildren<NGReactionPoint>();
                    if(rp != null)
                        foreach (var r in rp)
                            r.enabled = false;*/
                    if (--createCount == 0)
                        building.Activate();
                });
                
            }
        }
    }
    public void CreateFromBlocks()
    {
        var block = GetComponentsInChildren<Block>();
        foreach (var b in block)
        {
            var create = new Creator();
            
            create.m_block = b.name.Replace("(Clone)","");
            create.m_pos = b.transform.localPosition;
            m_creators.Add(create);
        }
    }
    
}
