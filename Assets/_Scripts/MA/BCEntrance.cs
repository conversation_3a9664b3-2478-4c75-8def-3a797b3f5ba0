using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCEntrance : BCBase
{
    [Save] public Vector3 m_doorPos;//TS - needs review - keeping this here duplicated the door Pos position vs the one in BuildingNav and this one can end up out-of-date
    [Save] public Vector3 m_innerPos;

    //private List<Collider> m_debugCollidersIgnored = new List<Collider>();
    
    override public (string id, Func<BCUIPanel> create) GetUIPanelInfo() => (BCGeneralInfoPanel.PanelID, () => new BCGeneralInfoPanel());
    
    override public void UpdateIsValid()
    {
        base.UpdateIsValid();
        
        if(Building != null)
        {
            var nav = Building.Nav; 
            if(nav != null && nav.HasDoor == false)
            {
                m_isValid = false;
            }
        }
    }
    
    protected override void Start()
    {
        m_doorPos = transform.position;
        //TODO: TS - currently the inteact door isdirectly on teh door and does not indicate a spot just outside a entrance. reverted to use doorPosOuter.
        //what should we use? doorPOsOuter, Interacts/Door?
        //var doorTransform = transform.Find("Interacts/Door");
        // if (doorTransform)
        // {
        //     m_doorPos = doorTransform.position;
        // }
        if (m_building != null)
        {
            m_innerPos = m_building.DoorPosInner; ////transform.position;
            //m_innerPos.y = m_doorPos.y;
            m_doorPos = m_building.DoorPosOuter;
        }
        
        base.Start();
    }

    public void DisableMovingObjectCollisions(NGMovingObject _mover)
    {
        NavAgent nav = _mover.m_nav;
        if(nav == null) return;
        Collider moverCollider = nav.Collider;
        foreach(var cmp in m_building.m_components)
        {
            var block = cmp.Block;
            if(block == null) continue;
            foreach(var doorCollider in block.m_doorColliders)
            {
                if(doorCollider != null && doorCollider.enabled && doorCollider.isTrigger == false)
                { 
                    //Debug.Log($"{GetType().Name} - DisableMovingObjectCollisions - IgnoreCollision {moverCollider.name} vs {doorCollider.name}");
                    Physics.IgnoreCollision(moverCollider, doorCollider, true);
                    //if(m_debugCollidersIgnored.Contains(moverCollider) == false) m_debugCollidersIgnored.Add(moverCollider);
                }
            }
        }
    }

    private void OnCollisionEnter(Collision collision)
    {
        HandleCollision(collision);
    }
    
    private void OnCollisionStay(Collision collision)
    {
        HandleCollision(collision);
    }

    private void HandleCollision(Collision collision)
    {
        GameObject _gameObject = collision.gameObject;
        var contactCollider = collision.GetContact(0).thisCollider;
        if (HandleCharacterCollision(_gameObject, contactCollider) == false)
        {
            var mainObject = collision.gameObject.GetComponentInParent<MainGameObjectReference>();
            if (mainObject != null)
            {
                HandleCharacterCollision(mainObject.MainGameObject, contactCollider);
            }
        }
    }

    private bool HandleCharacterCollision(GameObject _gameObject, Collider _myCollider)
    {
        var mover = _gameObject?.GetComponent<MACharacterBase>();
        if (mover != null)
        {
            if((mover.m_state == NGMovingObject.STATE.MA_MOVE_TO_INSIDE_BUILDING && 
               mover.m_destinationMABuilding == m_building) || 
               mover.m_state == NGMovingObject.STATE.MA_MOVE_TO_OUTSIDE_BUILDING)
            {//mover.m_stoppedForBuildingPlacement
                Debug.LogError($"{GetType().Name} - Warning! OnCollision - {mover.name} - {mover.m_state} - {(mover.m_destinationMABuilding == null ? "null" : mover.m_destinationMABuilding.name)} - Character Blocked from Entering Entrance block. Allowing in. Building Block collider: {(_myCollider != null ? _myCollider.name : "null")}");
                DisableMovingObjectCollisions(mover);//MIGHT HAPPEN IF YOU LOAD AND A WORKER IS ABOUT TO GET INTO A BUILDING< I ALSO PICKED UP A ENTRANCE BLOCK (TAVERN_ AND WANTED TO MOVE IT TO THE MILL
            }

            return true;
        }

        return false;
    }
    
    public void ResetMovingObjectCollisions(NGMovingObject _mover)
    {
        NavAgent nav = _mover.m_nav;
        if(nav == null) return;
        Collider moverCollider = nav.Collider;
        foreach(var cmp in m_building.m_components)
        {
            var block = cmp.Block;
            foreach(var doorCollider in block.m_doorColliders)
            {
                if(doorCollider != null && doorCollider.enabled && doorCollider.isTrigger == false)
                { 
                    //Debug.Log($"{GetType().Name} - ResetMovingObjectCollisions - IgnoreCollision {moverCollider.name} vs {doorCollider.name}");
                    Physics.IgnoreCollision(moverCollider, doorCollider, false);
                    //m_debugCollidersIgnored.Remove(moverCollider);
                }
            }
        }
    }

    public Transform GetDoor()
    {
        return transform.Find("Interacts/Door");
    }

    override public Vector3 GetDoorPos()
    {
        return m_building.DoorPosOuter; //TS - What out m_doorpos doesn't hold any value. BuildingNav already stores its own value.
    }
}
