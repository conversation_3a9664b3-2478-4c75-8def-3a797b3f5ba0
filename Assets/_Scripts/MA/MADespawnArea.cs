using System;
using System.Collections.Generic;
using UnityEngine;

public class MADespawnArea : MonoBehaviour
{
    [SerializeField]
    private bool m_freezePausedCharacters = false;
    
    private SphereCollider m_deSpawnArea = null;
    private MACharacterBase m_characterInArea = null;
    private HashSet<MACharacterBase> m_pausedCharacters = new();

    private void Awake()
    {
            m_deSpawnArea = gameObject.GetComponent<SphereCollider>();
    }
    
    protected virtual void OnTriggerEnter(Collider other)
    {
        var (newCharacter, newCharacterIsNull) = GetCharacterFromCollider(other);

        if(newCharacterIsNull ||
           (newCharacter.CharacterUpdateState.State != CharacterStates.GoingHome &&
           newCharacter.CharacterUpdateState.State is not CharacterStates.Despawn or CharacterStates.Dying or CharacterStates.Dead) || 
           (newCharacter.m_nav.OriginalTargetPosition - transform.position).xzMagnitude() > NavAgent.c_distanceToFinalPositionThreshold)
            return;
        
        if(m_characterInArea == null)
        {
            m_characterInArea = newCharacter;
            newCharacter.m_nav.PopPause("MADespawnArea");
            m_pausedCharacters.Remove(m_characterInArea);
        }
        else
        {
            newCharacter.m_nav.PushPause("MADespawnArea", false, m_freezePausedCharacters);
            if (m_freezePausedCharacters)
            {
                newCharacter.m_nav.ZeroVelocity();
            }
            m_pausedCharacters.Add(newCharacter);
        }
    }
    
    private static Dictionary<int, (MACharacterBase, bool)> s_colliderToCharacterCache = new();
    public static (MACharacterBase, bool) GetCharacterFromCollider(Collider collider)
    {
        int id = collider.GetInstanceID();
        if (s_colliderToCharacterCache.TryGetValue(id, out var details) == false)
        {
            details.Item1 = collider.gameObject.GetComponentInParent<MACharacterBase>();
            details.Item2 = details.Item1 == null;
            s_colliderToCharacterCache[id] = details;
        }
        return details;
    }
    protected virtual void OnTriggerStay(Collider other)
    {       
        var (newCharacter, newCharacterIsNull) = GetCharacterFromCollider(other);

        if(newCharacterIsNull ||
           (newCharacter.CharacterUpdateState.State != CharacterStates.GoingHome &&
            newCharacter.CharacterUpdateState.State is not CharacterStates.Despawn or CharacterStates.Dying or CharacterStates.Dead) || 
           (newCharacter.m_nav.OriginalTargetPosition - transform.position).xzMagnitude() > NavAgent.c_distanceToFinalPositionThreshold)
            return;
        
        if(m_characterInArea == null)
        {
            m_characterInArea = newCharacter;
            m_characterInArea.m_nav.PopPause("MADespawnArea");
            m_pausedCharacters.Remove(m_characterInArea);
        }
        else if(m_characterInArea != newCharacter)
        {
            newCharacter.m_nav.PushPause("MADespawnArea", false, m_freezePausedCharacters);
            m_pausedCharacters.Add(newCharacter);
        }
        else
        {
            m_characterInArea.m_nav.PopPause("MADespawnArea");
            m_pausedCharacters.Remove(m_characterInArea);
        }
    }
    
    protected virtual void OnTriggerExit(Collider other)
    {
        var (newCharacter, newCharacterIsNull) = GetCharacterFromCollider(other);

        if(newCharacterIsNull)
            return;

        if(m_characterInArea == newCharacter)
        {
            m_pausedCharacters.Remove(m_characterInArea);
            m_characterInArea.m_nav.PopPause("MADespawnArea");
            m_characterInArea = null;
        }
        else
        {
            newCharacter.m_nav.PopPause("MADespawnArea");
            m_pausedCharacters.Remove(newCharacter);
        }
    }
}
