using System;
using System.Collections.Generic;
using UnityEngine;
[System.Serializable] 
public class BCCharacterStatsInfo
{
    public const string PrefabPath = "_Prefabs/NCStats/"; 
    public static List<BCCharacterStatsInfo> s_characterStatsInfo = new List<BCCharacterStatsInfo>();
    public static List<BCCharacterStatsInfo> GetList=>s_characterStatsInfo;
    public string DebugDisplayName => m_name;

    public string id;
    public bool m_debugChanged;
    public string m_name;
    public string m_characterPrefabName;
    public float m_health;
    public float m_strength;
    public float m_attackPower;
    public float m_defense;
    public float m_walkSpeed;
    public float m_runSpeed;
    public float m_fleeSpeed;
    public float m_jumpHeight;
    public static bool PostImport(BCCharacterStatsInfo _what)
    {
        var name = $"{PrefabPath}{_what.m_name}";
        var characterPrefab = Resources.Load<NCCharacterStats>(name) ;
        if (characterPrefab == null)
        {
            Debug.LogError($"No character prefab found for {_what.m_name}");
            return false;
        }
        var characterStats = characterPrefab;
        //var characterStats = characterPrefab.GetComponent<NCCharacterStats>();
        if (characterStats == null)
        {
            Debug.LogError($"No character stats found for {_what.m_name}");
            return false;
        }
        characterStats.m_characterPrefabName = _what.m_characterPrefabName;
        characterStats.Physical.health = _what.m_health;
        characterStats.Physical.strength = _what.m_strength;
        characterStats.Physical.attackPower = _what.m_attackPower;
        characterStats.Physical.defense = _what.m_defense;
        characterStats.Physical.walkSpeed = _what.m_walkSpeed;
        characterStats.Physical.runSpeed = _what.m_runSpeed;
        characterStats.Physical.fleeSpeed = _what.m_fleeSpeed;
        characterStats.Physical.jumpHeight = _what.m_jumpHeight;
        
        return true;
    }
    public static List<BCCharacterStatsInfo> LoadInfo()  // Must be loaded after blocks
    {
        s_characterStatsInfo = NGKnack.ImportKnackInto<BCCharacterStatsInfo>(PostImport);
        return s_characterStatsInfo;
    }

    public static BCCharacterStatsInfo GetInfo(string _name) => s_characterStatsInfo.Find(o => o.m_name.Equals(_name, StringComparison.OrdinalIgnoreCase));
}
