using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Serialization;

public class NCCharacterBase : MonoBehaviour
{
    public enum CharacterType
    {
        <PERSON>,
        <PERSON>,
        <PERSON>wolf,
        Troll,
        QuestGiver,
        Tourist
    }
    public CharacterType characterType;
    [HideInInspector] public NavAgent navAgent;
    [HideInInspector] public Animator animator;
    [ReadOnlyInspector] public GameObject m_characterPrefab;

    public NCCharacterStats stats;
    public NCCharacterStats.PhysicalStats Physical;
    
    public List<NCCharacterComponentBase> components = new List<NCCharacterComponentBase>();  
    public List<NCCharacterComponentBase> activeComponents = new List<NCCharacterComponentBase>();
    
    [HideInInspector] public Transform visuals;

    virtual public float[] NavigationCosts => GlobalData.s_pedestrianCosts;

    virtual protected void Start()
    {
        visuals = transform.Find("Visuals");
        if(visuals == null)
        {
            Debug.LogError("No visuals found for " + gameObject.name);
        }
        if(stats == null)
        {
            Debug.LogError("No stats found for " + gameObject.name);
        }
        navAgent = GetComponent<NavAgent>();
        if(navAgent == null)
        {
            Debug.LogError("No NavAgent found for " + gameObject.name);
        }
        var characterPrefab = Resources.Load<GameObject>(NCCharacterStats.CharacterPrefabPath + stats.m_characterPrefabName);
        if(characterPrefab == null)
        {
            Debug.LogError("No character prefab found for " + gameObject.name);
        }
        m_characterPrefab = Instantiate(characterPrefab, visuals);
        animator = m_characterPrefab.GetComponentInChildren<Animator>();
        if(animator == null)
        {
            Debug.LogError("No animator found for " + gameObject.name);
        }
        StartCoroutine(AfterLoad());
       
    }
    private IEnumerator AfterLoad()
    {
        yield return new WaitUntil(() => ((GameManager.Me && GameManager.Me.LoadComplete)));
        var allComponents = GetComponentsInChildren<NCCharacterComponentBase>();
        components = allComponents.ToList();
        Physical = new NCCharacterStats.PhysicalStats(stats.Physical);
    }
    virtual protected void Update()
    {
        activeComponents.Clear();
        foreach(var c in components)
        {
            c.UpdateMe();
            if(c.State == NCCharacterComponentBase.ComponentState.Processing)
            {
                activeComponents.Add(c);
            }
        }
    }
    public void AddNCComponent(NCCharacterComponentBase component)
    {
        if(components.Contains(component) == false)
            components.Add(component);
    }
    public NCCharacterComponentBase GetNCComponent<T>() where T : NCCharacterComponentBase
    {
        return components.FirstOrDefault(c => c is T) as T;
    }

    public void TransitionToState(NCCharacterState newState)
    {
        // ... (state transition logic)
    }

    public virtual void Attack()
    {
        
    }
    public virtual bool GotoTarget(MABuilding target, float _speed = 0f)
    {
        if (target == null) return false;
        if (navAgent.PathPending == false)
        {
            var pos = target.DoorPosOuter;
            if ((transform.position - pos).xzSqrMagnitude() < 0.1f * 0.1f)
                return true;

            gameObject.SetActive(true);
            navAgent.Speed = (_speed == 0f) ? Physical.walkSpeed : _speed;
            navAgent.Unpause();
            navAgent.SetNavCosts(NavigationCosts);
            navAgent.SetTarget(pos, false, 0f, null);
            return true;
        }

        return false;
    }
    virtual public bool HasAgentArrived()
    {
        return navAgent.TargetReached;
    }
}
