using UnityEngine;

public class NCWorkingCharacterComponent : NCCharacterComponentBase
{
    public MABuilding WorkBuilding;
    override public void SetTarget(object target)
    {
        var building = target as MABuilding;
        if(building == null)
        {
            Debug.LogError("Invalid target for NCWorkingCharacterComponent: " + target);
            return;
        }
        WorkBuilding = building;
    }
}
