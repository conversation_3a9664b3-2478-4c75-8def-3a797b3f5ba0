using UnityEngine;

public class NCGoHomeToRestComponent : NCCharacterComponentBase
{
    private MABuilding HomeBuilding=>(CharacterBase.GetNCComponent<NCLookForHomeComponent>() as NCLookForHomeComponent)?.HomeBuilding;
    override public void UpdateActive()
    {
        if (State ==ComponentState.Processing) return;
        if (CharacterBase == null) return;
        base.UpdateActive();
        if(CharacterBase.Physical.health < .1)
        {
            var home = HomeBuilding;
            if(home != null)
            {
                if(CharacterBase.GotoTarget(home))
                {
                    State = ComponentState.Processing;
                    return;
                }
            }
        }
    }

    override public void UpdateProcessing()
    {
        if (CharacterBase.HasAgentArrived())
        {
            State = ComponentState.Waiting;
        }
    }

    override public void UpdateWaiting()
    {
        CharacterBase.Physical.health=Mathf.Clamp(CharacterBase.Physical.health+Time.deltaTime*.01f,0,CharacterBase.stats.Physical.health);
        if(CharacterBase.Physical.health >= CharacterBase.stats.Physical.health)
        {
            State = ComponentState.Active;
        }
    }


}
