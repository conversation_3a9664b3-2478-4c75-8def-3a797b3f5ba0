using UnityEngine;
[System.Serializable]
public class NCCharacterComponentBase : MonoBehaviour
{
    public enum ComponentState
    {
        Active,
        Processing,
        Waiting,
        Inactive
    }
    public NCCharacterBase CharacterBase;
    public bool Active = false;
    public ComponentState State = ComponentState.Inactive;

    virtual protected void Awake()
    {
        CharacterBase = GetComponentInParent<NCCharacterBase>();
        if(CharacterBase == null)
        {
            Debug.LogError("NCCharacterComponentBase: No NCCharacterBase found in parent");
        }
        else
        {
            CharacterBase.AddNCComponent(this);            
        }
        State = ComponentState.Active;
    }

    virtual public bool UpdateMe()
    {
        switch (State)
        {
            case ComponentState.Active:
                UpdateActive();
                break;
            case ComponentState.Processing:
                UpdateProcessing();
                break;
            case ComponentState.Waiting:
                UpdateWaiting();
                break;
            case ComponentState.Inactive:
                UpdateInactive();
                break;
        }
        return false;
    }
    virtual public void UpdateActive() { }
    virtual public void UpdateProcessing() { }
    virtual public void UpdateWaiting() { }
    virtual public void UpdateInactive() { }
    virtual public bool AcquireTarget() => false;
    virtual public void MoveToTarget() { }
    virtual public void AttackTarget() { }
    virtual public void Die() { }
    virtual public void Work() { }
    virtual public void Rest() { }
    virtual public void SetTarget(object target) { }
    virtual public void Flee() { }

}
