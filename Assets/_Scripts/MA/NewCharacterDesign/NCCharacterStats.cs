using UnityEngine;
[System.Serializable]
[CreateAssetMenu(fileName = "NCCharacterStats", menuName = "Scriptable Objects/NCCharacterStats")]
public class NCCharacterStats : NCStatsBase
{
    public const string CharacterPrefabPath = "_Prefabs/Characters/";

    public string m_characterPrefabName;
    [System.Serializable] public class PhysicalStats
    {
        public PhysicalStats(PhysicalStats _from)
        {
            health = _from.health;
            strength = _from.strength;
            attackPower = _from.attackPower;
            defense = _from.defense;
            walkSpeed = _from.walkSpeed;
            runSpeed = _from.runSpeed;
            fleeSpeed = _from.fleeSpeed;
            jumpHeight = _from.jumpHeight;
        }
        [Range(0, 1)] public float health;
        [Range(0, 1)] public float strength;
        [Range(0, 1)] public float attackPower;
        [Range(0, 1)] public float defense;
        [Range(0, 8)] public float walkSpeed;
        [Range(0, 16)] public float runSpeed;
        [Range(0, 20)] public float fleeSpeed;
        [Range(0, 1)] public float jumpHeight;
    }
    public PhysicalStats Physical;
}
 