using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MARewardEnvelope : MAGUIBase
{
    public TMP_Text m_envelopeRewardCountText;
    public float m_showEnvelopeReminderTime = 10f;
    private float m_showEnvelopeReminderTimer;
    private MAGameFlowDialog m_fromDialog;
    private Animator m_animator;
    private void Start()
    {
        m_animator = GetComponent<Animator>();
        m_animator?.SetTrigger("TriggerEnvelope");
        m_showEnvelopeReminderTimer = m_showEnvelopeReminderTime + Time.time;
    }
    void Update()
    {
        if (Time.time > m_showEnvelopeReminderTimer)
        {
            m_showEnvelopeReminderTimer = m_showEnvelopeReminderTime + Time.time;
            if(GameManager.Me.IsOKToPlayUISound())
                AudioClipManager.Me.PlaySoundOld("PlaySound_HUDEnvelopeJingle", GameManager.Me.transform);
        }        
    }
    public void Activate(MAGameFlowDialog _fromDialog, int _count)
    {
        m_fromDialog = _fromDialog;
        m_envelopeRewardCountText.text = $"{_count}";
    }
    public void ClickedEnelope()
    {
        m_fromDialog.ClickedEnvelope();
        AudioClipManager.Me.PlaySound("PlaySound_ScrollActivate", GameManager.Me.transform);
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        Destroy(gameObject);
    }
    public static MARewardEnvelope Create(MAGameFlowDialog _fromDialog, int _count)
    {
        var prefab = Resources.Load<MARewardEnvelope>("_Prefabs/Dialogs/MARewardEnvelope");
        var instance = Instantiate(prefab, NGManager.Me.m_topMiddleHolder);
        instance.Activate(_fromDialog, _count);
        return instance;
    }
}
