using MACharacterStates;
using UnityEngine;

namespace BehaviourDesignComponents
{
    public class CharacterStats : ScriptableObject
    {
        [Range(0,1)]public float health;
        [Range(0,1)]public float strength;
        [Range(0,1)]public float attackPower;
        [Range(0,1)]public float defense;
        [Range(0,1)]public float walkSpeed;
        [Range(0,1)]public float runSpeed;
        [Range(0,1)]public float fleeSpeed;
        [Range(0,1)]public float jumpHeight;
        
    }
    public abstract class CharacterState
    {
        protected CharacterBase m_characterBase;

        public CharacterState() { }

        public CharacterState(CharacterBase _characterBase)
        {
            this.m_characterBase = _characterBase;
        }

        public virtual void Enter() { }
        public virtual void Update() { }
        public virtual void Exit() { }
    }
    public class CharacterBase : MonoBehaviour
    {
        public enum CharacterType
        {
            <PERSON>,
            Zombie,
            Werewolf,
            Troll,
            QuestGiver,
            Tourist
        }

        public CharacterType characterType;
        public CharacterState currentState;
        public NavAgent navMeshAgent;
        public Animator animator;

        public CharacterStats stats;
        // ... other core properties and methods (health, speed, etc.)

        void Update()
        {
            if (currentState != null)
            {
                currentState.Update();
            }
        }

        public void TransitionToState(CharacterState newState)
        {
            // ... (state transition logic)
        }

        public virtual void Attack()
        {
            
        }

        // ... other shared methods (MoveTowards, PlayAnimation, etc.)
        public class CombatCharacter : MonoBehaviour
        {
            public CharacterBase Target { get; set; }

            // Methods for acquiring targets, attacking, etc.
            public void AcquireTarget()
            {
                // ... logic to find a suitable target
            }

            public virtual void Attack()
            {
                // ... generic attack logic (can be overridden in specific character scripts)
            }
        }
        public class WorkingCharacter : MonoBehaviour
        {
            public MABuilding WorkBuilding { get; set; }

            // Methods for acquiring targets, attacking, etc.
            public void AcquireTarget()
            {
                // ... logic to find a suitable target
            }

            public virtual void Working()
            {
            }
        }
        public class Worker : CharacterBase
        {
            // ... Worker-specific properties and states

            // No need for a 'target' reference here
        }
        public class Zombie : CharacterBase
        {
            private CombatCharacter combatComponent;

            void Start()
            {
                combatComponent = GetComponent<CombatCharacter>();
            }

            // ... Zombie-specific states

            public override void Attack()
            {
                if (combatComponent.Target != null)
                {
                    // Perform zombie attack on combatComponent.Target
                }
            }
        }
    }    
}
