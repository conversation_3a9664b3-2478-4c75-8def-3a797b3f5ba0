using System;
using UnityEngine;
using UnityEngine.UI;

public class MAQuickTipRoadBuildButton : MAQuickTipButton
{
    public Button m_button;
    
    private void Start()
    {
        Activate(Name);
    }
    
    private void Update()
    {
        var showButton = MAUnlocks.Me.m_designWalls || MAUnlocks.Me.m_designRoads;
        if(m_button.interactable != showButton)
        {
            transform.localScale = showButton ? Vector3.one : Vector3.zero;
            m_button.interactable = showButton;
        }

        if (m_isHighlighting == false)
        {
            m_highLight.SetActive(RoadManager.Me.RoadBuildMode);
        }
    }
}
