using UnityEngine;

public class MABanditRanged : MACreatureBase
{
	//[Header("MABanditRanged")]
	override public string HumanoidType => "BanditRanged";
	public override EDeathType DeathCountType => EDeathType.Enemy;

	[SerializeField]
	override protected string m_defaultWeaponStrikeType => c_weaponType_Club;
	[SerializeField]
	override protected string m_defaultWeaponDesign => "1|Weapon_CrossBow_TEST@|0|";
	protected override bool GetsDisabledBasedOnProximity => true;

	// This is the projectile as held by the character, before being fired.
	[SerializeField]
	GameObject m_heldProjectilePrefab;
	[SerializeField]
	string m_projectileAttachPoint;

	GameObject m_heldProjectile;
	
	protected override void Awake()
	{
		base.Awake();
	}

	public override void Activate(MACreatureInfo _creatureInfo, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation, bool _addToCharacterLists = true)
	{
		base.Activate(_creatureInfo, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);

		if (m_heldProjectilePrefab != null)
		{
			m_heldProjectile = GameObject.Instantiate(m_heldProjectilePrefab);
			m_heldProjectile.SetActive(false);

			Transform t = m_heldProjectile.transform;
			Transform attachTrans = transform.FindChildRecursiveByName(m_projectileAttachPoint);
			t.SetParent(attachTrans, false);
		}

		m_onRangeAttackFired += OnProjectileFired;
		m_onShowProjectile += OnShowProjectile;
	}

	private void OnProjectileFired()
	{
		if (m_heldProjectile != null)
		{
			m_heldProjectile.SetActive(false);
		}
	}

	private void OnShowProjectile()
	{
		if (m_heldProjectile != null)
		{
			m_heldProjectile.SetActive(true);
		}
	}

	public override void DestroyedTarget()
	{
		base.DestroyedTarget();
	}

	public override void DoPossessedAction()
	{
		
	}
}
