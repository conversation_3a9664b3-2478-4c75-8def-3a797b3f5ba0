using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MAQuickTipGUI : MAGUIBase
{ 
    public TMP_Text m_displayText;
    public Transform m_GUIHolder;
    private int m_displayHash;
    private int m_buildingId;
    private MABuilding m_building;
    
    public void Update()
    {
        bool enable = m_building.IsDisabledByPlants == false;
        if (m_building && m_GUIHolder && m_GUIHolder.gameObject.activeSelf != enable)
        {
            m_GUIHolder.gameObject.SetActive(enable);
        }
        if(MAMessageManager.Me.IsPopupTimerFinished && (m_building == null))
        {
            DestroyMe();
        }
    }
    
    public void OnClick()
    {
        if(DesignTableManager.Me.IsInDesignGloballyActively) return;
        if(m_building != null)
            NGBuildingInfoGUI.Create(m_building);
    }
    
    public void DestroyMe()
    {
        if(m_building != null) m_building.m_tipGUI = null;
        s_instances.Remove(m_buildingId);
        Destroy(gameObject);
    }
    
    void Activate(MABuilding _building, string _text)
    {
        m_building = _building;
        transform.position += Vector3.up * 2f;
        SetText(_text);
    }
    
    private void SetText(string _text)
    {
        m_displayText.text = _text;
        m_displayHash = _text.GetHashCode();
    }
    
    private static string ConvertToDisplayText(List<string> _texts)
    {
        var display = "";
        foreach(var t in _texts)
        {
            if(t.IsNullOrWhiteSpace() == false)
                display += t + "\n";
        }
        return display.TrimEnd('\n');
    }
    
    private void UpdateText(string _text)
    {
        if(_text.GetHashCode() == m_displayHash) return;
        
        SetText(_text);
    }

    private static Dictionary<int, MAQuickTipGUI> s_instances = new Dictionary<int, MAQuickTipGUI>();
    
    public static void HideAll(bool _hide)
    {
        foreach (var kvp in s_instances)
            if (kvp.Value != null)
                kvp.Value.GetComponent<Canvas>().enabled = !_hide;
    }

    public static MAQuickTipGUI Create(MABuilding _building, Transform _holder, string _text)
    {
        s_instances.TryGetValue(_building.m_linkUID, out var instance);
        //var text = ConvertToDisplayText(_texts);
        
        if(instance != null)
        {
            instance.UpdateText(_text);
        }
        else
        {
            var prefab = Resources.Load<MAQuickTipGUI>("_Prefabs/Dialogs/MAQuickTipGUI");
            instance = Instantiate(prefab, _holder);
            instance.Activate(_building, _text);
            s_instances[_building.m_linkUID] = instance;
        }
        return instance;
    }
}
