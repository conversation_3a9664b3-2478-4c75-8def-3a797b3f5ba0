using System;
using System.Collections.Generic;
using UnityEngine;

public class BCQuestActionGatherer : BCActionGatherer
{
	public List<MAFlowCharacter> m_characters = new();
    public GameObject m_treesParent = null;
    public override bool ShowWarning => false;

    public List<TreeHolder> m_treeHolders = new();
    [SerializeField]
    private string m_resourceMiniTableSaw = "MA_Mini_TableSaw";
    
    private float m_timeToMakeOneTimber = 3f;
    private float m_sawActiveTime = -1f;

    public Animator m_saw = null;
    public string m_sawBooleanParam = "Cut";

    private int m_itemToAdd = 0;
    
    public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
	    base.Activate(_indexOfComponentType, _quantityInBuilding);

	    m_sawActiveTime = -1f;
	    m_saw = gameObject.transform.FindRecursiveByName<Animator>(m_resourceMiniTableSaw);
	    if (m_saw != null)
	    {
		    m_saw.SetBool(m_sawBooleanParam, m_sawActiveTime > 0f);
	    }
    }

    public override void UpdateInternal(BuildingComponentsState _state)
    {
	    base.UpdateInternal(_state);
	    if (m_sawActiveTime > 0)
	    {
		    m_sawActiveTime -= Time.deltaTime;

		    if (m_sawActiveTime <= 0f)
		    {
			    var item = NGCarriableResource.GetInfo(m_output);
			    AddStockToVisuals(Block.m_outputStockVisuals, item);
			    
			    var inputStock2 = GetInputStock();
			    if (inputStock2 != null)
			    {
				    var item2 = inputStock2.Find(item);
				    if(item2 == null || item2.Needed <= 0) return;
				    inputStock2.AddOrCreateStock(item, 1);
			    }
			    
			    m_itemToAdd--;
			    m_saw.SetBool(m_sawBooleanParam, m_itemToAdd > 0);
		    }
	    }
    }

    override public bool AddResource(NGCarriableResource _resource)
    {
	    if(_resource == null) return false;
	    if (_resource == m_inputResource)
	    {
		    m_saw.SetBool(m_sawBooleanParam, true);
		   // AddStockToVisuals(Block.m_outputStockVisuals, _pickup.Contents);
			if (m_sawActiveTime <= 0)
			{
				m_sawActiveTime = m_timeToMakeOneTimber;
			}

			m_itemToAdd++;
			var inputStock2 = GetInputStock();
			if (inputStock2 != null)
			{
				var item2 = inputStock2.Find(m_outputResource);
				if(item2 == null || item2.Needed <= 0) return false;
				
				//TS - we delay this until saw is finished.
				//inputStock2.AddOrCreateStock(NGCarriableResource.GetInfo(m_output), 1);
				return true;
			}
		}

	    return false;
    }

    // private bool AddPickupDelayed(ReactPickup _pickup)
    // {
	   //  if(_pickup == null) return false;
	   //  if (_pickup.m_contents == NGCarriableResource.GetInfo(m_input))
	   //  {
		  //   AddStockToVisuals(Block.m_outputStockVisuals, _pickup.Contents);
		  //   var inputStock2 = GetInputStock();
		  //   if (inputStock2 != null)
		  //   {
			 //    var item2 = inputStock2.Find(NGCarriableResource.GetInfo(m_output));
			 //    if(item2 == null || item2.Needed <= 0) return false;
			 //    inputStock2.AddOrCreateStock(NGCarriableResource.GetInfo(m_output), 1);
			 //    return true;
		  //   }
	   //  }
    //
	   //  return false;
    // }

    public override bool AskForInputStock(bool _gatherOnly)
	{
		var freeWorker = m_characters.Find(x => x.m_state == NGMovingObject.STATE.IDLE || x.m_state == NGMovingObject.STATE.MA_DECIDE_WHAT_TO_DO);
		if(freeWorker == null) return false;
	
		(GameObject go, PeepActions action) what = GetPickupObject(freeWorker);
		if(what.go)
		{
			freeWorker.SetMoveToObject(what.go, what.action);
			//m_building.Leave(freeWorker);
			return true;
		}
		return false;
	}
	
    override public NGStock GetInputStock() => m_stock;
    
    private bool IsGoodTree(TreeHolder _tree)
    {
	    if (_tree == null) return false;
	    if (!_tree.gameObject.activeSelf)
			return false;
	    if (_tree.m_treeType != m_treeHolderType)
		    return false;
	    if (_tree.IsLocked)
		    return false;
	    //if (_tree.GetPrefab() == null)
	    //     return false;
	       
        var chopObject = _tree.ChopObject;
        if (chopObject != null)
        {
            if (!chopObject.IsChopObjectValid)
                return  false;
            if (chopObject.IsChoppedDown)
                return false;
            if (chopObject.m_worker != null)
                return false;
        }
        return true;
    }

    public override MACharacterBase GetAvailableWorker()
    {
        return m_characters.Find(x => x.m_state == NGMovingObject.STATE.IDLE);
    }
    
    public override TreeHolder GetNextResource()
    {
	    //var go = base.GetNextResource();
	    
	    m_hasResourcesInRange = true;
	    
	    if(m_treesParent == null)
	        return null;
	    GameObject treeObj = null;
	    foreach (var tree in m_treeHolders)
	    { 
	        if (IsGoodTree(tree))
	        {
	            return tree;
	        }
	    }
	    return null;
    }
}
