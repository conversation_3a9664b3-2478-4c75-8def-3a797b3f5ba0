using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;

[CustomEditor(typeof(MADebugShowAllBlocks))]
public class MADebugShowAllBlocksInspector : MonoEditorDebug.MonoBehaviourEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        var myScript = (MADebugShowAllBlocks) target;
        if (GUILayout.Button($"Regenerate"))
        {
            myScript.Activate();
        }
    }
}
#endif

public class MADebugShowAllBlocks : MonoBehaviour
{
    class ABuildingBlock
    {
        public ABuildingBlock(string _prefabName, SnapHinge.EType _attach)
        {
            m_name = _prefabName;
            m_attach = _attach;
        }
        public string m_name;
        public SnapHinge.EType m_attach;
    }

    public static bool m_isLoaded = false;

    private List<ABuildingBlock> m_houseBlocks = new List<ABuildingBlock>()
    {
        new ABuildingBlock("House_Tudor_Door_B", SnapHinge.EType.Right),
        new ABuildingBlock("House_Tudor_Window_A", SnapHinge.EType.Top),
        new ABuildingBlock("House_Tudor_Window_B", SnapHinge.EType.Left),
        new ABuildingBlock("House_Tudor_Window_A", SnapHinge.EType.Top),
        new ABuildingBlock("House_Tudor_Roof_A", SnapHinge.EType.Right),
        new ABuildingBlock("House_Tudor_Roof_B", SnapHinge.EType.None),

    };

    public MABuilding m_houseBuilding;
    public List<NGBlockInfo> m_allInfoBlocks;
    public List<Block> m_allBlocks;


    public Transform m_holder;
    public GameObject m_debugTextPrefab;
    public bool m_showDebug;
    public string m_justShow;
    public string m_justShowBuildingInfo;

    public int m_maxColumns = 10;
    public Vector3 m_blockPad = new Vector3(2, 0, 2);

    private Vector3 IPos = Vector3.zero;
    private float blockWidth = 5.1f;
    private float blockHeight = 5.1f;
    // Start is called before the first frame update
    void Start()
    {
        NGCarriableResource.LoadInfo();
        MAComponentInfo.LoadInfo();
        m_allInfoBlocks=NGBlockInfo.LoadInfo();
        Activate();
    }

    private int m_loadCount = 0;

    public void Activate()
    {
        m_holder.DestroyChildren();
        m_loadCount = 0;
        var loadBlocks = new List<NGBlockInfo>();
        foreach (var b in m_allInfoBlocks)
        {
            if (m_justShow.IsNullOrWhiteSpace() == false)
            {
                if (m_justShow.StartsWith("NG"))
                {
                    //if (b.m_nGBuildingInfo.Contains(m_justShow) == false)
                    //    continue;
                }
                else if (b.m_prefabName.StartsWith(m_justShow, StringComparison.OrdinalIgnoreCase) == false)
                {
                    continue;
                }
            }

            if (m_justShowBuildingInfo.IsNullOrWhiteSpace() == false)
            {
                //if (b.m_nGBuildingInfo.Contains(m_justShowBuildingInfo) == false)
                 //   continue;
            }
            m_loadCount++;
            loadBlocks.Add(b);
        }
        foreach(var b in loadBlocks)
            Block.Load(b.m_prefabName, GotBlock);
        m_isLoaded = true;
    }


    void CreateBuilding()
    {
        var lastblock = CreateBlock(m_houseBlocks[0].m_name, m_houseBuilding.m_blockHolder, m_houseBuilding.transform.position, SnapHinge.EType.None);
   
        for (int i = 1; i < m_houseBlocks.Count; i++)
        {
            var lastBlockHinge = lastblock.GetHinge(m_houseBlocks[i - 1].m_attach);
            var hingePos = lastblock.transform.position + lastBlockHinge.transform.localPosition;
            var p = lastBlockHinge.transform.position;
            lastblock = CreateBlock(m_houseBlocks[i].m_name, m_houseBuilding.m_blockHolder, hingePos, SnapHinge.OppositeHinge[m_houseBlocks[i-1].m_attach]);
        }
    }

    Block CreateBlock(string _name, Transform _holder, Vector3 _pos, SnapHinge.EType _attach)
    {
        var block = m_allBlocks.Find(o => o.BlockID.Equals(_name));
        var go = Instantiate(block.gameObject, _holder);
        var bounds = block.GetComponentInChildren<Renderer>().bounds;
        var bb = ManagedBlock.GetTotalVisualBounds(go);
        if (_attach != SnapHinge.EType.None)
        {
            var oppositeHinge = SnapHinge.OppositeHinge[_attach];
            var oppHinge = block.GetHinge(oppositeHinge);
            var hinge = block.GetHinge(_attach);
            var r = oppHinge.transform.position - hinge.transform.position;
            _pos += r-hinge.transform.localPosition;
        }
        go.transform.position = _pos;
        return go.GetComponentInChildren<Block>();
    }

    void GotBlock(GameObject _g)
    {
        m_loadCount--;
        if (_g != null)
        {
            var block = _g.GetComponentInChildren<Block>();
            if (block == null) return;
            m_allBlocks.Add(block);
            
            _g.transform.SetParent(m_holder);
            _g.transform.position = GetPos();
            if (m_showDebug)
            {
                var dObj = Instantiate(m_debugTextPrefab, _g.transform); 
                var txt = dObj.GetComponentInChildren<TMP_Text>();
                var info = NGBlockInfo.GetInfo(_g.GetComponentInChildren<Block>().m_blockInfoID);
                txt.text = $"{info.m_prefabName}\n{info.m_components}";
            }
        }
        if(m_loadCount == 0)
            MABuildingSupport.ToggleComponentIcons(m_holder, NGManager.Me.m_showComponentIcons);

    }

    Vector3 GetPos()
    {
        var rPos = IPos;
        IPos.x += blockWidth+m_blockPad.x;
        if (IPos.x > m_maxColumns * blockWidth)
        {
            IPos.x = 0;
            IPos.z += blockHeight+m_blockPad.z;
        }
        return rPos;
    }

}
