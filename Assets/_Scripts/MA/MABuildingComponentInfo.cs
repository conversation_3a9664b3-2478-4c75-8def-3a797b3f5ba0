using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MABuildingComponentInfo : NGBuildingInfoGUIComponentBase
{
    public Transform m_tableHolder;
    public MADesignInfoSheetLine m_designInfoBuildSheetLinePrefab;

    private MABuilding m_MABuilding;
    private int m_lastHash;
    
    private EType m_type;
    
    public void Update()
    {
        RefreshView();
    }

    public void Activate(EType _type, MABuilding _building)
    {
        
        //base.Activate();
        m_type = _type;
        m_MABuilding = _building;
        m_tableHolder.DestroyChildren();
        RefreshView();
    }
    
    void RefreshView()
    {
        if (m_MABuilding == null) return;
        
        switch(m_type)
        {
            case EType.ActionComponents: m_title.text = "Actions"; break;
            case EType.OtherComponents: m_title.text = "Info"; break;
            case EType.Blocks: m_title.text = "Block Values"; break;
        }
        
        var data = m_MABuilding.GetComponentData(m_type);
        
        string forHash = "";
        foreach(var val in data) forHash += val.Item2;

        var newHash = forHash.GetHashCode();
        if(m_lastHash != newHash)
        {
            m_lastHash = newHash;
            m_tableHolder.DestroyChildren();
            
            foreach(var val in data)
                MADesignInfoSheetLine.Create(m_tableHolder, val.Item1, val.Item2, true);
        }
    }
    
    public enum EType
    {
        ActionComponents,
        OtherComponents,
        Blocks,
    }
    
    public static MABuildingComponentInfo Create(Transform _holder, MABuilding _building, EType _type)
    {
        var prefab = Resources.Load<MABuildingComponentInfo>("_Prefabs/Dialogs/MABuildingComponentInfo");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_type, _building);
        return instance; 
    } 
}
