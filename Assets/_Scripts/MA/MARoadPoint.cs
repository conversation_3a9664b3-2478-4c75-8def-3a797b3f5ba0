using System;
using UnityEngine;

public class MARoadPoint : MonoBehaviour
{
    public enum MainRoadPointType
    {
        StartPoint,
        // OutsideEntryGate,
        // InsideEntryGate,
        // InsideExitGate,
        // OutsideExitGate,
        EndPoint,
    }
    
    public MainRoadPointType m_pointType;
    
    private GateOpener m_accessGate;

    public GateOpener AccessGate(Vector3 _target, bool _onlyConsiderRoadGates = true) // onlyConsiderRoadGates ignores gates formed by pavement-only road sets
    {
        if (m_accessGate == null)
            m_accessGate = GateOpener.ChooseBestGateFromPoints(transform.position, _target, _onlyConsiderRoadGates);
        return m_accessGate;
    }
}
