using UnityEngine;
using UnityEngine.UI;

public class MAResearchPopupWindowManager : MonoBehaviour
{
    public GameObject popupWindowPrefab; // Assign your popup prefab in the Inspector
    public RectTransform targetRectTransform; // The RectTransform where the popup should appear

    private GameObject activePopupWindow;

    private void Update()
    {
        // Check for right-click and if within the targetRectTransform
        if (Input.GetMouseButtonDown(1) && 
            RectTransformUtility.RectangleContainsScreenPoint(targetRectTransform, Input.mousePosition))
        {
            ShowPopupWindow();
        }
    }

    private void ShowPopupWindow()
    {
        if (activePopupWindow != null)
        {
            Destroy(activePopupWindow); // Destroy the old popup if one exists
        }

        activePopupWindow = Instantiate(popupWindowPrefab, transform);

        // Position the popup window under the mouse
        RectTransform popupRectTransform = activePopupWindow.GetComponent<RectTransform>();
        Vector2 anchoredPos = Input.mousePosition; // Get mouse position in screen space
        RectTransformUtility.ScreenPointToLocalPointInRectangle(
            targetRectTransform, anchoredPos, null, out anchoredPos); // Convert to local space
        popupRectTransform.anchoredPosition = anchoredPos;
    }
}