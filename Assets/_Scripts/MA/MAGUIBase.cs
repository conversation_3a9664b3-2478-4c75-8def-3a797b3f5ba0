using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class MAGUIBaseSingleton<T> : MAGUIBase where T : MAGUIBase
{
    private static T s_me;

    public static T Me
    {
        get => s_me;
        set => s_me = value;
    }

    public void Awake()
    {
        Me = this as T;
    }
    
    public void DestroyMe()
    {
        if(Me == this)
        {
            Me = null;
        }
        Destroy(gameObject);
    }
}

public class MAGUIBase : MonoBehaviour
{
    public const string GreenColor = "#006400";
    public const string RedColor = "red";
    
    public static List<MAGUIBase> ActiveGUI => (NGManager.Me != null) ? NGManager.Me.m_activeGUI : new List<MAGUIBase>();
    public string Name
    {
        get => m_name;
        set => m_name = value;
    }    
    public string m_name;
    [HideInInspector] public bool m_mouseDown;
    public bool m_isMouseWheelPriority = false;

    protected const string c_escapeBackLabel = "MAGUI";
    private bool m_hasPushedBackFunction = false;
    protected void PushBackFunction(Action _cb)
    {
        if(m_hasPushedBackFunction) return;
        GameManager.Me.PushEscapeBackFunction(c_escapeBackLabel, _cb);
        m_hasPushedBackFunction = true;
    }
    
    protected void PopBackFunction()
    {
        if(m_hasPushedBackFunction)
        {
            GameManager.Me.PopEscapeBackFunction(c_escapeBackLabel);
            m_hasPushedBackFunction = false;
        }
    }
    
    public void Activate(string _name = "")
    {
        AddToList(_name);
    }
    public void AddToList(string _name = "")
    {
        if(_name.IsNullOrWhiteSpace() == false)
          m_name = _name;
        if(ActiveGUI.Contains(this) == false)
            ActiveGUI.Add(this);
    }

    virtual protected void FixedUpdate()
    {
        m_mouseDown = false;
    }

    public void Activate(bool _flag)
    {
        gameObject.SetActive(_flag);
    }

    private void OnDestroy()
    {
        if (ActiveGUI.Contains(this))
            ActiveGUI.Remove(this);
    }

    private void OnDisable()
    {
        //Debug.Log("OnDisable");
    }
    private void OnEnable()
    {
        //Debug.Log("OnEnable");
    }

    private void OnBecameVisible()
    {
        //Debug.Log("OnBecameVisible");
    }
    virtual public void Highlight(bool _active)
    {
        
    }

    public void OnMouseDown()
    {
        m_mouseDown = true;
    }

    public static bool HighlightGUI(string _name, bool _active)
    {
        var found = ActiveGUI.Find(o => o.Name.Equals(_name, StringComparison.OrdinalIgnoreCase));
        if (found != null)
        {
            found.Highlight(_active);
            return true;
        }
        return false;
    }

    public static bool IsClicked(string _name)
    {
        var found = Find(_name);
        if (found == null)
        {
            Debug.LogError(MAParserSupport.DebugColor($"no GUI found '{_name}'"));
            return false;
        }

        return found.m_mouseDown;
    }

    public static MAGUIBase Find(string _name)
    {
        return ActiveGUI.Find(o => o.Name.Trim().Equals(_name.Trim(), StringComparison.OrdinalIgnoreCase));
    }
    
    public static bool ActivateGUI(string _name, bool _active)
    {
        var found = ActiveGUI.Find(o => o.Name.Equals(_name, StringComparison.OrdinalIgnoreCase));
        if (found != null)
        {
            found.Activate(_active);
            return true;
        }
        return false;
    }
    
    virtual public void SetScale(float _scale)
    {
        transform.localScale = new Vector3(_scale, _scale, _scale);
    }
}


