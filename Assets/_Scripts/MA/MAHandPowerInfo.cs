using System;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class MAHandPowerInfo
{
    public static List<MAHandPowerInfo> s_handPowers = new List<MAHandPowerInfo>();
    public static List<MAHandPowerInfo> GetList=>s_handPowers;
    public string DebugDisplayName => $"{m_name}[{m_level}]";

    public string id;
    public bool m_debugChanged;
    public string m_name;
    public int m_level;
    public float m_baseDamage;
    public float m_minDamage;
    public int m_numOfHits;
    public float m_aoeRadius;
    public float m_attackWidth;
    public float m_attackLength;
    public float m_bonusAttackChance;
    public float m_tickSpeed;
    public float m_manaCost;
    public float m_manaCostPerSecond;
    public float m_baseCooldownTime;
    public string m_title;
    public string m_description;
    public float m_knockbackPower;
    public float m_perChainDamageModifier = .9f;

    public static bool PostImport(MAHandPowerInfo _what)
    {
        return true;
    }
    public static List<MAHandPowerInfo> LoadInfo() // Must be loaded after blocks
    {
        s_handPowers = NGKnack.ImportKnackInto<MAHandPowerInfo>(PostImport);
        return s_handPowers;
    }

    public static MAHandPowerInfo GetInfo(string _name)
    {
        var power = s_handPowers.Find(o => $"{o.m_name}{o.m_level}".ToLower() == _name.ToLower());
        return power;
    }
    public static MAHandPowerInfo GetInfoByLevel(string _name, int _level)
    {
        int bestLevel = 0;
        MAHandPowerInfo best = null;
        _name = _name.ToLower();
        for (int i = 0; i < s_handPowers.Count; ++i)
        {
            var power = s_handPowers[i];
            if (power.m_level <= _level && power.m_level > bestLevel && power.m_name.ToLower() == _name)
            {
                bestLevel = power.m_level;
                best = power;
            }
        }
        return best;
    }
}
