using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCGuildBedroom : BCWorkerBase
{
#if UNITY_EDITOR    
    override public void DebugShowGUIDetails(GUIStyle _labelStyle, bool _showBase = true)
    {
        if(_showBase)
            base.DebugShowGUIDetails(_labelStyle, _showBase);
    }
#endif
    public override List<MACharacterBase> GetHeroesAllocated() => m_workersAllocated;
    public override List<MACharacterBase> GetHeroesPresent() => m_workersPresent;
    
    public override void GetCombinedValue(ref CombinedStat _value)
    {
        if(_value == null) _value = new HeroSlotCombinedStat();
        base.GetCombinedValue(ref _value);
    }
    
    override public void UpdateInternal(BuildingComponentsState _state)
    {
        UpdateOccupants();
    }

    public override bool Arrive(MACharacterBase _character)
    {
        bool result = base.Arrive(_character);
        if (result)
        {
            _character.gameObject.SetActive(false); // TS - if you want him to stay awake inside a building please speak to thomas
            
            //m_building.m_componentsDict.TryGetValue(typeof(BCGuildBedroom), out var bedrooms);
            //_character.CharacterUpdateState.ApplyState(CharacterStates.Idle);
        }
        return result;
    }

    public override bool Allocate(MACharacterBase _worker)
    {
        if(base.Allocate(_worker))
        {
            _worker.Home = this;
            return true;
        }
        return false;
    }
    
    void UpdateOccupants()
    {
        if(m_workersPresent.Count == 0) return;
        
        Building.m_isInhabited = true;
        
        var values = m_building.GetHeroesGuildMultipliers();
        
        for (var i = m_workersPresent.Count - 1; i >=0 ; i--)
        {
            ShowChimneySmoke();
            var hero = m_workersPresent[i] as MAHeroBase;
            if (hero == null)
                continue;
            
            if(BCGuildHealing.RequiresHealing(hero))
            {
                TryHeal(hero, values.healMultiplier);
                Building.IsHealing = true;
                continue;
            }
            
            if(BCGuildTraining.RequiresTraining(hero) && m_building.HasBuildingComponent<BCGuildTraining>())
            {
                TryTrain(hero, values.trainMultiplier);
                Building.IsTraining = true;
                continue;
            }
            
            // RW-03-APR-25: When a hero dies in a cave, they're teleported back to the Heroes Guild to recover health. This functionality
            // would be better taken care of by a block (either a healing block or a bedroom), but what if those don't exist? That hasn't 
            // been agreed by design yet, so I'm putting this here to give the hero _some_ path to recovery, but there's still some stuff
            // which needs to be finalised which regards to what's going on in this area.
            if (!hero.IsIncapacitated || hero.Health > hero.ReincarnationHealth)
            {
                m_building.EjectHero(hero);
            }
        }
    }
    
    private bool TryHeal(MAHeroBase _hero, float _multiplier)
    {
        foreach(var h in m_building.BuildingComponents<BCGuildHealing>())
        {
            if(h.TryHeal(_hero, _multiplier)) return true;
        }
        HealHero(_hero, _multiplier);
        return false;
    }
    
    private bool TryTrain(MAHeroBase _hero, float _multiplier)
    {
        foreach(var h in m_building.BuildingComponents<BCGuildTraining>())
        {
            if(h.TryTrain(_hero, _multiplier)) return true;
        }
        return false;
    }
    
    private void HealHero(MACharacterBase _hero, float _multiplier)
    {
        _hero.RecoverHealthImmediate(Mathf.Max(_hero.CreatureInfo.m_deadHealthRecoveryRate,_hero.CreatureInfo.m_healthDayRegeneration) * _multiplier);
    }
    
    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (BCHeroPanel.PanelID, () => new BCHeroPanel());
}

public class BCHeroPanel : BCUIPanel
{
    public override int Priority => 20;
    public const string PanelID = "heroes";
    public List<BCWorkerBase> m_bases = new();
    public override bool ForceDisableQuantityDisplay => true;
    public override bool m_forceDisableHighlightButton => true;
    
    public BCHeroPanel() : base(PanelID, "Heroes") { }

    public override string GetPrimaryText()
    {
        string text = "";
        BCBase.CombinedStat stat = null;
        foreach (var c in m_bases) c.GetCombinedValue(ref stat);
        text += stat?.GetValue(BCBase.CombinedStat.ValueDisplay.Default, true);
        
        text += "\n\nThe following Heroes live here.";
        
        return text;
    }
    
    public override string GetDescription()
    {
        int hereos = 0;
        foreach (var c in m_bases)
        {
            hereos += c.m_workersAllocated.Count;
        }
        if(hereos > 1) return $"{hereos} Heroes";
        return $"{hereos} Hero";
    }
    
    public override void AddComponent(BCBase _component)
    {
        BCWorkerBase hc = _component as BCWorkerBase;
        if(hc != null)
        {
            m_bases.Add(hc);
            m_quantity += hc.m_maxWorkers;
            m_all.Add(_component);
        }
        // Dont call base
    }
    
    override public IEnumerable<System.Action> CreateTableLines(Transform _holder)
    {
        List<MACharacterBase> healing = new();
        List<MACharacterBase> training = new();
        List<MACharacterBase> away = new();
        
        if(m_bases.Count == 0 || m_bases[0].Building == null)
            yield break;
            
        bool canTrain = m_bases[0].Building.HasBuildingComponent<BCGuildTraining>();
        
        foreach(var workerBase in m_bases)
        {
            foreach(var character in workerBase.m_workersAllocated)
            {
                var hero = character as MAHeroBase;
                if(hero == null || character.Home?.Building != workerBase.Building) continue;

                if(hero.m_insideMABuilding != workerBase.Building)
                    away.Add(character);
                else if(BCGuildHealing.RequiresHealing(hero))
                    healing.Add(character);
                else if(canTrain && BCGuildTraining.RequiresTraining(character as MAHeroBase))
                    training.Add(character);
                else
                    healing.Add(character);
            }
        }
        
        yield return () => MABuildingWorkerPanelLine.CreateTitle(_holder, "", "Experience", "Health");
        
        // Healing
        yield return () => MABuildingWorkerPanelLine.CreateTitle(_holder, MAMessageManager.GetTMPString("HeroHealing") + " Healing", "", "");
        if(healing.Count == 0)
        {
            yield return () => MADesignInfoSheetLine.Create(_holder, "<i>None</i>", null, false, false);
        }
        else
        {
            foreach(var c in healing) yield return () => MABuildingWorkerPanelLine.Create(_holder, c);
        }
        
        // Training
        yield return () => MABuildingWorkerPanelLine.CreateTitle(_holder, MAMessageManager.GetTMPString("HeroTraining") + " Training", "", "");
        if(training.Count == 0)
        {
            yield return () => MADesignInfoSheetLine.Create(_holder, "<i>None</i>", null, false, false);
        }
        else
        {
            foreach(var c in training) yield return () => MABuildingWorkerPanelLine.Create(_holder, c);
        }
        
        // Away
        yield return () => MABuildingWorkerPanelLine.CreateTitle(_holder, "Away", "", "");
        if(away.Count == 0)
        {
            yield return () => MADesignInfoSheetLine.Create(_holder, "<i>None</i>", null, false, false);
        }
        else
        {
            foreach(var c in away) yield return () => MABuildingWorkerPanelLine.Create(_holder, c);
        }
    }
}