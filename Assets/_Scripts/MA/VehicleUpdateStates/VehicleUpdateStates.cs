using System.Collections.Generic;
using UnityEngine;
using VehicleStates;

public interface IStateControlledVehicle
{
    public void SetVehicleState(VehicleBaseState _newState);
    public VehicleBaseState VehicleState { get; }
}

public static class VehicleStateFactory
{
    public enum VehicleState
    {
        kNone,
        kInitialSpawn,
        kMovingOffMap,
        kMovingToHome,
        kWaiting,
        kWaitingForCargo,
        kWaitingOffMap,
        kWaitingAtEntryGate,
        kWaitingAtExitGate,
        kCOUNT,
    }

    private static readonly Dictionary<VehicleState, System.Func<MAVehicle, VehicleBaseState>> m_stateLibrary = new Dictionary<VehicleState, System.Func<MAVehicle, VehicleBaseState>>()
    {
        { VehicleState.kNone, _vehicle => new VehicleBaseState() },
        { VehicleState.kInitialSpawn, _vehicle => new InitialSpawnState(_vehicle) },
        { VehicleState.kMovingOffMap, _vehicle => new MovingOffMap(_vehicle) },
        { VehicleState.kMovingToHome, _vehicle => new MovingToHome(_vehicle) },
        { VehicleState.kWaiting, _vehicle => new Waiting(_vehicle) },
        { VehicleState.kWaitingForCargo, _vehicle => new WaitingForCargo(_vehicle) },
        { VehicleState.kWaitingOffMap, _vehicle => new WaitingOffMap(_vehicle) },
        { VehicleState.kWaitingAtEntryGate, _vehicle => new WaitingAtEntryGate(_vehicle) },
        { VehicleState.kWaitingAtExitGate, _vehicle => new WaitingAtExitGate(_vehicle) },
    };
    
    public static void ApplyState(VehicleState _vehicleState, MAVehicle _vehicle)
    {
        VehicleBaseState currentState = _vehicle.VehicleState;

        if(currentState.State != _vehicleState)
        {
            if(m_stateLibrary.TryGetValue(_vehicleState, out System.Func<MAVehicle, VehicleBaseState> createState))
            {
                currentState.OnExit();
                currentState = createState(_vehicle);
            }
            else
            {
                Debug.LogError($"NGVehicleUpdateStates - ApplyState - Could not find state of type {_vehicleState.ToString()}");
            }
            _vehicle.SetVehicleState(currentState);
            currentState.OnEnter();
        }
        else
        {
            if(currentState.State != VehicleState.kNone) Debug.LogWarning($"NGVehicleUpdateStates - ApplyState - Trying to apply same state as exists: {_vehicleState.ToString()}");
            currentState.OnEnter();
        }
    }
}