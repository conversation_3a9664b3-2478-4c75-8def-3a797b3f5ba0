namespace VehicleStates
{
    [System.Serializable]
    public class VehicleBaseState
    {
        protected MAVehicle m_stateControlledVehicle;

        private VehicleStateFactory.VehicleState m_state = VehicleStateFactory.VehicleState.kNone;

        public VehicleStateFactory.VehicleState State
        {
            get => m_state;
            protected set => m_state = value != VehicleStateFactory.VehicleState.kNone ? value : m_state;
        }

        public virtual void OnEnter()
        {
        }

        public virtual void OnUpdate()
        {
        }

        public virtual void OnExit()
        {
        }

        // public virtual void OnSave(){}
        public virtual float WaitingTimeLeft => -1f;
    }
}