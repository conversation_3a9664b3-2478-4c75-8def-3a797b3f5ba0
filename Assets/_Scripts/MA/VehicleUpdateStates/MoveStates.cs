using System;
using UnityEngine;

namespace VehicleStates
{
    [System.Serializable]
    public class WaitingAtEntryGate : WaitingAtGate
    {
        public WaitingAtEntryGate(MAVehicle _vehicle) : base(_vehicle)
        {
            State = VehicleStateFactory.VehicleState.kWaitingAtEntryGate;
            m_stateControlledVehicle = _vehicle;
            m_stateToWaitFor = VehicleStateFactory.VehicleState.kMovingToHome;
        }
    } 
    
    [System.Serializable]
    public class WaitingAtExitGate : WaitingAtGate
    {
        public WaitingAtExitGate(MAVehicle _vehicle) : base(_vehicle)
        {
            State = VehicleStateFactory.VehicleState.kWaitingAtExitGate;
            m_stateControlledVehicle = _vehicle;
            m_stateToWaitFor = VehicleStateFactory.VehicleState.kMovingOffMap;
        }
    }
    
    [System.Serializable]
    public abstract class WaitingAtGate : Waiting
    {
        protected VehicleStateFactory.VehicleState m_stateToWaitFor;
        
        public WaitingAtGate(MAVehicle _vehicle) : base(_vehicle)
        {
            m_stateControlledVehicle = _vehicle;
        }

        public override void OnEnter()
        {
            m_stateControlledVehicle.m_waitFor = 2f;
            m_stateControlledVehicle.m_afterWait = m_stateToWaitFor;
            base.OnEnter();
            m_stateControlledVehicle.m_nav.Unpause();
            m_stateControlledVehicle.m_nav.Pause(false, true);
            m_stateControlledVehicle.StopTravelSound();
            //m_stateControlledVehicle.WaitUntilApplyState(m_stateToWaitFor, 2f);
        }
    } 

    [System.Serializable]
    public class MovingOffMap : VehicleBaseState
    {
        Vector3 m_targetPos;
        bool m_targetIsGate = false;
        public MovingOffMap(MAVehicle _vehicle)
        {
            State = VehicleStateFactory.VehicleState.kMovingOffMap;
            m_stateControlledVehicle = _vehicle;
            m_stateControlledVehicle.ReleaseQueueSlot();
        }

        bool IsGoingToGate()
        {
            MARoadPoint roadEndPoint = NGManager.Me.GetRoadEnd(m_stateControlledVehicle.transform.position);
            return (m_stateControlledVehicle.DestinationPosition.GetXZ() - roadEndPoint.transform.position.GetXZ()).magnitude < 1f == false;
        }
        
        protected void OnPathProcessed(NGMovingObject.TargetReachability _reachability)
        {
            switch(_reachability)
            {
                case NGMovingObject.TargetReachability.IsReachable:
                    if (IsGoingToGate() == false)
                    {
                        if (m_stateControlledVehicle.m_nav.TryGetTravelDistanceToTarget(out float outDistance))
                        {
                            float relativeDistance = outDistance / m_stateControlledVehicle.m_nav.DistanceToTarget;
                            if (relativeDistance > 1.75f)
                            {
                                m_stateControlledVehicle.m_nav.StopNavigation();
                                RetryLater();
                            }
                            break;
                        }
                    }
                    m_stateControlledVehicle.StartTravelSound();
                    break;
                case NGMovingObject.TargetReachability.IsNotReachable:
                case NGMovingObject.TargetReachability.IsNotReachableAndAlreadyAtNearestPos:
                case NGMovingObject.TargetReachability.PathFailure:
                    m_stateControlledVehicle.m_nav.StopNavigation();
                    RetryLater();
                    break;
            }
        }
        
        private void RetryLater()
        {
            VehicleStateFactory.ApplyState(VehicleStateFactory.VehicleState.kWaitingAtExitGate, m_stateControlledVehicle);
        }
        
        public override void OnEnter()
        {
            m_stateControlledVehicle.m_nav.Unpause();
            
            Vector3 cartPos = m_stateControlledVehicle.transform.position.GetXZ();
            Vector3 gatePos = Vector3.zero;
            MARoadPoint roadEndPoint = NGManager.Me.GetRoadEnd(cartPos);
            Vector3 endPos = roadEndPoint.transform.position.GetXZ();
            Vector3 targetPos;
            
            //is cart further away from endPos or is gate further away from parkingPos?
            float distCartToEndPos = (cartPos - endPos).xzSqrMagnitude();
            float distGateToEndPos = Single.MaxValue;

            GateOpener accessGate = roadEndPoint.AccessGate(endPos);
            m_targetIsGate = accessGate != null;
            if (m_targetIsGate)
            {
                gatePos = accessGate.transform.position;
                distGateToEndPos = (gatePos - endPos).xzSqrMagnitude();

                if (distGateToEndPos < distCartToEndPos)
                {
                    Vector3 vehiclePos = m_stateControlledVehicle.transform.position;
                    int direction = accessGate.QueueDirectionFromPosition(vehiclePos);
                    targetPos = accessGate.QueueSlotPosition(m_stateControlledVehicle.GetQueueSlot(accessGate, direction));
                }
                else
                {
                    targetPos = endPos;
                }
            }
            else
            {
                targetPos = endPos;
            }

            m_targetPos = targetPos;

            m_stateControlledVehicle.m_onPathProcessed -= OnPathProcessed;
            m_stateControlledVehicle.m_onPathProcessed += OnPathProcessed;
                
            if (m_stateControlledVehicle.SetMoveToPosition(targetPos, false) == false)
            {
                RetryLater();
            }
        }

        public override void OnUpdate()
        {
            if (m_stateControlledVehicle.m_nav.TargetReached)
            {
                var destPosXZ = m_stateControlledVehicle.GameState.m_destPos.GetXZ();
                MARoadPoint roadEndPoint = NGManager.Me.GetRoadEnd(m_stateControlledVehicle.transform.position);
                GateOpener accessGate = roadEndPoint.AccessGate(destPosXZ);
                if (m_targetIsGate && accessGate != null)
                {
                    if (destPosXZ.Approximately(m_targetPos.GetXZ()))
                    {
                        if (accessGate.IsGateOpen)
                        {
                            m_stateControlledVehicle.m_onPathProcessed -= OnPathProcessed;
                            m_stateControlledVehicle.m_onPathProcessed += OnPathProcessed;
                            m_stateControlledVehicle.ReleaseQueueSlot(accessGate);
                            
                            if (m_stateControlledVehicle.SetMoveToPosition(roadEndPoint.transform.position) == false)
                            {
                                RetryLater();
                            }
                        }
                        else
                        {
                            RetryLater();
                        }
                    }
                    else if(destPosXZ.Approximately(roadEndPoint.transform.position.GetXZ()))
                    {
                        OnFinalArrive();
                    }
                }
                else
                {
                    OnFinalArrive();
                }
            }
        }

        public override void OnExit()
        {
            base.OnExit();
            m_stateControlledVehicle.m_onPathProcessed -= OnPathProcessed;
            m_stateControlledVehicle = null;
        }

        private void OnFinalArrive()
        {
            m_stateControlledVehicle.transform.position = m_stateControlledVehicle.m_nav.transform.position.SetYToHeight();
            m_stateControlledVehicle.OnArrivedAtFinalDestination();
            VehicleStateFactory.ApplyState(VehicleStateFactory.VehicleState.kWaitingOffMap, m_stateControlledVehicle);
        }
    }

    [Serializable]
    public class MovingToHome : VehicleBaseState
    {    
        Vector3 m_targetPos;
        bool m_targetIsGate = false;
        public MovingToHome(MAVehicle _stateControlledVehicle)
        {
            State = VehicleStateFactory.VehicleState.kMovingToHome;
            m_stateControlledVehicle = _stateControlledVehicle;
        }

        public override void OnEnter()
        {
            m_stateControlledVehicle.gameObject.SetActive(true);
            m_stateControlledVehicle.m_nav.Unpause();
            
            var home = m_stateControlledVehicle.Home;
            
            if(home == null) return;
            
            Vector3 cartPos = m_stateControlledVehicle.transform.position.GetXZ();
            Vector3 gatePos = Vector3.zero;
            Vector3 parkingPos = m_stateControlledVehicle.GetQueuePosition().GetXZ();
            Vector3 targetPos;
            
            //is cart further away from parkingPos or is gate further away from parkingPos?
            //is cart further?
            float distCartToParking = (cartPos - parkingPos).xzSqrMagnitude();
            float distGateToParking = Single.MaxValue;
            
            MARoadPoint roadStartPoint = NGManager.Me.GetRoadStart(cartPos);
            GateOpener accessGate = roadStartPoint.AccessGate(parkingPos);
            m_targetIsGate = accessGate != null;
            if (m_targetIsGate)
            {
                gatePos = accessGate.transform.position;
                distGateToParking = (gatePos - parkingPos).xzSqrMagnitude();

                if (distGateToParking < distCartToParking)
                {
                    //go to gate first
                    Vector3 vehiclePos = m_stateControlledVehicle.transform.position;
                    int direction = accessGate.QueueDirectionFromPosition(vehiclePos);
                    targetPos = accessGate.QueueSlotPosition(m_stateControlledVehicle.GetQueueSlot(accessGate, direction));
                }
                else
                {
                    targetPos = parkingPos;
                }
            }
            else
            {
                targetPos = parkingPos;
            }
            m_targetPos = targetPos;
            
            m_stateControlledVehicle.m_onPathProcessed -= OnPathProcessed;
            m_stateControlledVehicle.m_onPathProcessed += OnPathProcessed;
                
            if (m_stateControlledVehicle.SetMoveToPosition(targetPos, false) == false)
            {
                RetryLater();
            }
        }

        private void RetryLater()
        {
            VehicleStateFactory.ApplyState(VehicleStateFactory.VehicleState.kWaitingAtEntryGate, m_stateControlledVehicle);
        }

        private bool m_testingPathHomeFromGate = false;
        public override void OnUpdate()
        {
            if (m_stateControlledVehicle.m_nav.TargetReached)
            {
                var home = m_stateControlledVehicle.Home;
                
                if(home == null || home.Building == null || home.Building.Nav.HasValidPath == false)
                {
                    return;
                }
                
                var doorPos = m_stateControlledVehicle.GetQueuePosition().GetXZ();
                if ((m_stateControlledVehicle.m_nav.transform.position.GetXZ()-doorPos.GetXZ()).xzMagnitude() > 1f)
                {
                    var destPosXZ = m_stateControlledVehicle.GameState.m_destPos.GetXZ();
                    MARoadPoint roadStartPoint = NGManager.Me.GetRoadStart(m_stateControlledVehicle.transform.position);
                    GateOpener accessGate = roadStartPoint.AccessGate(destPosXZ);
                    if (m_targetIsGate && accessGate != null)
                    {
                        if (destPosXZ.Approximately(m_targetPos.GetXZ()))
                        {
                            if (accessGate.IsGateOpen)
                            {
                                m_stateControlledVehicle.ReleaseQueueSlot(accessGate);
                                m_stateControlledVehicle.m_onPathProcessed -= OnPathProcessed;
                                m_stateControlledVehicle.m_onPathProcessed += OnPathProcessed;
                                
                                if (m_stateControlledVehicle.SetMoveToPosition(doorPos) == false)
                                {
                                    return;
                                }
                            }
                            else if (m_testingPathHomeFromGate == false)
                            {
                                m_testingPathHomeFromGate = true;
                                //comment this back in if we want the cart to try and find a way into town from outside the gate.
                                // at the moment the cart finds a path over and down some cliffs but physically fails to use that path
                                m_stateControlledVehicle.m_onPathProcessed -= OnPathProcessed;
                                m_stateControlledVehicle.m_onPathProcessed += OnPathProcessed;
                                
                                if (m_stateControlledVehicle.SetMoveToPosition(doorPos) == false)
                                {
                                    m_testingPathHomeFromGate = false;
                                    return;
                                }
                            }
                        }
                    }
                    else
                    {
                        RetryLater();
                    }
                }
                else if (m_stateControlledVehicle is MADeliveryCart)
                {
                    VehicleStateFactory.ApplyState(VehicleStateFactory.VehicleState.kWaitingForCargo,
                        m_stateControlledVehicle);
                }
                else
                {
                    m_stateControlledVehicle.m_waitFor =
                        m_stateControlledVehicle.VehicleMetaData.m_waitingTimeAtDestination;
                    m_stateControlledVehicle.m_afterWait = VehicleStateFactory.VehicleState.kMovingToHome;
                    VehicleStateFactory.ApplyState(VehicleStateFactory.VehicleState.kWaiting, m_stateControlledVehicle);
                }
            }
        }
        
        public override void OnExit()
        {
            base.OnExit();
            m_stateControlledVehicle.m_onPathProcessed -= OnPathProcessed;
        }

        protected bool IsGoingToGate()
        {
            var home = m_stateControlledVehicle.Home;
            if (home != null)
            {
                return (m_stateControlledVehicle.DestinationPosition.GetXZ() - m_stateControlledVehicle.GetQueuePosition().GetXZ())
                    .magnitude < 1f;
            }
            return false;
        }
        
        protected void OnPathProcessed(NGMovingObject.TargetReachability _reachability)
        {
            switch (_reachability)
            {
                case NGMovingObject.TargetReachability.IsReachable:
                    if (IsGoingToGate() && m_testingPathHomeFromGate)
                    {
                        if (m_stateControlledVehicle.m_nav.TryGetTravelDistanceToTarget(out float outDistance))
                        {
                            float relativeDistance = outDistance / m_stateControlledVehicle.m_nav.DistanceToTarget;
                            if (relativeDistance > 1.75f)
                            {
                                m_stateControlledVehicle.m_nav.StopNavigation();
                                RetryLater();
                            }
                            break;
                        }
                    }
                    m_stateControlledVehicle.StartTravelSound();
                    break;
                case NGMovingObject.TargetReachability.IsNotReachable:
                    if (/*IsGoingToGate() &&*/ m_testingPathHomeFromGate)
                    {
                        m_stateControlledVehicle.m_nav.StopNavigation();
                        RetryLater();
                    }
                    // if (IsGoingToGate() == false)
                    // {
                    //     m_stateControlledVehicle.m_nav.StopNavigation();
                    //     RetryLater();
                    // }
                    break;
                case NGMovingObject.TargetReachability.IsNotReachableAndAlreadyAtNearestPos:   
                    m_stateControlledVehicle.m_nav.StopNavigation();
                    RetryLater();
                    break;
                case NGMovingObject.TargetReachability.PathFailure:
                    m_stateControlledVehicle.m_nav.StopNavigation();
                    RetryLater();
                    break;
            }
        }
    }
}