using System;
using UnityEditor;
using UnityEngine;
			
public class BloodEmitter : MonoBehaviour
{
    [System.Serializable]
    public class BloodEmitterSettings
    {
        public bool m_playOnStart = true;
        public bool m_destroyOnFinish = true;
        public bool m_destroyOnlyComponent = true;
        public float m_splatsPerSecond = 4f;
        public int m_emitCountPerSplat = 2;
        public float m_totalBleedTime = 10f;
    }
    
    [Range(0, 60)]
    public float m_totalBleedTime = 1f; //characterBase?
    public float m_splatsPerSecond = 2f; //characterBase?

    public int m_emitCountPerSplat = 1;

    public bool m_playOnStart = true;
    public bool m_destroyOnFinish = true;
    public bool m_destroyOnlyComponent = false;

    [NonSerialized]
    public Vector3 m_normal;
    [NonSerialized]
    public float m_bloodRunningTimeLeft = 0f; //characterBase?
    
    private float m_timeUntilNextSplat = 0f;
    private Transform m_transform = null;
		
    private void Awake()
    {
        m_transform = transform;
        m_bloodRunningTimeLeft = m_totalBleedTime;
    }

    private void Start()
    {
        if (m_playOnStart)
        {
            Play();
        }
    }

    public void Play()
    {
        m_bloodRunningTimeLeft = m_totalBleedTime;
        m_timeUntilNextSplat = 0f;
    }
    
    public void Play(BloodEmitterSettings _settings)
    {
        m_bloodRunningTimeLeft = m_totalBleedTime;
        m_timeUntilNextSplat = 0f;
        
        m_playOnStart = _settings.m_playOnStart;
        m_destroyOnFinish = _settings.m_destroyOnFinish;
        m_destroyOnlyComponent = _settings.m_destroyOnlyComponent;
        m_splatsPerSecond = _settings.m_splatsPerSecond;
        m_emitCountPerSplat = _settings.m_emitCountPerSplat;
        m_totalBleedTime = _settings.m_totalBleedTime;
    }
    
    private void Update()
    {
        if(m_bloodRunningTimeLeft > 0)
        {
            m_bloodRunningTimeLeft -= Time.deltaTime;
            m_timeUntilNextSplat -= Time.deltaTime;
            
            if(m_timeUntilNextSplat <= 0f)
            {
                MABloodControl.Me.EmitBloodAtHit(m_transform.position, Vector3.down /*m_normal*/, m_transform, m_emitCountPerSplat);
                m_timeUntilNextSplat = 1 / m_splatsPerSecond;
            }
        }
        else
        {
            if (m_destroyOnFinish)
            {
                if(m_destroyOnlyComponent == false)
                {
                    Destroy(gameObject);
                }
                else
                {
                    Destroy(this);
                }
            }
            return;
        }
    }
}


#if UNITY_EDITOR
[CustomEditor(typeof(BloodEmitter))]
public class BloodEmitterEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        if (GUILayout.Button("Play"))
        {
            (target as BloodEmitter).Play();
        }
    }
}
#endif