using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class MAUILine : MonoBehaviour
{
    public GameObject m_startObject;
    public GameObject m_endObject;
    public GameObject m_startEntryObject;
    public GameObject m_endExitObject;
    public Image m_lineImage;
    public UIBezier m_lineBezier;
    public Image m_arrowImage;
    RectTransform startPoint => m_startObject.GetComponent<RectTransform>();
    RectTransform endPoint => m_endObject.GetComponent<RectTransform>();
    RectTransform startEntryPoint => m_startEntryObject.GetComponent<RectTransform>();
    RectTransform endExitPoint => m_endExitObject.GetComponent<RectTransform>();

    void Activate(GameObject from, GameObject fromEntry, GameObject to, GameObject toExit, float _thickness, Color _color, bool _showArrow)
    {
        m_startObject = from;
        m_endObject = to;
        m_startEntryObject = fromEntry;
        m_endExitObject = toExit;
        if (m_lineBezier != null)
        {
            m_lineBezier.color = _color;
            m_lineBezier.m_thickness = _thickness;
        }
        else
        {
            m_lineImage.color = _color;
            m_lineImage.rectTransform.sizeDelta = new Vector2(m_lineImage.rectTransform.sizeDelta.x, _thickness);
        }
        m_arrowImage.gameObject.SetActive(_showArrow);

    }
    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    void LateUpdate()
{
    if (startPoint == null || endPoint == null) return;

    // Adjust for parent scaling
    float scale = 1.0f / transform.parent.lossyScale.x;

    // Calculate world positions of start and end points to avoid local scale issues
    Vector3 startWorldPos = startPoint.position;
    Vector3 endWorldPos = endPoint.position;

    // Calculate the mid-point for positioning the line in the middle of the start and end points
    Vector3 midPosition = (startWorldPos + endWorldPos) / 2f;

    // Calculate angle based on world positions for accurate rotation
    float angle = Mathf.Atan2(endWorldPos.y - startWorldPos.y, endWorldPos.x - startWorldPos.x) * Mathf.Rad2Deg;

    if (m_lineBezier != null)
    {
        // Position and scale adjustments for Bezier line
        transform.localScale = Vector3.one * scale;  // Normalize scale
        m_lineBezier.transform.position = midPosition;

        m_lineBezier.m_points = m_startEntryObject == null || m_endExitObject == null
            ? new Vector2[] { startWorldPos - midPosition, endWorldPos - midPosition }
            : new Vector2[] { startWorldPos - midPosition, startEntryPoint.position - midPosition, endExitPoint.position - midPosition, endWorldPos - midPosition };

        m_lineBezier.m_randomAmount = 0.5f;
        m_lineBezier.m_maxKink = 150;
        m_lineBezier.m_thickness = 3 / scale; // Normalize thickness
        m_lineBezier.SetRandomSeed(gameObject.GetInstanceID());
    }
    else
    {
        // Position and rotate the line image
        float distance = Vector2.Distance(startWorldPos, endWorldPos) / scale;
        m_lineImage.transform.position = midPosition;
        m_lineImage.rectTransform.sizeDelta = new Vector2(distance, m_lineImage.rectTransform.sizeDelta.y / scale); // Adjust thickness
        m_lineImage.rectTransform.localRotation = Quaternion.Euler(0, 0, angle);
    }

    if (m_arrowImage != null)
    {
        m_arrowImage.transform.position = endWorldPos; // Position the arrow at the end
        m_arrowImage.GetComponent<RectTransform>().localRotation = Quaternion.Euler(0, 0, angle);
    }
}
    void LateUpdateOld()
    {
        if (startPoint == null || endPoint == null)
        {
            return;
        }

        var position = (startPoint.position + endPoint.position) / 2f;
        
        float angle = Mathf.Atan2(endPoint.anchoredPosition.y - startPoint.anchoredPosition.y, endPoint.anchoredPosition.x - startPoint.anchoredPosition.x) * Mathf.Rad2Deg;
        if (m_lineBezier != null)
        {
            float scale = 1.0f / transform.parent.lossyScale.x;
            position = startPoint.position;
            transform.localScale = Vector3.one * scale;
            
            m_lineBezier.transform.position = position;
            if (m_startEntryObject == null || m_endExitObject == null)
                m_lineBezier.m_points = new Vector2[] { startPoint.position - position, endPoint.position - position };
            else
                m_lineBezier.m_points = new Vector2[] { startPoint.position - position, startEntryPoint.position - position, endExitPoint.position - position, endPoint.position - position };
            m_lineBezier.m_randomAmount = .5f;
            m_lineBezier.m_maxKink = 150;
            m_lineBezier.m_thickness = 3 / scale;
            m_lineBezier.SetRandomSeed(gameObject.GetInstanceID());
        }
        else
        {
            // Position and rotate the line image
            float distance = Vector2.Distance(startPoint.anchoredPosition, endPoint.anchoredPosition);
            m_lineImage.transform.position = position;
            m_lineImage.rectTransform.sizeDelta = new Vector2(distance, m_lineImage.rectTransform.sizeDelta.y);
            m_lineImage.rectTransform.localRotation = Quaternion.Euler(0, 0, angle);
        }
        
        m_arrowImage.transform.position = position;//endPoint.position;
        m_arrowImage.GetComponent<RectTransform>().localRotation = Quaternion.Euler(0, 0, angle);
    }

    public static MAUILine Create(GameObject _from, GameObject _fromEntry, GameObject _to, GameObject _toExit, Transform _holder, float _thickness, Color _color, bool _showArrow = false)
    {
        var prefab = Resources.Load<MAUILine>("_Prefabs/Research/MAUILine");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_from, _fromEntry, _to, _toExit, _thickness, _color, _showArrow);
        return instance;
    }
}
