using System.Collections.Generic;
using UnityEngine;

public class BCGuildHealing : BCBase
{
    [Knack<PERSON>ield] public float m_heroHealingPerSecond = 1f;
    [KnackField] public int m_heroesToHeal = 1;
    private MACharacterBase m_lastHeroHealed;
    
    public string[] m_acceptsCharacterTypes = new string[] { "Giant", "GiantFemale" };

    public override List<MACharacterBase> GetHeroesPresent()
    {
        return m_building.GetHeroesPresent();
    }
    
    public bool TryHeal(MAHeroBase _hero, float _multiplier)
    {
        if(m_lastHeroHealed == null)
        {
            HealHero(_hero, _multiplier);
            m_lastHeroHealed = _hero;
            return true;
        }
        return false;
    }
    
    public static bool RequiresHealing(MACharacterBase _hero)
    {
        return _hero.NormalizedHealth < 1f;
    }
    
    public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        m_lastHeroHealed = null;
        base.Activate(_indexOfComponentType, _quantityInBuilding);
    }
    
    public override void UpdateInternal(BuildingComponentsState _state)
    {
        
    }

    public override void LateUpdateInternal()
    {
        base.LateUpdateInternal();
        SetCharacterVisualsEnabled(m_lastHeroHealed);
        m_lastHeroHealed = null;
    }

    private void HealHero(MACharacterBase _hero, float _multiplier)
    {
        _hero.RecoverHealthImmediate(m_heroHealingPerSecond * _multiplier);
    }
    
    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (m_info?.id, () => new BCHealingPanel(m_info));
    
    public class BCHealingPanel : BCUIPanel
    {
        protected BCGuildHealing m_healingCmp;
        private string m_blockID;
        public override string SpriteBlockID => m_blockID;
        public override string GetPrimaryText() =>  m_healingCmp?.m_info?.m_description;

        public BCHealingPanel(MAComponentInfo _info) : base(_info) { }

        public override void AddComponent(BCBase _component)
        {
            if(_component is BCGuildHealing)
            {
                m_blockID = _component.Block.BlockID;
                m_healingCmp = _component as BCGuildHealing;
                base.AddComponent(_component);
            }
        }
    }
}
