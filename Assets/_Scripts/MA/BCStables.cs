using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class BCStables : BCBase
{
/*
    [Save]private List<int> m_vehicleIds = new ();
    
    private int c_maxVehiclesPerStables = 1;
    private float m_parkingSpaceOffset = 0.4f;
    
    private Transform m_roadEntrance;
    private Transform m_parkingSpace;

    public MADeliveryCart AssignedVehicle => NGManager.Me.MAVehicles.Find(x => x.AssignedBuilding == m_building) as MADeliveryCart;
   
    public MADeliveryCart AvailableCart => AssignedVehicle; //TODO: use parked/available/empty

    public void OnVehicleRemoved(MAVehicle _vehicle)
    {
        m_vehicleIds.Remove(_vehicle.m_ID);
    }
    
    protected override void Awake()
    {
        base.Awake();
        
        if(m_roadEntrance == null)
        {
            string doorPath = "Interacts/Door1";
            m_roadEntrance = FindInteract(doorPath);;
        }
        
        if(m_parkingSpace == null)
        {          
            string parkingSpacePath = "Interacts/Door2";
            m_parkingSpace = FindInteract(parkingSpacePath);
        }
    }

    public override void UpdateInternal()
    {
        TrySpawnDeliverCart(OnSpawn);
        
        base.UpdateInternal();
    }

    protected override void PostLoad()
    {
        base.PostLoad();
        
        // Fail safe incase a cart isn't properly deallocated
        for(int i = m_vehicleIds.Count-1; i >= 0; i--)
        {
            if(GameManager.Me.m_state.m_vehicles.Find(x => x.m_id == m_vehicleIds[i]) == null)
            {
                m_vehicleIds.RemoveAt(i);
            }
        }
    }

    public void DebugSpawnCart()
    {
        MAVehicleControl.Me.SpawnNewDeliveryCart();
    }

    override public Vector3 GetDoorPos()
    {
        return m_building.Nav.GetPathPosAtIndex(0, m_parkingSpaceOffset);
    }

    private void TrySpawnDeliverCart(Action<MAVehicle> _onSpawn)
    {
        if(MAUnlocks.Me.m_unlockDeliveryCart == false) return;
        
        if(m_vehicleIds.Count >= c_maxVehiclesPerStables) return;

        MAVehicleControl.Me.SpawnNewDeliveryCart();
    }

    private Transform FindInteract(string _path)
    {  
        var doorTransform = transform.Find(_path);
        if (doorTransform)
        {
            return doorTransform;
        }
        
        //Debug.LogError($"{GetType().Name} - Warning - could not find transform {_path}. using standard gameObject transform as interact pos - possible undesired effect");
        return transform;
    }

    private void OnSpawn(MAVehicle _maVehicle)
    {
        if (_maVehicle == null) return;
        m_vehicleIds.Add(_maVehicle.m_ID);
        _maVehicle.Home = this;
    }*/
}