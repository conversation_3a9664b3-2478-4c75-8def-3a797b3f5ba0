using UnityEngine;

public class MAAttackComboState
{
	MACharacterBase m_owner;
	MAAttackCombo m_combo;
	int m_currentAttackIdx = -1;
	bool m_nextAttackQueued;
	bool m_branchingAttackQueued;
	bool m_playNextAttackAnimQueued;
	bool m_comboBreakQueued;
	float m_lastStateChangeTime;
	int m_lastAttackExecuted;

	public MAAttackInstance GetCurrentAttack()
	{
		return GetAttackInstanceFromIndex(m_currentAttackIdx);
	}

	public MAAttackInstance GetAttackInstanceFromIndex(int _index)
	{
		MAAttackSkill attackSkill = GetAttackFromIndex(_index);
		if (attackSkill != null)
		{
			return new MAAttackInstance(attackSkill, m_owner, _index);
		}
		return null;
	}

	public MAAttackSkill GetAttackFromIndex(int index)
	{
		if (index >= 0 && index < m_combo.AttackSkills.Count)
		{
			return m_combo.AttackSkills[index];
		}
		return null;
	}

	public MAAttackInstance GetNextAttack()
	{
		int skills = m_combo.AttackSkills.Count;
		if (skills <= 0)
			return null;
		
		int nextIndex = m_currentAttackIdx + 1;
		if (nextIndex >= skills)
			nextIndex = 0;

		return GetAttackInstanceFromIndex(nextIndex);
	}

	public MAAttackInstance ComboEndingAttack()
	{
		if (m_lastAttackExecuted < 0)
		{
			return null;
		}
		
		return GetAttackInstanceFromIndex(m_lastAttackExecuted);
	}

	public bool IsInCooldown()
	{
		return m_currentAttackIdx >= m_combo.AttackSkills.Count;
	}

	public bool IsReady()
	{
		return m_currentAttackIdx < 0;
	}

	public MAAttackComboState(MACharacterBase _owner, MAAttackCombo _combo)
	{
		m_owner = _owner;
		m_combo = _combo;
	}

	void BeginNextAttack()
	{
		m_currentAttackIdx++;
		// RW-03-FEB-25: If the next attack is a branch, but the user hasn't queued for a branching attack, skip it.
		if (m_combo.AttackSkills[m_currentAttackIdx].IsBranch && !m_branchingAttackQueued)
		{
			m_currentAttackIdx++;
		}
		m_nextAttackQueued = m_branchingAttackQueued = false;
		m_lastStateChangeTime = Time.time;
	}

	// RW-09-JAN-25: Note that you don't usually want to be calling this directly from outside this class. I've only
	// made it public for a very specific case. Usually, you want to be calling QueueComboBreak and letting the next call 
	// to CheckNewState process the change.
	public void BreakCombo()
	{
		if (m_currentAttackIdx >= 0 && m_currentAttackIdx < m_combo.AttackSkills.Count)
		{
			m_lastAttackExecuted = m_currentAttackIdx;
		}
		m_currentAttackIdx = m_combo.AttackSkills.Count;
		m_nextAttackQueued = m_branchingAttackQueued = false;
		m_lastStateChangeTime = Time.time;
	}

	public void QueueComboBreak(bool _shouldPlayRecoveryAnim=true)
	{
		if (!IsInCooldown())
		{
			m_comboBreakQueued = true;
		}

		// RW-04-MAR-25: Most combo breaks will warrant that the character plays the recovery anim 
		// for the last attack afterwards (if the player's dropped the combo, for example). However, if
		// the character's been interrupted and is now playing a different anim, this shouldn't happen.
		if (!_shouldPlayRecoveryAnim)
		{
			m_currentAttackIdx = m_combo.AttackSkills.Count;
			m_lastAttackExecuted = -1;
		}
	}

	public void ResetCombo()
	{
		m_currentAttackIdx = -1;
		m_nextAttackQueued = m_branchingAttackQueued = false;
		m_lastStateChangeTime = Time.time;
	}

	public void AttackCompleted(bool _interrupted)
	{
		MAAttackSkill currentAttack = GetAttackFromIndex(m_currentAttackIdx);
		// RW-04-MAR-25: This would be the case if something else has requested a combo break and it's 
		// already been processed. e.g. if ragdolled, the combo gets reset immediately and then the 
		// anim finished callback will fire later.
		if (currentAttack == null)
		{
			return;
		}

		// We've just finished the last attack of the combo. Go to cooldown.
		if (m_currentAttackIdx+1 >= m_combo.AttackSkills.Count)
		{
			QueueComboBreak();
			return;
		}
		// If we've just completed a branching attack, that ends the combo.
		else if (currentAttack.IsBranch)
		{
			QueueComboBreak();
			return;
		}

		MAAttackSkill nextAttack = m_combo.AttackSkills[m_currentAttackIdx+1];
		// If the next attack is a branching attack and the last attack of the combo,
		// but we haven't queued a branch attack, end the combo.
		bool nextAttackIsLast = m_currentAttackIdx+2 >= m_combo.AttackSkills.Count;
		if (nextAttack.IsBranch && nextAttackIsLast && !m_branchingAttackQueued)
		{
			QueueComboBreak();
			return;
		}
		else if ((m_nextAttackQueued || (nextAttack.IsBranch && m_branchingAttackQueued)) && !_interrupted)
		{
			m_playNextAttackAnimQueued = true;
		}
		// The player's dropped the combo - go to cooldown.
		else
		{
			QueueComboBreak();
		}
	}

	public bool CheckNewState()
	{
		if (m_comboBreakQueued)
		{
			BreakCombo();
			m_comboBreakQueued = false;
			return true;
		}
		// We're not attacking and are ready to attack.
		else if (m_currentAttackIdx < 0)
		{
			if (m_nextAttackQueued)
			{
				BeginNextAttack();
				return true;
			}
		}
		// We're on cooldown.
		else if (m_currentAttackIdx >= m_combo.AttackSkills.Count)
		{
			if (Time.time-m_lastStateChangeTime >= m_combo.Cooldown)
			{
				ResetCombo();
				return true;
			}
		}
		// We're going to play the next attack anim
		else if (m_playNextAttackAnimQueued)
		{
			BeginNextAttack();
			m_playNextAttackAnimQueued = false;
			return true;
		}
		return false;
	}

	public bool QueueNextAttack(bool _useBranch)
	{
		// We can't queue an attack while on cooldown.
		if (m_currentAttackIdx < m_combo.AttackSkills.Count)
		{
			m_nextAttackQueued = !_useBranch;
			m_branchingAttackQueued = _useBranch;
			return true;
		}

		return false;
	}

	public string GetComboID()
	{
		return m_combo.ID;
	}
}
