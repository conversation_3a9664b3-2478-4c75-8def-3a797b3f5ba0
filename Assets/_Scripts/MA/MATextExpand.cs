using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
[ExecuteInEditMode]
public class MATextExpand : MonoBehaviour
{
    public bool m_expandWidth = true;
    public bool m_expandHeight = true;
    private TMP_Text m_textMeshPro;
    private RectTransform m_rectTransform;
    void Awake()
    {
        m_textMeshPro = GetComponent<TMP_Text>();
        m_rectTransform = GetComponent<RectTransform>();
    }

    private void Update()
    {
        if(m_textMeshPro == null) return;
        Vector2 preferedSize = m_textMeshPro.GetPreferredValues();
        if(m_expandWidth)
            m_rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, preferedSize.x);
        if(m_expandHeight)
            m_rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, preferedSize.y);
    }
}
