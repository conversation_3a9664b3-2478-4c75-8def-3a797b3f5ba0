using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
using System.Text.RegularExpressions;
public class MATag : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointer<PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerEnterH<PERSON><PERSON>, IPointerExitHandler
{
    [SerializeField]
    private Transform m_panel = null;
    
    [SerializeField]
    private Image m_tagIcon = null;
    
    [SerializeField]
    private TextMeshProUGUI m_tagCount = null;    
    
    [SerializeField]
    private string m_triggerCloseAnimation = "close", m_triggerWiggleAnimation = "isupdated";

    [SerializeField]
    private Image m_slicedBar = null;
    
    [SerializeField]
    private Color m_healthFull = new Color(Color.green.r, Color.green.g, Color.green.b, 0.7f),
        m_healthEmpty = new Color(Color.red.r, Color.red.g, Color.red.b, 0.7f);
    
    [SerializeField]
    private GameObject m_stateIcon = null;
    
    [SerializeField]
    private float m_normHealthLowStartFlash = 0.2f, m_flashFrequency = 1f;
    
    [SerializeField] 
    private AnimationCurve m_flashCurve;
    
    public TagInfo m_tagInfo = null;

    private (float hue, float sat, float val, float alpha) m_maxHSV;
    private (float hue, float sat, float val, float alpha) m_minHSV;
    
    private Animator m_animator = null;
    
    private Queue<Transform> m_tagTargetQueue = new Queue<Transform>();
    
    private MACharacterBase m_character = null;
    
    private bool m_isClosing = false;
    private bool m_consciousBefore = true;
    private float m_flashTime = 1f;
    private float m_normHealthBefore = -1;

    private string TagType => m_tagInfo?.m_tagType ?? "";
    private string Icon => m_tagInfo?.m_icon ?? "";
    private string RightClickInfoPanelUI => m_tagInfo?.m_rightClickInfoPanelUI ?? "";
    private bool DisplayHealth => m_tagInfo?.m_displayHealthBar ?? false;
    private bool DisplayKO => m_tagInfo?.m_displayKO ?? false;
    private bool Individualised => m_tagInfo?.m_individualised ?? false;
    private bool m_isPointerOver;
    private float m_triggerTime;
    private MAPopup m_popup;
    
    public Image TagIcon => m_tagIcon;
    
    private Transform LastFocussedObject => m_tagTargetQueue.Peek();
    [System.Serializable]
    public class TagInfo
    {
        public string m_tagType = "";
        public string m_icon = ""; 
        public Transform[] m_targetObjects = null;
        public string m_rightClickInfoPanelUI;
        public bool m_individualised = false;
        public bool m_displayHealthBar = false;
        public bool m_displayKO = false;
    }
    
    private void Awake()
    {
        m_animator = GetComponent<Animator>();
        gameObject.SetActive(false);
        EnableText(false);
        m_slicedBar.gameObject.SetActive(false);
        m_stateIcon.SetActive(false);      
        
        Color.RGBToHSV(m_healthFull, out m_maxHSV.hue, out m_maxHSV.sat, out m_maxHSV.val);
        m_maxHSV.alpha = m_healthFull.a;
        Color.RGBToHSV(m_healthEmpty, out m_minHSV.hue, out m_minHSV.sat, out m_minHSV.val);
        m_minHSV.alpha = m_healthEmpty.a;
    }

    private void Update()
    {
        UpdatePopup();
        UpdateHealth();
        UpdateCharacterStateIcon();
    }
    
    private void UpdatePopup()
    {
        if (m_isPointerOver)
        {
            if (Time.time >= m_triggerTime)
            {
                ShowPopup(true);
            }
        }
    }
    public bool IsOnScreen()
    {
        return m_tagTargetQueue != null && m_tagTargetQueue.Count > 0;
    }
    
    public void Init(TagInfo _tagInfo)
    {
        if (_tagInfo.m_targetObjects == null || _tagInfo.m_targetObjects.Length == 0)
        {
            m_tagTargetQueue.Clear();
            if (m_isClosing == false)
            {
                m_isClosing = true;
                m_animator.SetTrigger(m_triggerCloseAnimation);
            }
            return;
        }
        
        gameObject.SetActive(true);
        m_isClosing = false;
        
        if(Icon != _tagInfo.m_icon || m_tagIcon.sprite == null)
        {
            m_tagIcon.sprite = Resources.Load<Sprite>(_tagInfo.m_icon);
        }

        int count = _tagInfo.m_targetObjects.Length;
        if (count > 0 && _tagInfo.m_individualised == false)
        {
            EnableText(true, count.ToString());
        }
        else EnableText(false);

        Queue<Transform> newQueue = new Queue<Transform>();
        foreach (Transform oldTargetTransform in m_tagTargetQueue)
        {
            if (oldTargetTransform != null && _tagInfo.m_targetObjects.Contains(oldTargetTransform)) //carefully filter out nulls and chars not on new list anymore
            {
                newQueue.Enqueue(oldTargetTransform);
            }
        }
        
        bool newTargetFound = false;
        foreach (Transform newTargetTransform in _tagInfo.m_targetObjects)
        {
            if(newQueue.Contains(newTargetTransform) == false)
            {
                newTargetFound = true;
                newQueue.Enqueue(newTargetTransform);
            }
        }

        if (newTargetFound || newQueue.Count != m_tagTargetQueue.Count)
        {
            m_animator.SetTrigger(m_triggerWiggleAnimation);
        }

        m_tagTargetQueue = newQueue;

        m_character = newQueue.Count == 0 ? null : newQueue?.Peek()?.GetComponent<MACharacterBase>();
#if UNITY_EDITOR
        if (m_character != null && m_flashCurve == null)
        {
            Debug.LogError($"{GetType()} - flashCure is null for character {m_character.name}");
        }
#endif

        m_slicedBar.gameObject.SetActive(UpdateHealth(true));
        UpdateCharacterStateIcon();
        
        m_tagInfo = _tagInfo;
    }

    private void UpdateCharacterStateIcon()
    {        
        if (DisplayKO == false || m_character == null) return;
        m_stateIcon.SetActive(m_character.IsUnconscious() != MACharacterBase.Consciousness.Conscious);
    }

    private bool UpdateHealth(bool _init = false)
    {
        if (DisplayHealth == false) return false;
        if (m_character == null) return false;

        bool isDead = m_character.IsUnconscious() is MACharacterBase.Consciousness.UnconsciousDead or MACharacterBase.Consciousness.Dead;

        float normHealth = m_character.NormalizedHealth;
        if (isDead == false)
        {
            Color colBar = m_slicedBar.color;
            float newAlpha = m_minHSV.alpha + (m_maxHSV.alpha - m_minHSV.alpha) * normHealth;
            
            if (_init || (Mathf.Approximately(m_normHealthBefore, normHealth) == false && 
                Mathf.Approximately(m_slicedBar.fillAmount, normHealth) == false))
            {
                m_slicedBar.fillAmount = normHealth;
                float newHue = m_minHSV.hue + (m_maxHSV.hue - m_minHSV.hue) * normHealth;
                float newSat = m_minHSV.sat + (m_maxHSV.sat - m_minHSV.sat) * normHealth;
                float newVal = m_minHSV.val + (m_maxHSV.val - m_minHSV.val) * normHealth;
                colBar = Color.HSVToRGB(newHue, newSat, newVal);
            }

            Color colIcon = m_tagIcon.color;
            if (normHealth <= m_character.NormReincarnationHealth)
            {
                float val = m_flashCurve.Evaluate(m_flashTime);
                colBar.a = val;
                colIcon.a = val;

                m_flashTime -= Time.deltaTime;
                m_flashTime = m_flashTime < 0f ? m_flashFrequency : m_flashTime;
            }
            else
            {
                colBar.a = newAlpha;
                colIcon.a = 1f;
                m_flashTime = 1f;
            }

            m_slicedBar.color = colBar;
            m_tagIcon.color = colIcon;
            m_consciousBefore = true;
        }
        else if (m_consciousBefore)
        {
            m_slicedBar.fillAmount = 1f;
            Color c = Color.HSVToRGB(m_minHSV.hue, m_minHSV.sat, m_minHSV.val);
            c.a = m_minHSV.alpha;
            m_slicedBar.color = c;
            m_consciousBefore = false;
        }
        m_normHealthBefore = normHealth;
        return true;
    }

    public void EnableText(bool _enable, string _text = "")
    {
        m_tagCount.gameObject.SetActive(_enable);
        m_tagCount.text = _text;
    }

    public void OnPointerClick(PointerEventData eventData)
    {
        if (m_tagTargetQueue.Count == 0) return;
        if (eventData.button == PointerEventData.InputButton.Left || eventData.button == PointerEventData.InputButton.Right)
        {
            Transform focussedObject = m_tagTargetQueue.Dequeue();
            if (focussedObject == null)
            {
                m_character = null;
            }
            else
            {
                m_tagTargetQueue.Enqueue(focussedObject);

                //if open -> switch to this target

                GameManager.Me.CameraTransition(
                    focussedObject.transform.position,
                    MATagManager.Me.CameraTransitionTime,
                    MATagManager.Me.CameraDistanceFromFocussedObject,
                    OnCameraTransitionComplete);

                if (RightClickInfoPanelUI.IsNullOrWhiteSpace() == false)
                {
                    if (eventData.button == PointerEventData.InputButton.Right)
                    {
                        UIManager.Me.m_centralInfoPanelManager.ToggleInfoPanel(RightClickInfoPanelUI, focussedObject);
                    }
                    else
                    {
                        UIManager.Me.m_centralInfoPanelManager.UpdateInfoPanelIfOpen(RightClickInfoPanelUI,
                            focussedObject);
                    }
                }

                m_character = focussedObject.GetComponent<MACharacterBase>();
#if UNITY_EDITOR
                if (m_character != null && m_flashCurve == null)
                {
                    Debug.LogError($"{GetType()} - flashCure is null for character {m_character.name}");
                }
#endif
                
                if(eventData.button == PointerEventData.InputButton.Right && m_character != null)
                {
                    MACharacterInfoUI.Create(m_character);
                }
                
                m_slicedBar.gameObject.SetActive(UpdateHealth());
                UpdateCharacterStateIcon();
            }
        }
    }

    private void OnCameraTransitionComplete()
    {
        //Debug.Log($"{GetType().Name} - {name} - finished camera transition");
    }
    
    public void OnCloseAnimFinished() //callback from animation event ('close' anim)
    {
        gameObject.SetActive(false);
        m_slicedBar.gameObject.SetActive(false);
        m_stateIcon.SetActive(false);
        EnableText(false);
    }
    public void OnPointerEnter(PointerEventData eventData)
    {
        var info = GetPopupInfo();
        if (info != null)
        {
            m_isPointerOver = true;
            m_triggerTime = Time.time + info.m_delayBeforePopup;         
        }
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        m_isPointerOver = false;
        ShowPopup(false);
    }
    public MAPopupHelperInfo GetPopupInfo()
    {
        var actualType =new string(m_tagInfo.m_tagType.Where(c => !char.IsDigit(c)).ToArray());
        var whatInfo = $"Tag:{actualType}";
        return MAPopupHelperInfo.GetInfo(whatInfo);
    }
    public void ShowPopup(bool show)
    {
        if (show)
        {
            if (m_popup != null) return;
            var popupInfo = GetPopupInfo();
            if (popupInfo == null) return;
            m_popup = MAPopup.Create(popupInfo.m_popupText, transform); 
        }
        else
        {
            if (m_popup != null)
            {
                m_popup.DestroyMe();
                m_popup = null;
            }
        }
    }
}
