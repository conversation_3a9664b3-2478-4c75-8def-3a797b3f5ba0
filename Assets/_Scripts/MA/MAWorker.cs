using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Random = UnityEngine.Random;

#if UNITY_EDITOR
using UnityEditor;
using System.Linq;
#endif

public class MAWorker : MACharacterBase
{
    public virtual bool CanAssignHome => true;
    public virtual bool CanAssignJob => true;
    public override Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary() { return MAWorkerStateLibrary.StateLibrary; }
    public override EDeathType DeathCountType => EDeathType.Worker;

    protected STATE m_initialWorkerState; 
    public STATE InitialWorkerState => m_initialWorkerState;
    
    public override string DefaultState
    {
        get => string.IsNullOrWhiteSpace(m_gameState?.m_defaultState) ? CharacterStates.WorkerState : m_gameState.m_defaultState;
        set => m_gameState.m_defaultState = value;
    }
    
    [Header("MAWorker")]
    public MAWorkerInfo m_workerInfo;

    public float m_energyRestThreshold;
    public float m_energyWorkThreshold;
    public float m_timeInState;
    public float m_visionRadius = 15;
    public float m_runMultiplier = 2f;

    //[Header("Follow Behaviour Settings")]
    //[SerializeField]
    protected FollowBehaviour m_followBehaviourSettings = new();
    // public float m_petrifiedVisionRadiusMultiplier = 0.5f;
   // public float m_agentStuckForceThroughDuration = 2.5f;
   
   // Design at time of writing: 25% through the night they go home
   public override bool IsTimeToHangout => DayNight.Me.m_timeStage >= DayNight.c_timeStageDayEnd && DayNight.Me.m_timeStage <= DayNight.c_timeStageDuskEnd+.5f;
   public override bool IsTimeToGoHome => DayNight.Me.IsSleepTimeForWorker() || IsLowEnergy();
   public override float ProductionPower => m_workerInfo.m_productionPower;
   public override float MaxEnergy => m_workerInfo.m_energy;
   public override bool IgnoreCharacterSize => true;
   public override KeyboardShortcutManager.EShortcutType KeyboardShortcut => KeyboardShortcutManager.EShortcutType.DragWorker;
   
   public override string GetCharacterLevelName() => m_workerInfo.m_level.ToString();
   
    public override string GetDefaultDisplayName()
    {
        if(m_workerInfo == null) return "";
        
        if(Gender == MAWorkerInfo.WorkerGender.Male)
            return RandomNameGenerator.GenerateRandomMaleName(m_ID);
        return RandomNameGenerator.GenerateRandomFemaleName(m_ID);
    }
    
    public override bool IsInAttackState => false;

    public override bool IsInMovementState => m_state == STATE.MA_MOVE_TO_BUILDING || m_state == STATE.MA_MOVE_TO_OBJECT ||
                                              m_state == STATE.MA_MOVE_TO_INSIDE_BUILDING || m_state == STATE.MA_CHASE_OBJECT ||
                                              m_state == STATE.MA_MOVE_TO_OUTSIDE_BUILDING ||
                                              m_state == STATE.MA_MOVE_TO_POSITION || IsFleeing;
    
    public override bool IsFleeing => PeepAction == PeepActions.Flee;
    public override bool IsInCombat => m_state == STATE.MA_PETRIFIED;

    public bool IsInChopObjectState => m_state == STATE.MA_CHOP_OBJECT;
    public bool IsInHidingState => m_state == STATE.HIDING;
    public bool IsInPrisonerState => m_state == STATE.MA_IN_PILLORY_STOCKS;
    public bool IsIdle => m_state == STATE.IDLE;
    public bool IsDecideWhatToDo => m_state == STATE.MA_DECIDE_WHAT_TO_DO;

		const float c_DoorProximityPossesssionThreshold = 3f;
		override public bool CanEnterPossessionMode
		{
			get
			{
				if (!base.CanEnterPossessionMode)
				{
					return false;
				}

				// RW-16-SEP-25: If the character is a worker and they're very near a door, don't allow possession.
				// This is because there's a lot of sensitivity at this time.
				Vector3 pos = m_transform.position;
				foreach (var b in NGManager.Me.m_maBuildings)
				{
					if ((b.DoorPosInner-pos).xzSqrMagnitude() <= c_DoorProximityPossesssionThreshold*c_DoorProximityPossesssionThreshold)
					{
						return false;
					}
				}

				return true;
			}
		}

    public override float GetBezierDistanceOverride(SpecialHandlingAction _action) => NGManager.Me.m_bezierLineWorkerRadius;
    
    override public string HumanoidType => Gender == MAWorkerInfo.WorkerGender.Male ? "HumanMale" : "HumanFemale";

    public GameState_Person GameStatePerson => m_gameState as GameState_Person;
    public override float EnergyRestThreshold { get => m_energyRestThreshold; set => m_energyRestThreshold = value; }
    public override bool CanBeTargetted { get => GameStatePerson.m_canBeTargeted; set { GameStatePerson.m_canBeTargeted = value; } }

    public override float EnergyWorkThreshold { get => m_energyWorkThreshold; set => m_energyWorkThreshold = value; }
    public override bool WaitingForPay { get => GameStatePerson.m_waitingForPay; set => GameStatePerson.m_waitingForPay = value; }
    public override PeepActions PeepAction
    {
        get => (PeepActions)GameStatePerson.m_peepAction;
        set
        {
            switch((PeepActions)GameStatePerson.m_peepAction)
            {
                case PeepActions.Chop:
                    ResetChopState();
                    break;
            }
            GameStatePerson.m_peepAction = (int)value;
        }
    }
    protected Transform m_visuals = null;
    
    private SkinnedMeshRenderer m_renderer;
    public STATE BackUpMovementState => (STATE)m_gameState.m_backupMovementState;
    
    private float m_fleeToPosTimer = -1f;
    private float m_fleeToPosTimeDuration = 5f;
    private float m_lookForTimer;
    private bool m_continuousCollisionDetectionMode = false;

   // [Serializable]
    public class FollowBehaviour
    {
        public float m_followPosNextUpdateTime = -1f;
        public float m_followPosUpdateDelay = 0.2f;
        public float m_followStepBackAmount = 3f;
        public float m_followAheadTarget = 5f;
        public float m_followRightTarget = 3.5f;
        public float m_followMaxDist = 3.5f;
    }

    private float m_tauntPrisonerCooldown = 0.0f;
    private float m_tauntPrisonerDuration = 8.0f;
    
    protected Transform m_balloonHolder = null;
    protected Balloon m_balloon = null;
    
    private enum ChopObjectPhase
    {
        None = 0,
        Intro,
        Loop,
        Wait,
        Outro
    }
    
    private ChopObjectPhase m_chopObjectPhase = ChopObjectPhase.None;
    private ChopObjectPhase m_newChopObjectPhase = ChopObjectPhase.None;
    private bool m_chopObjectPhaseNeedsReset = false;

    private IEnumerator coHangOut = null;

    public override bool IsIncapacitated => m_state == STATE.MA_DEAD || Health <= 0f;
    public override bool IsDisappearing => (m_state == STATE.MA_DEAD && m_deadTimer <= 0f) || m_state == STATE.MA_WAITING_TO_TELEPORT;

    public override string GetTypeInfo()
    {
        return m_workerInfo.m_name;
    }

    public override string GetStateInfo()
    {
        return $"State[{m_state}] Action[{PeepAction}] Inside[{(m_insideMABuilding != null ? m_insideMABuilding.name : "")}] DestPos[{DestinationPosition}]";
    }

#if UNITY_EDITOR
		public override List<string> GetStateNames()
		{
			var l1 = Enum.GetNames(typeof(STATE)).ToList();
			//var l2 = Enum.GetNames(typeof(PeepActions)).ToList();
			//l1.AddRange(l2);
			return l1;
		}
#endif //UNITY_EDITOR

    protected override void Awake()
    {       
        base.Awake();
		
        if (m_rigidBody == null)
            m_rigidBody = GetComponent<Rigidbody>();

        m_rigidBody.useGravity = false;
    }
    
    protected override void Start()
    {
        base.Start();
        SetWorkerName();
        SetGenderSoundSwitch();
        AudioClipManager.Me.SetSoundset(gameObject, m_ID % 15);
        m_renderer = GetComponentInChildren<SkinnedMeshRenderer>();
    }

    protected override void InitialiseGameState()
    {
        GameState_Person personState = CreateNewGameState();
        SetGameStateSaveData(personState);
        personState.m_type = GetType().Name;
        personState.m_creatureInfoName = "";
        personState.m_id = m_ID;
        personState.m_defaultState = CharacterStates.WorkerState;
        personState.m_workerInfo = m_workerInfo.m_name;
        personState.m_isMale = m_workerInfo.IsMale;
        personState.m_health = m_workerInfo.m_health;
        personState.m_energy = m_workerInfo.m_energy;
        personState.m_waitingForPay = false;
        personState.m_walkSpeed = m_workerInfo.GetRandomWorkerSpeed();
        personState.m_attackSpeed = personState.m_walkSpeed;
        personState.m_characterExperience = new CharacterExperience();
        GameManager.Me.m_state.m_people.AddUnique(personState);
        
        m_energyWorkThreshold = NGManager.Me.m_energyWorkThreshhold;
        m_energyRestThreshold = NGManager.Me.m_energyRestThreshhold;
    }
    
    public override void Activate(MACreatureInfo _creatureInfo, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation, bool _addToCharacterLists = true)
    {
        //m_rigidBody.isKinematic = true;
        base.Activate(_creatureInfo, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);
        
        GameState_Person gameState = GameStatePerson;
        m_newChopObjectPhase = (ChopObjectPhase)gameState.m_chopObjectPhase;
        AudioClipManager.Me.SetSoundSwitch("VO_Category", gameState.m_isMale ? "Male" : "Female", gameObject);
    }
    
    override protected void Update()
    {
        if (m_debugBreakHere)
        {
            Debug.DebugBreak();
        }
        base.Update();
        UpdateVisuals();
    }
    
    protected void UpdateVisuals()
    {
        return; // GL - 121224 - removing this on discussion with Mark
        if (m_renderer != null)
        {
            // TODO - put the NGManager references back in when Peter commits them
            var brightnessMultiplier = 1.0f;//NGManager.Me.m_workerBrightnessDay;
            var darkness = DayNight.Me.Darkness();
            if (darkness > .5f)//NGManager.Me.m_workerBrightnessThreshold)
                brightnessMultiplier = 4.0f;//NGManager.Me.m_workerBrightnessNight;
            // Update brightness (adjust brightnessMultiplier as needed)
            m_renderer.material.SetFloat("_Brightness", brightnessMultiplier);
        }
    }
    
    public override void DestroyMe()
    {
        ResetChopState();
        GameManager.Me.m_state.m_people.Remove(GameStatePerson);
        base.DestroyMe();
    }

    public override void UpdateGameState()
    {
        if(m_state == STATE.MA_LOADING) return;
        
        base.UpdateGameState();
        
        GameState_Person gameState = GameState as GameState_Person; 
        if (gameState == null)
		{
			Debug.LogError($"{GetType().Name} - UpdateGameState - gameState save data == null");
			return;
		}

        gameState.m_state = (int)m_state;

        if(m_cleaningPickup != null) //TODO: TS - it's possible that pickups haven't loaded yet => dont set targetObj to null if ref is null
        {
            ReactPickupPersistent pickup = (ReactPickupPersistent)m_cleaningPickup;
            gameState.m_targetObject = "";
        }

        if(m_destinationMABuilding)
            gameState.m_destBuilding = m_destinationMABuilding.m_linkUID;
        else gameState.m_destBuilding = 0;
        if(m_insideMABuilding)
            gameState.m_inside = m_insideMABuilding.m_linkUID;
        else gameState.m_inside = 0;
        //gameState.m_destPos = DestinationPosition;
        //gameState.m_peepAction = (int)m_peepAction;
        gameState.m_carrying = String.Empty;
        if(Carrying)
        {
            gameState.m_carrying = Carrying.Contents.m_name;
            gameState.m_carryingQuantity = (int)Carrying.Quantity;
            gameState.m_carryingProductUID = (Carrying as NGReactPickupAny)?.m_product?.m_uniqueID ?? "";
            gameState.m_carryingSpawnedFrom = -1;
            if(Carrying.m_spawnedFrom)
                gameState.m_carryingSpawnedFrom = Carrying.m_spawnedFrom.m_linkUID;
            else gameState.m_carryingSpawnedFrom = 0;
        }
    }
    override protected void UpdateState()
    {
        float timeInStatePrev = m_timeInState;
        m_timeInState += Time.deltaTime;
        
        switch (m_state)
        {
            case STATE.MA_CHOP_OBJECT:
                StateChopObject();
                break;
            case STATE.MA_WAITING_FOR_WORK:
                StateWaitingForWork();
                break;
            case STATE.MA_WAITING_FOR_HOME:
                StateWaitingForHome();
                break;
            case STATE.WORKING:
                StateWorking();
                break;
            case STATE.RESTING:
                StateResting(); 
                break;
            case STATE.MA_PETRIFIED:
                StatePetrified();
                break;
            case STATE.MA_WAITING_FOR_ANIMATION:
                break;
            case STATE.MA_HANGOUT:
                StateHangOut(timeInStatePrev);
                break;
            case STATE.MA_IN_PILLORY_STOCKS:
                StateInPilloryStocks();
                break;
            case STATE.MA_POSSESSED:
                StatePossessed();
                break;
            case STATE.MA_WAITING_TO_TELEPORT:
                StateWaitingToTeleport();
                break;
            case STATE.MA_FOLLOW_LEADER:
                CheckFollowLeader();
                break;
            default:
                base.UpdateState();
                break;
        }
    }
    
    public override bool CheckFollowLeader()
    {
        if (base.CheckFollowLeader() == false) return false;
        CheckThrowStoneAtThreat();
        return true;
    }

    public class ThrowStoneAtThreat
    {
        public enum EState { Wait, Check, Throw };
        private EState m_state = EState.Wait;
        private float m_stateTime = 2;
        private MACreatureBase m_throwTarget = null;

        const float c_threatRange = 15;
        static string c_pickupAndThrowStoneAnimation => NGManager.Me.m_pickAndThrowStoneAnimation;
        const float c_rockThrowXZSpeed = 20;
        static float c_rockThrowDamage = NGManager.Me.m_pickAndThrowRockDamage;
        static float c_rockThrowDelay => NGManager.Me.m_pickAndThrowRockDelay;
        public void Tick(MAWorker _this, float _dt)
        {
            switch (m_state)
            {
                case EState.Wait:
                    m_stateTime -= _dt;
                    if (m_stateTime <= 0)
                        m_state = EState.Check;
                    break;
                case EState.Check:
                    float bestD2 = c_threatRange * c_threatRange;
                    MACreatureBase bestCreature = null;
                    foreach (var c in NGManager.Me.m_MACreatureList)
                    {
                        if (c == null || c.IsAnyDeadState || !c.IsEnemy)
                            continue;
                        if (_this.CheckFollowLeaderTargetViable(c.transform.position) == false)
                            continue;
                        var toChr = c.transform.position - _this.transform.position;
                        var d2 = toChr.xzSqrMagnitude();
                        if (d2 < bestD2)
                        {
                            bestD2 = d2;
                            bestCreature = c;
                        }
                    }
                    if (bestCreature == null)
                    {
                        StartWait();
                    }
                    else
                    {
                        m_state = EState.Throw;
                        m_stateTime = 0.5f;
                        m_throwTarget = bestCreature;
                    }
                    break;
                case EState.Throw:
                    var label = $"ThrowRock_{Time.frameCount}";
                    _this.m_nav.PushPause(label, _freeze:true);
                    _this.transform.LookAt(m_throwTarget.transform.position.NewY(_this.transform.position.y), Vector3.up);
                    _this.PlaySingleAnimation(c_pickupAndThrowStoneAnimation, (_) => {
                        _this.m_nav.PopPause(label);
                    });
                    _this.DoAfter(c_rockThrowDelay, () => {
                        const string c_attachBone = "HandAttach_R";
                        var from = _this.transform.FindChildRecursiveByName(c_attachBone).position;
                        var to = m_throwTarget.GetHeadTransform().position;
                        var stone = Instantiate(NGManager.Me.m_pickAndThrowRockPrefab);
                        stone.transform.position = from;
                        var roughTime = (to - from).xzMagnitude() / c_rockThrowXZSpeed;
                        var targetRB = m_throwTarget.GetComponent<Rigidbody>();
                        if (targetRB != null)
                            to += targetRB.linearVelocity * roughTime;
                        var rb = stone.GetComponent<Rigidbody>();
                        rb.position = from;
                        rb.SetVelocityToMoveWithSpeed(to, c_rockThrowXZSpeed, (r) => {
                            Destroy(stone, .1f);
                            m_throwTarget.ApplyDamageEffect(IDamageReceiver.GetSourceFromCharacter(_this), c_rockThrowDamage, to);
                        });
                    });
                    StartWait();
                    break;
            }
        }

        private void StartWait()
        {
            m_state = EState.Wait;
            m_stateTime = Random.Range(1f, 3f);
        }
    }

    private ThrowStoneAtThreat m_throwStoneAtThreat = null;
    private void CheckThrowStoneAtThreat()
    {
        if (m_throwStoneAtThreat == null)
            m_throwStoneAtThreat = new ThrowStoneAtThreat();
        m_throwStoneAtThreat.Tick(this, Time.deltaTime);
    }

    override protected void CommonUpdateState()
    {
        base.CommonUpdateState();
    }	
    
    override protected void UpdateAliveOrDead()
    {
        AliveDuration += Time.deltaTime;
        if((m_state != STATE.MA_DEAD) && (m_state != STATE.HELD_BY_PLAYER) && (m_state != STATE.MA_WAITING_TO_TELEPORT) && (Health <= 0))
        {
            SetDead();
        }
    }
    
    public override bool SetMoveToBuilding(NGCommanderBase _destinationBuilding, PeepActions _action = PeepActions.None) // This should be the ONLY way the MA code should send workers to Buildings
    {
        if (base.SetMoveToBuilding(_destinationBuilding, _action))
        {
            SetDesiredSpeed();
        }
        return true;
    }

    public override bool SetMoveToPosition(Vector3 _position, bool _direct = false, PeepActions _action = PeepActions.None, float _destinationRadius = 0f) // This should be the ONLY way the MA code should send workers to position
    {
        m_destinationObject = null;
        m_destinationMABuilding = null;
        if (m_nav == null)
        {
            Debug.LogError($"{GetType().Name} - SetMoveToPosition m_agent == null with action {_action.ToString()} ");
            return false;
        }

        PeepAction = _action;
        
        bool isActive = gameObject.activeSelf;
        gameObject.SetActive(true);
        DestinationPosition = _position;
        if (m_insideMABuilding)
        {
            SetMovingObjectToLeaveBuilding(isActive);
            return true;
        }
        return NavigateToPosition(_position, false, 0f, _destinationRadius, false);
    }

	protected override void StateIdle()
	{
		SetDefaultAction();
	}

    protected void StateHangOut(float timeInStatePrev)
	{
        if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
            return;
        
		if (timeInStatePrev <= 0f)
		{
            m_gameState.m_wantsToPlay = 0f;
            Random.InitState(DateTime.Now.Millisecond);
            var lookAtRandom = Quaternion.Euler(0f, Random.Range(10f, 300f), 0f);
            LookAt(transform.position + (lookAtRandom * transform.forward * 5f));

			coHangOut = Co_HangOut();
            StartCoroutine(coHangOut);
		}

        if (IsTimeToHangout == false)
        {
			StopCurrentAnimation();
			if (coHangOut != null)
			{
				StopCoroutine(coHangOut);
				coHangOut = null;
			}
            
            SetDefaultAction();
        }
    }

    private IEnumerator Co_HangOut()
    {
        yield return new WaitForSeconds(Random.Range(1.5f, 3.5f));

        if (m_state != STATE.MA_HANGOUT)
            yield return null;

        if (IsTimeToHangout == false)
            yield return null;

        PlaySingleAnimation("WorkerDrinking", (interrupted) => {
            if (!interrupted)
            {
                coHangOut = Co_HangOut();
                StartCoroutine(coHangOut);
            }
        });
    }

	protected virtual void StatePetrified() 
    {
        MACreatureBase creature = UpdateTargetedByNearbyCreature() as MACreatureBase;
        if(creature == null && m_timeInState > CharacterSettings.m_petrifiedStateMinDuration)
        {
            if (BackUpMovementState != STATE.NONE)
            {
                SetState(BackUpMovementState);
                m_gameState.m_backupMovementState = (int)STATE.NONE;
            }
            else
            {
                SetDefaultAction();
            }
        }
    }

    protected void StateChopObject()
    {
        if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
            return;
        
        if((m_chopObjectPhase == ChopObjectPhase.Loop) || (m_chopObjectPhase == ChopObjectPhase.Wait))
        {
            // Only go to rest prematurely if ChopPhase is Loop or Wait
            if(IsTimeToHangout || IsTimeToGoHome)
            {
                SetDefaultAction();
                return;
            }
        }
        
        var tapMultiplier = GetSpeedMultiplier();
        SetDesiredSpeed();
        SetAnimationSpeed(tapMultiplier);
        
        if(m_destinationObject == null)
        {
            var go = TreeHolder.GetTreeObject(GameStatePerson.m_treeChopId);
            if (go != null)
            {
                Debug.LogError($"MAWorker - ChopObject - m_destinationObject == null, tree index '{GameStatePerson.m_treeChopId}'. go is {go}");
                m_destinationObject = go.transform;
            }
            else
            {
                SetDefaultAction();
                Debug.Log("MAWorker - ChopObject - m_destinationObject == null");
                return;
            }
        }

        var chopComponent = m_destinationObject.GetComponent<BCChopObject>();
        if(chopComponent == null)
        {
            if(TrySetupChopObjectState(m_destinationObject.gameObject) == false)
            {
                SetDefaultAction();
            }
            return;
        }
        
        if (m_chopObjectPhase != m_newChopObjectPhase)
        {
            m_chopObjectPhase = m_newChopObjectPhase;
            GameStatePerson.m_chopObjectPhase = (int)m_chopObjectPhase;
            SetupChopPhase(chopComponent);
        }
        else
        {
            RagdollController rc = gameObject.GetComponentInChildren<RagdollController>();
            if (rc != null)
            {
                if (!m_chopObjectPhaseNeedsReset && rc.IsRagdolled)
                {
                    m_chopObjectPhaseNeedsReset = true;
                }
                else if (m_chopObjectPhaseNeedsReset && rc.IsAnimated)
                {
                    m_newChopObjectPhase = (m_chopObjectPhase == ChopObjectPhase.Wait) ? ChopObjectPhase.Loop : m_chopObjectPhase;
                    m_chopObjectPhase = ChopObjectPhase.None;
                    m_chopObjectPhaseNeedsReset = false;
                }
            }
        }
    }

    private void SetupChopPhase(BCChopObject chopComponent)
    {
        if (m_chopObjectPhase == ChopObjectPhase.Intro)
        {
            if (!WorkplaceHasStockSpace())
            {
                ResetChopState();
                SetDefaultAction();
                return;
            }

            LookAt(m_destinationObject.position);

            var animIntro = chopComponent.GetChopIntroAnimation();
            if (animIntro != null)
            {
                PlaySingleAnimation(animIntro, (c) => { StartChopLoopPhase(); }, true, false, GetSpeedMultiplier());
            }
            else
            {
                StartChopLoopPhase();
            }
        }
        else if (m_chopObjectPhase == ChopObjectPhase.Loop)
        {
            LookAt(m_destinationObject.position);
            
            var animLoop = chopComponent.GetChopLoopAnimation();
            if (animLoop != null)
            {
                PlaySingleAnimation(animLoop, null, true, false, GetSpeedMultiplier());

                // Set both chopObjectPhase and newChopObjectPhase to ChopObjectPhase.Wait
                // to prevent ChopObjectPhase.Wait phase from baing saved, so the restored
                // phase is always ChopObjectPhase.Loop
                m_chopObjectPhase = ChopObjectPhase.Wait;
                m_newChopObjectPhase = ChopObjectPhase.Wait;
            }
            else
            {
                ApplyChoppingIteration();
            }
        }
        else if (m_chopObjectPhase == ChopObjectPhase.Outro)
        {
            var animOutro = chopComponent.GetChopOutroAnimation();
            if (animOutro != null)
            {
                PlaySingleAnimation(animOutro, (c) => { CollectChoppedResourse(); }, true, false, GetSpeedMultiplier());
            }
            else
            {
                CollectChoppedResourse();
            }
        }
    }

    private void StartChopLoopPhase()
    {
        m_newChopObjectPhase = ChopObjectPhase.Loop;
    }

    public void ApplyChoppingIteration()
    {
        if (m_destinationObject == null)
        {
            Debug.LogError("MAWorker - ChopObject - m_destinationObject == null");
            return;
        }
        
        if (!WorkplaceHasStockSpace() || m_destinationObject.GetComponent<BCChopObject>() == null)
        {
            StopWorkerLoopAnimation();
            ResetChopState();
            SetDefaultAction();
        }
    }

    public virtual MABuilding GetResourceDestination() => Job?.Building;

    public void ResourceHit()
    {
        if (m_destinationObject == null)
        {
            Debug.LogError("MAWorker - ChopObject - m_destinationObject == null");
            return;
        }
        
        var building = GetResourceDestination();
        if (building != null)
        {
            var chopComponent = m_destinationObject.GetComponent<BCChopObject>();
            if(chopComponent == null)
                return;
        
            var action = building?.GetActionGatherer(chopComponent.TreeHolder);
        
            float chopAmount = 0.1f;
            if(action != null)
            {
                float energyNeededForChop = Mathf.Min(chopComponent.ChopLeft, action.m_workerHarvestRate * building.GetWorkerSpeedMultiplier()) * action.m_energyRequiedToHarvest;
                
                float energyToUse = 0;
                if(MACharacterBase.c_useEnergy)
                {
                    energyToUse = Mathf.Min(Energy, energyNeededForChop);
                    ConsumePower(energyToUse);
                }
                else
                {
                    energyToUse = Mathf.Min(ProductionPower, energyNeededForChop);
                }
            
                chopAmount = energyToUse / action.m_energyRequiedToHarvest;
            }
            
            if(chopComponent.HitObject(chopAmount) || chopComponent.IsChoppedDown)
            {
                StopWorkerLoopAnimation();
                m_newChopObjectPhase = ChopObjectPhase.Outro;
            }
        }
    }
    
    private void CollectChoppedResourse()
    {
        ResetChopState();
        if (WorkplaceHasStockSpace() && GetResourceDestination() != null && m_destinationObject != null)
        {
            var chopComponent = m_destinationObject.GetComponent<BCChopObject>();
            if(chopComponent != null)
            {     
                var resource = chopComponent.GetCarriableResourceProduced();
                if(resource.IsNone == false)
                {
                    MABuilding building = GetResourceDestination();
                    var pickup = ReactPickupPersistent.CreateItem(building, resource, 1, building);
                    pickup.AssignToCarrier(this);
                    SetMoveToBuilding(building, PeepActions.DeliverToOutput);
                    return;
                }
            }
        }
        SetDefaultAction();
    }

    private void ResetChopState()
    {
        GameState_Person gameStatePerson = GameStatePerson;
        if(gameStatePerson.m_treeChopId > -1)
        {
            var go = TreeHolder.GetTreeObject(GameStatePerson.m_treeChopId);
            if(go != null)
            {
                var chopObject = go.GetComponent<BCChopObject>();
                if(chopObject != null) chopObject.m_worker = null;
            }
        }
        gameStatePerson.m_chopObjectPhase = 0;
        gameStatePerson.m_treeChopId = -1;
        m_chopObjectPhase = ChopObjectPhase.None;
        m_newChopObjectPhase = ChopObjectPhase.None;
        m_chopObjectPhaseNeedsReset = false;
    }

    private void StateInPilloryStocks()
    {
        if (m_tauntPrisonerCooldown > 0.0f)
        {
            m_tauntPrisonerCooldown -= Time.deltaTime;

            if (m_tauntPrisonerCooldown <= 0.0f)
            {
                PlaySingleAnimation("Worker_Stocks_Idle", null);
            }
        }

        if(NGManager.Me.IsWorkerUnderThreat(this))
        {
            TauntPrisoner();
        }
    }

    private void StatePossessed()
    {
        
    }

    private void StateWaitingToTeleport()
    {

    }

    public void TauntPrisoner()
    {
        if (m_tauntPrisonerCooldown <= 0.0f)
        {
            if (m_state == STATE.MA_IN_PILLORY_STOCKS)
            {
                PlaySingleAnimation("Worker_Stocks_Surprise", CB);

                void CB(bool _interrupted)
                {
                    PlaySingleAnimation("Worker_Stocks_Wriggle", null);
                }
            }
        }

        m_tauntPrisonerCooldown = m_tauntPrisonerDuration;
    }

    public override void FinishThrow()
    {
        if(Carrying == null)
        {
            SetDefaultAction();
        }
        else
        {
            //m_agent.SetNavigationPaused(true);
            //m_agent.LookAtPositionFromStatic(_throwDest, b => StartCoroutine(DelayedOnComplete(b)));
            Carrying.Thrown(GetThrowVelocityAccurate(DestinationPosition), m_destinationMABuilding?.transform,
                COLLISIONSTYLE.DEFAULT);
            MAReturnToJob();
        }
    }

    private static float s_debugSpeed = -1;
    private static DebugConsole.Command s_debugSpeedCmd = new ("workerspeed", _s => s_debugSpeed = floatinv.Parse(_s));
    public float GetSpeedMultiplier()
    {
        var multiplier = 1f;
        if(Job && Job.Building)
        {
            const float c_maxWorkerSpeedMultiplier = 5;
            multiplier = Mathf.Min(c_maxWorkerSpeedMultiplier, Job.Building.GetWorkerSpeedMultiplier());
            if (s_debugSpeed >= 0) multiplier = s_debugSpeed;
        }
        return multiplier;
    }
    
    public override float GetDesiredSpeed()
    {
        float runningFactor = PeepAction switch { PeepActions.Flee => m_runMultiplier, PeepActions.DespawnRun => m_runMultiplier, _ => 1f };
        float speedFactor = (1 + 0.0333f * Random.Range(-1f, 1f)) * runningFactor;
        return m_gameState.m_walkSpeed * speedFactor * GetSpeedMultiplier();
    }
    
    public void SetDesiredSpeed()
    {
        SetSpeed(GetDesiredSpeed());
    }
    
    override public void SetSpeed(float _speed)
    {
        m_nav.Speed = _speed;
		m_gameState.m_speed = _speed;
    }

    override protected bool StateMoveToBuilding()
    {
        if(HasAgentArrived() == false)
        {
            if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
                return false;
        }
            
        if(m_destinationMABuilding == null)
        {
            SetDefaultAction();
            return false;
        }
        
        SetDesiredSpeed();
        
        return base.StateMoveToBuilding();
    }

		protected override void OnStaggerEnded()
		{
			SetDefaultAction();
		}


    public void MAThrowCarrying(Transform _throwDest, Func<Vector3> _velocity, Vector3 _deliveryPos, Func<bool> OnComplete)//, Action OnThrow = null)
    {
        SetMoveToPosition(_deliveryPos, false, PeepActions.Throw);
    }
    
    protected override bool StateMoveToInsideBuilding()
    {
#if UNITY_EDITOR
        MABuilding building = m_insideMABuilding as MABuilding;
        Collider moverCollider = m_nav.Collider;
        // var blocks = building.GetComponentsInChildren<Block>();
        // foreach(var block in blocks)
        // {
        //     foreach(var doorCollider in block.m_doorColliders)
        //     {
        //         if(doorCollider.enabled && doorCollider.isTrigger == false)
        //         {
        //             if(Physics.GetIgnoreCollision(doorCollider, moverCollider) == false)
        //             {
        //                Debug.LogError($"{GetType().Name} - {name} - StateMoveToInsideBuilding - GetIgnoreCollision - false {moverCollider.name} vs {doorCollider.name}");
        //             }
        //         }
        //     }
        // }
#endif
        if(base.StateMoveToInsideBuilding() == false)
        {
            //UpdateTargetedByNearbyCreature();//TS - can likely be removed
            return false;
        }
        return true;
    }
    
    protected override bool StateMoveToOutsideBuilding()
    {
    #if UNITY_EDITOR
            MABuilding building = m_insideMABuilding as MABuilding;

            if (building != null)
            {
                Collider moverCollider = m_nav.Collider;
                
                foreach(var c in building.m_components)
                {
                    var block = c.Block;
                    if(block == null) continue;
                    foreach (var doorCollider in block.m_doorColliders)
                    {
                        if (doorCollider != null && doorCollider.enabled && doorCollider.gameObject.activeSelf && doorCollider.gameObject.activeInHierarchy && doorCollider.isTrigger == false)
                        {
                            if (Physics.GetIgnoreCollision(doorCollider, moverCollider) == false)
                            {
                                Debug.LogError($"{GetType().Name} - {name} - StateMoveToOutsideBuilding - GetIgnoreCollision - false {moverCollider.name} vs {doorCollider.name}");
                            }
                        }
                    }
                }
            }
    #endif
        if (base.StateMoveToOutsideBuilding() == false)
            return false;
        return true;
    }
    
    protected override void StateMoveToOutsideBuildingComplete()
    {
        switch (PeepAction)
        {
            case PeepActions.WaitingForWork:
                SetState(STATE.MA_WAITING_FOR_WORK);
                SetNavTarget(DestinationPosition, true);
                break;
            case PeepActions.CollectPickup:
                if ((m_destinationObject != null) && WorkplaceHasStockSpace())
                {
                    SetMoveToObject(m_destinationObject.gameObject, PeepAction, m_destinationObject.transform.right * 0.5f);
                }
                else
                {
                    SetDefaultAction();
                }
                break;
            case PeepActions.Chop:
                if ((m_destinationObject != null) && WorkplaceHasStockSpace())
                {
                    SetMoveToObject(m_destinationObject.gameObject, PeepAction, Vector3.zero, 2f);
                }
                else
                {
                    SetDefaultAction();
                }
                break;
            case PeepActions.Throw:
                SetState(STATE.MA_MOVE_TO_POSITION);
                SetNavTarget(DestinationPosition);
                break;
            
            case PeepActions.Chase:
                SetToChaseObject();
                break;
            default:
                base.StateMoveToOutsideBuildingComplete();
                break;
        }
    }
    
    override public bool SetupHeldByPlayer(NGCommanderBase _originator) {
        if(m_state != STATE.MA_DEAD && UsePhysicsForPickup == false)
            RagdollHelper.CancelRagdoll(gameObject);
        CustomStop = false;
        m_originator = _originator;
        if (m_insideMABuilding)
        {
            m_insideMABuilding.SetWorkerPickup(this);
        }
        m_insideMABuilding = null;
        if (m_state == STATE.MA_DEAD)
        {
            m_deadTimer = -1f;
            m_deadDisappearTimer = -1f;
        }
        SetState(STATE.HELD_BY_PLAYER);
        BlobShadowDisplay(m_blobShadow, false);
        //AudioClipManager.Me.PlaySound(m_pickUpPersonSound, transform);
        StopAgent();
        return	true;
    }

    public override void Whip()
    {
        Debug.LogError($"Worker {name} whipped. Get working!");
    }

    public override void Encourage(float _encourageLevel)
    {
        Debug.LogError($"Worker {name} encouraged {_encourageLevel}. Get working!");
    }

    protected bool BuildingHasStockSpaceForDestinationObject(MABuilding _stockBuilding)
    {
        if(_stockBuilding == null) return false;
        if(m_destinationObject != null)
        {
            var chopComponent = m_destinationObject.GetComponent<BCChopObject>();
            if(chopComponent != null)
            {
                var material = chopComponent.GetCarriableResourceProduced();
                if (_stockBuilding.HasSpaceForStock(material))
                    return true;
            }
            
            var pickup = m_destinationObject.GetComponent<ReactPickup>();
            if (pickup != null)
            {
                if (_stockBuilding.HasSpaceForStock(pickup.Contents))
                    return true;
            }
        }
        return false;
    }

    protected virtual bool WorkplaceHasStockSpace()
    {
        MABuilding building = GetResourceDestination();
        if(building == null || m_destinationObject == null) return false;

        if(IsLowEnergy()) return false;

        if (BuildingHasStockSpaceForDestinationObject(building) == false)
        {
            var pickup = m_destinationObject.GetComponent<ReactPickup>();
            if (pickup != null)
            {
                if (building.HasSpaceForStock(pickup.Contents))
                    return true;
                return false;
            }
        }

        return true;
    }

    private bool MASetToFlee()
    {
        if(m_carrying != null)
        {
            m_carrying.Drop(GlobalData.Me.m_pickupsHolder);
        }
        
        // if (PeepAction != PeepActions.Flee)
        // {
        //    sMASetToFlee();
        //     return;
        // }
        // else
        //{
            // MACharacterBase threat = UpdateTargetedByNearbyCreature() as MACharacterBase;
            // if (threat != null) return false; //returning false because we're already fleeing and very likely petrified (no reaction needed)
            // if (m_state == STATE.MA_MOVE_TO_POSITION && Home == null && Time.time > m_fleeToPosTimer)
            // {
            //     FleeToRandomSpot();
            //     return true;
            // }
        //}
        if (PeepAction != PeepActions.Flee)
        {
            if (Home == null)
            {
                FleeToRandomSpot();
            }
            else
            {
                SetMoveToComponent(Home, PeepActions.Flee);
                //Debug.Log($"{name} - Fleeing to Building {Home.name}");
            }
            return true;
        }
        return false;
    }
    
    private void FleeToRandomSpot()
    {
        Vector3 pos = transform.position;
        m_fleeToPosTimer = Time.time + m_fleeToPosTimeDuration;
        float distSqXYZ = VisionRadiusSq;
        Vector3 newDir = Random.insideUnitCircle;
        Vector3 newPos = pos + newDir * distSqXYZ;
        SetMoveToPosition(newPos, false, PeepActions.Flee);
        //Debug.Log($"{name} - Fleeing to Pos {newPos}");
    }
    
    public bool SetToChaseObject()
    {
        m_backupMass = m_rigidBody.mass;
        m_rigidBody.mass = m_rigidBody.mass < 20f ? m_rigidBody.mass : 20f;
        
        m_destinationObject = null;
        m_destinationMABuilding = null;
        DestinationPosition = Vector3.zero;
        
        if (m_nav == null)
        {
            Debug.LogError($"{GetType().Name} - SetToChaseObject m_agent == null. {name}");
            return false;
        }
        
        Transform targetObject = TargetObject.transform;
        if (targetObject == null)
        {
            Debug.LogError($"{GetType().Name} - SetToChaseObject targetObject == null. {name}");
            return false;
        }

        DestinationPosition = Vector3.zero;
        
        bool isActive = gameObject.activeSelf;
        gameObject.SetActive(true);
        if (m_insideMABuilding)
        {
            PeepAction = PeepActions.Chase;
            SetMovingObjectToLeaveBuilding(isActive);
            return true;
        }
        
        PeepAction = PeepActions.None;
        SetState(STATE.MA_CHASE_OBJECT);
        m_nav.Speed = GetDesiredSpeed();

        var chasePos = GetChasePos(targetObject);
        SetNavTarget(chasePos);
        return true;
    }

    protected Vector3 GetChasePos(Transform _targetObject)
    {
        var targetFwd = _targetObject.forward.GetXZ().normalized;
        var targetSide = Vector3.Cross(targetFwd, Vector3.up);
        var fb = m_followBehaviourSettings;
        var targetPos = _targetObject.position + fb.m_followAheadTarget * targetFwd + fb.m_followRightTarget * targetSide;
        return targetPos;
    }
    
	protected override bool StateChaseObject()
	{
        if (TargetObject == null)
        {
            m_nav.PopPause("ChaseObject");
            SetState(STATE.IDLE);
            return false;
        }

        if(UpdateTargetedByNearbyCreature())
            return false;

        Transform targetObject = TargetObject.transform;
        var chasePos = GetChasePos(targetObject);
		var distSq = (chasePos - transform.position).sqrMagnitude;
        var distSqToLeader = (targetObject.position - transform.position).sqrMagnitude;
        bool isV = IsTargetVisible(chasePos, out bool inVisRad, 1);

        var fb = m_followBehaviourSettings;
        bool refresh = true;
        if (Time.time < fb.m_followPosNextUpdateTime) refresh = false;
        if (refresh)
        {
            fb.m_followPosNextUpdateTime = Time.time + fb.m_followPosUpdateDelay;
        }

        
        var vToVal = transform.position.GetXZ() - TargetObject.transform.position.GetXZ();
        var distForward = vToVal.magnitude * Vector3.Dot(vToVal.normalized, TargetObject.transform.forward);

        bool leaderStopped = false;
        if (TargetObject.MovingTargetSelf.IsPossessed)
        {
            leaderStopped = GameManager.Me.GetPossessedDetails().vel.GetXZ().magnitude.IsZero();
        }
        else
        {
            if (TargetObject.MovingTargetSelf.m_nav.IsNavigating == false ||
                TargetObject.MovingTargetSelf.m_nav.IsTravelling == false)
            {
                leaderStopped = TargetObject.MovingTargetSelf.m_nav.m_finalSpeed.IsZero();
            }
        }
        if (distSq > fb.m_followMaxDist * fb.m_followMaxDist && refresh)
        {
            m_nav.PopPause("ChaseObject");
            m_nav.Speed = GetDesiredSpeed();
            SetNavTarget(chasePos, false, null, fb.m_followStepBackAmount + 0.1f);
        }
        else if (distForward > 2f && leaderStopped == false)
        {
            m_nav.PopPause("ChaseObject");
            
            float followSpeedMulti = 1 / (NavAgent.FinalSpeedMultiplier == 0 ? 1f : NavAgent.FinalSpeedMultiplier);
            m_nav.Speed = GameManager.Me.PossessedCharacter.m_possessionFwdWalkSpeed * followSpeedMulti * 0.975f;
        }
        else if (distSqToLeader < fb.m_followStepBackAmount * fb.m_followStepBackAmount && isV)
        {
            m_nav.PopPause("ChaseObject");
            m_nav.Speed = GetDesiredSpeed(); //m_nav.SetTarget(chasePos, false, NavigationCorridor, _onPathReady, _remoteDestinationDistance, _destinationRadius);

            SetNavTarget(chasePos, true); //, null, fb.m_followStepBackAmount + 0.1f);
        }
        else if(leaderStopped)
        {
            m_nav.PushPause("ChaseObject", true, false);
        }
        return true;
	}
    
    /// <returns> returns true if  -a threat is detected && the worker is forced to react- </returns>
	public bool UpdateThreatAlarm()
    {
        if(NGManager.Me.m_enableFleeing == false) return false;
        if(PeepAction == PeepActions.Ejected) return false;
        
        // If hiding don't flee
        if(IsInHidingState) return true;
        
        if(NGManager.Me.IsWorkerUnderThreat(this) == false) return false;
        return MASetToFlee();
            
        //TS - excluded from design until further notice
        // TownProximityState townProximitySelf = GetTownProximityState();
        // if (townProximitySelf == null)
        // {
        //     return true;
        // }
        //
        // switch (townProximitySelf.m_townProximity)
        // {
        //     case TownProximity.InsideTown:
        //         if (townProximitySelf.m_enclosurePath.IsOpen)
        //         {
        //             return HandleFleeing();
        //         }
        //         break;
        //     case TownProximity.OutsideNearTown:
        //     case TownProximity.FarOutsideTown:
        //         if (townProximitySelf.m_enclosurePath.IsOpen)
        //         {
        //             return HandleFleeing();
        //         }
        //         else
        //         {                        
        //             GateOpener _gate = GateOpener.NearestToPoint(transform.position);
        //             SetMoveToPosition(_gate.transform.position, false, PeepActions.Flee);
        //             return true;
        //         }
        //         break;
        // }
    }

    /// <returns> Returns a creature/character targeting this and we were forced to react </returns>
    protected MACharacterBase UpdateTargetedByNearbyCreature()
    {
        var creature = GetTargetedByNearbyCreature();
        if (creature != null)
            MASetAsPetrified(creature);
        
        return creature;
    }
    
    public void SetKinematic(bool _wantKinematic, bool _setToGroundLevel = true)
    {
        if(_wantKinematic && m_rigidBody.collisionDetectionMode == CollisionDetectionMode.ContinuousDynamic && m_rigidBody.isKinematic == false) 
        {//this is to suppress an extremely spammy & pointless internal Unity LogError that happens when you set isKinematic to true while in continuous collision detection mode, hoping Unity will remove that.
            m_continuousCollisionDetectionMode = true;
            m_rigidBody.collisionDetectionMode = CollisionDetectionMode.Discrete;
        }

        m_rigidBody.isKinematic = _wantKinematic;
        
        if (_wantKinematic == false)
        {
            if(m_continuousCollisionDetectionMode)
            {//re-apply the backup collision detection mode
                m_rigidBody.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
                m_continuousCollisionDetectionMode = false;
            }
            CapsuleCollider sc = GetComponent<CapsuleCollider>();
            if(_setToGroundLevel)
            {
                Vector3 currentPos = transform.position;
                Vector3 groundPosY = currentPos.SetYToHeight() + sc.center;
                currentPos.y = Mathf.Max(groundPosY.y, currentPos.y);
                transform.position = currentPos;
            }
        }
    }
    
    override protected void UpdateKinematic()
    {
        if(m_state == STATE.HELD_BY_PLAYER) return;
        if (GameManager.Me.IsPossessed(this) && GameManager.Me.IsControllingPossessed) return;
        if (m_nav.IsStuck) return;
        bool wantKinematic = false;
        bool wantGroundLevel = false;
        bool wantPushable = true;
        switch (m_state)
        {   
            case STATE.IDLE:
                if (IsQuestCharacter)
                {
                    wantPushable = false;
                    wantGroundLevel = true;
                }
                break;
            case STATE.MA_CHASE_OBJECT:
                if (IsQuestCharacter)
                {
                    wantPushable = true;
                    wantGroundLevel = false;
                }
                break;
            case STATE.MA_DECIDE_WHAT_TO_DO:
                if (IsQuestCharacter)
                {
                    wantPushable = false;
                    wantGroundLevel = true;
                }
                break;
            case STATE.MA_PETRIFIED:
                if (IsQuestCharacter)
                {
                    wantPushable = false;
                }
                break;
            // case STATE.MOVE_INSIDE_BUILDING:
            //     wantKinematic |= true;
            //     wantGroundLevel |= false;
            //     break;		
            case STATE.MOVE_TO_POSITION:
                wantPushable = true;
                wantGroundLevel = true;
                break;		
            case STATE.MA_MOVE_TO_INSIDE_BUILDING:
                Transform tr = transform;
                Vector3 pos = tr.position;
                float yGround = GlobalData.Me.GetRealHeight(pos);
                if(pos.y < yGround - 0.1f)
                {
                    wantGroundLevel = wantKinematic = true;
                    tr.position = pos.GroundPosition();
                }
                else
                {
                    wantGroundLevel |= false;
                    wantKinematic |= false;
                }
                break;				
            case STATE.MA_MOVE_TO_OUTSIDE_BUILDING:
                wantKinematic |= false;
                wantGroundLevel |= false;
                break;				
            case STATE.RESTING:
                wantKinematic |= true; //TS - this may not be necessary anymore
                wantGroundLevel |= false;
                break;
            case STATE.WORKING:
//				wantKinematic |= true;
                break;				
            case STATE.MA_DEAD:
				wantKinematic |= m_deadDisappearTimer >= 0;
                break;		
            case STATE.MA_HANGOUT:
                break;
            case STATE.MA_ANIMATOR_CONTROLLED:
            case STATE.MA_FOLLOW_TRANSFORM:
            case STATE.MA_IN_PILLORY_STOCKS:
                wantKinematic |= true;
                wantGroundLevel |= false;
                break;
        }
        wantKinematic |= m_forceKinematic;

				wantKinematic |= m_kinematicHolds.Count > 0;
		
        //	bool wantKinematdic = m_state == STATE.MA_MOVE_TO_INSIDE_BUILDING || m_state == STATE.RESTING || !TerrainManager.Me.IsVisible(transform.position);
        //	if (m_state == STATE.WORKING && m_controlledByBuilding is NGBuildingSite) wantKinematic = true;
        // if (m_forceThroughObstacle > 0)
        // { 
        //     m_forceThroughObstacle -= Time.deltaTime;
        //     wantKinematic = true;
        // }
        SetCanBePushed(wantPushable);
        if (m_rigidBody.isKinematic != wantKinematic)
        {
            SetKinematic(wantKinematic, wantGroundLevel);
        }
    }
    MAMovingInfoBase m_movingInfo = null;
    override public MAMovingInfoBase GetMovingInfo()
    {
        if (m_movingInfo == null) m_movingInfo = MAWorkerInfo.GetInfo(GameStatePerson.m_workerInfo);
        return m_movingInfo;
    }
    
    public void SetGenderSoundSwitch()
    {
        string gender = GameStatePerson.m_isMale ? "MALE" : "FEMALE";
        AudioClipManager.Me.SetSoundSwitch("Gender", gender, gameObject);
    }

    public void SetWorkerName()
    {
        int num = NGManager.Me.HouseList.Count;
        if (GameStatePerson.m_isMale == false)
            m_givenName = "Miss " +
                          NGManager.Me.WorkerFirstNamesFemale[
                              num % NGManager.Me.WorkerFirstNamesFemale.Length] + " " +
                          NGManager.Me.WorkerNames[num % NGManager.Me.WorkerNames.Length];
        else
            m_givenName = "Mr " + NGManager.Me.WorkerFirstNames[num % NGManager.Me.WorkerFirstNames.Length] +
                          " " + NGManager.Me.WorkerNames[num % NGManager.Me.WorkerNames.Length];
    }
    
    private void StateWaitingForHome()
    {
        if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
            return;
        
        if(IsTimeToHangout && SendToHangOut())
            return;
        
        if(IsTimeToGoHome && MAReturnToHome())
            return;
        
        if(IsTimeToHangout == false && IsTimeToGoHome == false && MAReturnToJob())
            return;

        var place = m_commander;
        if (place == null)
            place = m_destinationMABuilding;
        if (place == null && Home && Home.Building)
            place = Home.Building;
        
        FaceAway(place);

        // Only look for a new home if their block has been destroyed
        if (Home == null && m_workerInfo.m_autoFindHome && Time.time > m_lookForTimer)
        {
            m_lookForTimer = Time.time + 10f;
            var bestHome = InputUtilities.GetClosestFreeHome(this);
            if (bestHome)
            {
                bestHome.AddToHomePermanently(this);
                return;
            }
        }
        
        if(m_balloon == null)
        {
            if(m_balloonHolder == null)
            {
                m_balloonHolder = m_animHandler?.m_hatAttach ? m_animHandler.m_hatAttach : transform;
            }

            m_balloon = NGBalloonManager.Me.CreateBalloon(NGBalloonManager.BalloonType.Red, m_balloonHolder,
                "this param doesnt work", null, UpdateBalloon);
            m_balloon.SetText("I Need\na Home");
        }
    }
    
    protected void StateWaitingForWork()
    {
        if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
            return;
        
        if(IsTimeToGoHome || IsTimeToHangout)
        {
            SetDefaultAction();
            return;
        }
         
        // If job block is back in a building, go to work
        if(Job && Job.Building && MAReturnToJob())
            return;

        if (Home == null || Home.Building == null)
            return;
        var place = m_commander;
        if (place == null)
            place = m_destinationMABuilding;
        if (place == null)
            place = Home.Building;
            
        // Only look for a new job if their block has been destroyed
        if (Job == null && m_workerInfo.m_autoFindJob && Time.time > m_lookForTimer && NGUnlocks.WorkerAutoFindJob)
        {
            m_lookForTimer = Time.time + 10f;
            var bestJob = InputUtilities.GetClosestJob(this);
            if (bestJob)
            {
                bestJob.AddWorkerToWorkPermanently(this);
                return;
            }
        }

        FaceAway(place);
    }

    private void FaceAway(NGCommanderBase _place)
    {
        if (_place && isActiveAndEnabled && _place.PlayIdleAnimationsWhenOutside)
        {
            Vector3 facingDir = _place.DoorPosOuter - _place.DoorPosInner;
            var destAngle = Mathf.Atan2(facingDir.x, facingDir.z) * Mathf.Rad2Deg;

            Vector3 currDir = transform.forward;
            var currAngle = Mathf.Atan2(currDir.x, currDir.z) * Mathf.Rad2Deg;

            if (!m_turningToFaceAway)
            {
                if (Mathf.Abs(destAngle - currAngle) > 0.1f)
                {
                    transform.rotation = Quaternion.Slerp(transform.rotation, Quaternion.Euler(0, destAngle, 0), .1f);
                }
            }
        }
    }

    private float m_deadTime = 5f;
    private float m_deadTimer = -1;
    private float m_deadDisappearTime = 2;
    private float m_deadDisappearTimer = -1;
    [SerializeField] private float m_deadDisappearDownDistance = 2;
    protected override void StateDead()
    {
        base.StateDead();
        if(m_deadTimer > -1)
        {
            m_deadTimer -= Time.deltaTime;
            if(m_deadTimer <= 0)
            {
                m_deadDisappearTimer = m_deadDisappearTime;
                m_deadTimer = -1;

                Rigidbody[] rigidBodies = GetComponentsInChildren<Rigidbody>(); //includes ragdoll parts
                Array.ForEach(rigidBodies, rb => rb.isKinematic = true);
            }
        }

        if(m_deadDisappearTimer > -1)
        {
            m_deadDisappearTimer -= Time.deltaTime;
            if(m_deadDisappearTimer < 0)
            {
                m_deadDisappearTimer = -1;
                DestroyMe();
            }
            else
            {
                transform.position -= Vector3.up * m_deadDisappearDownDistance * (Time.deltaTime / m_deadDisappearTime);
            }
        }
    }
    
    protected override bool StateMoveToObject()
    {
        SetDesiredSpeed();
        if (HasAgentArrived() == false)
        {
            if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
                return false;
            return false;
        }
        m_insideMABuilding = null;
        switch (PeepAction)
        {
            case PeepActions.CollectPickup:
                var pickup = m_destinationObject == null ? null : m_destinationObject.GetComponent<ReactPickupPersistent>();
                if(pickup == null || pickup.IsBeingDragged)
                {
                    SetDefaultAction();
                    return true;
                }
                pickup.AssignToCarrier(this);
                if (Job != null)
                {
                    SetMoveToComponent(Job, PeepActions.DeliverToInput);
                }
                return true;
            case PeepActions.Chop:
                SetState(STATE.MA_CHOP_OBJECT);
                return true;
            case PeepActions.Throw:
                FinishThrow();
                return true;
            default:
                Debug.LogError($"worker '{name}' has moved to object with illegal PeepAction of '{PeepAction}'");
                SetDefaultAction();
                break;
        }

        return false;
    }
    
    override protected bool StateMoveToPosition()
    {
        if (HasAgentArrived() == false)
        {
            if(UpdateThreatAlarm() || UpdateTargetedByNearbyCreature())
                return false;
            return false;
        }
        
        return OnMoveToPosArrived();
    }
    
    protected override bool OnMoveToPosArrived()
    {
        m_insideMABuilding = null;
        switch (PeepAction)
        {
            case PeepActions.Ejected:
                if(m_timeInState >= NGManager.Me.m_ejectReturnDelay)
                {
                    SetDefaultAction();
                }
                break;
            case PeepActions.WaitingForWork:
                SetState(STATE.MA_WAITING_FOR_WORK);
                break;
            case PeepActions.WaitingForHome:
                SetState(STATE.MA_WAITING_FOR_HOME);
                break;
            case PeepActions.Throw:
                FinishThrow();
                break;
            case PeepActions.Flee:
                MASetToFlee();
                break;
            case PeepActions.HangOut:
                MASetAsHangOut();
                break;
            case PeepActions.Despawn:
            case PeepActions.DespawnRun:
                DestroyMe();
                break;
            case PeepActions.GoLookAt:
                if (m_isTurningToLookAt == null)
                {
                    if (GetLookAtTarget(CharacterGameState.m_lookAtTarget, out Vector3 target) == false || target == Vector3.zero)
                    {
                        Debug.LogError($"MAWorker - MoveToPositionAndLookAt - Valid LookAt Target? '{target}' for {name}");
                        SetState(STATE.IDLE);
                        PeepAction = PeepActions.None;
                        break;
                    }

                    if (LookAt(target, 110f, OnFinishedTurningTowards))
                    {
                        return true;
                    }
                }
                return false;
            case PeepActions.None:
                SetDefaultAction();
                break;
            default:
                Debug.LogError($"worker '{name}' has moved to position with illegal PeepAction of '{PeepAction}'");
                SetDefaultAction();
                break;
        }
        return true;
    }

    public bool MAReturnToJob()
    {
        if(Job == null)
        {
            return false;
        } 
        SetMoveToComponent(Job, PeepActions.ReturnToWork);
        return true;
    }
    
    public void RecallToWork()
    {
        if(Job == null) return;
        
        if(Job.Building == m_insideMABuilding) return;
        
        if(MAReturnToJob())
        {
            // Must do this to remove the worker from the present lists
            if(m_insideMABuilding)
            {
                (m_insideMABuilding as MABuilding).Leave(this);    
            }
        }
    }
    
    public void RecallToHome()
    {
        if(Home == null) return;
        
        if(Home.Building == m_insideMABuilding) return;
        
        if(MAReturnToHome())
        {
            // Must do this to remove the worker from the present lists
            if(m_insideMABuilding)
            {
                (m_insideMABuilding as MABuilding).Leave(this);    
            }
        }
    }
    
    public override bool MAReturnToHome()
    {
        if(Home == null)
        {
            return false;
        }
        SetMoveToComponent(Home, PeepActions.ReturnToRest);
        PeepAction = PeepActions.ReturnToRest;
        return true;
    }
    
    public bool MAWaitForHome()
    {
        SetState(STATE.MA_WAITING_FOR_HOME);
        return true;
    }
    
    override protected bool HasAgentArrived()
    {
        //if (m_nav.PathPending)
        //     return false;
        //
        // var remainingDistance = Mathf.Abs(m_destinationRemainingDistance - m_nav.DistanceToTarget);
        // if (remainingDistance < 0.00001f && m_nav.TargetReached == false && !m_nav.WaitingForPath && m_nav.IsPaused == false &&(NGManager.Me && NGManager.Me.m_pauseCharacters == false))
        // {
        //     if (m_stuckCount++ >= 30)
        //     {
        //         Debug.Log($"{name} has stuck count of {m_stuckCount} in {m_state} distance = {remainingDistance} nav speed set to {m_nav.Speed}", gameObject);
        //         m_stuckCount = 0;
        //         m_forceThroughObstacle = 5f; // GL - for a while move through physics obstacles
        //     }
        // }
        // else
        //     m_stuckCount = 0;
        //
        // m_destinationRemainingDistance = m_nav.DistanceToTarget;
        return base.HasAgentArrived();//m_nav.TargetReached;
    }
    
    override public void PlayerEndedDrag(NGCommanderBase _destination, SpecialHandlingAction _restrictedAction)
    {
        GetComponentInChildren<NGPickupBase>()?.DestroyMe();
        
        // Assignments without updating the character state
        var building = _destination as MABuilding;
        if(building != null)
        {
            if (_restrictedAction == SpecialHandlingAction.AssignJob)
                building.TryAssignJob(this, null, false);
            if (_restrictedAction == SpecialHandlingAction.AssignHome)
                building.TryAssignBedroom(this, false);
        }
    }

    override public void FinallyDroppedByPlayer(NGCommanderBase _destination, SpecialHandlingAction _restrictedAction)
    {
        if (_destination != null && _destination.ApplySpecialDropHandling(this, _restrictedAction))
            return;
            
        if (_destination == null ||
            _destination.IsOperational == false ||
            _destination.IsBeingConstructed ||
            _destination.AddObjectFromDrop(this, _restrictedAction) == false)
        {
            if(Health > 0f && m_destinationMABuilding != null && Carrying != null)
            {
                SetMoveToBuilding(m_destinationMABuilding, PeepActions.DeliverToInput);
            }
            else
            {
                SetDefaultAction();
            }
        }

        if (m_state == STATE.MA_DEAD)
        {
            m_deadTimer = m_deadTime;
            m_deadDisappearTimer = -1;
        }
    }

    override public void FinallyDroppedByPlayer(MADecorationActionBase _destination, SpecialHandlingAction _restrictedAction)
    {
        GetComponentInChildren<NGPickupBase>()?.DestroyMe();

        if (_destination != null && _destination.ApplySpecialDropHandling(this, _restrictedAction)) return;

        if (_destination == null )
        {
            SetDefaultAction();
        }
    }

    override public void NGSetAsWorking(bool _isVisibleInside)
    {
        if (Job == null)
        {
            PeepAction = PeepActions.Idle;
            SetState(STATE.IDLE);
            return;
        }
        PeepAction = PeepActions.Working;
        SetState(STATE.WORKING);
        gameObject.SetActive(_isVisibleInside);
    }
    
    override public void NGSetAsResting(bool _isVisibleInside = false)
    {
        PeepAction = PeepActions.Resting;
        SetState(STATE.RESTING);
        gameObject.SetActive(_isVisibleInside);
    }
    
    override public void MASetAsPetrified(MACharacterBase _attacker, bool _dropCarried = true, bool _rotateToThreat = true)
    {
        if(m_state != STATE.MA_PETRIFIED && m_state != STATE.MA_DEAD)
        {
            m_gameState.m_backupMovementState = (int)m_state;
            m_nav.PushPause("petrified", false, true);
            if(_dropCarried && m_carrying != null)
            {
                m_carrying.Drop(GlobalData.Me.m_pickupsHolder);
            }

            if (_attacker != null && _rotateToThreat)
            {
                LookAt(_attacker.transform.position);
            }
            SetState(STATE.MA_PETRIFIED);
        }
    }

    protected MACharacterBase QuestCharacterCheckForCreatures()
    {
        if(m_state == STATE.MA_PETRIFIED || m_state != STATE.MA_DEAD)
            return null;
        
        if (m_ignorePetrifiedInStates.Contains(m_state))
            return null;

        if(CanCheckForThreats == false)
            return null;
        
        OnCheckedThreats();
        
        var pos = transform.position;
        var nearChrs = AgentPresenceManager.Me.GetTemporaryArrayOfCharactersNear(transform.position, m_visionRadius, out var chrs, this);
        for (int i = 0; i < nearChrs; ++i)
        {
            var creature = chrs[i] as MACreatureBase;
            if (creature == null || creature.Health <= 0 || creature.gameObject.activeInHierarchy == false)
                continue;
            
            if (IsTargetVisible(creature.transform.position, pos, 1, GlobalData.AllowNavType.AnyPerfectNav))//TS - can change to lowNav if necessary
            {
                MASetAsPetrified(creature, false, false);
                return creature;
            }
        }        
        return null;
    }

    protected void FlowCharacterPetrified()
    {
        MACreatureBase creature = QuestCharacterCheckForCreatures() as MACreatureBase;
        if(creature == null && m_timeInState > CharacterSettings.m_petrifiedStateMinDuration)
        {
            if (BackUpMovementState != STATE.NONE)
            {
                SetState(BackUpMovementState);
                m_gameState.m_backupMovementState = (int)STATE.NONE;
            }
        }
    }

    override public void MASetAsHangOut()
    {
        SetState(STATE.MA_HANGOUT);
    }

    private HashSet<STATE> responsiveStates = new () 
    {
        STATE.MA_PETRIFIED
    };
    private HashSet<STATE> draggedStates = new ()
    {
        STATE.HELD_BY_PLAYER
    };
    private HashSet<STATE> ragdolledStates = new ()
    {
        STATE.MA_DEAD
    };

		public override void ApplyAudioState()
	{
		if (m_audioStateCoroutine != null)
		{
			StopCoroutine(m_audioStateCoroutine);
		}

		if (m_audioStateEvents != null && m_audioStateEvents.Count > 0)
		{
			var stateString = m_state.ToString();
			CharacterStateAudioEvent audioStateEvent =
				m_audioStateEvents.Find(x => stateString == x.m_characterState);
			PlayStateAudio(audioStateEvent);
		}
	}

    public float m_backupMass = 0f;
    override public void SetState(STATE _newState)
    {
        m_timeInState = 0;
        if (_newState == STATE.MA_LOADING)
        {
            m_state = _newState;
            return;
        }

        if(m_state == STATE.MA_DEAD)
        {
            if ((_newState != STATE.HELD_BY_PLAYER) && (_newState != STATE.MA_WAITING_TO_TELEPORT))
                Debug.LogError($"WARNING - {name} - ATTEMPTING TO SET MA_DEAD STATE TO {_newState.ToString()} STATE");
        }

        if (m_state == STATE.MA_PETRIFIED)
        {
           BlendAnimatorLayerWeight("Cower", 0);
        }
        
        SetCanBePushed(true);
        if (m_state != _newState)
        {
            var oldState = m_state;
            switch (oldState)
            {
                case STATE.MA_CHASE_OBJECT:
                    m_rigidBody.mass = m_backupMass > 0 ? m_backupMass : m_rigidBody.mass;
                    m_nav.PopPause("ChaseObject");
                    break;
                case STATE.MA_CHOP_OBJECT:
                    StopWorkerLoopAnimation();
                    break;
                case STATE.MA_HANGOUT:
                    StopCurrentAnimation();
                    if (coHangOut != null)
                    {
                        StopCoroutine(coHangOut);
                        coHangOut = null;
                    }
                    break;
                case STATE.MA_PETRIFIED:
                    m_nav.PopPause("petrified");
                    break;
                case STATE.MA_IN_PILLORY_STOCKS:
                    StopWorkerLoopAnimation();
                    break;
                case STATE.MA_FOLLOW_TRANSFORM:
                    m_followTransform = null;
                    break;
                case STATE.MA_DECIDE_WHAT_TO_DO:
                    break;      
            }
        }
        
        var rc = GetComponentInChildren<RagdollController>();
        if (rc != null)
        {
            if (responsiveStates.Contains(_newState))
            {
                rc.StartResponsiveState();
            }
            else if (draggedStates.Contains(_newState))
            {
                rc.StartDraggedState();
            }
            else if (ragdolledStates.Contains(_newState))
            {
                ActivateRagDoll();
            }
            else
            {
                rc.StartAnimatedState();
            }
        }

        m_state = _newState;
        name = GetGameObjectName();

				ApplyAudioState();

        switch (_newState)
        {
            case STATE.MA_PETRIFIED:
                BlendAnimatorLayerWeight("Cower", 1);
                break;
            case STATE.MA_DEAD:
                // MAManaBall.Create(transform, m_workerInfo);
                break;
            case STATE.MA_IN_PILLORY_STOCKS:
                PlayLoopAnimation("Worker_Stocks_Idle", null);
                break;
            
            case STATE.MA_DECIDE_WHAT_TO_DO:
                if (m_nav.IsTravelling)
                {
                    m_nav.StopNavigation();
                }
                if (IsQuestCharacter)
                {
                    SetCanBePushed(false);
                }
                break;      
        }
    }

    public override bool SetDead()
    {
        var stateChanged = base.SetDead();

        CharacterDead?.Invoke(this);

        if (stateChanged)
            MAManaBall.Create(transform, m_workerInfo);
        
        foreach(var mainCollider in m_mainColliders)
            mainCollider.enabled = false;
        GetComponent<Collider>().enabled = true;
        
        m_deadTimer = m_deadTime;
        return stateChanged;
    }

    public virtual void SetDefaultAction(bool _ignorePossessedCheck = false)
    {
        if (m_state == STATE.MA_PETRIFIED && m_timeInState < CharacterSettings.m_petrifiedStateMinDuration) return;
        if (GameManager.Me.PossessedCharacter == this && _ignorePossessedCheck == false) return;
        if (Health <= 0) { SetState(STATE.MA_DEAD); return; }
        if (m_carrying != null && Job && Job.Building && SetMoveToBuilding(Job.Building, PeepActions.DeliverToOutput)) return;
        if (IsTimeToHangout && SendToHangOut()) return;
        if (IsTimeToHangout == false && IsTimeToGoHome == false && MAReturnToJob()) return;
        if (MAReturnToHome()) return;
        if (MAWaitForHome()) return;
        Debug.LogError($"{name} has no Home or Job setting State to Idle");
        SetState(STATE.IDLE);
    }
    
    // _command can be
    // Building[ID]
    // RegionName
    public static int GetWorkerCount(string _command)
    {
        List<MAWorker> workers = new List<MAWorker>();
        if (_command.IsNullOrWhiteSpace() == false)
        {
            // By building name or id
            var buildingPassSplit = _command.Split('[', ']');
            if(buildingPassSplit.Length > 1)
            {
                _command = buildingPassSplit[1];

                var building = MABuilding.FindBuilding(_command);
                if (building == null)
                {
                    Debug.LogError($"Cant find building {_command}");
                    return 0;
                }
                building.GetUniqueWorkersAllocated(workers);
            }
            else
            {
                // By region
                _command = _command.ToLower();
                foreach(var building in NGManager.Me.m_NGCommanderList)
                {
                    if(building.DistrictID.Equals(_command))
                    {
                        (building as MABuilding)?.GetUniqueWorkersAllocated(workers);
                    }
                }
            }
            return workers.Count;
        }
        
        var found = NGManager.Me.m_MAWorkerList.FindAll(x => x.Job!=null && x.Home != null);
        return found.Count;
    }

    public static MAWorker Create(string _workerName, Vector3 _pos, bool _addToCharacterLists = true, Quaternion? _rot = null)
    {
        var workerInfo = MAWorkerInfo.GetInfo(_workerName);
        if(workerInfo == null)
        {
            Debug.LogError($"MAWorker.Create({_workerName}) has no workerInfos");
            return null;
        }
        return Create(workerInfo, _pos, _addToCharacterLists, _rot);
    }
    public static MAWorker Create(MAWorkerInfo.WorkerTypeEnum _workerType, Vector3 _pos)
    {
        var createRange = MAWorkerInfo.GetInfoByType(_workerType);
        if (createRange.IsNullOrEmpty())
        {
            Debug.LogError($"MAWorker.Create({_workerType}) has no workerInfos");
            return null;
        }

        var workerInfo = createRange[Random.Range(0, createRange.Count)];
        return Create(workerInfo, _pos);
    }

    protected Image m_workerEmote;
	protected bool m_initEmote;
    public void ResetEmote() { m_initEmote = false; }
    
	protected override void ApplyInitialCharacterState()
	{	
		MACharacterStateFactory.ApplyInitialState(CharacterStates.WorkerState, this);
	}
    
    private static MACreatureInfo ToCreatureInfo(MAWorker _worker, MAWorkerInfo _workerInfo)
    {
        MACreatureInfo characterInfo = new MACreatureInfo();
        characterInfo.m_health = _workerInfo.m_health;
        characterInfo.m_name = _workerInfo.m_name;
        characterInfo.m_displayName = _workerInfo.m_displayName;
        characterInfo.m_creatureType = _workerInfo.m_workerType;
        characterInfo.m_prefabName = _workerInfo.m_prefabName;
        characterInfo.m_visionRadius = _worker.m_visionRadius;
        characterInfo.m_proximityRadius = _worker.m_visionRadius * 1.25f;
        characterInfo.m_lowWalkSpeed = _workerInfo.m_lowWalkSpeed;
        characterInfo.m_highWalkSpeed = _workerInfo.m_highWalkSpeed;
        characterInfo.m_possessWalkSpeed = _workerInfo.m_lowWalkSpeed;
        characterInfo.m_possessRunSpeed = _workerInfo.m_highWalkSpeed;
        characterInfo.m_lowAttackSpeed = _workerInfo.m_lowWalkSpeed;
        characterInfo.m_highAttackSpeed = _workerInfo.m_highWalkSpeed;
        characterInfo.m_healthDayRegeneration = _workerInfo.m_healthDayRegeneration;
        characterInfo.m_health = _workerInfo.m_health;
        //characterInfo.m_workerTargets = _workerInfo.
        // characterInfo.m_componentTargets = _workerInfo.
        //characterInfo.m_creatureTargets = _workerInfo.
        characterInfo.m_canSmashWallTypes = new();
        characterInfo.m_despawnTime = -1;
        characterInfo.m_dayNightSpawn = "Anytime";
        characterInfo.m_defaultArmour = _workerInfo.m_defaultArmour;

        // characterInfo.m_bloodColour = _workerInfo.
        // characterInfo.m_bloodAmount = _workerInfo.
        return characterInfo;
    }
    
    private static MAWorker CreateWorkerObject(MAWorkerInfo _info, Vector3 _pos, Vector3 _rot)
    {
        var go = Instantiate(_info.m_prefab, _pos, Quaternion.Euler(_rot), GlobalData.Me.m_characterHolder);
        
        var worker = go.GetComponent<MAWorker>();
        if (worker == null)
        {
            Debug.LogError($"MAWorker.Create({_info.m_name}) has no MAWorker component");
            return null;
        }
        worker.m_workerInfo = _info;
        worker.SetState(STATE.MA_LOADING);
        worker.enabled = false;
        return worker;
    }
    
    public static MAWorker Load(GameState_Person _data)
    {
        MAWorkerInfo.RecoverWorkerInfo(ref _data.m_workerInfo);
        
        var info = MAWorkerInfo.GetInfo(_data.m_workerInfo);
        if(info == null)
        {
            Debug.LogError($"Unable to load worker info {_data}");
            return null;
        }
        
        var worker = CreateWorkerObject(info, _data.m_pos, _data.m_rotation);
        var characterInfo = ToCreatureInfo(worker, info);
        if(GameManager.HasLoadedFromSeed)
        {
            _data.m_health = info.m_health;
            worker.SetSpeed(info.GetRandomWorkerSpeed());
        }
        worker.ActivateWithSaveData(characterInfo, _data);
        worker.m_energyWorkThreshold = NGManager.Me.m_energyWorkThreshhold;
        worker.m_energyRestThreshold = NGManager.Me.m_energyRestThreshhold;     
        worker.AddCharacterToLists();

        // Not sure if we still need the below?
        if (_data.m_carrying.IsNullOrWhiteSpace() == false)
        {
            var spawnBuilding = GameManager.Me.GetMACommander<MABuilding>(_data.m_carryingSpawnedFrom);
            var carriedResource = NGCarriableResource.GetInfo(_data.m_carrying);
            ReactPickupPersistent item = null;
            if (carriedResource.IsProduct)
            {
                if (string.IsNullOrEmpty(_data.m_carryingProductUID) == false)
                {
                    var productImposter = new GameState_Product("") { m_uniqueID = _data.m_carryingProductUID };
                    var setup = new PickupSetup() { m_quantity = _data.m_carryingQuantity };
                    item = NGReactPickupAny.Create(spawnBuilding, productImposter.ResourceType, setup);
                }
            }
            else
                item = ReactPickupPersistent.CreateItem(spawnBuilding, carriedResource, _data.m_carryingQuantity, worker.m_destinationMABuilding);
            if (item != null)
                item.AssignToCarrier(worker);
        }
        return worker;
    }
    
    protected virtual GameState_Person CreateNewGameState() { return new GameState_Person(); }
    
    public static MAWorker Create(MAWorkerInfo _workerInfo, Vector3 _pos, bool _addToCharacterLists = true, Quaternion? _rot = null, MABuilding _optionalOwner = null)
    {
        Vector3 rot = _rot != null ? ((Quaternion)_rot).eulerAngles : Vector3.zero;

        var worker = CreateWorkerObject(_workerInfo, _pos, rot);
        worker.AllocateID();
        
        var characterInfo = ToCreatureInfo(worker, _workerInfo);
        worker.Activate(characterInfo, null, true, _pos, rot, _addToCharacterLists);
        if(_optionalOwner != null) worker.m_insideMABuilding = _optionalOwner;
        worker.SetSpeed(_workerInfo.GetRandomWorkerSpeed());
        worker.RigidBody.useGravity = true;
        worker.Energy = _workerInfo.m_energy;
        
        if(_addToCharacterLists)
            worker.AddCharacterToLists();

        worker.PostLoad(true);
        return worker;
    }
    
    public void AddCharacterToLists()
    {
        switch (m_workerInfo.WorkerType)
        {
            case MAWorkerInfo.WorkerTypeEnum.Worker:
                if(NGManager.Me.m_MAWorkerList.Contains(this) == false)
                    NGManager.Me.m_MAWorkerList.Add(this);
                if(NGManager.Me.m_MAHumanList.Contains(this) == false)
                    NGManager.Me.m_MAHumanList.Add(this);
                if(NGManager.Me.m_MACharacterList.Contains(this) == false)
                    NGManager.Me.AddCharacter(this);
                break;
            case MAWorkerInfo.WorkerTypeEnum.Tourist:
            case MAWorkerInfo.WorkerTypeEnum.QuestGiver:
                if(NGManager.Me.m_MAHumanList.Contains(this) == false)
                    NGManager.Me.m_MAHumanList.Add(this);
                if(NGManager.Me.m_MACharacterList.Contains(this) == false)
                    NGManager.Me.AddCharacter(this);
                break;
            case MAWorkerInfo.WorkerTypeEnum.Worshipper:
                if(NGManager.Me.m_MAWorshipperList.Contains(this) == false)
                    NGManager.Me.m_MAWorshipperList.Add(this);
                if(NGManager.Me.m_MAHumanList.Contains(this) == false)
                    NGManager.Me.m_MAHumanList.Add(this);
                if(NGManager.Me.m_MACharacterList.Contains(this) == false)
                    NGManager.Me.AddCharacter(this);
                break;
            
        }
    }


    public override void AttachBodyType(string _bodyType)
    {
        SetupVisuals();
        base.AttachBodyType("");
    }
    

    private void SetupVisuals()
    {
        m_contentRoot = m_contentRoot == null ? transform : m_contentRoot;
        var gameState = (m_gameState as GameState_Person);
        var cType = gameState != null ? (gameState.m_isMale ? CharacterVisuals.Type.WorkerMale : CharacterVisuals.Type.WorkerFemale) : CharacterVisuals.Type.None;
        InitialiseVisuals(cType);
        ResetEmote();
        OnVisualReady(m_contentRoot.gameObject);
    }

    override protected void OnDestroy()
    {
        if (Utility.IsShuttingDown) return;
        
        base.OnDestroy();

        if(NGManager.Me != null)
        {
            bool r2debug = NGManager.Me.m_MAWorkerList.Remove(this);
            bool r3debug = NGManager.Me.m_MAHumanList.Remove(this);
            bool r4debug = NGManager.Me.RemoveCharacter(this);
        }

        if(GameManager.Me != null)
            GameManager.Me.m_state.m_people.Remove(GameStatePerson);
    }

    public override void DoPossessedAction()
    {
    }

    override public bool SetMoveToObject(GameObject _gameObject, PeepActions? _action = null, Vector3 _offsetPos = default, float _arrivalRad = 0)
    {
        if (base.SetMoveToObject(_gameObject, _action, _offsetPos, _arrivalRad))
        {
            if (TrySetupChopObjectState(_gameObject, _action))
            {
            }

            return true;
        }

        return false;
    }
    
    virtual protected GameObject CharacterPrefab => (m_workerInfo == null || m_workerInfo.m_characterPrefab == null) ? GlobalData.Me.m_characterPrefab : m_workerInfo.m_characterPrefab;
    
    virtual public void InitialiseVisuals(CharacterVisuals.Type _type)
    {
        m_workerEmote = GetComponentInChildren<UnityEngine.UI.Image>();
        if(m_workerEmote != null)
            m_workerEmote.gameObject.SetActive(false);
        m_initEmote = false;
		
        var characterRoot = Instantiate(CharacterPrefab, m_contentRoot);
        var characterVisuals = characterRoot.GetComponentInChildren<CharacterVisuals>();
        if (characterVisuals == null)
        {
            return;
        }

        // RegisterToDispatcherEvents(characterRoot);
        
        var visuals = characterVisuals.gameObject;
        m_visuals = visuals.transform;
        characterVisuals.m_type = _type;

		if (m_ragdollController == null)
			m_ragdollController = GetComponentInChildren<RagdollController>();

		SetupWithAnimator(visuals.GetComponent<Animator>());
		
		m_nav.Initialise(this);
        
        m_nav.m_onStuckTimeExceeded -= OnStuckTimeExceeded;
        m_nav.m_onStuckTimeExceeded += OnStuckTimeExceeded;

        const float c_baseHeadScale = 1.4f;
        SetCharacterSize(c_baseHeadScale, characterVisuals.m_ignoreHeadScale);
        
        if (m_workerInfo.m_lowBodyColor <= m_workerInfo.m_highBodyColor)
        {
            SetBodyColour(Color.HSVToRGB(Random.Range(m_workerInfo.m_lowBodyColor, m_workerInfo.m_highBodyColor), .9f, .8f));
            SetSkinColour(GlobalData.Me.GetSkinColour(Random.Range(m_workerInfo.m_lowSkinColor, m_workerInfo.m_highSkinColor)));
            SetHairColour(GlobalData.Me.GetHairColour(Random.Range(m_workerInfo.m_lowHairColor, m_workerInfo.m_highHairColor)));
        }
        visuals.AddComponent<TransformFrameLimiter>();
    }

    public override void PostLoad(bool _justCreated=false)
    {
        if(GameStatePerson.m_walkSpeed == 0f)
        {
            GameStatePerson.m_walkSpeed = m_workerInfo.GetRandomWorkerSpeed();
            GameStatePerson.m_attackSpeed = GameStatePerson.m_walkSpeed;
        }
        
        if (GameState.m_isPossessed)
            GameManager.Me.PossessObject(this); // do this before base.PostLoad to avoid overriding the "not loaded yet" kinematic
        base.PostLoad(_justCreated);
        LoadWorkerState(this, (STATE)m_gameState.m_state);
    }

    override public void SetupPossessed(bool _possessedOn)
    {
        AudioClipManager.Me.SetPossessedSwitch(gameObject, _possessedOn);
        GameState.m_isPossessed = _possessedOn;

        if (_possessedOn)
		{
			if ((m_state != STATE.MA_POSSESSED) && (m_state != STATE.MA_DEAD))
			{
                SetState(STATE.MA_POSSESSED);
                ResetChopState();
			}
		}
		else
		{
            if (m_state != STATE.MA_DEAD)
            {
                SetDefaultAction(true);
            }
		}
    }

    public override void SetupFollowLeaderState(bool _on)
    {
        if (_on)
        {
            if (m_state != STATE.MA_FOLLOW_LEADER && m_state != STATE.MA_DEAD)
            {
                SetState(STATE.MA_FOLLOW_LEADER);
            }
        }
        else
        {
            if (m_state != STATE.MA_DEAD)
            {
                SetDefaultAction();
            }
        }
    }

	protected override void SetTeleportingState()
	{
        if (m_state != STATE.MA_WAITING_TO_TELEPORT)
		    SetState(STATE.MA_WAITING_TO_TELEPORT);
	}

    protected override void OnTeleportEnded()
    {
        if (m_state == STATE.MA_DEAD)
            return;

        m_nav.ClearPath();
        SetDefaultAction();

        if (m_state == STATE.MA_DEAD)
        {
            m_deadTimer = m_deadTime;
            m_deadDisappearTimer = -1;
        }
	}
    
    public Vector3 GetThrowVelocity(NGCommanderBase _target)
    {
        return GetThrowVelocity(_target.DoorPosInner);
    }
	
    public Vector3 GetThrowVelocity(Vector3 _targetPosition)
    {
        var throwDir = (_targetPosition - transform.position).GetXZNorm();
        throwDir.y = 1;
        throwDir *= NGManager.Me.m_throwVelocityScaler;
        return throwDir;
    }

    public Vector3 GetThrowVelocityAccurate(Transform _targetTransform)
    {
        return GetThrowVelocityAccurate(_targetTransform.position);
    }
	
    public Vector3 GetThrowVelocityAccurate(Vector3 _targetPos)
    {
        const float c_throwSpeed = 6f;
        float time = (Carrying.transform.position - _targetPos).xzMagnitude() / c_throwSpeed;
        var throwDir = Global3D.GetVelocityRequiredForPointToPointOverTime(transform.position, _targetPos, time);
        return throwDir;
    }
	
    public void SetBodyColour(Color _c, bool _noOverride = false) {
        if (_noOverride == false)
            _c = GlobalData.Me.PrepareClothingColour(_c);
        Extensions.SetTintWindowColour(gameObject, 0, _c, true);
    }
    
    public void SetSkinColour(Color _c) {
        Extensions.SetTintWindowColour(gameObject, 1, _c, true);
    }
    
    public void SetHairColour(Color _c) {
        Extensions.SetTintWindowColour(gameObject, 2, _c, true);
    }
    
    private void UpdateBalloon(Balloon _balloon)
    {
        switch(m_state)
        {
            case STATE.MA_WAITING_FOR_HOME:
                break;
            default:
                if(_balloon == m_balloon)
                    m_balloon.DestroyMe();
                break;
        }
    }
    
    // protected override bool FeelDamage(float _damageFelt, Vector3 _sourceOfDamage)
    // {
    //     //base.FeelDamage(_damageFelt, _sourceOfDamage);	
      
    //     if (_damageFelt < m_settings.m_damageFeltStep)
    //     {
    //         return false;
    //     }
		
    //     // m_lastHitTime = Time.time;

    //     AnimateStagger(_sourceOfDamage, _damageFelt);
    //     return true;
    // }

    private bool TrySetupChopObjectState(GameObject _gameObject, PeepActions? _action = null)
    {
        if (_gameObject == null || !_action.HasValue || _action.Value != PeepActions.Chop)
        {
            return false;
        }
        
        TreeHolder treeHolder = _gameObject.GetComponentInChildren<TreeHolder>();
        if (treeHolder == null || treeHolder.IsLocked)
        {
            return false;
        }

        var chopObject = treeHolder.ChopObject;
        if (chopObject == null)
        {
            chopObject = _gameObject.AddComponent<BCChopObject>();
        }
        chopObject.m_worker = this;

        m_chopObjectPhase = ChopObjectPhase.None;
        m_newChopObjectPhase = ChopObjectPhase.Intro;
        m_chopObjectPhaseNeedsReset = false;
        
        GameState_Person gameStatePerson = GameStatePerson;
        gameStatePerson.m_chopObjectPhase = (int)m_newChopObjectPhase;
        GameStatePerson.m_treeChopId = treeHolder.m_treeIndex;
        return true;
    }

    public struct ReceivedDamageInfo
    {
        public ReceivedDamageInfo(float _damageDone, MAAttackInstance _attack)
        {
            m_damageDone = _damageDone;
            m_time = Time.time;
            m_attacker = null;

            if(_attack != null)
            {
                m_damageDone = _attack.Damage;
                m_attacker = _attack.Attacker;
            }
        }

        public float m_damageDone;
        public float m_time;
        public MACharacterBase m_attacker;
    }

    public List<ReceivedDamageInfo> m_receivedDamageHistory = new List<ReceivedDamageInfo>();

    public void RememberDamage(float _damageDone, MAAttackInstance _attack)
    {
        m_receivedDamageHistory.Add(new ReceivedDamageInfo(_damageDone, _attack));

        for(int i = m_receivedDamageHistory.Count - 2; i >= 0; i--)
        {
            // Forget after 10 seconds
            if(Time.time - m_receivedDamageHistory[i].m_time > 10.0f)
            {
                m_receivedDamageHistory.RemoveAt(i);
            }
        }
    }

    public override void ApplyDamageEffect(IDamageReceiver.DamageSource source, float _damageDone, Vector3 _sourceOfDamage, MAAttackInstance attack = null, MAHandPowerInfo handPower = null)
    {
        RememberDamage(_damageDone, attack);

        if (IsInPrisonerState)
        {
            TauntPrisoner();
            return;
        }

        base.ApplyDamageEffect(source, _damageDone, _sourceOfDamage, attack, handPower);
    }

        // protected void OnStuckTimeExceeded(float _stuckFactor)
        // {
        //     //Debug.Log($"NGWorker - OnStuckTimeExceeded - {name} - stuckFactor {_stuckFactor}");
        //     m_forceThroughObstacle = m_agentStuckForceThroughDuration;
        //     //m_stuckCount = 5;
        // }
        //

    private static void LoadWorkerState(MAWorker _worker, STATE _state)
    {
        bool setDefaultAction = false;
        GameState_Person gameState = _worker.m_gameState as GameState_Person;
        MABuilding inside = _worker.m_insideMABuilding as MABuilding;
        _worker.enabled = true;
        switch (_state)
        {
            case STATE.HIDING:
                if(inside == null)
                {
                    Debug.LogError($"worker {_worker.m_ID} has null m_insideMABuilding but is in state {_state.ToString()}");
                    break;
                }
                _worker.transform.position = _worker.GetSpawnHeight(_worker.transform.position, _worker.m_insideMABuilding);
                inside.WorkerArrivesToHide(_worker);
                break;
            case STATE.MA_MOVE_TO_BUILDING:
                if (_worker.m_destinationMABuilding == null)
                {
                    Debug.LogError($"worker {_worker.m_ID} has null m_destinationMABuilding. ID: '{_worker.GameStatePerson.m_destBuilding}'. but is in state {_state.ToString()} ");
                    setDefaultAction = true;
                    break;                    
                }
                _worker.SetMoveToBuilding(_worker.m_destinationMABuilding, _worker.PeepAction);
                break;
            case STATE.MA_MOVE_TO_INSIDE_BUILDING:
                if (_worker.m_insideMABuilding == null)
                {
                    Debug.LogError($"worker {_worker.m_ID} has null m_insideMABuilding but is in state {_worker.m_state.ToString()}. dest building id: '{_worker.GameStatePerson.m_destBuilding}'. worker home id: '{_worker.GameStatePerson.m_homeComponentId}'");
                    setDefaultAction = true;
                    break;                    
                }
                if (_worker.m_nav == null)
                {
                    Debug.LogError($"worker {_worker.m_ID} has null m_agent but is in state {_worker.m_state.ToString()}");
                    break;
                }
                _worker.MASetStateMoveIntoBuilding();
                _worker.MoveToPos(_worker.m_insideMABuilding.DoorPosInner, false);
                break;
            case STATE.MA_MOVE_TO_OUTSIDE_BUILDING:
                if(_worker.m_insideMABuilding == null)
                {
                    Debug.LogError($"worker {_worker.m_ID} has null m_insideMABuilding but is in state {_state.ToString()}");
                    setDefaultAction = true;
                }
                else
                {
                    //_worker.MASetStateMoveOutOfBuilding(_worker.transform.position);
                    _worker.SetMovingObjectToLeaveBuilding(false);
                }
                if(_worker.m_nav == null)
                    Debug.LogError($"worker {_worker.m_ID} has null m_agent but is in state {_state.ToString()}");
                _worker.m_nav.Speed = _worker.GetDesiredSpeed();
                break;
            case STATE.MA_MOVE_TO_POSITION:
                _worker.SetMoveToPosition(_worker.DestinationPosition, false, (PeepActions)gameState.m_peepAction);
                break;
            case STATE.MA_WAITING_FOR_WORK: 
                break;
            case STATE.MA_WAITING_FOR_HOME:
                break;
            case STATE.HELD_BY_PLAYER:
                setDefaultAction = true;
                break;
            case STATE.WORKING:
                if(inside != null)
                {
                    _worker.transform.position = _worker.GetSpawnHeight(_worker.transform.position, _worker.m_insideMABuilding);
                    inside.WorkerArrivesToWork(_worker);
                }
                break;
            case STATE.RESTING:
                if(inside != null)
                {
                    _worker.transform.position = _worker.GetSpawnHeight(_worker.transform.position, _worker.m_insideMABuilding);
                    inside.WorkerArrivesToRest(_worker);
                }
                break;
            case STATE.MA_DEAD:
                RagdollController rc = _worker.GetComponentInChildren<RagdollController>();
                if ((rc != null) && !rc.IsRagdolled)
                {
                    _worker.ActivateRagDoll();//TODO: TS - make sure bones layers are not collidable when body is dead. (use param)
                }
                _worker.m_nav.Pause(true, true);
                break;
            case STATE.MA_MOVE_TO_OBJECT:
            case STATE.MA_CHOP_OBJECT:
                if (gameState.m_peepAction == (int)PeepActions.Chop)
                {
                    if (gameState.m_treeChopId > -1)
                    {
                        var chopObject = TreeHolder.GetTreeObject(gameState.m_treeChopId);
                        var th = chopObject?.GetComponent<TreeHolder>();
                        if (chopObject == null || th == null || th.IsLocked)
                        {
                            setDefaultAction = true;
                        }
                        else
                        {
                            _worker.SetMoveToObject(chopObject.gameObject, PeepActions.Chop, Vector3.zero, 2f);
                        }
                    }
                    else
                    {
                        Debug.LogError(
                            $"Worker ID {gameState.m_id} has no chopTreePosition in gameState - {_worker.name}");
                        setDefaultAction = true;
                    }
                }
                break;
            case STATE.MA_HANGOUT:
                //Debug.LogError($"LoadWorkerState worker {_worker.m_ID} state HANGOUT");
                break;
        }
        if(setDefaultAction || _state == STATE.MA_PETRIFIED) //Don't bother loading petrified, if the zombie still exists it will repetrify us
            _worker.SetDefaultAction();
        else if (_state == STATE.MA_WAITING_TO_TELEPORT)
            _worker.PostLoadTeleport();
        else
            _worker.SetState(_state);
    }

    private static DebugConsole.Command s_workerPickupItem = new("workerpickup", _s =>
    {
        MAWorker worker = null;
        ReactPickup pickup = null;
        var split = _s.Split(',', ';');
        int wId = -1;
        if (split.Length > 0)
        {
            if (int.TryParse(split[0], out wId) == false)
            {
                Debug.LogError("Debug - Cannot parse worker '{_s}'");
                return;
            }
            worker = NGManager.Me.m_MACharacterList.Find(x => x.m_ID == wId) as MAWorker;
            if (worker == null)
            {
                Debug.LogError($"Debug - Cannot find worker '{wId}'");
                return;
            }
        }
        else
        {
            Debug.LogError($"Debug - Missing Params 'workerId; pickupID'");
            return;
        }

        if (split.Length > 1)
        {
            if (GlobalData.Me != null)
            {
                float distSq = Single.MaxValue;
                ReactPickup p = null;
                foreach (Transform pu in GlobalData.Me.m_pickupsHolder)
                {
                    ReactPickup pick = pu.GetComponent<ReactPickup>();
                    if (pick != null && pick.Name == split[1])
                    {
                        pickup = pick;
                        float dSq = pick.transform.position.DistanceXZSq(worker.transform.position);
                        if (dSq < distSq)
                        {
                            distSq = dSq;
                            p = pickup;
                        }
                    }
                }

                if (p == null)
                {
                    Debug.LogError($"Debug - Pickup Error null");
                    return;
                }
                bool can = worker.SetMoveToObject(p.gameObject, PeepActions.CollectPickup);
                if (can == false)
                {
                    Debug.LogError($"Debug - Worker '{wId}' cannot moveToObject '{split[1]}'");
                    return;
                }
            }
        }
        else
        {
            Debug.LogError($"Debug - Missing Param 'workerId; pickupID'");
            return;
        }
    });

    private static DebugConsole.Command s_spawnworker = new ("spawnworker", _type =>
    {
        MAWorker worker = null;
        if (_type.IsNullOrWhiteSpace())
        {
            _type = "CommonWorker";
        }
        
        string[] splitName = _type.Split(',');
        string _buildingId = "";
        
        MACharacterBase character = null;
        object spawnWhere = null;
        MABuilding building = null;
        if (splitName.Length > 1)
        {
            if (splitName.Length > 0)
            {
                _type = splitName[0];
                _buildingId = splitName[1];
            }
            else return;

            if (!string.IsNullOrWhiteSpace(_buildingId))
            {
                building = MABuilding.FindBuilding(_buildingId, true);
            }

            Vector3? pos = null;

            if (building == null)
            {
                if (splitName.Length > 1)
                {
                    building = MACreatureControl.FindBuildingForCharacter(_type);
                    string[] attempts = { "Pos", "Building", "Decoration", "ComponentBuilding", "Components", "NamedPoint" };
                    for (int i = 0; i < attempts.Length; i++)
                    {
                        if (InputUtilities.GetObjectFromLocationString($"{attempts[i]}[{splitName[1]}]", out spawnWhere,
                                true) && spawnWhere != null)
                        {
                            building = spawnWhere as MABuilding;
                            if (building != null)
                            {
                                SpawnAtBuilding(_type, building);return;
                            }

                            pos = spawnWhere as Vector3?;
                            if (pos != null)
                            {
                                worker = Create(_type, (Vector3)pos);
                                return;
                            }
                        }
                    }
                }
            }
            else
            {
                SpawnAtBuilding(_type, building); 
                return;
            }

            return;
        }
        
        LayerMask layerMask = 1 << LayerMask.NameToLayer("Default");
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        if (Physics.Raycast(ray, out RaycastHit hitInfo1, int.MaxValue, layerMask))
        {
            building = hitInfo1.collider.GetComponentInParent<MABuilding>();

            if (building != null)
            {
                SpawnAtBuilding(_type, building);
                return;
            }
        }

        layerMask = 1 << GlobalData.Me.m_moaTerrain.gameObject.layer;
        if (Physics.Raycast(ray, out RaycastHit hitInfo2, int.MaxValue, layerMask))
        {
            worker = Create(_type, hitInfo2.point);
        }

        MAWorker SpawnAtBuilding(string _workerType, MABuilding _building)
        {
            var workerLocal = Create(_workerType, _building.DoorPosInner);
            workerLocal.m_insideMABuilding = _building;
            workerLocal.SetMoveToPosition(_building.DoorPosOuter, false, PeepActions.Idle);
            Debug.LogError(
                $"DebugConsole.Command 'spawnworker' - '{_type}' - id: '{workerLocal.m_ID}' '{workerLocal.Name}' spawned at building  '{building.name}'");
            return workerLocal;
        }
    });
    
#if UNITY_EDITOR
    override public void DebugShowGUIDetails(MACharacterWindow _window, GUIStyle _labelStyle)
    {
        base.DebugShowGUIDetails(_window, _labelStyle);
        EditorGUILayout.LabelField($"m_insideMABuilding: ", (m_insideMABuilding != null) ? m_insideMABuilding.name : "NULL", _labelStyle);
        EditorGUILayout.LabelField($"m_destinationMABuilding: ",(m_destinationMABuilding != null) ? m_destinationMABuilding.name: "NULL", _labelStyle);
        EditorGUILayout.LabelField($"Home: ", (Home != null && Home.Building) ? Home.Building.name : "NULL", _labelStyle);

        if (Job && Job.Building != null && m_state == STATE.WORKING)
        {
            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.LabelField($"MyJob: ", Job.Building.name, _labelStyle);
            EditorGUILayout.LabelField($"Progress: ", $"{Job.Building.GetProductScore():F4}", _labelStyle);
            EditorGUILayout.EndHorizontal();
        }
        else
        {
            EditorGUILayout.LabelField($"MyJob: ", (Job != null) ? Job.name : "NULL", _labelStyle);
        }
        EditorGUILayout.LabelField($"Carrying: ", GameStatePerson.m_carrying, _labelStyle);

        if(TargettedBy.Count > 0)
        {
            EditorGUILayout.LabelField($"m_targetedBy[{TargettedBy.Count}]", _labelStyle);
            EditorGUI.indentLevel++;
            foreach(var creature in TargettedBy)
            {
                EditorGUILayout.LabelField(creature.Name, _labelStyle);
            }
            EditorGUI.indentLevel--;
        }
        if(GameStatePerson != null)
        {
            var newSpeed = EditorGUILayout.FloatField("m_speed:", GameStatePerson.m_speed);
            if (newSpeed != GameStatePerson.m_speed) GameStatePerson.m_speed = newSpeed;
            var newEnergy = EditorGUILayout.FloatField("m_energy:", GameStatePerson.m_energy);
            if (newEnergy != GameStatePerson.m_energy) GameStatePerson.m_energy = newEnergy;
            var newHealth =EditorGUILayout.FloatField($"m_health: ", Health);
            if (newHealth != Health) Health = newHealth;
            EditorGUILayout.LabelField($"m_waitingForPay: ", WaitingForPay.ToString(), _labelStyle);
            EditorGUILayout.LabelField($"m_peepAction: ", PeepAction.ToString(), _labelStyle);
            switch (PeepAction)
            {
                case PeepActions.Working:
                    if (m_state == STATE.WORKING && m_insideMABuilding as MABuilding)
                    {
                        var componentsWithWorker = (m_insideMABuilding as MABuilding).GetComponentsWithWorker(this);
                        foreach(var c2 in componentsWithWorker)
                            c2.DebugShowGUIDetails(_labelStyle, false);
                    }
                    break;
            }
            
        }
        m_debugBreakHere = EditorGUILayout.Toggle("Force Breakpoint", m_debugBreakHere);

    }
#endif
}

#if UNITY_EDITOR
[CustomEditor(typeof(MAWorker))]
public class MAWorkerEditor : MACharacterBaseEditor
{
    private string num = "Enter Smash Force";
    private string numEnergy = "Enter Energy";
    private string numHealth = "Enter Health";
    
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        MAWorker worker = (MAWorker)target;

        GUILayout.BeginHorizontal();
        string num2 = GUILayout.TextField(num);
        if(num2 != null && num != num2)
        {
            num = num2;
        }

        if (GUILayout.Button($"pushPausetestPause"))
        {
            worker.m_nav.PushPause("Test");
        }
        if (GUILayout.Button($"pushPausetestUnPause"))
        {
            worker.m_nav.PopPause("Test");
        }
        if(GUILayout.Button($"RagDoll (Smash?)"))
        {
            worker.SetState(NGMovingObject.STATE.MA_DEAD);
            worker.Health = 0;
            worker.m_nav.Pause(true, true);
            worker.DeallocateJobAndHome();
            
            float numf = 1f;
            if(float.TryParse(num, out numf) == false)
            {
                numf = 1f;
            }
            worker.ActivateRagDoll(Vector3.right * numf);
        }
        GUILayout.EndHorizontal();
        
        GUILayout.BeginHorizontal();
        string numEnergy2 = GUILayout.TextField(numEnergy);
        if(numEnergy2 != null && numEnergy != numEnergy2)
        {
            numEnergy = numEnergy2;
        }
        
        if(MACharacterBase.c_useEnergy)
        {
            if(GUILayout.Button($"Energy = {numEnergy}"))
            {
                if(float.TryParse(numEnergy, out float numEnergyf))
                {
                    worker.Energy = Mathf.Clamp01(numEnergyf);
                }
            }
        }

        string numHealth2 = GUILayout.TextField(numHealth);
        if(numHealth2 != null && numHealth != numHealth2)
        {
            numHealth = numHealth2;
        }
        if(GUILayout.Button($"Health = {numHealth}"))
        {
            if(float.TryParse(numHealth, out float numHealthf))
            {
                worker.Health = numHealthf;
            }
        }
        GUILayout.EndHorizontal();
    }
}
#endif
