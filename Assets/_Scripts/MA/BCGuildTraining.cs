using UnityEngine;
using System.Collections.Generic;

public class BCGuildTraining : BCBase
{
    [<PERSON>nack<PERSON>ield] public float m_heroTrainingPerSecond = 1f;
    [KnackField] public int m_heroesToTrain = 1;
    private MACharacterBase m_lastHeroTrained;
    
    public bool TryTrain(MAHeroBase _hero, float _multiplier)
    {
        if(m_lastHeroTrained == null)
        {
            TrainHero(_hero, _multiplier);
            m_lastHeroTrained = _hero;
            return true;
        }
        return false;
    }
    
    public static bool RequiresTraining(MAHeroBase _hero)
    {
        return _hero.CharacterGameState.RequiresTraining;
    }
    
    public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        m_lastHeroTrained = null;
        base.Activate(_indexOfComponentType, _quantityInBuilding);
    }
    
    public override void UpdateInternal(BuildingComponentsState _state)
    {
    }
    
    public override void LateUpdateInternal()
    {
        base.LateUpdateInternal();
        SetCharacterVisualsEnabled(m_lastHeroTrained);
        m_lastHeroTrained = null;
    }
    
    public void TrainHero(MAHeroBase _hero, float _multiplier)
    {
        _hero.AddExperience(m_heroTrainingPerSecond * _multiplier * Time.deltaTime, "Training");
    }
    
    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (m_info?.id, () => new BCTrainingPanel(m_info));
    
    public class BCTrainingPanel : BCUIPanel
    {
        protected BCGuildTraining m_component;
        private string m_blockID;
        public override string SpriteBlockID => m_blockID;
        public override string GetPrimaryText() =>  m_component?.m_info?.m_description;

        public BCTrainingPanel(MAComponentInfo _info) : base(_info) { }

        public override void AddComponent(BCBase _component)
        {
            if(_component is BCGuildTraining)
            {
                m_blockID = _component.Block.BlockID;
                m_component = _component as BCGuildTraining;
                base.AddComponent(_component);
            }
        }
        
        /*override public IEnumerable<System.Action> CreateTableLines(Transform _holder)
        {
            bool createdTitle = false;
            List<MACharacterBase> heroes = new();
            yield return () => MABuildingWorkerPanelLine.CreateHeroTitle(_holder);
            
            if(m_component.Building != null)
            {
                heroes = m_component.Building.GetHeroesPresent();
            }
            
            if(heroes.Count == 0)
            {
                yield return () => MADesignInfoSheetLine.Create(_holder, "None", null, true);
            }
            else
            {
                
            }
        }*/
    }
}
