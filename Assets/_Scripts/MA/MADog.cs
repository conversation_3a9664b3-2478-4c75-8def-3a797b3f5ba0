using System;
using System.Collections;
using System.Collections.Generic;
using System.Numerics;
using UnityEngine;
using Quaternion = UnityEngine.Quaternion;
using Random = UnityEngine.Random;
using Vector3 = UnityEngine.Vector3;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class MADog : <PERSON>Ani<PERSON>, IDamager
{
    [Header("MADog")]
    override public string HumanoidType => "Dog";

    public override Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary() => MADogStateLibrary.StateLibrary;
    public override bool IsNotBusy => (IsOnPatrol || IsRoamingFreely || IsFollowing) && TargetObject == null && (CharacterSettings.m_isBodyGuard || NGManager.Me.IsUnderAttack());

    private MATimer m_checkForFoodTimer = new MATimer();
    public float m_eatProbability = 0.05f;
    public float m_eatFrequency = 4f;
    
    private MATimer m_checkToPooTimer = new MATimer();
    public float m_pooProbability = 0.05f;
    public float m_pooFrequency = 3f;
    
    private MATimer m_checkToPeeTimer = new MATimer();
    public float m_peeProbability = 0.05f;
    public float m_peeFrequency = 2f;		

    public bool m_isQuadruped = true;
    public QuadrupedSettings m_quadrupedSettings = new QuadrupedSettings();
    private Vector3 m_groundPosRear = Vector3.zero, m_groundPosFront = Vector3.zero;
    private LayerMask m_groundLayers;
    
    private Coroutine m_switchRoutine = null;
    
    public override string InitialState
    {
        get => string.IsNullOrWhiteSpace(m_gameState?.m_initialState) ? CharacterStates.RoamForTarget : m_gameState.m_initialState;
        set => m_gameState.m_initialState = value;
    }

    public override string DefaultState
    {
        get => string.IsNullOrWhiteSpace(m_gameState?.m_defaultState) ? CharacterStates.RoamForTarget : m_gameState.m_defaultState;
        set => m_gameState.m_defaultState = value;
    }

    protected override void Awake()
    {
        base.Awake();
        m_groundLayers = LayerMask.GetMask(new string[] { "Default", "Terrain", "Roads" });
        m_contentRootName = "Target";
        m_headMainBoneName = "head";
        m_headBoneName = "head";
        m_neckBoneName = "neck";
        m_toeBoneName = "claws_b.R";
        
    }

    protected override void Update()
    {
        base.Update();
        ThreatCheck();
        UpdateDistanceTravelled();

        if (m_isQuadruped && GameManager.Me.IsPossessed(this))// && m_isTouchingGround)
        {
            UpdateQuadruped();
        }
    }

    [Serializable]
    public class QuadrupedSettings
    {
        public float m_quadroPedFront = 1.5f, m_quadroPedRear = -1.5f;
        public float m_downCastDistance = 2f;//reduce
        public float m_fallingLerp = 0.1f;
        public float m_maxDown = 0.5f;
        public float m_centre = 0.5f;
        public float m_forwardTiltLerp = 0.4f;
        public float m_modelFollowLerp = 0.5f;
    }

    public bool m_debugGizmoQuadruped = false;
    private void UpdateQuadruped()
    {
        Transform tr = transform;
        var pos = tr.position;
        var forward = tr.forward;
        var contentTr = m_contentRoot;
        var contentForward = contentTr.forward;
        var contentRight = contentTr.right;
        var contentPos = contentTr.position;
        var upF = (Vector3.up + new Vector3(0, contentForward.y, 0)) * m_neckHeight;
        var upR = (Vector3.up + new Vector3(0, -contentForward.y, 0)) * m_neckHeight;
        var quadFront = pos + forward * m_quadrupedSettings.m_quadroPedFront;
        var quadRear = pos + forward * m_quadrupedSettings.m_quadroPedRear;
        
        float downDistFront = Mathf.Abs(m_quadrupedSettings.m_downCastDistance + m_quadrupedSettings.m_downCastDistance - m_neckHeight);
        float downDistBack = Mathf.Abs(m_quadrupedSettings.m_downCastDistance + m_quadrupedSettings.m_downCastDistance - m_neckHeight);

        bool isFrontFalling = GetGroundPos(quadFront + upF, downDistFront, ref m_groundPosFront) == false;
        bool isRearFalling = GetGroundPos(quadRear + upR, downDistBack, ref m_groundPosRear) == false;
        
        float quadLerpLerp = m_quadrupedSettings.m_forwardTiltLerp;        
        if (isFrontFalling && isRearFalling)
        {
            m_groundPosFront = quadFront;
            m_groundPosRear = quadRear;
            if (m_debugGizmoQuadruped)
            {
                Debug.DrawLine(quadFront + upF, m_groundPosFront, Color.red);
                Debug.DrawLine(quadRear + upR, m_groundPosRear, Color.red);
            }
            quadLerpLerp = m_quadrupedSettings.m_fallingLerp;
        }
        else if (isFrontFalling)
        {
            m_groundPosFront = quadFront;
            m_groundPosFront.y -= m_quadrupedSettings.m_maxDown;
            if (m_debugGizmoQuadruped) Debug.DrawLine(quadFront + upF, m_groundPosFront, Color.red);
            quadLerpLerp = m_quadrupedSettings.m_fallingLerp;
        }
        else if (isRearFalling)
        {
            m_groundPosRear = quadRear;
            m_groundPosRear.y -= m_quadrupedSettings.m_maxDown;
            if (m_debugGizmoQuadruped) Debug.DrawLine(quadRear + upR, m_groundPosRear, Color.red);
            quadLerpLerp = m_quadrupedSettings.m_fallingLerp;
        }
        
        var midPos = Vector3.Lerp(m_groundPosFront, m_groundPosRear, m_quadrupedSettings.m_centre);
        var vGround = (m_groundPosFront - m_groundPosRear);
        var vNGround = vGround.normalized;
        var perpendicularUpN = Vector3.Normalize(Vector3.Cross(vNGround, contentRight));

        if (m_debugGizmoQuadruped)
        {
            Debug.DrawLine(contentPos + contentForward * m_quadrupedSettings.m_quadroPedFront,
                contentPos + contentForward * m_quadrupedSettings.m_quadroPedRear, Color.yellow);
            Debug.DrawLine(midPos, midPos + vNGround, Color.white);
            Debug.DrawLine(midPos, midPos + contentRight, Color.black);
            Debug.DrawLine(midPos, midPos + perpendicularUpN, Color.grey);
        }

        //tilt forward/backward
        Vector3 projectedNormal = Vector3.ProjectOnPlane(perpendicularUpN, contentRight).normalized;
        Quaternion tiltRotation = Quaternion.FromToRotation(contentTr.up, projectedNormal);
        var contentRotation = contentTr.rotation;
        contentTr.rotation = Quaternion.Lerp(contentRotation, tiltRotation * contentRotation, quadLerpLerp);

        //mode/content matching main transform smoothly
        var newContentPos = Vector3.Lerp(m_contentRoot.position, midPos, m_quadrupedSettings.m_modelFollowLerp);
        newContentPos.y = Mathf.Clamp(newContentPos.y, pos.y - m_headHeight, pos.y + m_headHeight);
        m_contentRoot.position = newContentPos;
    }

    private bool GetGroundPos(Vector3 _rayOriginDown, float _dist, ref Vector3 _groundPos)
    {
        if (Physics.Raycast(_rayOriginDown, Vector3.down,
                out var hitAtFront, _dist, m_groundLayers , QueryTriggerInteraction.Ignore))
        {
            Debug.DrawLine(_rayOriginDown, hitAtFront.point, Color.green);
            _groundPos = hitAtFront.point;
            return true;
        }
        else
        {
            //probably best to override this value with a value suitable for 'falling' 
            _groundPos = _rayOriginDown.GroundPosition();
            return false;
        }
    }

    public override TargetResult GetBestTarget(float _persistence = 1)
    {
        TargetResult bestTarget = base.GetBestTarget(_persistence);
        // if (bestTarget != null)
        // {
        //     if (bestTarget.m_targetObject.TargetObjectType == TargetObject.TargetType.CharacterType &&
        //         bestTarget.m_targetObject.GetComponent<MACharacterBase>() is MAChicken chicken)
        //     {
        //         var pos = transform.position;
        //         NGMovingObject leader = Leader;
        //         float visSq = VisionRadiusSq;
        //         if (leader != null && (IsLeaderAvailable ||
        //                             chicken.transform.position.DistanceSq(pos) > visSq || 
        //                             leader.transform.position.DistanceSq(pos) > visSq))
        //         {
        //             return null;
        //         }
        //     }
        // }
        return bestTarget;
    }

    public void ThreatCheck()
    {
        if (IsLeaderAvailable) return;
        TargetObject selfAsTarget = SelfAsTarget;
        if (selfAsTarget != null)
        {
            TargetObject.Attacker attacker = selfAsTarget.GetClosestAttacker();
            if (attacker != null && attacker.m_attacker.Transform.position.DistanceSq(transform.position) < 8 * 8 && IsFleeing == false)
            {
                if (m_switchRoutine != null) StopCoroutine(m_switchRoutine);
                SwitchStates(CharacterStates.Flee, 0f);
                return;
            }
        }
    }

    public void FoodCheck()
    {
        if (m_switchRoutine != null) return;
        if (m_checkForFoodTimer.IsFinished)
        {
            m_checkForFoodTimer.Set(m_eatFrequency);
            if (CharacterSettings.m_canEatPickups && DayNight.Me.m_isFullDay &&
                UnityEngine.Random.Range(0, 1f) < m_eatProbability)
            {
                Transform closestTransform = FindClosestTransform(GlobalData.Me.m_pickupsHolder, transform.position,
                    CharacterSettings.m_eatRadius);
                if (closestTransform != null)
                {
                    SetTargetObj(new MACharacterBase.TargetResult()
                    {
                        m_targetObject = TargetObject.Create(closestTransform, TargetObject.TargetType.Pickup)
                    });
                    SwitchStates(CharacterStates.EatPickup, 0f);
                    return;
                }
            }
        }
    }

    public void PooCheck()
    {
        if (m_switchRoutine != null) return;
        if (m_checkToPooTimer.IsFinished)
        {
            m_checkToPooTimer.Set(m_pooFrequency);
            if (CharacterSettings.m_canPoo && DayNight.Me.m_isFullDay && UnityEngine.Random.Range(0, 1f) < m_pooProbability &&
                m_gameState.m_lastDayPooed != DayNight.Me.m_day && IsInTown() == false)
            {
                SwitchStates(CharacterStates.PooAtPosition, 0f);
                return;
            }
        }
    }

    public const float c_checkForNewLeaderHitProbability = 0.2f;
    public void LeaderCheck()
    {
        if (CharacterSettings.m_isBodyGuard && IsQuestCharacter == false)
        {
            if (Leader == null && Random.Range(0, 1f) < c_checkForNewLeaderHitProbability)
            {
                var randomPick = NGManager.Me.m_MAHumanList.PickRandom();
                Leader = randomPick;
            }
        }
    }

    public void PeeCheck()
    {
        if (m_switchRoutine != null) return;
        if (m_checkToPeeTimer.IsFinished)
        {
            m_checkToPeeTimer.Set(m_peeFrequency);
            if (CharacterSettings.m_canPee && DayNight.Me.m_isFullDay && UnityEngine.Random.Range(0, 1f) < m_peeProbability && m_gameState.m_lastDayPeed != DayNight.Me.m_day)
            {
                Transform closestTransform = FindClosestTransform(NGManager.Me.m_decorationHolder, transform.position, CharacterSettings.m_peeRadius);
                if (closestTransform != null)
                {
                    SetTargetObj(new MACharacterBase.TargetResult() { m_targetObject = TargetObject.Create(closestTransform, TargetObject.TargetType.InteractionPointType) });
                    SwitchStates(CharacterStates.PeeOnDecoration, 0f);
                    return;
                }
            }
        }
    }
    private void SwitchStates(string _toState, float _delay = 0f)
    {
        if (_delay > 0f)
        {
            m_switchRoutine = StartCoroutine(SwitchStatesDelayed(CharacterStates.PeeOnDecoration, _delay));
            return;
        }
        MACharacterStateFactory.ApplyCharacterState(_toState, this);
        m_switchRoutine = null;
    }

    private IEnumerator SwitchStatesDelayed(string _toState, float _delay = 1.5f)
    {
        StopCurrentAnimation(); //wait for dog sit pose to finish and dog to stand up
        yield return new WaitForSeconds(_delay);
        MACharacterStateFactory.ApplyCharacterState(_toState, this);
        m_switchRoutine = null;
        yield break;
    }

    protected static Transform FindClosestTransform(Transform _trParent, Vector3 _toPos, float _withinRange)
    {
        int checkCount = 3;
        Transform closestTr = null;
        var bestv = Vector3.zero;
        float bestDistSq = _withinRange * _withinRange;
        foreach (Transform trChild in _trParent)
        {
            var v = (trChild.position - _toPos);
            float distSq = v.xzSqrMagnitude();
            if (distSq < bestDistSq)
            {
                bestv = v;
                checkCount--;
                bestDistSq = distSq;
                closestTr = trChild;
                if (checkCount == 0)
                    break;
            }
        }
        return closestTr;
    }

    public override void Activate(MACreatureInfo _info, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation,
        bool _addToCharacterLists = true)
    {
        base.Activate(_info, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);
        CharacterGameState.m_immortal = true;
        GameManager.Me.m_onPossess -= OnPossess;
        GameManager.Me.m_onPossess += OnPossess;
    }

    private void OnPossess(bool _startPossess, NGLegacyBase _obj)
    {
        if (_obj == this)
        {
            m_isQuadruped = _startPossess;
        }
    }

    protected override void InitialiseGameState()
    {
        base.InitialiseGameState();
    }
    
    public override bool CanInteract(NGMovingObject _chr)
    {
        return MAQuestManager.Me.RequiredMovingObjects().Contains(this);// GameManager.Me.CanPossess(this);
    }

    public override string GetInteractLabel(NGMovingObject _chr)
    {
        return "Possess";

        //if (GameManager.Me.CanPossess(this))
        //{
        //    return "Possess";
        //}
        //else
        //{
        //    return base.GetInteractLabel(_chr);
        //}
    }

    public override bool SetDead()
    {
        m_isQuadruped = false;
        if(GameManager.Me != null) GameManager.Me.m_onPossess -= OnPossess;
        return base.SetDead();
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        if(GameManager.Me != null) GameManager.Me.m_onPossess -= OnPossess;
    }

    public override void DoInteract(NGMovingObject _chr)
    {
        GameManager.Me.Unpossess(false);
        GameManager.Me.PossessObject(this);

        //if (GameManager.Me.CanPossess(this))
        //{
        //    GameManager.Me.Unpossess(false);
        //    GameManager.Me.PossessObject(this);
        //}
        //else
        //{
        //    base.DoInteract(_chr);
        //}
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(MADog))]
public class MADogEditor : MACharacterBaseEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
    }
}
#endif