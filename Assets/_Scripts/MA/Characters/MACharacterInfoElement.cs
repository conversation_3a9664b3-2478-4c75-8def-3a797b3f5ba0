using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MACharacterInfoElement : MonoBehaviour
{
    public GameObject m_buttonHolder;
    public Button m_button;
    public TMP_Text m_title;
    public TMP_Text m_value;
    private Func<string> m_updateValue;
    
    public void Activate(string _title, Func<string> _updateValue, Action _onClick = null)
    {
        if(_title == null)
        {
            m_title.gameObject.SetActive(false);
            m_value.alignment = TextAlignmentOptions.Center;
            var margin = m_value.margin;
            margin.w = 10;
            margin.y = 10;
            m_value.margin = margin;
        }
        m_title.text = _title;
        m_updateValue = _updateValue;
        
        m_buttonHolder.SetActive(_onClick != null);
        if(_onClick != null)
            m_button.onClick.AddListener(() => _onClick());
        UpdateValue();
    }
    
    public void UpdateValue()
    {
        if(m_updateValue == null) return;
        m_value.text = m_updateValue();
    }
    
    public static MACharacterInfoElement Create(Transform _parent, string _title, Func<string> _updateValue, Action _onClick = null)
    {
        var prefab = Resources.Load<MACharacterInfoElement>("_Prefabs/UI/MACharacterInfoElementUI");
        var instance = Instantiate(prefab, _parent);
        instance.Activate(_title, _updateValue, _onClick);
        return instance;
    }
    
}