using System;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public class CharacterExperience
{
	[Serializable]
	public class Reward
	{
		public string m_power;
		public bool m_hasBeenGiven;
		public int m_index;
		
		private const Char c_add = '+';
		private const Char c_multiply = '*';
		
		private const string c_rewardFollower = "follower";
		private const string c_rewardHealth = "health";
		private const string c_rewardExperience = "experience";
		private const string c_rewardStrength = "strength";
		private const string c_rewardSpeed = "speed";
		private const string c_rewardDefence = "defence";
		private const string c_rewardSword = "sword";
		private const string c_rewardMace = "mace";
		private const string c_rewardAxe = "axe";
		private const string c_rewardHammer = "hammer";
		
		public static string GetRewardDescription(string m_power)
		{
			var rSplit = m_power.Split(c_add, c_multiply);
			if (rSplit.Length != 2 || float.TryParse(rSplit[1], out float value) == false)
			{
				return m_power;
			}
			
			switch(rSplit[0].ToLower())
			{
				case c_rewardFollower: return "Allows hero to tag others to help out.";
				case c_rewardHealth: return "Increases the maximum Hero health.";
				case c_rewardExperience: return "Increases experience gained per kill.";
				case c_rewardStrength: return "Inccreases combat damage.";
				case c_rewardSpeed: return "Increases the Heroes speed.";
				case c_rewardDefence:  return "Increases number of hits Hero can take.";
				case c_rewardSword: return "Improves sword weilding.";
				case c_rewardMace: return "Improves mace weilding.";
				case c_rewardAxe: return "Improves axe weilding.";
				case c_rewardHammer: return "Improves hammer weilding.";
			}
			return m_power;
		}
		
		public bool Apply(MAHeroBase _hero)
	    {
			if(m_hasBeenGiven)
				return false;
			
	        var rSplit = m_power.Split(c_add, c_multiply);
	        if (rSplit.Length != 2 || float.TryParse(rSplit[1], out float value) == false)
	        {
	            Debug.LogError("Invalid reward format: " + m_power);
	            return false;
	        }
	        
	        bool isAdd = m_power.Contains(c_add);
	        bool isMultipliy = m_power.Contains(c_multiply);
	        
	        m_hasBeenGiven = true;
	        
	        bool executed = false;
	        switch (rSplit[0].ToLower().Trim())
	        {
	            case c_rewardFollower:
		            if(isAdd)
		            {
			            executed = true;
						_hero.HeroGameState.m_maxTags += value;
						MAUnlocks.Me.m_possessedCanTagWorkers = true;
						MAUnlocks.Me.m_possessedCanTagTourists = true;
						MAUnlocks.Me.m_possessedCanTagHeroes = true;
						MAUnlocks.Me.m_possessedCanTagAnimals = true;
		            }
	                break;
	            case c_rewardHealth:
	                if (isMultipliy)
	                {
						executed = true;
	                    _hero.HeroGameState.m_healthMultiplier *= value;
		            }
	                break;
	            case c_rewardExperience:
					if(isMultipliy)
					{
						executed = true;
						_hero.HeroGameState.m_characterExperience.m_experienceMultiplier *= value;
		            }
	                break;
	            case c_rewardStrength:
	                if(isMultipliy)
	                {
						executed = true;
	                    _hero.HeroGameState.m_attackMultiplier *= value;
		            }
	                break;
	            case c_rewardSpeed:
	                if(isMultipliy)
	                {
						executed = true;
						_hero.HeroGameState.m_speedMultiplier *= value;
	                }
	                break;
	            case c_rewardDefence:
	                if(isMultipliy)
	                {
						executed = true;
	                    _hero.HeroGameState.m_defenceMultiplier *= value;
	                }
	                break;
	            case c_rewardSword:
		            if(isMultipliy)
		            {
			            executed = true;
			            _hero.HeroGameState.m_swordMultiplier *= value;
		            }
		            break;
	            case c_rewardHammer:
		            if(isMultipliy)
		            {
			            executed = true;
			            _hero.HeroGameState.m_hammerMultiplier *= value;
		            }
		            break;
	            case c_rewardMace:
		            if(isMultipliy)
		            {
			            executed = true;
			            _hero.HeroGameState.m_maceMultiplier *= value;
		            }
		            break;
	            case c_rewardAxe:
		            if(isMultipliy)
		            {
			            executed = true;
			            _hero.HeroGameState.m_axeMultiplier *= value;
		            }
	                break;
	        }
	        
	        if(executed == false)
	        {
		        Debug.LogError($"HeroReward <{m_power}> - was not executed");	
	        }
	        return true;
	    }
	}
	
	public float m_experienceMultiplier = 1f;
	//public SDictionary<int,string> m_rewardsGainedMap = new();
	public SDictionary<int,Reward> m_rewards = new();
	
	public void ApplyChosenRewards(MAHeroBase _hero)
	{
		bool changed = false;
		foreach(var r in m_rewards.m_values)
		{
			changed |= r.Apply(_hero);
		}
		
		if(changed)
		{
			// Update weapon multipliers
			_hero.SetWeaponDesign(_hero.CharacterGameState.m_weaponDesign);
		}
	}
	
	public Reward RewardMatches(int _level, int _index)
	{
		if(m_rewards.TryGetValue(_level, out var reward))
		{
			if(reward == null) return null;
			
			if(reward.m_index == _index)
			{
				return reward;
			}
		}
		return null;
	}
	
	public bool RewardGiven(int _level)
	{
		// Level zero has no reward
		if(_level == 0)
			return true;
			
		if(m_rewards.TryGetValue(_level, out var reward))
		{
			return reward.m_hasBeenGiven;
		}
		return false;
	}
	
	public bool TryAssignReward(int _level, int _index, string _power)
	{
		if(m_rewards.TryGetValue(_level, out var reward) && reward != null)
		{
			if(reward.m_hasBeenGiven)
				return false;
		}
		
		if(reward == null)
		{
			reward = new Reward();
			m_rewards[_level] = reward;
		}
		
		reward.m_power = _power;
		reward.m_index = _index;
		return true;
	}
	
	public bool CanLevelup()
	{
		return RewardGiven(m_level) == false;
	}
	
	public float m_experience;
	public int m_level;
//	public float m_experienceThisLevel;
	
	public List<ExperienceLog> m_experienceLog = new();

	// These need to be refined and perhaps inserted into knack?
	public string GetCharacterLevelName()
	{
		switch(m_level)
		{
			case 0:
			case 1: return "Fledgling";
			case 2: return "Journeyman";
			case 3: return "Seasoned";
			case 4: return "Veteran";
		}
		return "TODO";
	}
	public int CharacterLevel => m_level;// Mathf.FloorToInt(LevellingFormula(m_experience));

	public CharacterExperience()
	{
		m_level = 0;
		m_experience = 0;
	}
	
	public void AddExperience(float _experienceValue, string _reason)
	{
		m_experience += _experienceValue;
//		m_experienceThisLevel += _experienceValue;

		//RecalculateLevel();
		LogExperience(_experienceValue, _reason);
	}

	public void LogExperience(float _experienceValue, string _reason)
	{
		if (m_experienceLog == null) m_experienceLog = new();
		
		_reason = _reason.RemoveWhiteSpaceAndToLower();
		int iMostRecentEntry = m_experienceLog.Count - 1;
		if (m_experienceLog.Count == 0 || m_experienceLog[iMostRecentEntry].Reason != _reason)
		{
			ExperienceLog charXPLog = new ExperienceLog(_experienceValue, _reason);
			m_experienceLog.Add(charXPLog);
			iMostRecentEntry = m_experienceLog.Count - 1;
		}
		m_experienceLog[iMostRecentEntry].m_experienceValueAdded += _experienceValue;
		m_experienceLog[iMostRecentEntry].m_timesAdded++;	
	}
	

	// protected float LevellingFormula(float _experience)
	// {
	// 	return 1 + Mathf.Sqrt(_experience);
	// }

	//just for logging how and why we gained experience
	[Serializable]
	public class ExperienceLog
	{
		static readonly List<(string reasonName, byte reasonId)> s_experienceTypes = new()
		{
			("fight", 1), ("kill", 2), ("possessedmove", 3), ("training", 4), ("debug", 10)
		};

		public float m_experienceValueAdded;
		public int m_timesAdded;
		public byte m_reason;

		public ExperienceLog(float _experienceValueAdded, string _reason)
		{
			m_experienceValueAdded = _experienceValueAdded;
			int i = s_experienceTypes.FindIndex(x => x.reasonName == _reason);
			if (i > -1)
				m_reason = s_experienceTypes[i].reasonId;
			else m_reason = byte.MaxValue;
		}
		
		public string Reason
		{
			get
			{
				int i = s_experienceTypes.FindIndex(x => x.reasonId == m_reason);
				if (i > -1)
					return s_experienceTypes[i].reasonName;
				else return "";
			}
		}
	}
}