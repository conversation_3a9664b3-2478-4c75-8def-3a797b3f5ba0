using System;
using TMPro;
using UnityEngine;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine.UI;

public class MACharacterInfoUI : NGBaseInfoGUI
{
    public GameObject m_possessButton;
    public GameObject m_recallButton;
    public GameObject m_levelupButton;
    public Image m_newLevelup;
    public GameObject m_editNameButton;
    
    public TMP_Text m_recallText;
    public Transform m_infoPanel;
    public Transform m_combatPanel;
    public Transform m_historyPanel;
    public TMP_Text m_title = null;
    
    [SerializeField]
    private float m_refreshTime = 0.5f;

    protected MATimer m_timer = null;
    protected MACharacterBase m_character = null;

    private List<MACharacterInfoElement> m_elements = new();

    private void Update()
    {
        if(m_character == null)
        {
            ClickedClose();
        }
        if (m_timer.IsFinished)
        {
            UpdateInfo();
        }
    }
    
    private void DestroyElements()
    {
        foreach(var field in m_elements)
        {
            Destroy(field.gameObject);
        }
        m_elements.Clear();
    }
    
    public void OnZoom()
    {
        if(m_character == null) return;
        
        MAParser.MoveCamera(m_character.transform.position, 20f);
        ClickedClose();
    }
    
    public void OnEditName()
    {
        if(m_character == null || NGRename.Me != null) return;
        
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        NGRename.Create(m_character.Name, GotNewName);
    }
    
    public void GotNewName(string _newName)
    {
        if(m_character == null) return;
        
        m_character.Name = _newName;
    }
    
    public void OnRecall()
    {
        var action = MABuildingWorkerPanelLine.GetWorkerActionButton(m_character);
        
        if(action.action == null) return;
        
        action.action();
    }
    
    public void OnPossess()
    {
        if(GameManager.Me.CanPossess(m_character) == false) return;
        
        GameManager.Me.PossessObject(m_character);
        ClickedClose();
    }
    public void OnLevelup()
    {
        ClickedClose();
        MAHeroLevelupGUI.Create(m_character as MAHeroBase);
    }

    void Activate(MACharacterBase _character)
    {
        m_timer = new(m_refreshTime);
        
        base.Activate("");
        
        if (_character != null)
        {
            DestroyElements();
            
            m_character = _character;
            
            Setup();
                        
            UpdateInfo();
        }
    }
    
    private void AddInfo(string _name, Func<string> _value, Action _onClick = null) { m_elements.Add(MACharacterInfoElement.Create(m_infoPanel, _name, _value, _onClick)); }
    private void AddCombat(string _name, Func<string> _value, Action _onClick = null) { m_elements.Add(MACharacterInfoElement.Create(m_combatPanel, _name, _value, _onClick)); }
    private void AddHistory(string _name, Func<string> _value, Action _onClick = null) { m_elements.Add(MACharacterInfoElement.Create(m_historyPanel, _name, _value, _onClick)); }
    
    private string GetAllocatedBuilding(BCBase _base)
    {
        if(_base == null)
            return "None";
        if(_base.Building) return _base.Building.GetBuildingTitle();
        return _base.Title;
    }
    private string GetAllocatedJob(BCBase _base)
    {
        if(_base == null)
            return "None";
        return _base.m_info.GetJobTitle();
    }
    
    private Action GetBuildingZoomAction(BCBase _base)
    {
        if(_base == null) return null;
        return () => { _base.Highlight(); this.ClickedClose(); };
    }
    
    public static string GetStateName(MACharacterBase _character)
    {
        string state = "None";
        if(_character is MAWorker)
        {
            state = _character.PeepAction.ToString();
        }
        else if(_character.CharacterUpdateState != null)
        {
            state = _character.CharacterUpdateState.State;
        }
        return Regex.Replace(state, "([A-Z])", " $1").Trim();
    }
    
    private string GetStrength()
    {
        var baseDamage = m_character.BaseDamageMultiplier;
        
        if(baseDamage == 1f)
            return "Basic";
        
        return $"{m_character.BaseDamageMultiplier.ConvertMultiplierToString()}";
    }
    
    private void SetupSimple()
    {
        m_combatPanel.gameObject.SetActive(false);
        m_historyPanel.gameObject.SetActive(false);
        
        string description = "";
        
        if(m_character is MAWorker)
            description = (m_character as MAWorker).m_workerInfo.m_description;
        else
            description = m_character.CreatureInfo.m_introDescription;
            
        if(description.IsNullOrWhiteSpace())
        {
            description = "Their past, motivations, and allegiance remain unknown.";
        }
        
        AddInfo(null, () => "-");//Spacer
        AddInfo(null, () => description);
    }
    
    private void SetupHero(GameState_Character _gameState)
    {
        var characterType = m_character.CreatureInfo.m_displayName;
        AddInfo("Type", () => characterType);
        AddInfo("State", () => GetStateName(m_character));
        AddInfo("Level", () => m_character.GetCharacterLevelName());
        AddInfo("Speed", () => m_character.SpeedDescription);
        AddInfo("Health", () => m_character.HealthDescription);
        AddInfo("Experience", () => $"{m_character.Experience:N0}/{m_character.GetExperienceRequiredForNextLevel():N0}");
        AddInfo("Home", () => GetAllocatedBuilding(m_character.Home), GetBuildingZoomAction(m_character.Home));

        AddCombat("Strength", () => GetStrength());
        AddCombat("Armour", () => m_character.ArmourDescription);
        AddCombat("Armour Type", () => (_gameState.m_armourDesign != null && _gameState.m_armourDesign.HasDesign) ? _gameState.m_armourDesign.GetDominantMaterial() : "None");    
        AddCombat("Weapon Type", () => (_gameState.m_weaponDesign != null && _gameState.m_weaponDesign.HasDesign) ? _gameState.m_weaponDesign.GetDominantMaterial() : "None");
        AddCombat("Weapon Damage", () => m_character.Weapon == null ? "None" : (m_character.Weapon.DamageMultiplier+1).ConvertMultiplierToString());
        AddCombat("Max Followers", () => (m_character as MAHeroBase).HeroGameState.m_maxTags.ToString());

        AddHistory("Kills", () => _gameState.m_kills.ToString());
        AddHistory("Knocked Down", () => _gameState.m_timesKnockedDown.ToString());

        // Only show if we hired this character
        if (_gameState.m_dayHired >= 0) AddHistory("Hired", () => $"Day { _gameState.m_dayHired }");
        AddHistory("Possessed Time", () => _gameState.m_timePossessed.FormatTime());
        AddHistory("Freewill Time", () => _gameState.m_timeUnpossessed.FormatTime());
    }
    
    private void SetupWorker(GameState_Character _gameState)
    {
        var characterType = (m_character as MAWorker).m_workerInfo.m_displayName;
        AddInfo("Type", () => characterType);
        AddInfo("State", () => GetStateName(m_character));
        AddInfo("Level", () => m_character.GetCharacterLevelName());
        AddInfo("Speed", () => m_character.SpeedDescription);
        AddInfo("Health", () => m_character.HealthDescription);
        AddInfo("Home", () => GetAllocatedBuilding(m_character.Home), GetBuildingZoomAction(m_character.Home));
        AddInfo("Job", () => GetAllocatedJob(m_character.Job), GetBuildingZoomAction(m_character.Job));
        
        AddCombat("Strength", () => GetStrength());
        AddCombat("Armour", () => m_character.ArmourDescription);
        
        // Only show if we hired this character
        if (_gameState.m_dayHired >= 0) AddHistory("Hired", () => $"Day { _gameState.m_dayHired }");
        AddHistory("Possessed Time", () => _gameState.m_timePossessed.FormatTime());
        AddHistory("Freewill Time", () => _gameState.m_timeUnpossessed.FormatTime());
    }
    
    private void SetupDog(GameState_Character _gameState)
    {
        m_combatPanel.gameObject.SetActive(false);
        
        AddInfo("Type", () => /*characterType*/"Husky Dog");
        AddInfo("State", () => /*m_character.PeepAction.ToString()*/"Panting");
        AddInfo("Speed", () => m_character.SpeedDescription);
        AddHistory("Possessed Time", () => _gameState.m_timePossessed.FormatTime());
        AddHistory("Distance Travelled", () => $"{_gameState.m_distanceTravelled:F2}m");
        AddHistory("Items Found", () => _gameState.m_itemsCollected.ToString());
    }
    
    private void Setup()
    {
        var gameState = m_character.CharacterGameState;

        if (m_character is MADog)
        {
            SetupDog(gameState);
        }
        else if(m_character is MAHeroBase)
        {
            SetupHero(gameState);
        }
        else if(m_character is MACreatureBase)
        {
            SetupSimple();
        }
        else if(m_character is MATourist || m_character is MAHoodedCharacter || m_character is MAQuestGiver)
        {
            SetupSimple();
        }
        else if(m_character is MAWorker)
        {
            SetupWorker(gameState);
        }
    }
    
    private void UpdateInfo()
    {
        bool canPossess = GameManager.Me.CanPossess(m_character);
        m_possessButton.SetActive(canPossess);
        var hero = m_character as MAHeroBase;
        m_levelupButton.SetActive(hero != null);
        if (hero)
        {
            m_newLevelup.enabled = hero.HeroGameState.m_characterExperience.CanLevelup();
        }
        if (m_character == null)
        {
            ClickedClose();
            return;
        }
        
        bool isQuestDog = m_character is MADog && m_character.Name == "QuestDog";

        // We should move this to either knack or have a virtual function in MACharacterBase
        bool canRecall = m_character is MAWorker && m_character is not MAQuestGiver && m_character is not MATourist;
        bool canRename = m_character is not MACreatureBase && m_character is not MAQuestGiver && m_character is not MATourist && !isQuestDog;
        
        var action = MABuildingWorkerPanelLine.GetWorkerActionButton(m_character);
        
        if(action.description.IsNullOrWhiteSpace() == false)
        {
            m_recallText.text = action.description;
        }
        m_recallButton.SetActive(action.description.IsNullOrWhiteSpace() == false);
        m_editNameButton.SetActive(canRename);
        
        foreach(var field in m_elements)
        {
            field.UpdateValue();
        }
        
        m_title.text = m_character.Name;
        
        m_timer.Set(m_refreshTime);
    }
    
    public static MACharacterInfoUI Create(MACharacterBase _character)
    {
        if(_character == null) return null;
        
        if(s_infoShowing != null)
        {
            s_infoShowing.DestroyMe();
            s_infoShowing = null;
        }
        
        var go = Instantiate(NGManager.Me.m_MACharacterInfoGUIPrefab, NGManager.Me.NGInfoGUIHolder);
        var big = go.GetComponent<MACharacterInfoUI>();
        big.Activate(_character);
        s_infoShowing = big;
        return big;
    }
}