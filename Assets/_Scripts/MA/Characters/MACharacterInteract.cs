using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MACharacterInteract : MonoBehaviour
{
	public float m_specialAbilityHealCharacterPerSecond = -1;
	public bool m_destroyOnDeactivate = false;
	private Coroutine m_despawnRoutine = null;
	
	public enum InteractSpotState
	{
		Invalid = 0,
		IsOccupied,
		IsReserved,
		IsActive,
		IsFinished,
	}

	[Serializable]
	public class InteractionType
	{
		public string m_animSetClipName = "Wave";
		public float m_minimumDuration = 0;
	}
	
	public InteractionType[] m_animSetClipNames = new[] { new InteractionType() };
	
	public GameState_Interact m_saveData = null;
	
	public float m_reduceWantsToPlay = 0.25f;
	public float m_lookAtSpeedDegPerSec = 100f;
	public float m_usedRecentlyDuration = 30f;
	
	public SphereCollider m_activationTrigger = null;

	protected Dictionary<int, float> m_characterTimeStamps = new();
	
	private Coroutine m_stareRoutine = null;
	bool m_animFinished = false;
	
	public float ActivationRadius => m_activationTrigger == null ? 0f : m_activationTrigger.radius;
	
	private void Awake()
	{
		m_activationTrigger = GetComponent<SphereCollider>();
	}
	
	private void Start()
	{
		Load();
	}
	

	public float ActivateInteract(MACharacterBase _character)
	{
		SpotData spot = null;
		int m_activeCharacterId = _character.m_ID;
		for (int i = m_saveData.m_spotList.Count - 1; i >= 0; i--)
		{
			SpotData savedSpot = m_saveData.m_spotList[i];
			if (savedSpot.OccupierCharacterId == m_activeCharacterId)
			{
				spot = savedSpot;
			}
		}

//override for healing log?
		if (spot == null)
		{
			MAChicken chicken = GetComponent<MAChicken>();
			if (chicken != null)
			{
				//_character.LookAt(transform.position, 50f);
			}

			SetFinished(m_activeCharacterId);
			return 0f;
		}

		if (m_characterTimeStamps.TryAdd(_character.m_ID, Time.time) == false)
		{
			m_characterTimeStamps[_character.m_ID] = Time.time;
		}

		m_durationInteracting = 0f;
		float minDuration = 0f;
		if (m_animSetClipNames != null && m_animSetClipNames.Length > 0)
		{
			Transform charTr = _character.transform;
			if ((charTr.position.GetXZ() - spot.m_spot.transform.position.GetXZ()).magnitude > 2f)
			{
				int issue = 0;
			}
			//charTr.position = spot.m_spot.transform.position;
			_character.LookAt(transform.position + spot.m_spot.transform.forward * 10f, m_lookAtSpeedDegPerSec);
			_character.BlendAnimatorLayerWeight("Combat", 0);
			_character.m_nav.Pause(true, true);
			_character.SetCanBePushed(false);
			m_animFinished = false;
			InteractionType animSetClipName = m_animSetClipNames.PickRandom();
			_character.PlayAnim(animSetClipName.m_animSetClipName, OnFinished);
			
			minDuration = animSetClipName.m_minimumDuration;

			spot.Activate();

			void OnFinished(MAAnimationSet.AnimationParams info, bool _interrupted)
			{
				m_animFinished = true;

				bool minDurationElapsed = animSetClipName.m_minimumDuration <= 0 || spot.m_durationInteracting >= animSetClipName.m_minimumDuration;
				if (_character != null)
				{
					if(minDurationElapsed)
					{
						_character.ResetHealthRecoveryRate();
					}
				}

				if (minDurationElapsed)
				{
					SetFinished(m_activeCharacterId);
				}
			}
		}
		else
		{
			MAChicken chicken = GetComponent<MAChicken>();
			if (chicken != null)
			{
				if (m_stareRoutine != null)
				{
					StopCoroutine(m_stareRoutine);
					m_stareRoutine = null;
				}

				m_stareRoutine = StartCoroutine(StareAtObject(_character));
			}
			else
			{
				spot.Activate();
				if (m_stareRoutine != null)
				{
					StopCoroutine(m_stareRoutine);
					m_stareRoutine = null;
				}

				m_stareRoutine = StartCoroutine(StareAtObject(_character));
			}
		}

		if (m_specialAbilityHealCharacterPerSecond > 0)
		{
			_character.SetHealthRecoverRateOverride(m_specialAbilityHealCharacterPerSecond);
			_character.Energy = 1f;
		}
		return minDuration;
	}

	private void Update()
	{
		m_durationInteracting += Time.deltaTime;

		// if (m_minimumDuration > 0 && m_durationInteracting > m_minimumDuration && m_animFinished)
		// {
		// 	SetFinished(m_activeCharacterId);
		// }
	}

	private float m_durationInteracting = 0f;

	public void DeactivateInteract(MACharacterBase _character)
	{
		_character.CharacterGameState.m_wantsToPlay = Mathf.Clamp01(_character.CharacterGameState.m_wantsToPlay - m_reduceWantsToPlay);
		for (int i = m_saveData.m_spotList.Count - 1; i >= 0; i--)
		{
			SpotData savedSpot = m_saveData.m_spotList[i];
			if (savedSpot.OccupierCharacterId == _character.m_ID)
			{
				savedSpot.Clear();
			}
		}
		
		_character.BlendAnimatorLayerWeight("Combat", 0);
		_character.m_nav.Unpause();
		_character.SetCanBePushed(true);
		_character.StopCurrentAnimation();
		
		Collider[] colliders = GetComponentsInChildren<Collider>();
		foreach (var characterCollider in _character.m_mainColliders)
		{
			foreach (var interactCollider in colliders)
			{					
				Physics.IgnoreCollision(characterCollider, interactCollider, false);
			}					
		}

		if (m_destroyOnDeactivate)
		{
			DespawnObject();
		}
		
		if (m_specialAbilityHealCharacterPerSecond > 0)
		{
			_character.ResetHealthRecoveryRate();
		}
	}
	
	private void DespawnObject()
	{
		m_despawnRoutine = StartCoroutine(DespawnRoutine());
	}

	private IEnumerator DespawnRoutine()
	{
		const float duration = 1.25f;
		float timeTaken = duration;
		while (timeTaken > 0f)
		{			
			yield return new WaitForEndOfFrame();
			timeTaken -= Time.deltaTime;
			transform.localScale = Vector3.one * (timeTaken / duration);
		}
		Destroy(gameObject);
		yield break;
		m_despawnRoutine = null;
	}
	/// <summary>
	///character is passed-in simply because we might want some conditions as to what kind of character hangs out with who
	/// </summary>
	public bool HasNewSpaceForCharacter(MACharacterBase _character)
	{
		bool available = false;
		for (int i = m_saveData.m_spotList.Count - 1; i >= 0; i--)
		{
			SpotData spotData = m_saveData.m_spotList[i];
			if (spotData.OccupierCharacterId == _character.m_ID)
			{
				return false;
			}
			available |= spotData.IsAvailable;
		}
		return available || GetComponent<MAChicken>();
	}
	
	public SpotData TryReserveForCharacter(MACharacterBase _character)
	{
		int id = _character.m_ID;
		SpotData spotUsed = null;
		List<SpotData> availableSpots = new List<SpotData>();
		for (int i = m_saveData.m_spotList.Count - 1; i >= 0; i--)
		{
			SpotData savedSpot = m_saveData.m_spotList[i];
			if (savedSpot.OccupierCharacterId == id)
			{
				spotUsed = savedSpot;
				if(savedSpot.m_spot.IsPositionVisible(_character.transform.position, out Vector3 safePos))
				{
					availableSpots.Add(savedSpot);
				}
				else
				{					
					NGDecoration magicLog = GetComponent<NGDecoration>();
					if(magicLog != null && magicLog.Name == "MA_HeroLog")
					{
						availableSpots.Add(savedSpot);
					}
				}
			}
			else if (savedSpot.IsAvailable)
			{
				if(savedSpot.m_spot.IsPositionVisible(_character.transform.position, out Vector3 safePos))
				{
					availableSpots.Add(savedSpot);
				}
				else
				{
					NGDecoration magicLog = GetComponent<NGDecoration>();
					if(magicLog != null && magicLog.Name == "MA_HeroLog")
					{
						availableSpots.Add(savedSpot);
					}
				}
			}
		}

		SpotData random = availableSpots.PickRandom();
		random?.Reserve(id, spotUsed != null ? spotUsed.State : InteractSpotState.IsReserved);
		spotUsed = random;
		bool valid = RemoveDuplicates(spotUsed);
		return valid ? spotUsed : null;
	}

	private SpotData TryOccupyWithCharacter(MACharacterBase _character)
	{
		int id = _character.m_ID;
		SpotData spotUsed = null;
		for (int i = m_saveData.m_spotList.Count - 1; i >= 0 && spotUsed == null; i--)
		{
			SpotData savedSpot = m_saveData.m_spotList[i];
			if (savedSpot.OccupierCharacterId == id)
			{
				switch (savedSpot.State)
				{
					case InteractSpotState.IsReserved:
						savedSpot.Reserve(id, InteractSpotState.IsOccupied);
						spotUsed = savedSpot;
						//make do
						break;
					case InteractSpotState.IsOccupied:
						spotUsed = savedSpot;
						//make do? could be loading?
						break;
				}
			}
		}
		
		bool valid = RemoveDuplicates(spotUsed);
		return spotUsed;
	}
	
	public bool TryRemoveCharacter(MACharacterBase _character)
	{
		int id = _character.m_ID;
		SpotData spotUsed = null;
		for (int i = m_saveData.m_spotList.Count - 1; i >= 0; i--)
		{
			SpotData savedSpot = m_saveData.m_spotList[i];
			if (savedSpot.OccupierCharacterId == id)
			{
				savedSpot.Clear();
				spotUsed = savedSpot;
				break;
			}
		}

		bool valid = RemoveDuplicates(spotUsed);
		return valid == false;
	}

	/// <summary>
	/// Verifies and cleans up data, ensuring we enver have duplicate character Ids in the spot reserve list
	/// </summary>
	private bool RemoveDuplicates(SpotData spotUsed)
	{
		if (spotUsed != null)
		{
			foreach (SpotData spotSaveData in m_saveData.m_spotList)
			{
				if (spotSaveData.OccupierCharacterId == spotUsed.OccupierCharacterId && spotSaveData != spotUsed)
				{
					spotSaveData.Clear();
				}
			}
			return true;
		}
		return false;
	}

	public void Deregister(MAInteractSpot _spot)
	{
		_spot.m_spotData.Finish();

		if (m_saveData != null)
		{		
			m_saveData.m_spotList.Remove(_spot.m_spotData);
		}
	}
	
	public bool Finish(MACharacterBase _character)
	{
		if (TryRemoveCharacter(_character))
		{
			//_character.StopCurrentAnimation();
		}
		return true;
	}
	
	private void SetFinished(int _characterId)
	{
		//MACharacterBase _character = NGManager.Me.FindCharacterByID(_characterId);
		for (int i = m_saveData.m_spotList.Count - 1; i >= 0; i--)
		{
			SpotData savedSpot = m_saveData.m_spotList[i];
			if (savedSpot.OccupierCharacterId == _characterId)
			{
				savedSpot.Finish();
				//m_activeCharacterId
				break;
			}
		}
	}
	
	public bool IsFinished(MACharacterBase _character)
	{
		for (int i = m_saveData.m_spotList.Count - 1; i >= 0; i--)
		{
			SpotData savedSpot = m_saveData.m_spotList[i];
			if (savedSpot.OccupierCharacterId == _character.m_ID)
			{
				return savedSpot.State == InteractSpotState.IsFinished;
			}
		}
		return true;
	}

	public bool UsedRecently(MACharacterBase _character)
	{
		return m_characterTimeStamps.TryGetValue(_character.m_ID, out float time) && Time.time - time < m_usedRecentlyDuration;
	}
	
	private void OnTriggerEnter(Collider _other)
	{
		MACharacterBase characterBase = _other.GetComponent<MACharacterBase>();
		if (characterBase != null)
		{
			SpotData spotData = TryOccupyWithCharacter(characterBase);
			if (spotData != null)
			{
				characterBase.NavigateToPosition(spotData.m_spot.transform.position);
				characterBase.LookAt(spotData.m_spot.transform.forward * 10f, 180f);
				
				Collider[] colliders = GetComponentsInChildren<Collider>();
				foreach (var characterCollider in characterBase.m_mainColliders)
				{
					foreach (var interactCollider in colliders)
					{					
						Physics.IgnoreCollision(characterCollider, interactCollider, true);
					}					
				}
			}
		}
	}
	
	private IEnumerator StareAtObject(MACharacterBase _character)
	{
		_character.LookAt(transform.position, m_lookAtSpeedDegPerSec);
		yield return new WaitForSeconds(5);
		SetFinished(_character.m_ID);
	}

	public static void LoadAll()
	{
		foreach (var point in NGDecorationInfoManager.Me.m_decorationHolder.GetComponentsInChildren<MACharacterInteract>())
		{
			point.Load();
		}

		var saves = SaveData;
		for (int i = saves.Count - 1; i >= 0; i--)
		{
			if (saves[i].m_interact == null)
			{
				saves.RemoveAt(i);
			}
		}
	}
	
	public void Load()
	{ 
		if(Application.isEditor && Application.isPlaying == false) return;
		
		if (gameObject.activeInHierarchy == false)
		{
			//m_deferLoad = true;
		}
		else
		{
			Vector3 pos = transform.position;
			float closestDist = float.MaxValue;
			GameState_Interact closestSavedSpot = null;
			foreach (var savedSpot in SaveData)
			{
				float dist = (savedSpot.m_position - pos).xzSqrMagnitude();
				if (closestDist > dist)
				{
					closestDist = dist;
					closestSavedSpot = savedSpot;
				}
				// if (m_interactPositions.Count > savedSpot.m_index) //m_interactTransforms.Length		
			}
			
			SaveData.Remove(m_saveData);
			if(closestSavedSpot != null && (closestSavedSpot.m_position - pos).xzSqrMagnitude() < 5f)
			{
				m_saveData = closestSavedSpot;
				SaveData.Remove(closestSavedSpot);
			}
			else
			{
				m_saveData = new GameState_Interact();
			}

			m_saveData.m_position = pos;
			m_saveData.m_interact = this;
			SaveData.Insert(0, m_saveData);

			List<MAInteractSpot> interactTransforms = new();
			GetComponentsInChildren(interactTransforms);
			if (interactTransforms.Count == 0)
			{
				interactTransforms.Add(gameObject.AddComponent<MAInteractSpot>());
			}
			
			int iInteract = 0;
			while (m_saveData.m_spotList.Count < interactTransforms.Count)
			{
				m_saveData.m_spotList.Add(new SpotData());
				interactTransforms[iInteract].m_spotData = m_saveData.m_spotList[^1];
				m_saveData.m_spotList[^1].m_spot = interactTransforms[iInteract++];
			}
			
			for(int i = m_saveData.m_spotList.Count - 1; i >= 0; i--)
			{
				SpotData savedSpot = m_saveData.m_spotList[i];

				if (i >= interactTransforms.Count)
				{
					m_saveData.m_spotList.RemoveAt(i);
					continue;
				}
				
				if(savedSpot.IsAvailable && 
				   (NGManager.Me.TryFindCharacterByID(savedSpot.OccupierCharacterId, out MACharacterBase character) == false || character == null))
				{
					savedSpot.Clear();
				}
			}
		}
	}
	
	private static List<GameState_Interact> m_saveDataGlobal = new(); //(not saved)
	public static List<GameState_Interact> SaveData => m_saveDataGlobal;//GameManager.Me.m_state.m_savedInteractionPoints

	public void Save()
	{
		if (m_saveData != null)
		{
			m_saveData.m_position = transform.position;
		}
	}

	public static void SaveAll()
	{
		foreach (var point in SaveData)
		{
			if (point.m_interact != null)
			{
				point.m_interact.Save();
			}
		}
	}

	[Serializable]
	public class SpotData
	{
		[NonSerialized] public MAInteractSpot m_spot;
		
		[SerializeField]
		private int m_state;
		
		[SerializeField]
		private int m_occupierCharacterId = -1;
		public int OccupierCharacterId => m_occupierCharacterId;
		
		public float m_durationInteracting = 0f;

		public InteractSpotState State => Enum.TryParse(m_state.ToString(), out InteractSpotState state) ? state : InteractSpotState.Invalid;
		
		public void Reserve(int _characterId, InteractSpotState _spotState)
		{
			m_occupierCharacterId = _characterId;
			m_state = (int)_spotState;
		}
		
		public void Activate()
		{
			m_state = (int)InteractSpotState.IsActive;
		}
		
		public void Finish()
		{
			m_state = (int)InteractSpotState.IsFinished;
		}

		public void Clear()
		{
			m_occupierCharacterId = -1;
			m_state = 0;
		}

		public bool IsAvailable => m_occupierCharacterId == -1;
	}
	
	[Serializable]
	public class GameState_Interact
	{
		[NonSerialized] public MACharacterInteract m_interact;
		public Vector3 m_position;
		public List<SpotData> m_spotList = new();
	}
}