using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class MAInteractSpot : MonoBehaviour
{
	public Quaternion m_constantLocalRotation = Quaternion.identity;
	public Vector3 m_constantLocalPos = Vector3.zero;
	
	public MACharacterInteract.SpotData m_spotData = null;
	
	public bool m_showDebugMarker = false;
	
	private Transform m_editorIndicatorDummy = null;
	private void Awake()
	{
		Transform tr = transform;
		m_constantLocalRotation = tr.parent.rotation;
		m_constantLocalPos = tr.localPosition.GetXZ();
#if UNITY_EDITOR
		UpdateDummy();
#endif
	}

	private void OnEnable()
	{
		MACharacterInteract parentInteract = transform.parent.GetComponent<MACharacterInteract>();
		if (parentInteract != null)
		{
			parentInteract.Load();
		}
	}

	private void Update()
	{
		//if (m_editorIndicatorDummy || (m_spotSaveData != null && m_spotSaveData.State != MACharacterInteract.InteractSpotState.Invalid))
		{
			Transform tr = transform;
			Transform p = tr.parent;
			//var parentLocRot = p.localRotation;
			//tr.rotation = m_constantLocalRotation;//Quaternion.Inverse(parentLocRot) * m_constantLocalRotation * tr.localRotation;
			//m_constantLocalRotation = parentLocRot;
			tr.localPosition = Vector3.zero;
			tr.position = (p.position + m_constantLocalPos).GroundPosition();
		}
		
#if UNITY_EDITOR
		UpdateDummy();
#endif
	}

	private void OnDestroy()
	{
		MACharacterInteract parentInteract = transform.parent?.GetComponent<MACharacterInteract>();
		if (parentInteract != null)
		{
			parentInteract.Deregister(this);
		}
	}

	public bool IsPositionVisible(Vector3 _fromOrigin, out Vector3 _safePos)
	{
		_safePos = _fromOrigin;
		if (MACharacterBase.IsTargetVisible(transform.position, _fromOrigin) == false)
		{
			_safePos = transform.position;
			return true;
		}
		return false;
	}
	
	public void UpdateDummy()
	{
		if(m_showDebugMarker)
        {
        	if (m_editorIndicatorDummy == null)
        	{
	            GameObject dummyObject = Resources.Load<GameObject>("_Prefabs/Spawns/Editor/EditorIndicatorSphere");
        		if (dummyObject != null)
        		{
	                Transform tr = transform;
        			m_editorIndicatorDummy = Instantiate(dummyObject, tr.parent, false).transform;
        			m_editorIndicatorDummy.position = tr.position;
        		}
                else
                {
	                m_showDebugMarker = false;
                }
        	}
        	else
        	{
        		m_editorIndicatorDummy.position = transform.position;
        	}
        }
        else
        {
        	if (m_editorIndicatorDummy != null)
        	{
        		Destroy(m_editorIndicatorDummy.gameObject);
        		m_editorIndicatorDummy = null;
        	}
        }
	}
}


#if UNITY_EDITOR
[CustomEditor(typeof(MAInteractSpot))]
public class MAInteractSpotEditor : Editor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
		
		MAInteractSpot spot = target as MAInteractSpot;

		spot.UpdateDummy();
	}
}

#endif