using UnityEngine;
//TODO: TS - remove from werewold, has been supercededs by proper ragdoll collisions/attacks/etc
public class CreatureCollision : MonoBehaviour
{
    private Collider m_collider = null;

    private void Awake()
    {
        m_collider = GetComponent<Collider>();
    }

    private void HandleCollision(Collision _other)
    {
        Debug.Log($"{GetType().Name} - collision with: {_other.gameObject.name}");
        // HumanlikeCollision otherHumanlike = _other.gameObject.GetComponent<HumanlikeCollision>();
        // if (otherHumanlike == null)
        // {
        //     Physics.IgnoreCollision(m_collider, _other.collider);
        // }
    }
    
    private void OnCollisionEnter(Collision _other)
    {
        HandleCollision(_other);
    }

    private void OnCollisionStay(Collision _other)
    {
        HandleCollision(_other);
    }

    private void OnCollisionExit(Collision _other)
    {
        HandleCollision(_other);
    }
}
