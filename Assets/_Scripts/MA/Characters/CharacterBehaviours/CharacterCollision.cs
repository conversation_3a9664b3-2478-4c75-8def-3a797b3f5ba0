using UnityEngine;
using System;

public class CharacterCollision : MonoBehaviour
{
    private Collider m_collider = null;

    public Action<Collision> m_onCollisionEnter = null;
    public Action<Collision> m_onCollisionStay = null;
    public Action<Collision> m_onCollisionExit = null;
    
    public string[] m_exclusiveColliderPairNames = new string[] { "BodyCollider" };
    
    private void Awake()
    {
        m_collider = GetComponent<Collider>();
    }

    private bool AssertContacts(ContactPoint[] _contacts)
    {
        if (m_exclusiveColliderPairNames == null || m_exclusiveColliderPairNames.Length == 0)
        {
            return true;
        }
        
        foreach(var contact in _contacts)
        {
            foreach(var colliderName in m_exclusiveColliderPairNames)
            {
                if(contact.otherCollider.name == colliderName && contact.thisCollider.name == colliderName)
                {
                    return true;
                }
            }
        }
        return false;
    }
    
    private void OnCollisionEnter(Collision _collision)
    {
		if (m_onCollisionEnter != null)
		{
           if(AssertContacts(_collision.contacts) == false)
                return;
           
            //Debug.Log($"{GetType().Name} - OnCollisionEnter '{name}' with: {_collision.gameObject.name}");
            m_onCollisionEnter.Invoke(_collision);
		}
    }

    private void OnCollisionStay(Collision _collision)
    {
		if (m_onCollisionStay != null)
		{
            if(AssertContacts(_collision.contacts) == false)
                return;

            //Debug.Log($"{GetType().Name} - OnCollisionStay '{name}' with: {_collision.gameObject.name}");
            m_onCollisionStay.Invoke(_collision);
		}
    }

    private void OnCollisionExit(Collision _collision)
    {
		if (m_onCollisionExit != null)
		{
            if(AssertContacts(_collision.contacts) == false)
                return;

            //Debug.Log($"{GetType().Name} - OnCollisionExit '{name}' with: {_collision.gameObject.name}");
            m_onCollisionExit.Invoke(_collision);
		}
    }
}
