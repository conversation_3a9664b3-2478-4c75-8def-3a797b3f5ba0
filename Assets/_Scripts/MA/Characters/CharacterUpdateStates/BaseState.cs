using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[Serializable]
public abstract class BaseState
{
	public virtual KeyboardShortcutManager.EShortcutType KeyboardShortcut => KeyboardShortcutManager.EShortcutType.HeldObjects;
	
	[ReadOnlyInspector][SerializeField]
	private string m_state;
	public string State
	{
		get => m_state;
		protected set => m_state = value != CharacterStates.Idle ? value : m_state;
	}

	public virtual void OnPreEnter() { }
	public virtual void OnEnter() { }
	public virtual void OnUpdate() { }
	public virtual void OnFixedUpdate() { }
	public virtual void OnExit() { }
	
	public virtual void SetupCharacterState() { }
	
	public abstract bool ApplyState(string _state);
	
	public BaseState(string _state)
	{
		m_state = _state;
	}

	public virtual float WaitingTimeLeft => -1f;
	
	// public virtual void OnSave(){}
}
