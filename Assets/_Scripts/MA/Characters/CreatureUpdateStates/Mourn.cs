using System;
using UnityEngine;
using Vector3 = UnityEngine.Vector3;

namespace MACharacterStates
{
	[Serializable]
	public class Mourn : CommonState
	{
		public Mourn(string _state, MACharacterBase _character) : base(_state, _character) { }

		private float m_timeTakenToArrive = 0f;
		private bool m_arrived = false;
		private MATimer m_posRefreshTimer = new MATimer();
		
		public override void OnEnter()
		{
			base.OnEnter();

			FindPosAndGo();
		}

		private void FindPosAndGo()
		{
			Vector3 pos = Vector3.zero;
			float rad = 0f;
			if (m_character.Leader != null)
			{
				pos = m_character.Leader.transform.position;
				rad = (m_character.Leader as MACharacterBase)?.m_bodyToBodyCollider?.radius ?? 0f;
			}
			else
				pos = m_character.GameState.m_destPos;
			
			float allowedDistance = rad +
			                        m_character.m_bodyToBodyCollider.radius +
			                        m_character.CharacterSettings.m_followModeDistance;
			
			pos = pos + (m_character.transform.position - pos).normalized * allowedDistance;

			m_character.SetMoveToPosition(pos);
			m_timeTakenToArrive = 0f;
			m_posRefreshTimer.Set(4f);
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
			
			if (m_posRefreshTimer.IsFinished)
			{
				FindPosAndGo();
			}

			if (HasAgentArrived())
			{
				if (m_gameStateData.m_timeInState - m_timeTakenToArrive > m_character.CharacterSettings.m_mournTime)
				{
					MACharacterBase leader = m_character.Leader as MACharacterBase;
					if (leader == null || leader.CharacterGameState.m_consciousness != (int)MACharacterBase.Consciousness.UnconsciousDead)
					{
						ApplyState(CharacterStates.RoamForTarget);
						return;
					}
				}

				if (m_arrived == false)
				{
					m_character.PlayAnim("Mourn");
					m_arrived = true;
				}
			}
			else
			{
				m_timeTakenToArrive += Time.deltaTime;
			}
		}

		public override void OnExit()
		{
			m_character.StopCurrentAnimation(false);
			m_character.Leader = null;
			base.OnExit();
		}
	}
}