using System;
using BehaviourDesign;
using UnityEngine;
using Random = UnityEngine.Random;
using Vector3 = UnityEngine.Vector3;

namespace MACharacterStates
{
	public class Interaction : CommonState
	{ 
		public Interaction(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		private MATimer m_maTimer = new MATimer();
		private MATimer m_evaluateTarget = new MATimer();
		private const float m_tickEvaluateTarget = 1f;
		
		private const float m_maxInteractTimeAllowed = 10f;
		private MACharacterInteract m_interact = null;
		private MACharacterInteract.SpotData m_spot = null;
		private TargetObject m_target = null;
		
		public override void OnEnter()
		{
			base.OnEnter();

			m_target = m_character.TargetObject;

			TargetObject targetObject = m_character.TargetObject;
			if (targetObject == null)
			{
				ApplyState(CharacterStates.RoamForTarget);
				return;
			}
			
			m_maTimer.Set(m_maxInteractTimeAllowed);

			m_interact = targetObject.GetComponent<MACharacterInteract>();
			Vector3 destinationPos;
			if (m_interact != null)
			{
				m_spot = m_interact?.TryReserveForCharacter(m_character);
				if (m_spot == null)
				{
					destinationPos = targetObject.transform.position;
					m_maTimer.Set(5f);
					m_character.SetMoveToPosition(destinationPos);
					m_character.SetTargetObj(null);
					return;
				}

				if (m_interact.m_activationTrigger == null)
				{
					destinationPos = m_spot.m_spot.transform.position;
				}
				else
				{
					destinationPos = targetObject.transform.position;
				}
			}
			else
			{
				destinationPos = targetObject.transform.position;
				m_maTimer.Set(5f);
			}
			
			m_character.SetMoveToPosition(destinationPos);
			m_character.m_nav.Unpause();
			//m_character.SetTargetObj(null);
		}
		
		public override void OnUpdate()
		{
			base.OnUpdate();
			
			if (m_interact == null || m_maTimer.IsFinished)
			{
				ApplyState(CharacterStates.RoamForTarget);
				return;
			}
			
			if (m_evaluateTarget.IsFinished)
			{
				if (EvaluateTarget())
					return;
			
				m_evaluateTarget.Set(m_tickEvaluateTarget);
			}
			
			if (m_interact != null)
			{
				bool isFinished = m_interact.IsFinished(m_character);
			
				if(isFinished)
				{
					m_interact = null;
					ApplyState(CharacterStates.RoamForTarget);
					return;
				}
				else if (m_spot.State != MACharacterInteract.InteractSpotState.IsActive && HasAgentArrived())
				{
					float durationInteracting = m_interact.ActivateInteract(m_character);
					if(durationInteracting > 0) m_maTimer.Set(durationInteracting);
				}
			}
			else
			{
				ApplyState(CharacterStates.RoamForTarget);
				return;
			}
		}
		
		public override void OnExit()
		{
			base.OnExit();
			
			if (m_character.TargetObject == m_target)
			{
				m_character.SetTargetObj(null);
				m_target = null;
			}
			
			if (m_interact != null)
			{
				if (m_interact.IsFinished(m_character) == false)
				{
					m_interact.Finish(m_character);
				}

				m_interact.DeactivateInteract(m_character);
			}

			m_character.BlendAnimatorLayerWeight("Combat", 0);
			m_character.m_nav.Pause(true, true);
			m_character.SetCanBePushed(false);
			m_interact = null;
		}
	}
}