using System;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class HeroHeldByPlayer : HeldByPlayer
	{
		public override KeyboardShortcutManager.EShortcutType KeyboardShortcut
		{
			get
			{
				if(m_patrolModeUnlocked == false) 
					return KeyboardShortcutManager.EShortcutType.HeldObjects;
				if(m_patrolAreaSelectionCircle == null || m_patrolAreaSelectionCircle.IsActive == false)
					return KeyboardShortcutManager.EShortcutType.EnablePatrolAreaSelection;
				return KeyboardShortcutManager.EShortcutType.DisableOrAdjustPatrolAreaSelection;
			}
		}

		private MAAreaSelectionCircle m_patrolAreaSelectionCircle = null;
		private bool m_positionApplied = false;
		private MAHeroBase m_hero;
		private Pickup m_pickup = null;
		
		private bool m_patrolModeUnlocked = false;
		
		public HeroHeldByPlayer(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			base.OnEnter();
			
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
			m_patrolModeUnlocked = MAUnlocks.Me.m_patrolMode;
#else
			m_patrolModeUnlocked = false;
#endif
			
			m_pickup = m_character.GetComponent<Pickup>();
			
			if (m_patrolModeUnlocked)
			{
				m_hero = m_character as MAHeroBase;

				float radius = m_character.GuardRadius;
				
				m_patrolAreaSelectionCircle = new MAAreaSelectionCircle(
					radius,
					m_character.CharacterSettings.m_guardModeMinRadius,
					m_character.CharacterSettings.m_guardModeMaxRadius);
				
				UpdateSelectionCircle();
			}
		}
		
		public override void OnUpdate()
		{
			base.OnUpdate();
		
			if (m_patrolModeUnlocked)
			{
				UpdateSelectionCircle();
			}
			else
			{
				if (m_pickup.IsDragActive == false)
				{
					SetReturnToBase();
				}
			}
		}

		private void SetReturnToBase()
		{
			Vector3 mousePosNow = Input.mousePosition;
			Ray heldCharacterRay = Camera.main.ScreenPointToRay(mousePosNow);
			if (Physics.Raycast(heldCharacterRay, out RaycastHit hitInfo, int.MaxValue, 1 << GlobalData.Me.m_moaTerrain.gameObject.layer))
			{
				Vector3 possiblePos = hitInfo.point.GroundPosition();
				if (DistrictManager.Me.IsWithinDistrictBounds(possiblePos) == false)
				{
					Vector3 destPos = Vector3.zero;
					if(m_character.ObjectiveWaypoint != null &&
					   DistrictManager.Me.IsWithinDistrictBounds((Vector3)m_character.ObjectiveWaypoint))
					{
						destPos = (Vector3)m_character.ObjectiveWaypoint;
					}
					else
					{
						if (m_character.Home == null)
						{
							Debug.LogError($"{GetType().Name} - No valid building found for hero go to");
							return;
						}
						
						if(m_character.Home.Building != null)
						{
							destPos = m_character.Home.Building.DoorPosOuter;
						}
						else
						{
							destPos = m_character.Home.transform.position;
						}
					}
							
					m_character.SetToGuard(destPos, m_character.CharacterSettings.m_guardModeOverrideRadius);
				}
			}
		}

		public override void OnExit()
		{
			base.OnExit();
			
			if ((m_pickup != null && m_pickup.IsDragActive == false) && 
			    (m_patrolAreaSelectionCircle != null && m_patrolAreaSelectionCircle.IsActive))
			{
				Vector3? validPos = m_patrolAreaSelectionCircle.ValidCentre;
				if (validPos != null)
				{
					m_hero.SetToGuard((Vector3)validPos, m_patrolAreaSelectionCircle.Radius);
					m_hero.CharacterGameState.m_guardAreaIsBespoke = true;
					m_positionApplied = true;
					m_patrolAreaSelectionCircle.SetLocked(m_positionApplied);
				}
			}
			
			if (m_patrolModeUnlocked)
			{
				m_patrolAreaSelectionCircle.ClearCircle();
			}
		}

		private void UpdateSelectionCircle()
		{
			if (m_positionApplied == false)
			{
				if (m_patrolAreaSelectionCircle.IsActive)
				{
					if (m_pickup.IsDragActive == false)
					{
						Vector3? validPos = m_patrolAreaSelectionCircle.ValidCentre;
						if (validPos != null)
						{
							m_hero.SetToGuard((Vector3)validPos, m_patrolAreaSelectionCircle.Radius);
							m_hero.CharacterGameState.m_guardAreaIsBespoke = true;
							m_positionApplied = true;
							m_patrolAreaSelectionCircle.SetLocked(m_positionApplied);
						}
					}
				}
				else if (m_pickup.IsDragActive == false)
				{
					SetReturnToBase();
				}

				m_patrolAreaSelectionCircle.Update();
			}
		}
	}
}