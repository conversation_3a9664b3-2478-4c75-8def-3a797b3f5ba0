using System;
using System.Collections.Generic;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public abstract class CommonState : CharacterBaseState
	{
		public CommonState(string _state, MACharacterBase _character) : base(_state, _character) { }

		protected string DefaultState => m_character.StateLibrary().ContainsKey(CharacterStates.Follow)
		                                 && (m_character.Leader as MACharacterBase) != null
			? CharacterStates.Follow : m_character.DefaultState;

		protected string PatrolOrReturnToGuard => m_character.LastPatrolPath != null
			? CharacterStates.PatrolToWaypoint
			: CharacterStates.ReturnToGuardLocation;
		
		protected string GuardState
		{
			get
			{
				if (m_character.IsTargetWithinGuardRadius())
				{
					return CharacterStates.LookForTarget;
				}
				else if (m_character.IsWithinGuardRadius() == false)
				{
					return PatrolOrReturnToGuard;
				}

				return CharacterStates.GuardLocation;
			}
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			if (m_character.ObjectiveWaypoint != null && m_character.LastPatrolPath != null && m_character.IsAtObjectiveWaypoint())
			{
				m_character.LastPatrolPath = null; //ohoh
			}
		}
		
		protected virtual bool EvaluateTarget(float _persistence = 1f)
		{
			var target = m_character.GetBestTarget(_persistence);
			m_character.SetTargetObj(target);
			var state = GetAttackStateForTarget();
			if (state != State)
			{
				ApplyState(state);
				return true;
			}
			return false;
		}

		protected string CheckSmashWallsInPath()
		{
			float height = m_character.GetComponent<Collider>().bounds.size.y * 0.5f;
			var nearPos = m_character.m_nav.m_nearestPosition.GroundPosition(height);
			var dir = m_character.m_nav.m_lookAheadPosition.GroundPosition(height) -
			          nearPos;
			string dealWithWallBlockage = CheckAgainstWalledEnclosures(nearPos + dir);
			return dealWithWallBlockage;
		}
		
		protected virtual string GetAttackStateForTarget()
		{
			if(m_character.IsTimeToGoHome)
				return CharacterStates.GoingHome;
			
			if (m_character.IsEscortedCreataure)
			{
				if (m_character.GetTargetedByNearbyCreature() != null)
					return CharacterStates.Petrified;
			}
			
			IDamageReceiver objectiveTarget = null;
			bool isTargetAttackingYourLeader = false;
			bool leaderAvailable = false;
			bool tooFarFromLeader = false;
			
			MACharacterBase leader = m_character.StateLibrary().ContainsKey(CharacterStates.Follow)
				? m_character.Leader as MACharacterBase
				: null;
			
			TargetObject targetObject = m_character.TargetObject;
			if (targetObject == null)
			{
				if (m_character.IsFollowLeader) return CharacterStates.FollowLeader;
				if (leader != null && m_character.IsLeaderAvailable)
				{
					return CharacterStates.Follow;
				}
				
				objectiveTarget = m_character.ObjectiveTarget;
				
				if (m_character.ObjectiveWaypoint != null)
				{
					if (objectiveTarget != null)//moving or not?
					{
						string smashSmash = CheckSmashWallsInPath();
						if(string.IsNullOrWhiteSpace(smashSmash) == false)
							return smashSmash;
					}
					if(m_gameStateData.m_guardObjectiveWaypoint)
					{
						if (m_character.IsWithinGuardRadius() == false)
						{
							if (objectiveTarget != null && objectiveTarget.Transform.position.GetXZ().Approximately(((Vector3)m_character.ObjectiveWaypoint).GetXZ()))
							{
								return  CharacterStates.PatrolToWaypoint;
							}
							if (m_character.IsHarvestingCreataure)
							{
								if (m_character.Carrying != null)
									return CharacterStates.GoingHome;
								if (ShouldGoBackToHarvest())
									return CharacterStates.CreatureHarvesting;
							}
							return PatrolOrReturnToGuard;
						}
						else
						{
							if (m_character.IsEscortedCreataure)
							{
								var escortee = m_character as MAEscortee;
								if (escortee.IsPathCompleted())
									return CharacterStates.Despawn;
								else
									return PatrolOrReturnToGuard;
							}
							
							return CharacterStates.GuardLocation;
						}
					}

					return DefaultState;
				}
				return DefaultState;
			}
			
			if (leader != null)
			{
				leaderAvailable = m_character.IsLeaderAvailable;
				tooFarFromLeader = m_character.CharacterSettings.m_isBodyGuard && leader.transform.position.DistanceSq(m_character.Transform.position) > m_character.GuardRadius * m_character.GuardRadius;
				if (leaderAvailable == false || tooFarFromLeader)
				{
					return CharacterStates.Follow;
				}
				IDamager targetAsDamager = targetObject.DamageReceiver as IDamager;
				IDamageReceiver leaderAsDamageReceiver = m_character.Leader;
				if (targetAsDamager != null && leaderAsDamageReceiver.TargettedBy.Contains(targetAsDamager))
				{
					isTargetAttackingYourLeader = true;
				}
			}
			
			Transform targetTransform = targetObject.transform;
			var attack = m_character.GetNextAttackAvailableInCombo();
			bool isVisible = m_character.IsTargetVisible(targetObject.transform.position, out bool isTargetWithinVisionRadius);
			bool isGroupTarget = (m_character.GroupBehaviour != null) && m_character.GroupBehaviour.HasValidTarget(m_character);
			if (isGroupTarget)
			{
				if (!isVisible)
					return CharacterStates.LookForTarget;
				
				var aoc = AttackOrChase(attack);
				if (!string.IsNullOrEmpty(aoc))
					return aoc;
				
				return CharacterStates.ChaseTarget;
			}

			if (!m_character.IsTargetAliveAndValid(targetObject) ||
				(isTargetAttackingYourLeader == false && !m_character.IsTargetAudible(targetTransform.position)))
			{
				switch (targetObject.TargetObjectType)
				{
					case TargetObject.TargetType.InteractionPointType:
						return CharacterStates.Interaction;
				}
				
				if (m_gameStateData.m_guardObjectiveWaypoint)
				{
					float guardRadius = m_character.GuardRadius;
					if (m_character.ObjectiveWaypoint != null)
					{
						string smashSmash = CheckSmashWallsInPath();
						if(string.IsNullOrWhiteSpace(smashSmash) == false)
							return smashSmash;
						
						Vector3 guardPos = (Vector3)m_character.ObjectiveWaypoint;
						if ((guardPos - targetObject.GetDestinationPosition(m_character).destinationPos).xzMagnitude() >
						    (guardRadius + m_character.CreatureInfo.m_visionRadius))
							return CharacterStates.ReturnToPatrol;
					}
					return GuardState;
				}
				else if (m_character.LastPatrolPath != null)
				{
					Vector3 posNearestOriginalPath = NavAgent.GetPathPosition(m_character.LastPatrolPath, m_character.transform.position);
					if((posNearestOriginalPath - targetObject.GetDestinationPosition(m_character).destinationPos).xzSqrMagnitude() > m_character.VisionRadiusSq)
						return CharacterStates.ReturnToPatrol;
				}
				return DefaultState;
			}
			
			string attackOrChase = AttackOrChase(attack);
			if (attackOrChase.IsNullOrWhiteSpace() == false)
			{
				if (isVisible || (attackOrChase != CharacterStates.ChaseTarget))
					return attackOrChase;
			}

			if(!isVisible)
			{
				if(m_character.ObjectiveWaypoint != null && !isTargetWithinVisionRadius)
				{
					if (m_gameStateData.m_guardObjectiveWaypoint)
					{
						float guardRadius = m_character.GuardRadius;
						Vector3 guardPos = (Vector3)m_character.ObjectiveWaypoint;
						if((guardPos - targetObject.GetDestinationPosition(m_character).destinationPos).xzMagnitude() > (guardRadius + m_character.CreatureInfo.m_visionRadius))
							return CharacterStates.ReturnToPatrol;
					}
					else
					{
						Vector3 posNearestOriginalPath = NavAgent.GetPathPosition(m_character.LastPatrolPath, m_character.transform.position);
						if((posNearestOriginalPath - targetObject.GetDestinationPosition(m_character).destinationPos).xzSqrMagnitude() > m_character.VisionRadiusSq)
							return CharacterStates.ReturnToPatrol;
					}
				}
				else
				{
					string smashSmash = CheckSmashWallsInPath();
					if(string.IsNullOrWhiteSpace(smashSmash) == false)
						return smashSmash;
					
					if (m_gameStateData.m_guardObjectiveWaypoint)
					{
						if (m_character.IsEscortedCreataure)
						{
							if (m_character.IsAtObjectiveWaypoint())
							{
								var escortee = m_character as MAEscortee;
								if (escortee.IsPathCompleted())
									return CharacterStates.Despawn;
							}

							return PatrolOrReturnToGuard;
						}
						
						if (m_character.IsTargetWithinGuardRadius())
						{
							return CharacterStates.LookForTarget;
						}
						else if(m_character.IsWithinGuardRadius() == false)
						{
							return PatrolOrReturnToGuard;
						}
						return CharacterStates.GuardLocation;
					}
					return CharacterStates.LookForTarget;
				}
			}
			
			switch (targetObject.TargetObjectType)
			{
				// case TargetObject.TargetType.BuildingType:
				// 	MABuilding home = m_character.MyHome as MABuilding;
				// 	if (home != null && targetObject.GetComponent<MABuilding>() == home)
				// 		return CharacterStates.GoingHome;
				// 	break;
				case TargetObject.TargetType.InteractionPointType:
					return CharacterStates.Interaction;
			}
			
			if (m_gameStateData.m_guardObjectiveWaypoint && m_character.LastPatrolPath == null) //arrived at objective and in guard stance
			{
				if ((m_character.IsWithinGuardRadius() && isTargetWithinVisionRadius) ||
				    m_character.IsTargetWithinGuardRadius(m_character.CreatureInfo.m_visionRadius))
				{
					
				}
				else
				{
					return CharacterStates.ReturnToGuardLocation;
				}
			}
			string shouldSmashWalls = CheckSmashWallsInPath();
			if(string.IsNullOrWhiteSpace(shouldSmashWalls) == false)
				return shouldSmashWalls;
			
			if (m_character.IsEscortedCreataure)
			{
				if (m_character.IsAtObjectiveWaypoint())
				{
					var escortee = m_character as MAEscortee;
					if (escortee.IsPathCompleted())
						return CharacterStates.Despawn;
				}

				return PatrolOrReturnToGuard;
			}

			return CharacterStates.ChaseTarget;
		}

		protected string AttackOrChase(MAAttackInstance _attack)
		{
			if (_attack == null)
				return "";

			var target = m_character.TargetObject;
			if (target == null)
				return "";
	
			if (m_character.CanReachTargetWithAttack(_attack, out var targetPos))
			{
					if ((_attack.IsProjectile && (target.TargetObjectType == TargetObject.TargetType.BuildingType))
						|| m_character.CanTargetBeHit(m_character.TargetObject, _attack))
					{
						return CharacterStates.AttackClose;
					}

				return CharacterStates.ChaseTarget;
			}
			
			return "";
		}

		protected string FindSearchStateForGuardMode(string _fallback, bool _isTargetWithinVisionRadius)
		{
			if ((m_character.IsWithinGuardRadius() && _isTargetWithinVisionRadius) ||
			    m_character.IsTargetWithinGuardRadius(m_character.CreatureInfo.m_visionRadius))
			{
				return _fallback;
			}
			return PatrolOrReturnToGuard;
		}
		
		protected Vector3 CheckForWallAhead()
		{
			if (m_character.WallsInProximity.Count > 0)
			{
				float height = m_character.GetComponent<CapsuleCollider>().height * 0.5f;
				// var aheadPos = m_character.m_nav.m_nearestPosition +
				//                (m_character.m_nav.m_lookAheadPosition - m_character.m_nav.m_nearestPosition);
				Ray ray = new Ray(m_character.m_nav.m_nearestPosition.GroundPosition(height),
					m_character.m_nav.m_lookAheadPosition.GroundPosition(height) - m_character.m_nav.m_nearestPosition.GroundPosition(height));
				foreach (Collider collider in m_character.WallsInProximity)
				{
					if (collider.bounds.IntersectRay(ray) && collider.Raycast(ray, out var hit, 10f))
					{
						return hit.point;
					}
				}
			}

			return Vector3.zero;

			// Vector3 directionAway = selfPos - m_threatPosition;
			// Vector3 newTarget = directionAway.normalized * Mathf.Clamp(m_runAwayToRadius, 1, m_runAwayToRadius);
			// if (GlobalData.IsPerfectNavable(GlobalData.Me.GetNavAtPoint(newTarget)) == false)
			// {
			// 	var r = MACharacterBase.ScanForNavigableRandomPos(selfPos, m_runAwayToRadius * 0.15f, m_runAwayToRadius, false, GlobalData.AllowNavType.AnyPerfectNav);
			// 	if(r != null) newTarget = (Vector3)r;
			// }
			//

			//how big is a nav grid point
			//find points along existing path (from current nearest pos up to 	
		}
		
		protected string CheckAgainstWalledEnclosures(Vector3 _targetPos)
		{
			float hasArriveroonies = 0f;
			bool hasArrived = HasAgentArrived();
			bool hasWallsInProximity = m_character.WallsInProximity.Count > 0;
			//bool isNearDestination = m_character.m_nav.DistanceToTarget < 1f;
			bool hasWallOnPath = false;
			Vector3 selfPos = m_character.transform.position;
			//2nd check if we are standing at a mere smashable wall obstacle
			Vector3 rayOrigin = selfPos.GroundPosition(1.5f);
			if (m_character.CharacterSettings.m_canDestroyWalls && hasWallsInProximity)
			{
				var ray = new Ray(rayOrigin, _targetPos - rayOrigin);
				foreach (var wall in m_character.WallsInProximity)
				{
					if (wall.Raycast(ray, out var hitWall, 5))
					{
						if (m_character is MAWerewolf)
						{
							return CharacterStates.JumpWall;
						}
						else if (TrySmashWall(hitWall.point, wall))
						{
							return CharacterStates.SmashWall;
						}
					}
				}
			}

			if ((hasArrived || hasWallOnPath) && hasWallsInProximity)
			{
				//1st check if we are standing outside an enclosure/town wall
				List<Enclosure> enclosures = MACreatureControl.Me.GetEnclosures(_targetPos);
				foreach(var enclosure in enclosures)
				{
					if (enclosure.EnclosedArea.OverlapPoint(selfPos.GetXZVector2()) == false &&
					    enclosure.OuterPath.OverlapPoint(selfPos.GetXZVector2()))
					{
						if (m_character.CharacterSettings.m_canDestroyWalls == false)
						{
							return CharacterStates.PatrolTownWalls;
						}

						foreach (var wall in m_character.WallsInProximity)
						{
							if (TrySmashWall(m_character.transform.position, wall))
							{
								return CharacterStates.SmashWall;
							}
						}
				 	}
				}

				if (m_character.CharacterSettings.m_canDestroyWalls)
				{
					//2nd check if we are standing at a mere smashable wall obstacle
					/*Vector3 */
					rayOrigin = selfPos.GroundPosition(1.5f);
					var ray = new Ray(rayOrigin, _targetPos - rayOrigin);
					foreach (var wall in m_character.WallsInProximity)
					{
						if (wall.Raycast(ray, out var hit, 3))
						{
							if (TrySmashWall(m_character.transform.position, wall))
							{
								return CharacterStates.SmashWall;
							}
						}
					}
				}
			}
			return "";
		}
		
		protected bool TrySmashWall(Vector3 _wallNearestPos, Collider collider)
		{
			var hitPoint = RoadManager.Me.m_pathSet.GetClosestPathToPoint(null, true, _wallNearestPos, 10, out var path);
			float distSq = hitPoint.GetXZ().DistanceSq(_wallNearestPos.GetXZ());
			const float distAllowed = 3f;
			if(distSq < distAllowed * distAllowed)
			{
				bool isGate = collider.GetComponentInParent<GateOpener>() != null;
				PathBreak pathBreak = PathBreak.Create(hitPoint, _wallNearestPos, 0, m_character.AttackRange, isGate);
				if(pathBreak.RepairLevel == 0)
				{
					return false;
				}
				MACharacterBase.TargetResult ts = new MACharacterBase.TargetResult();
				ts.m_targetObject = TargetObject.Create(pathBreak.gameObject);
				ts.m_targetLocationState = MACharacterBase.TargetResult.TargetState.IsWall;
				m_character.SetTargetObj(ts);
							
				return true;
			}
			else
			{
				//GetClosestPathToPoint returned the same point as the creaturePos -> no result, no navBlocker 'wall' found.
			}
			return false;
		}

		protected bool TryGetValidAttackState(out string _outAttackState)
		{
			_outAttackState = "";
			
			if(m_character.IsTargetAliveAndValid(m_character.TargetObject) == false)
				return false;
			
			var attack = m_character.GetNextAttackAvailableInCombo();
			string attackOrChase = AttackOrChase(attack);
			if(attackOrChase.IsNullOrWhiteSpace() == false)
			{
				_outAttackState = attackOrChase;
				return true;
			}

			return false;
		}
		
		/// <param name="_ignoreMoveIntoBuildingState">ignores the character arriving outside a building while on way to another destination</param>
		/// <param name="_ignoreMoveOutOfBuildingState">ignores the character arriving outside a building while on way to another destination</param>
		/// <returns></returns>
		protected virtual bool HasAgentArrived(bool _ignoreMoveIntoBuildingState = false, bool _ignoreMoveOutOfBuildingState = false)
		{
			if(/*(_ignoreMoveIntoBuildingState && m_character.m_state == NGMovingObject.STATE.MA_MOVE_TO_INSIDE_BUILDING)||*/ 
			   (_ignoreMoveOutOfBuildingState && m_character.m_state == NGMovingObject.STATE.MA_MOVE_TO_OUTSIDE_BUILDING))
				return false;
			
			return m_character.m_nav.TargetReached;

		}
		
		public static Transform FindClosestTransform(Transform _trParent, Vector3 _toPos, float _withinRange)
		{
			int checkCount = 3;
			Transform closestTr = null;
			var bestv = Vector3.zero;
			float bestDistSq = _withinRange * _withinRange;
			foreach (Transform trChild in _trParent)
			{
				var v = (trChild.position - _toPos);
				float distSq = v.xzSqrMagnitude();
				if (distSq < bestDistSq)
				{
					bestv = v;
					checkCount--;
					bestDistSq = distSq;
					closestTr = trChild;
					if (checkCount == 0)
						break;
				}
			}
			return closestTr;
		}
		//
		// if (m_character.m_nav.TargetReached)
		// {
		// 	switch (m_character.m_state)
		// 	{
		// 		case NGMovingObject.STATE.MA_MOVE_TO_INSIDE_BUILDING:
		// 			return !_ignoreMoveIntoBuildingState;
		// 		case NGMovingObject.STATE.MA_MOVE_TO_OUTSIDE_BUILDING:
		// 			return !_ignoreMoveIntoBuildingState;
		// 	}
		// 	return true;
		// }
		// return false;

		private bool ShouldGoBackToHarvest()
		{
			if (m_character.CharacterGameState.m_harvestHotspotId >= 0)
				return true;
			
			if (m_character.m_harvestHotspot != null)
				return true;
			
			foreach (var pickup in GameManager.Me.m_state.m_pickups)
			{
				var pickupObject = pickup.Pickup;
				if (pickupObject.IsReservedBy(m_character))
					return true;
			}

			return false;
		}
	}
}