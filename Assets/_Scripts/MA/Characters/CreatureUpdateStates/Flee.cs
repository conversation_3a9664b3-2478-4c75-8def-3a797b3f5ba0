using System;
using UnityEngine;
using Random = UnityEngine.Random;
using Vector3 = UnityEngine.Vector3;

namespace MACharacterStates
{
	[Serializable]
	public class Flee : CommonState
	{
		private Vector3 m_threatPosition = Vector3.zero;
		private MATimer m_updateRunAway = new MATimer();
		private float m_runAwayToRadius = 10f;
		
		public Flee(string _state, MACharacterBase _character) : base(_state, _character) { }

		public override void OnEnter()
		{
			base.OnEnter();
			
			m_updateRunAway.Set(0.5f);
			
			RunAway();
		}
		
		public override void OnExit()
		{
			base.OnExit();
		}

		private void RunAway()
		{
			bool threatFound = FindThreatPos();
			if (threatFound == false && HasAgentArrived())
			{
				ApplyState(DefaultState);
				return;
			}
				
			var selfPos = m_character.transform.position;
			if (m_threatPosition == Vector3.zero)
			{
				m_threatPosition = selfPos + Random.insideUnitCircle.V3XZ();
			}
			m_gameStateData.m_speed = m_gameStateData.AttackSpeed;
			Vector3 directionAway = selfPos - m_threatPosition;
			Vector3 newTarget = directionAway.normalized * Mathf.Clamp(m_runAwayToRadius, 1, m_runAwayToRadius);
			if (GlobalData.IsPerfectNavable(GlobalData.Me.GetNavAtPoint(newTarget)) == false)
			{
				var r = MACharacterBase.ScanForNavigableRandomPos(selfPos, m_runAwayToRadius * 0.15f, m_runAwayToRadius, false, GlobalData.AllowNavType.AnyPerfectNav);
				if(r != null) newTarget = (Vector3)r;
			}

			m_character.SetMoveToPosition(newTarget);
		}
		
		public override void OnUpdate()
		{
			base.OnUpdate();

			bool hasArrived = HasAgentArrived();
			if (m_updateRunAway.IsFinished || hasArrived)
			{
				m_updateRunAway.Set(0.5f);

				RunAway();
			}
		}

		private bool FindThreatPos()
		{
			TargetObject selfAsTarget = m_character.SelfAsTarget;
			if (selfAsTarget != null)
			{
				TargetObject.Attacker attacker = selfAsTarget.GetClosestAttacker();
				if (attacker != null && attacker.m_sqDistanceFurtherThanPreviousAttacker < 8 * 8)
				{
					m_threatPosition = attacker.m_attacker.Transform.position;
					return true;
				}
			}
			return false;
		}
		
		protected void OnPathReturned(NGMovingObject.TargetReachability _targetReachability)
		{
			switch(_targetReachability)
			{
				case NGMovingObject.TargetReachability.IsReachable:
				case NGMovingObject.TargetReachability.IsNotReachable:
					break;
				case NGMovingObject.TargetReachability.IsNotReachableAndAlreadyAtNearestPos:
				case NGMovingObject.TargetReachability.PathFindingAlreadyInProgress:
				case NGMovingObject.TargetReachability.PathFailure:
					ApplyState(DefaultState);
					break;
			}
		}
	}
}