using System;
using System.Collections.Generic;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class ChaseTarget : CommonState
	{
		private const float m_tickEvaluateTarget = 1f;
		private const float c_tickUpdateTargetPos = 0.5f;

		[ReadOnlyInspector][SerializeField] private float m_timeEvaluateTarget = -1f;
		[ReadOnlyInspector][SerializeField] private float m_timeUpdateTargetPos = -1f;

		private Vector2 m_currentTargetPos = Vector3.zero;

		public ChaseTarget(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnExit()
		{
			base.OnExit();
		}

		public override void OnEnter()
		{
			base.OnEnter();
            m_character.BlendAnimatorLayerWeight("Combat", 1);
			m_character.m_nav.Unpause();
			m_gameStateData.m_speed = m_gameStateData.AttackSpeed;

			m_timeEvaluateTarget = Time.time + m_tickEvaluateTarget;
		}
		
		public override void OnUpdate()
		{
			base.OnUpdate();

			if (Time.time > m_timeEvaluateTarget)
			{
				if (EvaluateTarget(1.2f))
					return;

				m_timeEvaluateTarget = Time.time + m_tickEvaluateTarget;
			}

			var state = GetAttackStateForTarget();
			if (state != CharacterStates.ChaseTarget)
			{
				ApplyState(state);
				return;
			}

			if (m_timeUpdateTargetPos < Time.time)
			{
				var destPos = m_character.TargetObject.GetDestinationPosition(m_character, c_tickUpdateTargetPos);
				m_gameStateData.m_targetPos = destPos.destinationPos;
				float deltaPosChangeSqXZ = (m_currentTargetPos - m_gameStateData.m_targetPos.GetXZVector2()).SqrMagnitude();
				if (deltaPosChangeSqXZ >= 2f)
				{
					m_currentTargetPos = m_gameStateData.m_targetPos.GetXZVector2();
					m_character.SetMoveToPosition(m_gameStateData.m_targetPos, destPos.isInsideValidDamageArea);
				}

				m_timeUpdateTargetPos = Time.time + c_tickUpdateTargetPos;
			}
		}
	}
}