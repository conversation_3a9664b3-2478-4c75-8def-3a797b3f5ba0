using UnityEngine;
using System;
using Random = UnityEngine.Random;

namespace MACharacterStates
{
	[Serializable]
	public class GuardLocation : RoamForTarget
	{
		public GuardLocation(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		protected override string GetAttackStateForTarget()
		{
			if(m_character.IsTimeToGoHome)
				return CharacterStates.GoingHome;

			if (m_character.IsEscortedCreataure)
			{
				if (m_character.GetTargetedByNearbyCreature() != null)
					return CharacterStates.Petrified;
				else if (m_character.IsAtObjectiveWaypoint())
				{
					var escortee = m_character as MAEscortee;
					if (escortee.IsPathCompleted())
						return CharacterStates.Despawn;
				}
			}
			
			MACharacterBase leader = m_character.StateLibrary().ContainsKey(CharacterStates.Follow)
				? m_character.Leader as MACharacterBase
				: null;
			if (leader != null && leader.IsLeaderAvailable == false)
			{
				var leaderPos = leader.transform.position;
	            var leaderToSelfDirect = m_character.transform.position - leaderPos;
	            var leaderToSelfDirectDist = leaderToSelfDirect.xzMagnitude();
	            float allowedDistance = m_character.GuardRadius;
	            bool isLeaderTooFar = leaderToSelfDirectDist > allowedDistance;
	            if (isLeaderTooFar)
	            {
		            return CharacterStates.Follow;
	            }
			}

			return base.GetAttackStateForTarget();
		}

		protected override bool TryMoveToSearchSpot()
		{
			return base.TryMoveToSearchSpot();
		}

		/// this override ensures the valid spot is always within guard radius of the character's objective waypoint.
		protected override Vector3? ScanForValidSpot()
		{
			float guardRadius = m_character.GuardRadius;
			
			return MACharacterBase.ScanForNavigableRandomPos((Vector3)m_character.ObjectiveWaypoint, 0f,
				guardRadius, m_character.IsAllowedToGuardOutsideDistrict,
				GlobalData.AllowNavType.AnyPerfectNav);
		}

		protected override bool TryStartPatrol()
		{
			return false;
		}

		public override void OnEnter()
		{
			base.OnEnter();

			if (m_character.IsHarvestingCreataure)
			{
				if (m_character.Carrying != null)
					ApplyState(CharacterStates.GoingHome);
				else
					ApplyState(CharacterStates.CreatureHarvesting);
			}
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
		}

		public override void OnExit()
		{
			base.OnExit();
		}
	}

	[Serializable]
	public class GoToWaypointIgnoringTargets : CommonState
	{
		//TS - this is a special state that ensures the spawned character will 'run' (not walk) to guard the gate
		//will ignore all distractions, including enemies until he reaches the guard-radius (MACharacterSettings at time of writing)
		//to disable Guard mode ensure m_gameStateData.m_guardObjectiveWaypoint is set to false again.

		private MATimer m_timer = null;
		protected bool m_pathReturnedSuccessfully = false;
 
		public GoToWaypointIgnoringTargets(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			base.OnEnter();

			Vector3? pos = m_character.ObjectiveWaypoint;
			if (pos == null)
			{
				pos = m_character.transform.position;
				int iSpawnPoint =
					NGManager.Me.m_MACreatureSpawnPoints.FindIndex(x => x.IsSpawnPointOfCharacter(m_character.m_ID));

				if (iSpawnPoint != -1)
				{
					pos = NGManager.Me.m_MACreatureSpawnPoints[iSpawnPoint].transform.position;
				}
				else if (m_character.Home && m_character.Home.Building)
				{
					float dist = 3f;
					Vector3 awayFromDoor =
						m_character.Home.Building.DoorPosOuter - m_character.Home.Building.DoorPosInner;
					Vector3 awayFromDoorNorm = awayFromDoor.normalized;
					// Should this not take into account the door pos not the character pos?
					pos = m_character.transform.position +
					      awayFromDoorNorm * (dist * 0.5f + dist * 0.5f * UnityEngine.Random.Range(0f, 1f));
					m_gameStateData.m_speed = m_gameStateData.WalkSpeed;
				}
			}

			m_character.SetToGuard((Vector3)pos, m_character.GuardRadius);
			m_gameStateData.m_speed = m_gameStateData.AttackSpeed;
			MoveToPos((Vector3)pos);
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			if(m_pathReturnedSuccessfully)
			{
				if (HasAgentArrived())
				{
					ApplyState(CharacterStates.RoamForTarget);
				}
			}
			else if(m_timer != null && m_timer.m_length > 0 && m_timer.IsFinished)
			{
				m_timer = null;
				m_character.m_nav.StopNavigation();
				ApplyState(CharacterStates.RoamForTarget);
			}
		}

		public override void OnExit()
		{
			base.OnExit();
			m_gameStateData.m_speed = m_gameStateData.WalkSpeed;
			m_character.m_onPathProcessed -= OnPathReturned;
		}

		protected virtual bool MoveToPos(Vector3 newPos)
		{
			m_character.m_nav.StopNavigation();

			m_character.m_onPathProcessed -= OnPathReturned;
			m_character.m_onPathProcessed += OnPathReturned;

			m_pathReturnedSuccessfully = false;

			m_character.SetMoveToPosition(newPos);

			m_timer = new MATimer(4f);
			return true;
		}

		private void OnPathReturned(NGMovingObject.TargetReachability _targetReachability)
		{
			//important! requesting a path may not always be immediate. This is also a cause of certain v3(0,0,0) pos worker bugs.
			//therefore we use a callback to confirm the new path and set a waiting-flag.

			m_pathReturnedSuccessfully = true;
		}
	}

	[Serializable]
	public class DogGuardLocation : GuardLocation
	{
		public DogGuardLocation(string _state, MACharacterBase _character) : base(_state, _character) { }

		private MADog Dog => m_character as MADog;
		
		private MATimer m_checkToPeeTimer = new MATimer();
		
		public float m_peeProbability = 0.05f;
		public float m_peeFrequency = 2f;
		
		public const float c_checkForNewLeaderAfter = 6f;
		private MATimer m_checkForNewLeader = new();

		
		protected override void StandAround()
		{
			base.StandAround();
			m_character.PlayAnim("Sit");
		}

		public override void OnEnter()
		{
			base.OnEnter();
			m_checkForNewLeader = new MATimer(c_checkForNewLeaderAfter);
			m_checkToPeeTimer.Set(m_peeFrequency);
		}

		public override void OnUpdate()
		{
			if (m_checkForNewLeader.IsFinished)
			{
				Dog.LeaderCheck();
				m_checkForNewLeader.Set(c_checkForNewLeaderAfter);
			}
			
			base.OnUpdate();
			
			Dog.PeeCheck();
			Dog.PooCheck();
			Dog.FoodCheck();
		}

		protected override void MoveToSearchSpot(Vector3 _newPos)
		{
			m_character.StopCurrentAnimation();
			base.MoveToSearchSpot(_newPos);
		}

		public override void OnExit()
		{
			m_character.StopCurrentAnimation();
			base.OnExit();
		}
	}
}