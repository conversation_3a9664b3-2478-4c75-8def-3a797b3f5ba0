using System;

namespace MACharacterStates
{
	[Serializable]
	public class Unconscious : CharacterBaseState
	{
		public Unconscious(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		private float m_ragDollTimer = -1;

		public override void OnEnter()
		{
			base.OnEnter();

			m_character.SetTargetObj(null);
			m_character.m_nav.Pause(true, true);
			m_character.m_nav.StopNavigation();
			m_character.m_ragdollController.PauseNextStateChange = true;

		}

		protected virtual bool WakeUpConditionMet()
		{
			if (m_gameStateData.m_timeInState >= m_character.CharacterGameState.m_unconsciousTimeLeft)
			{
				return true;
			}

			return false;
		}

		protected virtual void OnWakeUpConditionMet()
		{
			// m_character.m_ragdollController.notMovingDuration = RagdollController.c_defaultNotMovingDuration;
			//m_character.m_ragdollController.m_pauseNextStateChange = false;
			ApplyState(CharacterStates.StandUp);
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			if (WakeUpConditionMet())
			{
				OnWakeUpConditionMet();
				return;
			}
		}

		public override void OnExit()
		{
			base.OnExit();

			m_character.m_nav.Unpause();
		}
	}

	public class UnconsciousDead : Unconscious
	{
		public UnconsciousDead(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			base.OnEnter();
			m_character.CharacterGameState.m_consciousness = (int)MACharacterBase.Consciousness.UnconsciousDead;
			m_character.SetHealthRecoverRateOverride(m_character.CreatureInfo.m_deadHealthRecoveryRate);
			m_character.CurrentComboState?.ResetCombo();
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
		}

		public override void OnExit()
		{
			base.OnExit();
		}

		protected override void OnWakeUpConditionMet()
		{
			base.OnWakeUpConditionMet();
			m_gameStateData.m_consciousness = 0;
			m_character.ResetHealthRecoveryRate();
		}

		protected override bool WakeUpConditionMet()
		{
			if (m_character.ReincarnationHealth < m_character.Health)
			{
				return true;
			}

			return false;
		}
	}
}