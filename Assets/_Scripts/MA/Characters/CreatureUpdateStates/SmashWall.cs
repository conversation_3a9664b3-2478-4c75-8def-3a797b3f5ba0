using System;
using UnityEngine;

namespace MACharacterStates
{
	public class SmashWall : CommonState
	{
		public SmashWall(string _state, MACharacterBase _character) : base(_state, _character) { }

		private MATimer m_failSafeTimer = new MATimer();
		private bool m_redoAttack = false;
		
		public override void OnEnter()
		{
			base.OnEnter();		
			m_character.BlendAnimatorLayerWeight("Combat", 1);
			m_character.m_nav.Pause(true, true);
			m_character.SetCanBePushed(false);
			if (m_character.TargetObject != null)
			{
				m_character.LookAt(m_character.TargetObject.transform.position, 480f, OnFinishedTurning);
				m_failSafeTimer.Set(4f);
			}
			else
			{
				ApplyState(m_character.DefaultState);
			}
		}

		private void DamageTarget()
		{
			m_character.DoAttackOnTarget();
		}
		
		public void OnHitTarget()
		{
			m_failSafeTimer.Set(4f);
			m_character.m_onCollisionWithTarget -= OnHitTarget;
			DamageTarget();
		}
		
		private void OnFinishedTurning()
		{
			m_character.m_onFinishedTurningToLookAt -= OnFinishedTurning;
			StartAttack();
		}
		
		public void StartAttack()
		{
			//m_isAttacking = true;
			m_failSafeTimer.Set(4f);
			m_character.m_onCollisionWithTarget -= OnHitTarget;
			m_character.m_onCollisionWithTarget += OnHitTarget;
			m_character.QueueNextAttack(false);
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			if (m_failSafeTimer.IsFinished)
			{
				m_redoAttack = true;
			}
			
			if(m_redoAttack)
			{
				if (m_character.TargetObject != null)
				{
					m_character.LookAt(m_character.TargetObject.transform.position, 480f, OnFinishedTurning);
				}
				m_redoAttack = false;
			}
		}

		public override void OnExit()
		{
			base.OnExit();
			m_character.m_onCollisionWithTarget -= OnHitTarget;
			m_character.m_onFinishedTurningToLookAt -= OnFinishedTurning;
			m_character.SetCanBePushed(true);
		}
	}
}
	