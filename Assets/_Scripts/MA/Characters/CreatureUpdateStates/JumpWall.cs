using System;
using System.Threading;
using UnityEngine;

namespace MACharacterStates
{
	public class JumpWall : CommonState
	{
		private Vector3 vel = Vector3.zero;
		private int onGroundState = 0;
		private bool isOnGround = false;
		private float timer;

		public JumpWall(string _state, MACharacterBase _character) : base(_state, _character) { }

		public override void OnEnter()
		{
			base.OnEnter();

			// m_character.PlayAnim("Jump", null);
			m_character.m_nav.PushPause("ASDASDASD", true, true);
			m_character.m_nav.enabled = false;
			onGroundState = 0;
			isOnGround = m_character.m_isTouchingGround;
			timer = 0;
		}

		public override void OnFixedUpdate()
		{
			if (onGroundState == 0)
				onGroundState = 1;
			else if (onGroundState == 1)
			{
				vel = m_character.transform.forward * 600f;
				vel += Vector3.up * Mathf.Sqrt(2f * 6f * Mathf.Abs(Physics.gravity.y)) * 80f;
				m_character.RigidBody.AddForce(vel, ForceMode.Impulse);
				onGroundState = 2;
				timer = Time.time;
			}
			else if (onGroundState == 2 && Time.time > timer + 2f)
			{
				OnJumpEnded();
				onGroundState = 3;
			}
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
		}

		public override void OnExit()
		{
			base.OnExit();
			var asd = m_character.GetComponent<Collider>();
			asd.enabled = true;
		}

		public void OnJumpEnded()
		{
			m_character.StopCurrentAnimation();

			m_character.m_nav.PopPause("ASDASDASD");
			m_character.m_nav.enabled = true;

			ApplyState(CharacterStates.ChaseTarget);
		}
	}
}
	