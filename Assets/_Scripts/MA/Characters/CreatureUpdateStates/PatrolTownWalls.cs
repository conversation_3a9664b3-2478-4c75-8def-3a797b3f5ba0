using System;
using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;

namespace MACharacterStates
{
	[Serializable]
	public class PatrolTownWalls : RoamForTarget
	{
		public PatrolTownWalls(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}
		
		protected override float RoamingStandAroundMinTime => m_character.CharacterSettings.m_patrolWallsStandAroundMinTime;
		protected override float RoamingStandAroundMaxTime => m_character.CharacterSettings.m_patrolWallsStandAroundMaxTime;
		protected override float RoamingWalkAroundMinTime => m_character.CharacterSettings.m_patrolWallsWalkAroundMinTime;
		protected override float RoamingWalkAroundMaxTime => m_character.CharacterSettings.m_patrolWallsWalkAroundMaxTime;
		
		protected TargetObject m_targetObject = null;
		protected Enclosure m_enclosureToPatrol = null;

		protected Vector2 m_currentTargetPointOnPath = Vector2.zero;
		protected int m_currentClosestPointIndexOnOuterPath = -1;

		private bool m_wantClockwisePatrol = true;

		public override void OnEnter()
		{
			m_tickEvaluateTarget = 1f;

			m_targetObject = m_character.TargetObject;
			m_wantClockwisePatrol = Random.Range(0f, 1f) < 0.5f;
			m_enclosureToPatrol =
				FindClosestEnclosure(out m_currentTargetPointOnPath, out m_currentClosestPointIndexOnOuterPath);
			base.OnEnter();
		}

		protected override bool TryMoveToSearchSpot()
		{
			if(m_character.m_nav.PathPending)
				return false;

			Vector2[] points = m_enclosureToPatrol.OuterPath.points;
			List<Vector3> pointsV3 = new List<Vector2>(points).ConvertToTypeList((v2) => new Vector3(v2.x, 0, v2.y));
			m_character.m_nav.SetPath(pointsV3, true, true);
			m_roamingStandAroundTime = -1;
			m_roamingWalkAroundTime = Time.time + Random.Range(RoamingWalkAroundMinTime, RoamingWalkAroundMaxTime);

			m_character.m_nav.Unpause();
			m_character.m_nav.Unpause();
			m_character.m_nav.Speed = m_character.GetDesiredSpeed();
			m_character.PlayAnim("Run");//MACreatureBase.CreatureMoveAnimations.Run.ToString());

			m_character.m_onPathProcessed -= OnPathReturned;
			m_character.m_onPathProcessed += OnPathReturned;

			return true;
		}

		private Enclosure FindClosestEnclosure(out Vector2 _OUTclosestPoint, out int _OUTclosestPathIndex)
		{
			Enclosure bestEnclosureFound = null;
			float smallestDistanceXZSq = Single.MaxValue;
			_OUTclosestPoint = Vector2.zero;
			_OUTclosestPathIndex = -1;
			if (m_targetObject == null) return null;
			Vector2 pos = m_targetObject.transform.position.GetXZVector2();
			_OUTclosestPoint = pos;

			foreach(var enclosure in MACreatureControl.Me.m_enclosures)
			{
				_OUTclosestPoint = enclosure.Value.OuterPath.ClosestPoint(pos);
				float distXZSq = (_OUTclosestPoint - pos).sqrMagnitude;
				if(smallestDistanceXZSq > distXZSq)
				{
					smallestDistanceXZSq = distXZSq;
					bestEnclosureFound = enclosure.Value;

					//TODO: TS - Consider overlapping enclosures.
				}
			}

			_OUTclosestPathIndex = Mathf.RoundToInt(NavAgent.PointOnPathV2(bestEnclosureFound.OuterPath.points, pos));

			return bestEnclosureFound;
		}

		protected override bool EvaluateTarget(float _persistence = 1)
		{
			var target = m_character.GetBestTarget(_persistence);

			if(target != null &&
			   (target.m_targetLocationState == MACharacterBase.TargetResult.TargetState.WithinOpenWalls ||
			    target.m_targetLocationState == MACharacterBase.TargetResult.TargetState.OutsideWalls))
			{
				m_character.SetTargetObj(target);

				var state = GetAttackStateForTarget();
				if (state != State)
				{
					ApplyState(state);
					return true;
				}
			}
			return false;
		}
	}
}