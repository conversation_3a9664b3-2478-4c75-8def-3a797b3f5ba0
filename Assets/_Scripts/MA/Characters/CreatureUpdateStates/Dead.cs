using System;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class Dead : CharacterBaseState
	{
		private MATimer m_timeBeforeModelFreeze = new MATimer();
		private MATimer m_timeBeforeDisappear = new MATimer();
		private MATimer m_deadDisappearTimer = null;

		private float m_metersToDescend = 1f;
		private bool m_sleeping = false;

		public Dead(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			base.OnEnter();

			//enable bodyCollider only to gather size
			m_character.m_bodyToBodyCollider.isTrigger = true;
			m_character.m_bodyToBodyCollider.enabled = true;
			m_metersToDescend = m_character.m_bodyToBodyCollider.bounds.size.y;
			m_character.m_bodyToBodyCollider.enabled = false;
			
			m_character.SetTargetObj(null);
			m_character.m_nav.Pause(true, true);
			m_character.m_nav.StopNavigation();
			
			RagdollController ragdollController = m_character.GetComponentInChildren<RagdollController>();
			if (ragdollController != null)
			{
				float timeBeforeDisappear = m_character.CharacterSettings.m_deadTime;
				m_timeBeforeDisappear.Set(Mathf.Clamp(timeBeforeDisappear, 0, timeBeforeDisappear));
			
				Rigidbody[] rigidBodies = m_character.GetComponentsInChildren<Rigidbody>();
				foreach (Rigidbody rigidBody in rigidBodies)
				{
					rigidBody.useGravity = false;
					rigidBody.constraints = RigidbodyConstraints.FreezeAll;
				}
			
				foreach (var mainCollider in m_character.m_mainColliders)
					mainCollider.enabled = false;
			
				m_character.RigidBody.includeLayers = (LayerMask)0;
				m_character.RigidBody.includeLayers = LayerMask.NameToLayer("Terrain");
			}
			else
			{
				m_character.StopCurrentAnimation();
				m_character.m_nav.enabled = false;
				m_character.m_anim.enabled = false;
				
				float timeBeforeDisappear = m_character.CharacterSettings.m_deadTime;
				m_timeBeforeDisappear.Set(Mathf.Clamp(timeBeforeDisappear, 0, timeBeforeDisappear));
			
				Rigidbody[] rigidBodies = m_character.GetComponentsInChildren<Rigidbody>();
				foreach (Rigidbody rigidBody in rigidBodies)
				{
					rigidBody.useGravity = true;
					rigidBody.isKinematic = false;
					rigidBody.constraints = RigidbodyConstraints.None;
					rigidBody.angularDamping = 1f;
					rigidBody.linearDamping = 1.5f;
				}
			
				foreach (var mainCollider in m_character.m_mainColliders)
					mainCollider.enabled = true;
				
				m_character.m_bodyToBodyCollider.enabled = true;
				m_character.RigidBody.AddTorque(UnityEngine.Random.insideUnitCircle.GetVector3XZ() * 0.2f);
			}
			
			m_character.CharacterGameState.m_consciousness = (int)MACharacterBase.Consciousness.Dead;
			
		}
		
		public override void OnUpdate()
		{
			base.OnUpdate();
			
			if (m_timeBeforeModelFreeze != null && m_timeBeforeModelFreeze.IsFinished &&
			    Mathf.Approximately(m_character.RigidBody.linearVelocity.sqrMagnitude, 0f) && m_sleeping == false)
			{
				SetCharacterToSleep();
			}
			
			if (m_timeBeforeDisappear != null && m_timeBeforeDisappear.IsFinished)
			{
				m_timeBeforeDisappear = null;
				m_deadDisappearTimer = new MATimer(m_character.CharacterSettings.m_deadDisappearTime);
				if (m_sleeping == false)
				{
					SetCharacterToSleep();
				}
			}
			
			if (m_deadDisappearTimer != null)
			{
				if (m_deadDisappearTimer.IsFinished)
				{
					m_deadDisappearTimer = null;
					m_character.enabled = false;
					m_character.DestroyMe();
				}
				else
				{
					float metresPerSec = m_metersToDescend / m_deadDisappearTimer.m_length;
					float metersPerFrame = Time.deltaTime * metresPerSec;
					m_character.transform.position += Vector3.up * metersPerFrame * -1;
				}
			}
		}

		public override void OnExit()
		{
			base.OnExit();
			
			m_character.CharacterGameState.m_consciousness = (int)MACharacterBase.Consciousness.Conscious;
		}

		private void SetCharacterToSleep()
		{
			RagdollController ragdollController = m_character.GetComponentInChildren<RagdollController>();
			if (ragdollController != null)
				ragdollController.StartDeadState();

			m_character.m_anim.enabled = false;
			
			Rigidbody[] rigidBodies = m_character.GetComponentsInChildren<Rigidbody>();//main root body only, not ragdoll
			foreach (Rigidbody rigidBody in rigidBodies)
			{
				rigidBody.isKinematic = true;
				rigidBody.useGravity = false;
				rigidBody.constraints = RigidbodyConstraints.FreezeAll;
			}
			m_sleeping = true;
		}
	}
}