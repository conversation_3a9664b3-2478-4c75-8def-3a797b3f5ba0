using System;
using UnityEngine;

namespace MACharacterStates
{
	public class HeroSpawn : Spawn
	{
		public HeroSpawn(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnUpdate()
		{
			if (HasAgentArrived()) // || Time.time >= m_stopTime)
			{
				ApplyState(m_character.DefaultState);
				/*if (m_character.ObjectiveWaypoint != null)
				{
					ApplyState(CharacterStates.PatrolToWaypoint);
				}
				else
				{
					ApplyState(CharacterStates.RoamForTarget);
				}*/
			}
		}
	}
	
	public class HeroEjected : Spawn
	{
		private float m_returnTime = 0;
		
		public HeroEjected(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			m_returnTime = Time.time + NGManager.Me.m_ejectReturnDelay;
			base.OnEnter();
		}

		public override void OnUpdate()
		{
			if (HasAgentArrived() && Time.time >= m_returnTime) // || Time.time >= m_stopTime)
			{
				ApplyState(m_character.DefaultState);
				/*if(m_character.Home != null)
				{
					ApplyState(CharacterStates.GoingHome);
				}
				else
				if (m_character.ObjectiveWaypoint != null)
				{
					ApplyState(CharacterStates.PatrolToWaypoint);
				}
				else
				{
					ApplyState(CharacterStates.RoamForTarget);
				}*/
			}
		}
	}
}