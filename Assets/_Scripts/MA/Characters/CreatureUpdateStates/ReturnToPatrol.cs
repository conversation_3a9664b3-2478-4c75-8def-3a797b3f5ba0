using System;
using System.Collections.Generic;
using UnityEngine;
using Vector3 = UnityEngine.Vector3;

namespace MACharacterStates
{
	public class ReturnToPatrol : RoamForTarget
	{
		public ReturnToPatrol(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		protected override bool TryStartPatrol()
		{
			return false;
		}
		
		public override void OnEnter()
		{
			m_roamingStandAroundTime = -1;
			m_roamingWalkAroundTime = -1;
			
			if(m_character.ObjectiveWaypoint != null)
			{
				if(m_character.LastPatrolPath == null)
				{
					ApplyState(CharacterStates.PatrolToWaypoint);
					return;
				}
				else
				{
					m_character.m_nav.Unpause();

					m_character.m_onPathProcessed -= OnReturnPathReturned;
					m_character.m_onPathProcessed += OnReturnPathReturned;

					m_gameStateData.m_speed = m_gameStateData.AttackSpeed;

					Vector3 posNearestOriginalPath = NavAgent.GetPathPosition(m_character.LastPatrolPath, m_character.transform.position);
					m_character.SetMoveToPosition(posNearestOriginalPath);
				}
			}
			else
			{
				ApplyState(CharacterStates.RoamForTarget);
				return;
			}
		}

		private void OnReturnPathReturned(MACreatureBase.TargetReachability _targetReachability)
		{
			switch(_targetReachability)
			{
				case MACreatureBase.TargetReachability.IsReachable:
					//m_character.PlayAnim("Walk");
					m_character.m_onPathProcessed -= OnReturnPathReturned;
					break;
				case MACreatureBase.TargetReachability.IsNotReachable:
					//m_character.PlayAnim("Walk");
					m_character.m_onPathProcessed -= OnReturnPathReturned;
					break;
				case MACreatureBase.TargetReachability.IsNotReachableAndAlreadyAtNearestPos:
					//m_character.PlayAnim("StopLook");
					m_character.m_onPathProcessed -= OnReturnPathReturned;
					break;
				case MACreatureBase.TargetReachability.PathFindingAlreadyInProgress:
					break;
				case MACreatureBase.TargetReachability.PathFailure:
					//m_character.PlayAnim("StopLook");
					m_character.m_onPathProcessed -= OnReturnPathReturned;
					break;
			}

		}
		
		public override void OnUpdate()
		{
			bool hasArrived = HasAgentArrived();
			bool isAtObjective = m_character.IsAtObjectiveWaypoint();
			if(hasArrived || isAtObjective)
			{
				if(hasArrived == false)
				{
					m_character.m_nav.StopNavigation();
				}
				
				if(m_character.ObjectiveWaypoint != null)
				{
					if(isAtObjective)
					{
						m_character.ObjectiveWaypoint = null;
						m_character.LastPatrolPath = null;
						ApplyState(CharacterStates.RoamForTarget);		
						return;
					}
					m_character.LastPatrolPath = null;
					ApplyState(CharacterStates.PatrolToWaypoint);
					return;
				}
				else
				{	
					ApplyState(CharacterStates.RoamForTarget);
					return;
				}
			}
			
			base.OnUpdate();
		}

		public override void OnExit()
		{
			base.OnExit();
			m_character.m_onPathProcessed -= OnReturnPathReturned;
		}
	}
}