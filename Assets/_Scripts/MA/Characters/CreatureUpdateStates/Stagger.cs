using System;
using UnityEngine;

namespace MACharacterStates
{
    [Serializable]
    public class Stagger : CommonState
    {
        public Stagger(string _state, MACharacterBase _character) : base(_state, _character)
        {
        }

        private const float c_stopDrag = 0.5f;
        private float m_dragBackup = 0f;

        public override void OnEnter()
        {
            base.OnEnter();

            m_character.BlendAnimatorLayerWeight("Combat", 1);
            m_character.m_nav.PushPause("Stagger", true, true);
            m_character.SetCanBePushed(false);

            m_dragBackup = m_character.RigidBody.linearDamping;
            m_character.RigidBody.linearDamping = c_stopDrag;
        }

        public override void OnUpdate()
        {
            base.OnUpdate();

            m_character.AddExperience(m_character.CreatureInfo.m_inCombatExp * Time.deltaTime, "fight");

            if (m_gameStateData.m_timeInState < m_character.CharacterSettings.m_staggerCooldown)
                return;

            if (EvaluateTarget() == false)
            {
                ApplyState(CharacterStates.RoamForTarget);
            }
        }

        public override void OnExit()
        {
            base.OnExit();

            m_character.RigidBody.linearDamping = m_dragBackup;
            m_character.m_nav.PopPause("Stagger");
            m_character.SetCanBePushed(true);
        }
    }
}