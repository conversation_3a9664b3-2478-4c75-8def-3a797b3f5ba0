using System;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class EatPickup : CommonState
	{
		private const float c_maxFindTime = 8f; //if hasnt started eating after x seconds we break out of state. to prevent doggetting stuck if food is in a awkward position
		private const float c_maxEatTime = 8f;
		private float m_eating = -1f;

		public EatPickup(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

//speed, object, anim, what to do (destroy), offset/range,
		public override void OnEnter()
		{
			base.OnEnter();
			
			m_eating = -1f;
			TargetObject target = m_character.TargetObject;
			var isValid = IsPickupValid(target);
			if (isValid.isValid)
			{
				m_character.m_nav.StopNavigation();
				m_character.m_onPathProcessed -= OnPathReturned;
				m_character.m_onPathProcessed += OnPathReturned;
				m_character.SetMoveToObject(target.gameObject, PeepActions.None);
				m_character.SetSpeed(m_character.CharacterGameState.AttackSpeed);
			}
			else
			{
				ApplyState(m_character.DefaultState);
			}
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			if (m_eating >= 0f)
			{
				m_eating += Time.deltaTime;
				if (m_eating > c_maxEatTime)
				{
					ApplyState(m_character.DefaultState);
				}

				return;
			}
			else if (m_gameStateData.m_timeInState > c_maxFindTime)
			{
				ApplyState(m_character.DefaultState);
				return;
			}

			TargetObject target = m_character.TargetObject;
			var validPickup = IsPickupValid(target);
			if (validPickup.isValid == false)
			{
				ApplyState(m_character.DefaultState);
				return;
			}

			var targetPos = target.transform.position;
			float dist = (targetPos - m_character.transform.position).xzSqrMagnitude();
			if (dist < 3f)
			{
				m_eating = 0f;
				m_character.m_nav.StopNavigation();
				validPickup.pickup.DestroyMe();
				m_character.LookAt(targetPos, 120f, () =>
				{
					if (!m_character.PlayEatPickupAnim("Eat"))
					{
						ApplyState(m_character.DefaultState);
					}
				});
			}
			else if (HasAgentArrived())
			{
				m_character.SetMoveToObject(target.gameObject, PeepActions.None);
				m_character.SetSpeed(m_character.CharacterGameState.AttackSpeed);
			}
		}

		public override void OnExit()
		{
			base.OnExit();
			m_character.m_onPathProcessed -= OnPathReturned;
		}

		protected (bool isValid, ReactPickup pickup) IsPickupValid(TargetObject _pickup)
		{
			if (_pickup == null)
				return (false, null);

			if (_pickup.TargetObjectType != TargetObject.TargetType.Pickup)
				return (false, null);

			if (_pickup.gameObject.activeSelf == false)
				return (false, null);

			ReactPickup pickup = _pickup.GetComponent<ReactPickup>();
			if (pickup == null)
				return (false, null);

			if (_pickup.transform.parent != GlobalData.Me.m_pickupsHolder)
				return (false, pickup);

			return (true, pickup);
		}

		protected void OnPathReturned(NGMovingObject.TargetReachability _targetReachability)
		{
			switch (_targetReachability)
			{
				case NGMovingObject.TargetReachability.IsReachable:
					break;
				case NGMovingObject.TargetReachability.IsNotReachable:
				case NGMovingObject.TargetReachability.IsNotReachableAndAlreadyAtNearestPos:
				case NGMovingObject.TargetReachability.PathFindingAlreadyInProgress:
				case NGMovingObject.TargetReachability.PathFailure:
					ApplyState(m_character.DefaultState);
					break;
			}
		}
	}
}