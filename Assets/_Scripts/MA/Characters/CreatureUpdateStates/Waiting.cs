using System;
using UnityEngine;

namespace MACharacterStates
{
	[System.Serializable]
	public class Waiting : Idle
	{
		public Waiting(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		protected float m_desiredWaitingTime = 0f; // 0 waits forever or until state is changed
		protected string s = "";

		private Coroutine m_waitingCoroutine = null;
		private float m_dragBackup = 0f;
		private float m_startTime = 0f;
		private float m_waitingTime = 0f;

		public override float WaitingTimeLeft => m_startTime > 0f ? m_startTime + m_waitingTime - Time.time : 0f;

		public override void OnEnter()
		{
			base.OnEnter();
			m_dragBackup = m_character.RigidBody.linearDamping;
			m_character.m_nav.Pause(false, true);
			Wait(0);
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
		}

		public override void OnExit()
		{
			base.OnExit();

			m_startTime = 0f;
			m_waitingTime = 0f;
			m_character.RigidBody.linearDamping = m_dragBackup;
			m_character.m_nav.Unpause();
		}

		private void Wait(float _durationSecs)
		{
			m_startTime = _durationSecs > 0 ? Time.time : 0f;
			m_waitingTime = _durationSecs;
		}
	}
	
	[System.Serializable]
	public class WaitForAnimationEnd : Waiting
	{
		public GameState_Character GameStateData => m_gameStateData;

		public WaitForAnimationEnd(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			base.OnEnter();
			
			string scheduledAnimSetKey = m_character.m_scheduledAnimSetKey;
			if (string.IsNullOrEmpty(scheduledAnimSetKey) || !m_character.PlayWaitForAnimationEndAnim(scheduledAnimSetKey))
			{ 
				ApplyState(m_gameStateData.m_backupState);
			}
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
		}

		public override void OnExit()
		{
			m_gameStateData.m_backupState = "";
			m_character.m_scheduledAnimSetKey = "";
			base.OnExit();
		}
	}
}