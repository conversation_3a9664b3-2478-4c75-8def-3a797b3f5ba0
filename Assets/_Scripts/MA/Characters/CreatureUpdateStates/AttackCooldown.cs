using System;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class AttackCoolDown : CommonState
	{
		public AttackCoolDown(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		// private float m_coolDownStart = -1;
		// private float m_coolDownStop = -1;

		private const float c_stopDrag = 0.5f;
		private float m_dragBackup = 0f;

		public override void OnEnter()
		{
			base.OnEnter();

            m_character.BlendAnimatorLayerWeight("Combat", 1);
			// m_character.SetCanBePushed(false);

			// m_dragBackup = m_character.RigidBody.linearDamping;
			// m_character.RigidBody.linearDamping = c_stopDrag;
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			m_character.AddExperience(m_character.CreatureInfo.m_inCombatExp * Time.deltaTime, "fight");
			//m_coolDownEndTime = Mathf.Max(m_coolDownEndTime, m_character.m_lastHitTime + m_character.CharacterSettings.m_staggerCooldown);

			if (!m_character.IsComboInCooldown())
			{
				if (EvaluateTarget() == false)
				{
					ApplyState(CharacterStates.RoamForTarget);
				}
			}
		}

		public override void OnExit()
		{
			//m_character.ResetCombo();

			base.OnExit();

			// m_character.RigidBody.linearDamping = m_dragBackup;
			// m_character.SetCanBePushed(true);
		}
	}
	
	public class WerewolfAttackCooldown : AttackCoolDown
	{
		public WerewolfAttackCooldown(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}
	}
}