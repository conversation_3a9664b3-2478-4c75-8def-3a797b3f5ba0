using System;
using UnityEngine;
using Random = UnityEngine.Random;
using Vector3 = UnityEngine.Vector3;

namespace MACharacterStates
{
	[Serializable]
	public class RoamForTarget : CommonState
	{
		public RoamForTarget(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override bool IsPatrolState => true;

		protected float m_tickEvaluateTarget = 1f;
		protected CharacterCollision m_characterCollision = null;
		public bool m_allowOutsideOpenDistricts = false;

		protected virtual float RoamingStandAroundMinTime => m_character.CharacterSettings.m_roamingStandAroundMinTime;
		protected virtual float RoamingStandAroundMaxTime => m_character.CharacterSettings.m_roamingStandAroundMaxTime;
		protected virtual float RoamingWalkAroundMinTime => m_character.CharacterSettings.m_roamingWalkAroundMinTime;
		protected virtual float RoamingWalkAroundMaxTime => m_character.CharacterSettings.m_roamingWalkAroundMaxTime;

		[ReadOnlyInspector] [SerializeField] protected float m_timeEvaluateTarget = -1;
		[ReadOnlyInspector] [SerializeField] protected float m_roamingStandAroundTime = -1;
		[ReadOnlyInspector] [SerializeField] protected float m_roamingWalkAroundTime = -1;
		private MATimer m_walkingTimer = null;
		private MATimer m_standingTimer = null;
		private bool m_isLeavingBuilding = false;
		private float m_backupAnimSpeed = 1f;

		protected bool IsPatrollingGroup
		{
			get { return (m_character.GroupBehaviour != null) && m_character.GroupBehaviour.CanPatrol; }
		}
		
		public override void OnEnter()
		{
			base.OnEnter();
			
			if (m_character.m_anim != null)
			{
				m_backupAnimSpeed = m_character.m_anim.speed;
			}
			
            m_character.BlendAnimatorLayerWeight("Combat", 0);
            m_character.SetCanBePushed(true);
            m_character.m_nav.Unpause();
            
            if(m_character.ObjectiveWaypoint != null)
            {
	            if(TryStartPatrol())
		            return;
            }
            
            m_character.SetTargetObj(null);

            if (m_character.m_insideMABuilding != null)
            {
	            m_character.SetMoveToPosition(m_character.m_insideMABuilding.DoorPosOuter);
            }
            else
            {
	            m_walkingTimer = new MATimer();
	            m_roamingWalkAroundTime = Time.time;
            }
            
			m_gameStateData.m_speed = m_gameStateData.WalkSpeed;

			m_characterCollision = m_character.GetComponent<CharacterCollision>();
			if(m_characterCollision != null)
			{
				m_characterCollision.m_onCollisionEnter += OnCollisionEnterWithCharacter;
			}
		}
		
		public override void OnUpdate()
		{
			base.OnUpdate();
			if(m_character.IsTimeToGoHome)
			{
				var pos = m_character.GetDespawnPosition();
				if((m_character.transform.position - pos).xzSqrMagnitude() > m_character.CharacterSettings.m_homeDistanceSq)
				{
					ApplyState(CharacterStates.GoingHome);
					return;
				}
			}
			
			if(m_character.ObjectiveWaypoint != null)
			{
				if(TryStartPatrol())
					return;
			}

			if (Time.time > m_timeEvaluateTarget)
			{
				if (EvaluateTarget())
					return;

				m_timeEvaluateTarget = Time.time + m_tickEvaluateTarget;
			}

			if(m_isLeavingBuilding)
			{
				if (HasAgentArrived())
				{
					m_walkingTimer = new MATimer();
					m_walkingTimer.Set(Random.Range(RoamingWalkAroundMinTime, RoamingWalkAroundMaxTime));
					m_standingTimer = null;

					if (TryMoveToSearchSpot() == false)
					{
						StandAround();
					}
				}
				return;
			}
			
			if (m_character.IsHarvestingCreataure)
				return;

			if (m_walkingTimer == null && m_standingTimer == null && HasAgentArrived())
			{
				//arrived at outerpos of insidebuilding
				m_walkingTimer = new MATimer();
				m_roamingWalkAroundTime = Time.time;
			}

			if (m_walkingTimer != null && (m_walkingTimer.IsFinished || HasAgentArrived()))
			{
                m_standingTimer = new MATimer();
				float standingTime = Random.Range(RoamingStandAroundMinTime, RoamingStandAroundMaxTime);
				if (IsPatrollingGroup)
					standingTime = m_character.GroupBehaviour.GetStandingTimeForCharacter(standingTime, m_character);
                m_standingTimer.Set(standingTime);
                m_walkingTimer = null;
                StandAround();
			}
			
			if (m_standingTimer != null && m_standingTimer.IsFinished)
			{
				if (!IsPatrollingGroup || m_character.GroupBehaviour.SetCanMoveAndCheckGroupCanRoam(m_character))
				{
					m_walkingTimer = new MATimer();
					float walkingTime = Random.Range(RoamingWalkAroundMinTime, RoamingWalkAroundMaxTime);
					if (IsPatrollingGroup)
						walkingTime = m_character.GroupBehaviour.GetWalkingTimeForCharacter(walkingTime, m_character);
					m_walkingTimer.Set(walkingTime);
					m_standingTimer = null;

					if (TryMoveToSearchSpot() == false)
					{
						StandAround();
					}
				}
			}
		}

		protected virtual bool TryStartPatrol()
		{
			if(m_gameStateData.m_guardObjectiveWaypoint)
			{
				if(m_character.IsWithinGuardRadius() == false)
				{
					ApplyState(CharacterStates.PatrolToWaypoint);//this one might need to be patrolToWayPoint because he wont attack in this state. or you can try to make gotoGuard somethign that does allow attacking. OR, keepthat state to make sure you run towards the closest valid chaseTarget distance of the current waypoint.
				}
				else //TS - prevent going into guard location if already in state?
				{
					ApplyState(CharacterStates.GuardLocation);
				}

				return true;
			}
			ApplyState(CharacterStates.PatrolToWaypoint);
			return true;
		}

		protected virtual void StandAround()
		{
			//m_character.m_nav.Pause(true, true);
			
			if(m_character.m_anim)
			{
				m_character.m_anim.speed = m_backupAnimSpeed + Random.Range(-0.1f, 0.1f);
			}
		}

		protected virtual Vector3? ScanForValidSpot()
		{
			return MACharacterBase.ScanForNavigableRandomPos(m_character.transform.position, 0f , m_character.CreatureInfo.m_visionRadius, m_allowOutsideOpenDistricts, GlobalData.AllowNavType.AnyPerfectNav);
		}
		
		protected virtual bool TryMoveToSearchSpot()
		{
			if(m_character.m_nav.PathPending)
				return false;

			Vector3? newPos = null;
			if (IsPatrollingGroup)
				newPos = m_character.GroupBehaviour.GetRoamDestinationForCharacter(m_character);
			else
				newPos = ScanForValidSpot();
			
			if(newPos == null)
				return false;

			MoveToSearchSpot((Vector3)newPos);
			
			return true;
		}

		protected virtual void MoveToSearchSpot(Vector3 _newPos)
		{
			if (m_character.m_anim)
			{
				m_character.m_anim.speed = m_backupAnimSpeed;
			}

			m_character.m_nav.Unpause();

			m_character.m_onPathProcessed -= OnPathReturned;
			m_character.m_onPathProcessed += OnPathReturned;

			m_gameStateData.m_speed = m_gameStateData.WalkSpeed;
			m_character.SetMoveToPosition(_newPos);
		}

		protected void OnCollisionEnterWithCharacter(Collision _other)
		{
			MACharacterBase otherCharacter = _other.gameObject.GetComponent<MACharacterBase>();
			if(otherCharacter.TargetObject == null || otherCharacter.TargetObject.GetComponent<MACharacterBase>() != m_character)
			{
				if(m_roamingWalkAroundTime == -1)
				{
					Transform charTr = m_character.transform;
					Vector3 charPos = charTr.position;
					m_roamingWalkAroundTime = Time.time + Random.Range(RoamingWalkAroundMinTime, RoamingWalkAroundMaxTime);
					m_roamingStandAroundTime = -1;
					MoveToSearchSpot(charPos + (charPos - otherCharacter.transform.position).normalized * 2);
				}
			}
		}

		protected void OnPathReturned(MACreatureBase.TargetReachability _targetReachability)
		{
			switch(_targetReachability)
			{
				case MACreatureBase.TargetReachability.IsReachable:
					m_character.m_onPathProcessed -= OnPathReturned;
					//m_character.PlayAnim("Walk");
					break;
				case MACreatureBase.TargetReachability.IsNotReachable:
					m_character.m_onPathProcessed -= OnPathReturned;
					//m_character.PlayAnim("Walk");
					break;
				case MACreatureBase.TargetReachability.IsNotReachableAndAlreadyAtNearestPos:
					m_character.m_onPathProcessed -= OnPathReturned;
					//m_character.PlayAnim("StopLook");
					break;
				case MACreatureBase.TargetReachability.PathFindingAlreadyInProgress:
					break;
				case MACreatureBase.TargetReachability.PathFailure:
					m_character.m_onPathProcessed -= OnPathReturned;
					//m_character.PlayAnim("StopLook");
					break;
			}
		}

		public override void OnExit()
		{
			base.OnExit();

			if (m_character.m_anim != null)
			{
				m_character.m_anim.speed = m_backupAnimSpeed;
			}
			
			m_character.m_onPathProcessed -= OnPathReturned;
			if(m_characterCollision != null)
			{
				m_characterCollision.m_onCollisionEnter -= OnCollisionEnterWithCharacter;
				m_characterCollision = null;
			}
		}
	}

	[Serializable]
	public class WerewolfRoamForTarget : RoamForTarget
	{
		[ReadOnlyInspector] [SerializeField] protected new MAWerewolf m_character = null;

		public WerewolfRoamForTarget(string _state, MACharacterBase _character) : base(_state, _character)
		{
			m_character = _character as MAWerewolf;
		}

		protected override void StandAround()
		{
			base.StandAround();
		}
	}

	[Serializable]
	public class DogRoamTown : GuardLocation
	{
		public DogRoamTown(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}
		
		private float c_townRadius = 30f;
		private MATimer m_checkForNewLeader = new();
		public const float c_checkForNewLeaderAfter = 6f;
		
		private MADog Dog => m_character as MADog;

		public override void OnEnter()
		{
			if (m_character.CharacterSettings.m_isBodyGuard && m_character.IsQuestCharacter == false)
			{
				if (m_character.Leader == null)
				{
					var nearestTownTavern =
						NGManager.Me.FindBuildingsWithComponent(MAComponentInfo.GetInfoByClass(typeof(BCActionTavern)),
							out var result, true);
					int checkCount = 3;
					MABuilding closestTr = null;
					var bestv = Vector3.zero;
					float bestDistSq = Single.MaxValue;
					foreach (MABuilding building in result)
					{
						var v = (building.transform.position - m_character.transform.position);
						float distSq = v.xzSqrMagnitude();
						if (distSq < bestDistSq)
						{
							bestv = v;
							checkCount--;
							bestDistSq = distSq;
							closestTr = building;
							if (checkCount == 0)
								break;
						}
					}

					if (closestTr != null)
					{
						m_character.SetToGuard(closestTr.transform.position, c_townRadius);
					}
				}
			}

			m_checkForNewLeader = new MATimer(c_checkForNewLeaderAfter);
			
			base.OnEnter();			
		}

		public override void OnUpdate()
		{
			if (m_checkForNewLeader.IsFinished)
			{
				Dog.LeaderCheck();
				m_checkForNewLeader.Set(c_checkForNewLeaderAfter);
			}

			base.OnUpdate();
		}
	}
}