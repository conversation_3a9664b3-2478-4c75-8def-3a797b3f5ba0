using System.Collections;
using System.Collections.Generic;
using ClipperLib;
using UnityEngine;

public class Enclosure
{
    private PolygonCollider2D m_enclosedArea;
    private PolygonCollider2D m_outerWallPathArea;
    private float m_desiredScaleOffset = 3.5f;
    private Vector2[] m_outerPath = null;
    
    public PolygonCollider2D EnclosedArea => m_enclosedArea;
    public PolygonCollider2D OuterPath => m_outerWallPathArea;
    
    public void Execute(PathManager.Path _wallPath)
    {
        if((_wallPath.Set.m_isPerimeterType && _wallPath.IsCycle) == false) return;
        CalculateWallEnclosure(_wallPath);
    }
    
    public void CalculateOuterWallEnclosure(PathManager.Path _wallPath, float desiredScaleOffset)
    {
        const int c_scaleToInt = 1024;
        int scaleShift = 0;
        for (scaleShift = 0; 1 << scaleShift < c_scaleToInt; scaleShift++) {}
        
        List<List<IntPoint>> solution = new();
        List<IntPoint> intPoints = new(_wallPath.Count + 1);
        for(int i = 0; i < _wallPath.Count; i++)
        {
            intPoints.Add(new IntPoint(_wallPath.m_path[i].xz.x * c_scaleToInt, _wallPath.m_path[i].xz.y * c_scaleToInt));
        }

        intPoints.Add(new IntPoint(_wallPath.m_path[0].xz.x * c_scaleToInt, _wallPath.m_path[0].xz.y * c_scaleToInt));
        
        ClipperOffset co = new();
        co.AddPath(intPoints, JoinType.jtMiter, EndType.etClosedPolygon);
        co.Execute(ref solution, (double)c_scaleToInt * desiredScaleOffset);

        /*string testout = "OUTPUT: \n";
        foreach (IntPoint point in solution[0])
        {
            testout += "\n" + point.X + "/" + point.Y + " -> " + point.X / c_scaleToInt + "/" + point.Y / c_scaleToInt;
        }
        Debug.Log(testout);*/
        
        m_outerPath = new Vector2[_wallPath.Count + 1];
                    
        long sum = 0;
        IntPoint v1 = solution[0][^1];
        for(int i = 0; i < solution[0].Count; i++)
        {
            IntPoint v2 = solution[0][i];
            sum += (v2.X - v1.X) * (v2.Y + v1.Y);
            v1 = v2;
            
            DoublePoint dPoint = new(solution[0][i].X / c_scaleToInt/*>> scaleShift*/, solution[0][i].Y / c_scaleToInt/*>> scaleShift*/);
            
            m_outerPath[i].x = (float)dPoint.X;
            m_outerPath[i].y = (float)dPoint.Y;
        }

        IsClockwise = sum > 0;
        //Debug.Log($"isClockWise {IsClockwise}");
        
        DoublePoint lastDPoint = new(solution[0][0].X >> scaleShift, solution[0][0].Y >> scaleShift);
        m_outerPath[_wallPath.m_path.Count].x = (float)lastDPoint.X;
        m_outerPath[_wallPath.m_path.Count].y = (float)lastDPoint.Y;

        m_outerWallPathArea.points = m_outerPath;
    }

    public bool IsClockwise
    {
        get;
        private set;
    } = false;
    
    // public static bool IsClockwise(IList<(double X, double Y)> vertices)
    // {
    //     double sum = 0.0;
    //     var v1 = vertices[^1];
    //     for (int i = 0; i < vertices.Count; i++) {
    //         var v2 = vertices[i];
    //         sum += (v2.X - v1.X) * (v2.Y + v1.Y);
    //         //Console.WriteLine($"(({v2.X,2}) - ({v1.X,2})) * (({v2.Y,2}) + ({v1.Y,2})) = {(v2.X - v1.X) * (v2.Y + v1.Y)}");
    //         v1 = v2;
    //     }
    //     //Console.WriteLine(sum);
    //     return sum > 0.0;
    // }

    public void CalculateWallEnclosure(PathManager.Path _wallPath)
    {
        if((_wallPath.Set.m_isPerimeterType && _wallPath.IsCycle) == false) return;
                   
        Vector2[] points = new Vector2[_wallPath.Count + 1];
        for(int i = 0; i < _wallPath.Count; i++)
        {
            points[i] = _wallPath.m_path[i].xz;
            if(Mathf.Approximately(points[i].x, 0f) && Mathf.Approximately(points[i].y, 0))
            {
                Debug.LogError($"wall path set {_wallPath.Set.name} has a point at 0,0");
            }
        }

        points[_wallPath.m_path.Count] = _wallPath.m_path[0].xz;
        
        m_enclosedArea.points = points;
        m_enclosedArea.autoTiling = false;

        //m_enclosures[wall] = _polygonCollider2D;
    
        // if(meshFilter != null) //gizmos only
        // {
        //     meshRenderer.material.color = new Color(Color.green.r, Color.green.g, Color.green.b, 0.25f);
        //     meshFilter.mesh = _polygonCollider2D.CreateMesh(false, false);
        // }

        //iEnclosurePolygon++;
        
        CalculateOuterWallEnclosure(_wallPath, m_desiredScaleOffset);
    }

    public void DestroyMe()
    {
        if(m_enclosedArea != null) GameObject.Destroy(m_enclosedArea.gameObject);
        else if(m_outerWallPathArea != null) GameObject.Destroy(m_outerWallPathArea.gameObject);
    }

    public Enclosure(PolygonCollider2D _collider, PolygonCollider2D _colliderWide, float _desiredScaleOffset)
    {
        m_enclosedArea = _collider;
        m_outerWallPathArea = _colliderWide;
        m_desiredScaleOffset = _desiredScaleOffset;
    }
}
    