using System;

namespace MACharacterStates
{
	[Serializable]
	public class Dying : CommonState
	{
		public Dying(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			base.OnEnter();

			if (m_character.IsHarvestingCreataure)
			{
				if (m_character.Carrying != null)
					m_character.Carrying.Drop(GlobalData.Me.m_pickupsHolder);
			}

			bool canRecover = m_character.CreatureInfo.m_deadHealthRecoveryRate > 0;
			m_character.m_bodyToBodyCollider.enabled = false;
			m_character.SetTargetObj(null);
			m_character.SetCanBePushed(canRecover);
			m_character.m_nav.Pause(true, true);

			if (canRecover == false)
			{
				MAManaBall.Create(m_character.transform, m_character.CreatureInfo);
			}

			m_character.SetDead();
			
			if (canRecover)
			{
				m_gameStateData.m_consciousness = (int)MAHeroBase.Consciousness.UnconsciousDead;
				ApplyState(CharacterStates.UnconsciousDead);
			}
			else
			{
				m_gameStateData.m_consciousness = (int)MAHeroBase.Consciousness.Dead;
			}

			m_character.CharacterDead?.Invoke(m_character);
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
			if (m_character.CharacterSettings.m_dyingTime <= m_gameStateData.m_timeInState)
			{
				ApplyState(CharacterStates.Dead);
			}
		}

		public override void OnExit()
		{
			base.OnExit();

			//m_character.SetCanBePushed(false);
		}
	}
}