using System;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class AttackFar : CommonState
	{
		float m_attackStartTime = -1;
		private bool m_jumping = false;

		private bool m_waitingForJumpPath = false;

		private Transform m_attackFarCollider = null;

		public AttackFar(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}
		
		private Transform AttackFarCollider
		{
			get
			{
				if(m_attackFarCollider == null)
					m_attackFarCollider = Array.Find(m_character.transform.GetComponentsInChildren<Transform>(true),
						x => x.name == "WorkerToCreatureAttackFarCollider");
				return m_attackFarCollider;
			}
		}
		
		public override void OnEnter()
		{
			base.OnEnter();

			if(AttackFarCollider != null)
				m_attackFarCollider.gameObject.SetActive(false);

			TargetObject target = m_character.TargetObject;
			if(target == null || m_character.IsTargetAliveAndValid(target) == false ||
			   m_character.IsTargetVisible(target.transform.position) == false)
			{
				ApplyState(CharacterStates.ChaseTarget);
				return;
			}

			m_attackStartTime = Time.time + m_character.CharacterSettings.m_jumpDelay;
			m_character.transform.LookAt(m_character.TargetObject.transform);
			m_character.m_nav.Pause(false, true);
			m_character.m_onRangeAttackFired -= OnRangeAttackFired;
			m_character.m_onRangeAttackFired += OnRangeAttackFired;
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
			
			TargetObject target = m_character.TargetObject;
			if (target != null && target.TargetObjectType == TargetObject.TargetType.CharacterType)
			{
				MACharacterBase enemy = target.GetComponent<MACharacterBase>();			
				m_character.AddExperience(m_character.CreatureInfo.m_inCombatExp * enemy.Level, "fight");
			}
			
			if(m_jumping == false)
			{
				if(m_character.TargetObject != null)
				{
					m_character.transform.LookAt(target.transform);
				}

				if(m_attackStartTime > -1 && Time.time > m_attackStartTime)
				{
					if(m_character.IsTargetAliveAndValid(m_character.transform) == false ||
					   m_character.IsTargetVisible(target.transform.position) == false)
					{
						ApplyState(CharacterStates.ChaseTarget); //look for target of chase target?
						return;
					}
					else
					{
						target.PrepareToTakeDamage();
						m_character.PrepareToDoDamage();
						
						Debug.Log("AttackFar AttackStart!");
						m_character.m_attackJumpTarget = target.transform.position.GetXZ();
						m_gameStateData.m_speed = m_gameStateData.AttackSpeed * 2f;
						m_character.SetMoveToPosition(m_character.m_attackJumpTarget, true);
						m_jumping = true;
						if(AttackFarCollider != null)
							m_attackFarCollider.gameObject.SetActive(true);
						// m_character.PlayAnim("JumpToTarget");
					}
				}
			}
			else
			{
				bool arrived = HasAgentArrived();
				bool attackTimeElapsed =
					Time.time > m_attackStartTime +
					Mathf.Max(m_character.CharacterSettings.m_jumpDuration);
				if(arrived || attackTimeElapsed)
				{
					AttackSuccess();
					return;
				}
			}
		}

		private void AttackSuccess()
		{			
			//make sure target listens for collision in this case (different from close attacks)
			m_jumping = false;
			//m_character.PlayAnim("Stop");

			if(m_character.IsTargetAliveAndValid(m_character.transform) == false)
			{
				ApplyState(CharacterStates.ChaseTarget);
			}
			else
			{
				m_character.DoAttackOnTarget();
				ApplyState(CharacterStates.AttackCooldown);
			}

			if(AttackFarCollider != null)
				m_attackFarCollider.gameObject.SetActive(false);
		}
		
		private void OnRangeAttackFired()
		{
			// m_character.m_onRangeAttackFired -= OnRangeAttackFired;
			AttackSuccess();
		}

		public override void OnExit()
		{
			base.OnExit();
			m_character.m_nav.Unpause();
			m_character.m_nav.Unpause();
			m_character.ResetDoDamage();
			m_character.TargetObject.ResetTakeDamage();

			m_character.m_onRangeAttackFired -= OnRangeAttackFired;
		}
	}

// public class ZombieAttack : Attack
// {
// 	public ZombieAttack(MACreatureBase.CREATURE_STATE _state, MACharacterBase _character) : base(_state, _character) { }
//
// 	public override void OnEnter() { base.OnEnter(); }
//
// 	public override void OnUpdate() {
// 		base.OnUpdate();
// 		//m_character.StateCreatureAttack();
// 	}
// 	public override void OnExit() { base.OnExit(); }
// }
}
