using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;

[System.Serializable]
public class MACreatureInfo : MAMovingInfoBase
{ 
    public const string CreaturePrefabPath = "_Prefabs/Creatures/";
    public bool IsAttack=>m_behaviour=="Attack";
    public static List<MACreatureInfo> s_creatureInfos = new List<MACreatureInfo>();
    public static List<MACreatureInfo> GetList=>s_creatureInfos; 
    
    public string m_creatureType;
    public int m_forceBodyType = -1;
    public string m_behaviour;
    public float m_visionRadius;
    public float m_proximityRadius;
    public float m_lowAttackSpeed;
    public float m_highAttackSpeed;
    public float m_attackRate;
    public float m_scale;
    public float m_killExp;
    public float m_inCombatExp;
    public float m_walkingExp;
    //public float m_healthNightRegeneration;
    public float m_deadHealthRecoveryRate;
    public float m_thownObjectDamageMultiplier;

    public string m_nightSprite;
    public string m_introDescription;
    public bool m_showNewCreatureFlag;
    
    private int m_isDayWalker = -1;
    private int m_isNightWalker = -1;
    //anytime
    public bool IsDayWalker
    {
        get
        {  
            if (m_isDayWalker >= 0) return m_isDayWalker == 1;
            string dn = m_dayNightSpawn.ToLower();
            var isDayWalker = dn.IsNullOrWhiteSpace() || dn.Contains("anytime") || dn.Contains("dayonly");
            m_isDayWalker = isDayWalker ? 1 : 0;
            return isDayWalker;
        }
    }
    
    public bool IsNightWalker
    {
        get
        {  
            if (m_isNightWalker >= 0) return m_isNightWalker == 1;
            string dn = m_dayNightSpawn.ToLower();
            var isNightWalker = dn.IsNullOrWhiteSpace() || dn.Contains("anytime") || dn.Contains("nightonly");
            m_isNightWalker = isNightWalker ? 1 : 0;
            return isNightWalker;
        }
    }

    [ScanField] public string m_workerTargets;
    [ScanField] public string m_componentTargets;
    [ScanField] public string m_creatureTargets;
    
    public List<int> m_canSmashWallTypes = new();
    
    public float m_despawnTime;
    public string m_dayNightSpawn;
    public int m_bloodColour;
    public int m_bloodAmount;

    public float m_takesWallDamage;

    [ScanField] public string m_attackCombo;
    [NonSerialized]
    public MAAttackCombo m_attackComboObj = null;
    [ScanField] public string m_runeCombo;
    [NonSerialized]
    public MAAttackCombo m_runeComboObj = null;
    [ScanField] public string m_rangedCombo;
    [NonSerialized]
    public MAAttackCombo m_rangedComboObj = null;
    
    public bool m_isGroup;
    public float m_groupPatrolRadius;

    override public float GetDamageModifier(string _damgeType)
    {
        // TODO
        return 1;
    }

    public GameState_Design PickRandomArmourDesign()
    {
        //return new GameState_Design("3|MA_Armour_Mannequin@|MA_Armour_LeatherGL_Chest@|MA_Armour_CheapPadded_Arms@|2|1.1.0.1.0|2.0.0.2.0|");
        if (string.IsNullOrEmpty(m_defaultArmour)) return null;
        var bits = m_defaultArmour.Split('\n', StringSplitOptions.RemoveEmptyEntries);
        if (bits.Length == 0) return null;
        return new GameState_Design(bits.PickRandom());
    }

    public bool CanUseSearchRadius => m_proximityRadius > 0f;
    public bool CanUseDespawnTime => m_despawnTime > 0f;
        
    public MACharacterBase m_prefab;

    public HashSet<MAWorkerInfo> WorkerTargets
    {
        get
        {
            if (m_workerTargetInfos == null)
            {
                if(m_workerTargets.IsNullOrWhiteSpace() == false)
                {
                    var list = m_workerTargets.StringToList(MAWorkerInfo.GetInfo);
                    if (list != null && list.Count > 0)
                    {
                        m_workerTargetInfos = new HashSet<MAWorkerInfo>(list);
                        return m_workerTargetInfos;
                    }
                }
                m_workerTargetInfos = new();
            }
            return m_workerTargetInfos;
        }
    }
    
    public HashSet<MACreatureInfo> CreatureTargets
    {
        get
        {
            if (m_creatureTargetInfos == null)
            {
                if(m_creatureTargets.IsNullOrWhiteSpace() == false)
                {
                    var list = m_creatureTargets.StringToList(MACreatureInfo.GetInfo);
                    if (list != null && list.Count > 0)
                    {
                        m_creatureTargetInfos = new HashSet<MACreatureInfo>(list);
                        return m_creatureTargetInfos;
                    } 
                }
                m_creatureTargetInfos = new();
            }
            return m_creatureTargetInfos;
        }
    }
    
    public HashSet<MAComponentInfo> ComponentTargets
    {
        get
        {
            if (m_componentTargetInfos == null)
            {
                if(m_componentTargets.IsNullOrWhiteSpace() == false)
                {
                    var list = m_componentTargets.StringToList(MAComponentInfo.GetInfo);
                    if (list != null && list.Count > 0)
                    {
                        m_componentTargetInfos = new HashSet<MAComponentInfo>(list);
                        return m_componentTargetInfos;
                    }
                }
                m_componentTargetInfos = new();
            }
            return m_componentTargetInfos;
        }
    }

    private HashSet<MAMovingInfoBase> m_movingTargets = null;
    public override HashSet<MAMovingInfoBase> GetMovingInfoTargets()
    {
        if (m_movingTargets == null)
        {
            m_movingTargets = new HashSet<MAMovingInfoBase>();
            m_movingTargets.UnionWith(WorkerTargets);
            m_movingTargets.UnionWith(CreatureTargets);
        }
        return m_movingTargets;
    }

    private HashSet<MAWorkerInfo> m_workerTargetInfos = null;
    private HashSet<MACreatureInfo> m_creatureTargetInfos = null;
    private HashSet<MAComponentInfo> m_componentTargetInfos = null;
    
    public float GetNewAttackSpeed() => UnityEngine.Random.Range(m_lowAttackSpeed, m_highAttackSpeed);
    
    public MACreatureInfo Clone()
    {
        return (MACreatureInfo)this.MemberwiseClone();
    }
    public static bool PostImport(MACreatureInfo _what)
    {
        if (_what.m_prefabName.IsNullOrWhiteSpace() == false)
        {
            if(MACreatureControl.Me)
                _what.m_prefab = MACreatureControl.Me.GetCreaturePrefabByName(_what.m_prefabName);
        }

        if (_what.m_attackRate <= 0f)
        {
            Debug.LogError($"MACreatureInfo - PostImport - '{_what.m_name}'. Attack rate is {_what.m_attackRate}. Must be greater than 0 and default is 1");
            _what.m_attackRate = 1f;
        }

        if (_what.m_attackRate < 0.5f)
        {
            Debug.LogError($"MACreatureInfo - PostImport - '{_what.m_name}'. Beware Attack rate is very low at {_what.m_attackRate}. Does the animation have enough frames to make this look good?");
        }
        else if (_what.m_attackRate > 1.5f)
        {
            Debug.LogError($"MACreatureInfo - PostImport - '{_what.m_name}'. Beware Attack rate is very high at {_what.m_attackRate}. Is this intended?");
        }

		_what.m_attackComboObj = MAAttackCombo.GetByID(_what.m_attackCombo);
		_what.m_runeComboObj = MAAttackCombo.GetByID(_what.m_runeCombo);
		_what.m_rangedComboObj = MAAttackCombo.GetByID(_what.m_rangedCombo);
		_what.m_deathDropOptions = _what.m_deathDropFavour.Split("<br />");

        return true;
    }
    public static List<MACreatureInfo> LoadInfo()  // Must be loaded after blocks
    {
        s_creatureInfos = NGKnack.ImportKnackInto<MACreatureInfo>(PostImport);
        return s_creatureInfos;
    }

    public static List<MACreatureInfo> GetInfoByType(string _type) => s_creatureInfos.FindAll(o => o.m_creatureType.RemoveWhiteSpaceAndToLower().Equals(_type.RemoveWhiteSpaceAndToLower())); 
    public static MACreatureInfo GetInfo(string _name) => s_creatureInfos.Find(o => o.m_name.Equals(_name, StringComparison.OrdinalIgnoreCase));
}
