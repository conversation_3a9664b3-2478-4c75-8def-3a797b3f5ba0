using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using BehaviourDesignComponents;
using MACharacterStates;
using SaveContainers;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.EventSystems;
using Random = UnityEngine.Random;
using Vector2 = UnityEngine.Vector2;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class MABuilding : MABuildingSupport, IStrikePoint, IDamageReceiver
{
#region Fields

    [Header("MABuilding")] 
    [HideInInspector] public Transform m_blockHolder;
    public List<string> m_debugBuildingComponents = new List<string>();
    [HideInInspector] public double m_debugProductScore;
    [HideInInspector] public bool m_showingIcons = true;
    
    public List<BCBase> m_components = new ();
    private List<BCActionBase> m_actionComponents = new ();
    private List<BCStockIn> m_stockIns = new ();
    private List<BCStockOut> m_stockOuts = new ();
    public Dictionary<Type, List<BCBase>> m_componentsDict = new ();

    public override string DisplayName => GetBuildingTitle();
    public float m_CardHolderYOffset = 1.5f;
    public BuildingCardHolder CardHolder { get { return m_cardHolder; } set { m_cardHolder = value; } }
    private BuildingCardHolder m_cardHolder;
    public float m_workThisUpdate;
    public bool m_chimneySmokeThisUpdate; 
    [Multiline(10)]
    public string m_debugInfoString;
    [Multiline(10)]
    public string m_debugInfoStringByColumn;
    private bool m_haveComponentsChanged = false;
    public float m_nextDeliverTime = 0f;
    public void SetComponentsChanged() { m_haveComponentsChanged = true; }
    
        
    private float PowerAvailable { get { return m_power[m_currentPowerIndex]; } set { m_power[m_currentPowerIndex] = value; } }
    private float NextPower { get { return m_power[NextPowerIndex]; } set { m_power[NextPowerIndex] = value; } }
    private int NextPowerIndex => (m_currentPowerIndex+1)&1;
    private int m_currentPowerIndex = 0;
    private float[] m_power = new float[2];
    
    public float m_holdDuration = 0;
    public float m_speedupMultiplier = 0f;
    private Coroutine m_speedupDecayCoroutine;
    
    [NonSerialized] private int m_previousActionNameHash;
    public void InhibitCardHolder(bool _inhibit)
    {
        if (m_cardHolder != null && m_cardHolder.gameObject.activeSelf == _inhibit)
            m_cardHolder.gameObject.SetActive(!_inhibit);
    }
    public List<BCActionBase> ActionComponents => m_actionComponents;
    public List<BCStockIn> StockIns => m_stockIns;
    public List<BCStockOut> StockOuts => m_stockOuts;
    
    [HideInInspector] public bool m_debugSelectedForWindow;
    [HideInInspector] public bool m_debugShowComponets;

    public Action<MABuilding> BuildingDestroyed;
#endregion

#region Properties
/***** properties ******/
    public virtual float HealthNorm
    {
        get => m_stateData.m_healthNorm;
        set
        {
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
            if (value < m_stateData.m_healthNorm) // damaged this building
            {
                GameManager.Me.TrackBuildingDamage(this);
            }
#endif
            m_stateData.m_healthNorm = value;
        }
    }
    
    public float Health
    {
        get
        {
            float defValue = DefenseValueMax;
            return HealthNorm <= 0 ? HealthNorm : (HealthNorm * (defValue == 0 ? 1f : defValue));
        }
        set
        {
            if (value <= 0)
            {
                HealthNorm = 0f;
                return;
            }
            float defValue = DefenseValueMax;
            HealthNorm = Mathf.Clamp(value / (defValue == 0 ? 1f : defValue), 0f, Single.MaxValue);
        }
    }

    Vector3 IStrikePoint.StrikePoint => LocalHighestPoint;
    
#endregion

#region Overrides
    /***** override properties ******/
    virtual public bool BuildingHasDoor() => HasBuildingComponent<BCEntrance>();
    override public float ToProductScore {get => GetProductScore(); set { } }
    override public bool IsOperational => true;
    override public bool IsLongPressReceiver => true;

    #endregion
#region Awake & Start & Update
    override protected void Awake()
    {
        base.Awake();
        if (m_blockHolder == null) m_blockHolder = transform.Find("Visuals");
    }
    override protected void Start()
    {
        CheckCache();

        base.Start();
        if(NGManager.Me)
        {
            if(NGManager.Me.m_NGCommanderList.Contains(this) == false)
                NGManager.Me.m_NGCommanderList.Add(this);
            if(NGManager.Me.m_maBuildings.Contains(this) == false)
            {
                NGManager.Me.m_maBuildings.Add(this);
                PathManager.s_blockDataDirty = true; 
            }
        }
        
        StartCoroutine(WaitForActivate());
    }
    
    IEnumerator WaitForActivate()
    {
        if(IsSetPiece)
        {
            CheckSetPieceState();
            // Make sure all the components are loaded first
            var blocks = GetComponentsInChildren<Block>();
            foreach(var block in blocks)
            {
                block.AddComponents();
            }
            
            // Register all components
            foreach (var bc in GetComponentsInChildren<BCBase>(true))
            {
                AddComponent(bc);
            }
            StartupComponents();
            
            RefreshTopmost();
        }
        
        yield return new WaitUntil(() => (MADebugShowAllBlocks.m_isLoaded || (GameManager.Me && GameManager.Me.DataReady)) );
         
        ActivateSetPiece();
        
        yield return new WaitUntil(() => (MADebugShowAllBlocks.m_isLoaded || (GameManager.Me && GameManager.Me.LoadComplete)) );
        
        CheckActivate();
        
        if(IsPermanentlyLocked)
        {
            foreach(var sid in GetComponentsInChildren<ShowInDesign>(true))
            {
                var mr = sid.GetComponent<MeshRenderer>();
                if(mr) mr.enabled = false;
            }
        }
    }

    private void ActivateSetPiece()
    {
        if(IsSetPiece)
        {
            CheckSetPieceState();
            if (m_linkUID == 0) m_linkUID = BuildingPlacementManager.Me.GenerateBuildingId();
            GenerateDesignString();
        }
    }
    
    private static DebugConsole.Command s_getInspectionScore = new ("inspectbuilding", _s => 
    {
        RaycastHit hit;
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);

        if (Physics.Raycast(ray, out hit)) 
        {
            var ma = hit.collider.gameObject.GetComponentInParent<MABuilding>();
            if (ma != null)
            {
                Debug.LogError("InspectionResult: " + ma.GetInspectionScore());
            }
        }
    });
    public float GetInspectionScore()
    {
        var buildingBlocks = m_blockHolder.GetComponentsInChildren<Block>();
        float totalBedroomRating = 0;
        int bedrooms = 0;
        int entrances = 0;
        foreach(var cmp in m_components)
        {
            if(cmp as BCBedroom)
            {
                bedrooms++;
                var dec = cmp.Block.GetComponent<Decoration>();
                bool isDecorated = false, isPainted = false, isStickered = false, hasRoof = false;
                int bedroomRating = 1;

                if(dec != null)
                {
                    foreach(var group in dec.m_groups)
                    {
                        foreach(var entry in group.m_entries)
                        {
                            if(!string.IsNullOrEmpty(entry.m_data))
                            {
                                switch(entry.m_data[0])
                                {
                                    case DesignTableManager.c_paintId: isPainted = true; break;
                                    case DesignTableManager.c_patternId: isDecorated = true; break;
                                }
                            }
				            if(entry.m_stickers.Count > 0) isStickered = true;
                        }
                    }
                }
                
                var topMost = cmp.Block.GetTopmostBlock(buildingBlocks.ToList());
                if(topMost != null)
                {
                    if(topMost.BlockInfo.m_mADrawerInfos.Contains("roof", StringComparison.InvariantCultureIgnoreCase))
                    {
                        hasRoof = true;
                    }
                    else
                    {
                        var roof = topMost.GetComponent<BCRoof>();
                        hasRoof = roof != null;
                    }
                }
                
                if(isPainted || isStickered || isDecorated) bedroomRating += 2;
                if(hasRoof) bedroomRating++;
                totalBedroomRating += bedroomRating / 4f;
            }
            else if(cmp as BCEntrance)
            {
                entrances++;
            }
        }
        
        float score = bedrooms > 0 ? totalBedroomRating/bedrooms : 0;
        
        const float c_lessThan5BedroomsMultiplier = 1f;
        const float c_between5And7BedroomsMultiplier = 0.85f;
        const float c_greaterThan7BedroomsMultiplier = 0.7f;
        const float c_noEntranceMultiplier = 0.2f;
        
        if(bedrooms >= 8) score *= c_greaterThan7BedroomsMultiplier;
        else if(bedrooms >= 5) score *= c_between5And7BedroomsMultiplier;
        else if(bedrooms >= 1) score *= c_lessThan5BedroomsMultiplier;
        
        if(entrances == 0) score *= c_noEntranceMultiplier;

        return Mathf.Clamp(score, 0, 1f);
    }
    
    void CheckActivate()
    {
        UpdateBuildingName();

        if (GetComponent<MADebugBuildingCreator>())
        {
            GetComponent<MADebugBuildingCreator>()?.Create();
        }
        else
        {
            Activate();
        }
    }

    void GenerateDesignString()
    {
        if(m_stateData.m_buildingDesign != null && m_stateData.m_buildingDesign.m_design.IsNullOrWhiteSpace() == false)
            return;

        SDictionary<int,ArrayWrapper<long>> cmpIds = new();
        var snapshotFrom = DesignTableManager.Me.GenerateDesignData(false, Visuals.gameObject, false, cmpIds);
        
        DesignTableManager.Me.UpdateBuildingDesign(this, snapshotFrom, cmpIds);
    }
    
    private Dictionary<(string type, int radius), List<TreeHolder>> m_resourcesInRange = new();
    
    protected override void OnBuildingMoved()
    {
        RecacheAllTreeHolders();
    }
    
    public void RemoveTreeCache(string _type, int _radius)
    {
        m_resourcesInRange.Remove((_type, _radius));
    }
    
    private void RecacheAllTreeHolders()
    {
        foreach(var key in m_resourcesInRange.Keys)
        {
            CacheTreeHolders(key.type, key.radius);
        }
    }
    
    public List<TreeHolder> CacheTreeHolders(string _type, int _radius)
    {
        var index = (_type, _radius);
        var instances = TerrainPopulation.Me.InstancesInRange(transform.position, _radius, true);
        if(m_resourcesInRange.TryGetValue(index, out var list) == false)
        {
            list = new();
            m_resourcesInRange.Add(index, list);
        }
        list.Clear();
        foreach(var inst in instances)
        {
            var holder = inst.GetComponent<TreeHolder>();
            if(holder == null || holder.GetPrefab() == null || holder.m_treeType != _type)
                continue;
                
            list.Add(holder);
        }
        return list;
    }
    
    public List<TreeHolder> GetTreeHolders(string _type, int _radius)
    {
        m_resourcesInRange.TryGetValue((_type,_radius), out var list);
        if(list != null) return list;
        return CacheTreeHolders(_type,_radius);
    }
    
    public void EnableClicking(bool _value)
    {
        var click = gameObject.GetComponent<NGStandardClick>();
        var pickup = gameObject.GetComponent<Pickup>();
        
        if(_value)
        {
            if(click == null) click = gameObject.AddComponent<NGStandardClick>();
            if(pickup == null) pickup = gameObject.AddComponent<Pickup>();
            
            click.enabled = true;
            pickup.enabled = true;
        }
        else
        {
            if(click != null) Destroy(click);
            if(pickup != null) Destroy(pickup);
        }
    }
    
    public void EnableDragging(bool _value)
    {
        var pickupBehaviour = gameObject.GetComponent<BuildingPickupBehaviour>();
        var pickup = gameObject.GetComponent<Pickup>();
        
        if(_value)
        {
            if (pickupBehaviour == null) pickupBehaviour = gameObject.AddComponent<BuildingPickupBehaviour>();
            if(pickup == null) pickup = gameObject.AddComponent<Pickup>();
            
            pickupBehaviour.enabled = true;
            pickup.enabled = true;
        }
        else
        {
            if(pickupBehaviour != null) Destroy(pickupBehaviour);
        }
    }
    
    public void Activate()
    {
        bool needsNavRebase = false; // PDM - set this in the case where you need it, for the general case we don't want to do it
        if (needsNavRebase)
        {
            var buildingNav = Nav;
            if (buildingNav)
            {
                DoorPosInner = buildingNav.DoorInterior + transform.position;
                DoorPosOuter = buildingNav.DoorExterior + transform.position;
            }
        }
        SetupBalloons();
    }

    private int m_queueSlots = 0;

    public int ClaimQueueSlot()
    {
        for (int i = 0, bit = 1; i < 32; ++i, bit <<= 1)
        {
            if ((m_queueSlots & bit) == 0)
            {
                m_queueSlots |= bit;
                return i;
            }
        }
        return -1;
    }

    public void LoadQueueSlot(int _slot)
    {
        if (_slot == -1) return;
        var bit = 1 << _slot;
        if ((m_queueSlots & bit) != 0)
            Debug.LogError($"Loading queue slot {_slot} that is already occupied in {name}");
        m_queueSlots |= bit;
    }

    public void ReleaseQueueSlot(int _slot)
    {
        if (_slot < 0 || _slot >= 32) return;
        m_queueSlots &= ~(1 << _slot);
    }    

    public void DestroyStock()
    {
        foreach (var c in StockIns)
            c.DestroyStock();
        foreach (var c in StockOuts)
            c.DestroyStock();
    }
    public void ClearStock()
    {
        EjectInputs();
        EjectOutputs();
    }
    private void EjectInputs()
    {
        foreach (var c in StockIns)
            c.EjectAllStock();
    }

    private void EjectOutputs()
    {
        foreach (var c in StockOuts)
            c.EjectAllStock();
    }

    
    private static DebugConsole.Command s_toggleLoadSeedComponents = new ("loadseedcmp", _s => {
        NGBuildingInfoGUI buildingInfo = FindObjectOfType<NGBuildingInfoGUI>();
        if (buildingInfo == null)
        {
            Debug.LogError("command \"loadseedcmp\" only works on individual buildings. please select a building and open its info panel, then use the command");
            return;
        }
        MABuilding b = buildingInfo.m_building as MABuilding;
        b.m_stateData.m_shouldLoadSeedData = !b.m_stateData.m_shouldLoadSeedData;
        Debug.LogError($"Building {b.name} will {(b.m_stateData.m_shouldLoadSeedData ? "" : "not ")}load component data on new game");
    });
    private static DebugConsole.Command s_ejectinputscmd = new("ejectinputs", _s => {
        var ma = ContextMenuManager.Me.CurrentOwnerObject as MABuilding;
        if (ma != null)
            ma.EjectInputs();
    });
    private static DebugConsole.Command s_ejectoutputscmd = new("ejectoutputs", _s =>
    {
        var ma = ContextMenuManager.Me.CurrentOwnerObject as MABuilding;
        if (ma != null)
            ma.EjectOutputs();
    });
    private static DebugConsole.Command s_showResources = new("toggleresourcedisplay", _s =>
    {
        s_showResourcesInComponents = !s_showResourcesInComponents;
    });

#if UNITY_EDITOR || DEVELOPMENT_BUILD
    private bool m_debugWorkAudio = false;
    private static DebugConsole.Command s_debugworkcmd = new("debugworkaudio", _s => {
        var ma = ContextMenuManager.Me.CurrentOwnerObject as MABuilding;
        if (ma != null)
            Utility.SetOrToggle(ref ma.m_debugWorkAudio, _s);
    });
    private void DebugWorkAudio()
    {
        if (m_debugWorkAudio) m_workThisUpdate += .01f;
    }
#else
    private void DebugWorkAudio() {}
#endif
    protected override void OnPlantDestroyed()
    {
        RefreshOrderBoard();
        base.OnPlantDestroyed();
    }
    
    private List<ICardHolderSegment> GetCardSegments()
    {
        List<ICardHolderSegment> segments = new();
        foreach(var entry in m_componentsDict)
        {
            if(entry.Value.Count == 0) continue;
            
            foreach(var c in entry.Value)
            {
                var segment = c as ICardHolderSegment;
                
                if(segment == null) continue;
                
                segments.Add(segment);
                if(c.AllowMultipleSegmentsOnCardHolder == false)
                    break;    
            }
        }
        return segments;
    }
    
    public void RefreshOrderBoard()
    {
        CardHolder?.UpdatePosition(this);
        
        if(!GameManager.s_cardHoldersVisible || NGManager.Me.m_NGCommanderListOutOfRange.Contains(this) || IsDisabledByPlants)
            return;
        
        var segments = GetCardSegments();
        
        if(segments.Count == 0)
        {
            m_cardHolder?.DestroyMe();
            m_cardHolder = null;
        }
        else if(m_cardHolder == null)
        {
            m_cardHolder = BuildingCardHolder.Create(this, segments.ToArray());
        }
    }

    private bool m_possessed = false;
    public bool Possess()
    {        
        var turrets = BuildingComponents<BCActionTurret>();
        foreach(var t in turrets)
        {
            if(t.Possess())
            {
                m_possessed = true; 
                return true;
            }
        }
        return false;
    }
    
    public void PossessNext()
    {
        var turrets = BuildingComponents<BCActionTurret>();
        var turretList = turrets.ToList();
        
        if(turretList.Count <= 1) return;
        
        int currentIndex = 0;
        foreach(var turret in turretList)
        {
            if(turret.Unpossess())
            {
                currentIndex = turretList.IndexOf(turret);
                break;
            }
        }
        
        if(currentIndex < 0) return;
        
        for(int i = 0; i < turretList.Count; i++)
        {
            var nextIndex = (currentIndex + 1 + i) % turretList.Count;
            if(turretList[nextIndex].Possess())
            {
                m_possessed = true;
                break;
            }
        }
    }

    public void Unpossess()
    {
        m_possessed = false;
        var turrets = BuildingComponents<BCActionTurret>();
        foreach(var t in turrets)
        {
            if(t.Unpossess())
            {
                break;
            }
        }
    }
    
    public void RemoveCompletedOrderStock()
    {
        foreach(var cmp in m_components)
        {
            if(cmp is BCFactory || cmp is BCActionDispatch || cmp is BCStockBase)
            {
                cmp.RemoveCompletedOrderStock();
            }
        }
    }
    
    private void CheckCardHolderVisibility()
    {
        bool showCardHolder = GameManager.Me.IsPossessingBuilding == false && GameManager.Me.IsPossessing == false && m_temporarilyHidden == false;
        if (m_cardHolder != null && m_cardHolder.gameObject.activeSelf != showCardHolder)
            m_cardHolder.gameObject.SetActive(showCardHolder);
    }

    public bool m_isInhabited = false;
    
    private bool IsAnOutputResource(NGCarriableResource _res)
    {
        foreach(var outputtedRes in m_outputResources)
        {
            if(_res.IsProduct)
            {
                if(outputtedRes.IsProduct)
                {   
                    return true;
                }
            }
            else if(outputtedRes == _res)
            {
                return true;
            }
        }
        return false;
    }
    
    public NGStock GetStockToDeliver(NGCarriableResource _onlyThis)
    {
        NGStock stockToDeliver = new NGStock();
        
        if(_onlyThis == null || _onlyThis.IsNone)
            return stockToDeliver; 
        
        foreach (var c in m_components)
        {
            if(c as BCStockIn || c is not BCStockBase) continue;
            
            var stock = c.GetStock();
            
            foreach(var item in stock.Items)
            {
                if(item.m_stock <= 0) continue;
                
                if(_onlyThis.IsProduct)
                {
                    if(item.Resource.IsProduct)
                    {
                        stockToDeliver.AddOrCreateStock(item.Resource, item.m_stock);
                    }
                }
                else if(item.Resource == _onlyThis)
                {
                    stockToDeliver.AddOrCreateStock(item.Resource, item.m_stock);
                    return stockToDeliver;
                }
            }
        }
        return stockToDeliver;
    }
    
    private NGStock GetNonOutputStockToDeliver()
    {
        NGStock stockToDeliver = new NGStock();
        foreach (var c in m_components)
        {
            if(c as BCStockIn || c is not BCStockBase) continue;
            
            var stock = c.GetStock();
            
            foreach(var item in stock.Items)
            {
                if(item.m_stock <= 0 || IsAnOutputResource(item.Resource)) continue;
                
                stockToDeliver.AddOrCreateStock(item.Resource, item.m_stock);
            }
        }
        return stockToDeliver;
    }
    
    private const bool c_enableWorkersDeliveringOtherResourcesWhenIdle = true;
    private const bool c_workersConsiderAllResourcesTypesForDelivery = true;
    
    private void UpdateStockOutDelivery()
    {
        float time = Time.time;
        for(int i = 0; i < ActionComponents.Count; i++)
        {
            var producer = ActionComponents[i] as BCActionProducer;
            if(producer == null || producer.m_nextStockCheckTime > time) continue;
            
            var worker = producer.GetAvailableWorker();
            if(worker == null) continue;
            
            var s1 = GetStockToDeliver(producer.OutputResource);
            BestStockDestinationCheck bestDestination = GetBestStockDestination(s1);
            
            if(c_enableWorkersDeliveringOtherResourcesWhenIdle)
            {
                if(bestDestination.Empty && producer.HasStartedMaking == false)
                {
                    if(c_workersConsiderAllResourcesTypesForDelivery)
                    {
                        bestDestination = GetBestStockDestination(GetOutputStock(true));
                    }
                    else
                    {
                        var s2 = GetNonOutputStockToDeliver();
                        bestDestination = GetBestStockDestination(s2);
                    }
                }
            }
            
            producer.m_nextStockCheckTime = time + Random.Range(0.5f, 2f);
            producer.SendOutToDeliver(bestDestination, worker);
            break;
        }
    }
    
    private BuildingComponentsState m_componentsState = new();
    public bool IsTraining { get; set; }
    public bool IsHealing { get; set; }
    
    private void StartDecay()
    {
        EndDecay(); // Ensure no overlapping coroutines
        m_speedupDecayCoroutine = StartCoroutine(DecayWorkMultiplier());
    }
    
    private IEnumerator DecayWorkMultiplier()
    {
        m_holdDuration = Mathf.Min(m_holdDuration, NGManager.Me.m_decayWorkPerSecond * NGManager.Me.m_maxDecayWorkTime);
        while (m_holdDuration > 0)
        {
            m_holdDuration -= Time.deltaTime * NGManager.Me.m_decayWorkPerSecond;
            //m_holdDuration *= 1 - .006f.TCLerp();
            if (m_holdDuration < .001f) m_holdDuration = 0;
            UpdateSpeedupMultiplier();
            yield return null;
        }
    }
    
    private void EndDecay()
    {
        if (m_speedupDecayCoroutine != null)
        {
            StopCoroutine(m_speedupDecayCoroutine);
            m_speedupDecayCoroutine = null;
        }
    }
    
    public AkRTPCHolder m_tapMultiplierAudioSpeed;
    public AkEventHolder m_tapMultiplierEvent;
    public int m_tapMultiplierAudioHandle;
    
    private void UpdateSpeedupMultiplier()
    {
        // Adjust the divisor in the exponential formula to slow growth by 70%
        float growthRate = 2f * 1.7f; // Original rate (2f) scaled by 1.7 to slow by 70%
        m_speedupMultiplier = 4 * (1 - Mathf.Exp(-m_holdDuration / growthRate));
        //Debug.LogError($"Duration {m_holdDuration} mult {m_multiplier}");
    }
    
    public float m_handAnimationSpeed = 0f;
    
    private int m_buildingCacheLinkUID = -1;
    private string m_buildingCacheName = null;
    private string m_buildingCacheBigName = null;
    private static Dictionary<string, MABuilding> s_buildingCache = new(StringComparer22.InsensitiveTrimmed);
    private static Dictionary<string, List<MABuilding>> s_priorNameBuildingCache = new (StringComparer22.InsensitiveTrimmed);
    private void CheckCache()
    {
        if (m_linkUID != m_buildingCacheLinkUID)
        {
            m_buildingCacheLinkUID = m_linkUID;
            if (m_buildingCacheLinkUID != -1)
                s_buildingCache.Remove(m_buildingCacheLinkUID.ToString());
            s_buildingCache[m_linkUID.ToString()] = this;
        }
        if (m_buildingCacheName != name)
        {
            if (m_buildingCacheName != null && s_buildingCache[m_buildingCacheName] == this)
                s_buildingCache.Remove(m_buildingCacheName);
            s_buildingCache[name] = this;
            m_buildingCacheName = name;
        }
        if (m_buildingCacheBigName != Name)
        {
            if (m_buildingCacheBigName != null && s_buildingCache[m_buildingCacheBigName] == this)
                s_buildingCache.Remove(m_buildingCacheBigName);
            if (m_buildingCacheBigName != null && s_priorNameBuildingCache.TryGetValue(m_buildingCacheBigName, out var priorListFrom) && priorListFrom.Count > 0)
            {
                priorListFrom.Remove(this);
                if (priorListFrom.Count > 0)
                {
                    s_buildingCache[m_buildingCacheBigName] = priorListFrom[^1];
                    //Debug.LogError($"Restore clashed Name for {m_buildingCacheBigName} to {priorListFrom[^1].name}-{priorListFrom[^1].m_linkUID} from {name}-{m_linkUID}");
                    priorListFrom.RemoveAt(priorListFrom.Count-1);
                }
            }
            if (s_buildingCache.TryGetValue(Name, out var existing) && existing != null && existing != this)
            {
                if (s_priorNameBuildingCache.TryGetValue(Name, out var priorList) == false)
                    s_priorNameBuildingCache[Name] = priorList = new ();
                priorList.Add(existing);
                //Debug.LogError($"Name clash for {Name} in {existing.name}-{existing.m_linkUID} and {name}-{m_linkUID}");
            }
            s_buildingCache[Name] = this;
            m_buildingCacheBigName = Name;
        }
    }

    override protected void Update()
    {
        CheckCache();

        if(!GameManager.Me.DataReady)
            return;
        
        IsTraining = false;
        IsHealing = false;
        m_isInhabited = false;
        m_componentsState.Clear();
        
        foreach(var component in m_components)
        {
            if(component.Building == null) continue;
            component.PreUpdate(m_componentsState);
        }
        
        foreach(var component in m_actionComponents)
        {
            if(component.Building == null || component.m_workersAllocated.Count == 0) continue;
            component.UpdateInternal(m_componentsState);
        }
        
        foreach(var component in m_actionComponents)
        {
            if(component.Building == null || component.m_workersAllocated.Count > 0) continue;
            component.UpdateInternal(m_componentsState);
        }
        
        foreach(var component in m_components)
        {
            if(component.Building == null || component is BCActionBase) continue;
            component.UpdateInternal(m_componentsState);
        }
        
        UpdateStockOutDelivery();
        
        base.Update();
        
        DebugWorkAudio();

        CheckCardHolderVisibility();
            
        if(m_haveComponentsChanged)
        {
            UpdateBuildingName();
            Nav?.MoveBuilding();
            
            // To avoid messiness, we kill the cardholder and get the components to regenerate the cards
            CardHolder?.DestroyMe();
            
            RefreshOrderBoard();
            
            m_haveComponentsChanged = false;
            m_hasDragComponent = false;
            m_hasTapComponent = false;
            
            // Update component validity
            foreach(var c in m_components)
            {
                c.UpdateIsValid();
                
                if(c.m_info != null)
                {
                    m_hasDragComponent |= c.m_info.m_drag;
                    m_hasTapComponent |= c.m_info.m_tap;
                }
            }
            
            EnableDragging(m_hasDragComponent);
            EnableClicking(true);
            
            var buildingTitle = GetActionComponentBasedTitle();
            var buildingTitleHash = buildingTitle.GetHashCode();
            if (buildingTitleHash != m_previousActionNameHash && buildingTitle.IsNullOrWhiteSpace() == false && GameManager.Me.LoadComplete)
            {
                Utility.ShowObjectMessagePosition(buildingTitle, GetCentalPosition(), gameObject, 5, null, true);
            }
            m_previousActionNameHash = buildingTitleHash;
            
            if(HasBuildingComponent<BCFactory>() == false && Order.IsNullOrEmpty() == false)
            {
                RemoveOrder(Order);
            }
        }
        
        if(GameManager.Me.IsPossessing || GameManager.Me.IsPossessingBuilding)
        {
            m_tipGUI?.DestroyMe();
            m_tipGUI = null;
            CardHolder?.UpdatePosition(this);
        }
        else
        {
            ShowQuickTip();
        }
        
        // m_debugInfoString = GetBuildingInfoString();
        // m_debugInfoStringByColumn = GetBuildingInfoStringByColumn();
        if (DesignTableManager.IsDesignInPlace)
        {
            if (m_showingIcons == false)
            {
                foreach (var i in m_blockHolder.GetComponentsInChildren<MAComponentIcons>())
                    i.Toggle(true);

                m_showingIcons = true;
            }
        }
        else
        {
            if (m_showingIcons)
            {
                foreach (var i in m_blockHolder.GetComponentsInChildren<MAComponentIcons>())
                    i.Toggle(false);
                m_showingIcons = false;
            }
        }
    }

    public void LateUpdate()
    {
        PowerAvailable = 0;
        m_currentPowerIndex = NextPowerIndex;
        
        if(StockRefreshRequired)
        {
            StockRefreshRequired = false;
            RefreshStockRequirements();
        }

        var playAudio = false;
        if (PauseManager.IsPaused == false)
        {
            if (m_workAudioTriggers.Count > 0)
            {
                foreach (var audio in m_workAudioTriggers)
                    audio.Play(gameObject);
            }
            m_workAudioTriggers.Clear();
                        
            if(HasTapComponent)
            {
                JoltBlocks(m_speedupMultiplier - 2f);
                playAudio = m_speedupMultiplier > 0.1f;
                m_tapMultiplierAudioSpeed.SetValue(gameObject, m_speedupMultiplier);
            }
        }
        BCBase.PlayAudio(playAudio, m_tapMultiplierEvent, gameObject, ref m_tapMultiplierAudioHandle);
    }

    public void FixedUpdate()
    {
        m_workThisUpdate = 0f;
        m_chimneySmokeThisUpdate = false;
    }

 
    #endregion
    
#region Components
    public void AddComponent(BCBase _what)
    {
        var info = _what.GetInfo();
        if(info == null)
        {
            Debug.LogError($"Component of type: {_what.GetType()} has no MAComponentInfo!");
            return;
        }
        
        bool added = false;
        var type = _what.GetType();
        if (m_componentsDict.TryGetValue(type, out var cList))
        {
            if (cList.Contains(_what) == false)
            {
                cList.Add(_what);
                added = true;
            }
        }
        else
        {
            m_componentsDict.Add(type, new List<BCBase>() {_what});
            added = true;
        }

        var action = _what as BCActionBase;
        if (action && m_actionComponents.Contains(action) == false)
            m_actionComponents.Add(action);
        
        var stockIn = _what as BCStockIn;
        if(stockIn && m_stockIns.Contains(stockIn) == false)
            m_stockIns.Add(stockIn);
            
        var stockOut = _what as BCStockOut;
        if(stockOut && m_stockOuts.Contains(stockOut) == false)
            m_stockOuts.Add(stockOut);

        if (m_components.Contains(_what) == false)
        {
            m_components.Add(_what);
            added = true;
        }
        
        if (added)
        {
            m_hasFactoryAndStockIn = (HasBuildingComponent<BCFactory>() && HasBuildingComponent<BCStockIn>());
            
            NGManager.Me.AddBuildingComponentMapping(info, this);
            m_haveComponentsChanged = true;
            StockRefreshRequired = true;
            m_debugBuildingComponents.Add(_what.GetType().ToString());
        } 
    }

    public void ActivateAllComponents()
    {
        foreach(var c in m_componentsDict)
        {
            for(int i = 0; i < c.Value.Count; ++i)
            {
                c.Value[i].Activate(i, c.Value.Count);
            }
        }
    }
    
    public override void OnBuildingDesignChanged()
    {
        foreach(var component in m_components)
        {
            component.OnBuildingDesignChanged();
        }
    }
    
    public void StartupComponents()
    {
        List<(BCBase component, int indexOfComponentType, int quantityInBuilding)> toActivate = new();
        foreach(var c in m_componentsDict)
        {
            for(int i = 0; i < c.Value.Count; ++i)
            {
                var cmp = c.Value[i];
                int count = c.Value.Count;
                if(cmp.SetOwner(this, count, BlockDragAction.None)) toActivate.Add((cmp, i, count));
            }
        }
        
        if(GameManager.Me.DataReady)
        {
            foreach(var item in toActivate)   
            {
                item.component.Activate(item.indexOfComponentType, item.quantityInBuilding);
            }
            
            m_actionComponents.Sort((a,b) => a.m_uid > b.m_uid ? 1 : -1);
        }
        
        UpdateBuildingName();
        
        Nav?.MoveBuilding();

        //Do visualSwitcher
        m_switchObjects = new List<SwitchObjects>();
        foreach (var block in GetComponentsInChildren<Block>())
        {
            if (block.m_visualSwitcher != null)
                m_switchObjects.Add(block.m_visualSwitcher);
        }
    }
    
    private void UpdateBuildingName()
    {
        var newName = "";
        foreach(var entry in m_componentsDict)
        {
            int count = entry.Value.Count;
            if(count == 0) continue;
            if(entry.Value[0] is not BCActionBase) continue;
            var title = entry.Value[0].Title;
            if(title.IsNullOrWhiteSpace()) continue;
            var countStr = count > 1 ? "X"+count : ""; 
            newName += $"{title}{countStr}&";
        }
        newName = newName.TrimEnd('&');
        if (newName.IsNullOrWhiteSpace()) newName = "NoAction";
        
        gameObject.name = $"{newName}:{Name}>[{m_linkUID}]";
    }
    
    public void RemoveComponent(BCBase _what, BlockDragAction _action)
    {
        bool removed = false;
        var type = _what.GetType();
        int remainingSameTypeCount = 0;
        if (m_componentsDict.TryGetValue(_what.GetType(), out var cList))
        {
            if (cList.Contains(_what))
            {
                cList.Remove(_what);
                remainingSameTypeCount = cList.Count;
                if(cList.Count == 0)
                {
                    m_componentsDict.Remove(_what.GetType());
                }
                removed = true;
            }
        }
        
        var action = _what as BCActionBase;
        if (action && m_actionComponents.Contains(action)) 
            m_actionComponents.Remove(action);
            
        var stockIn = _what as BCStockIn;
        if (stockIn && m_stockIns.Contains(stockIn)) 
            m_stockIns.Remove(stockIn);
            
        var stockOut = _what as BCStockOut;
        if (stockOut && m_stockOuts.Contains(stockOut)) 
            m_stockOuts.Remove(stockOut);

        if (m_components.Contains(_what))
        {
            m_components.Remove(_what);
            removed = true;
        }

        if (removed)
        {
            m_hasFactoryAndStockIn = (HasBuildingComponent<BCFactory>() && HasBuildingComponent<BCStockIn>());
            MAComponentInfo bcInfo = _what.GetInfo();
            NGManager.Me.RemoveBuildingComponentMapping(bcInfo, this);
            m_haveComponentsChanged = true;
            StockRefreshRequired = true;
            _what.SetOwner(null, remainingSameTypeCount, _action);
            if (m_debugBuildingComponents.Contains(_what.GetType().ToString()))
                m_debugBuildingComponents.Remove(_what.GetType().ToString());
        }
    }
    
    public IEnumerable<T> BuildingComponents<T>(bool _checkBaseTypes = false) where T : BCBase
    {
        if (_checkBaseTypes)
        {
            // More efficient: use a generator only when needed
            return m_components.OfType<T>();
        }

        if (m_componentsDict.TryGetValue(typeof(T), out var tList))
        {
            // Avoid 'as T' which may return null silently — Cast<T>() is cleaner and throws if incorrect
            return tList.Cast<T>();
        }

        return Enumerable.Empty<T>();
    }
    /*public IEnumerable<T> BuildingComponents<T>(bool _checkBaseTypes = false) where T : BCBase
    {
        if (_checkBaseTypes)
        {
            foreach (var c in m_components)
            {
                if (c is T t)
                    yield return t;
            }
        }
        else if (m_componentsDict.TryGetValue(typeof(T), out var tList))
        {
            foreach (var bc in tList)
            {
                yield return bc as T;
            }
        }
    }*/

    public IEnumerable<BCBase> BuildingComponents(Type _type, bool _checkBaseTypes = false)
    {
        if (_checkBaseTypes)
        {
            // Use LINQ Where + no manual yield
            return m_components.Where(c => c.GetType().IsTypeOrSubclassOf(_type));
        }
    
        if (m_componentsDict.TryGetValue(_type, out var tList))
        {
            // Return list directly — no need to yield
            return tList;
        }
    
        // Always return empty enumerable if nothing found
        return Enumerable.Empty<BCBase>();
    }
    
    /*public IEnumerable<BCBase> BuildingComponents(Type _type, bool _checkBaseTypes = false)
    {
        if (_checkBaseTypes)
        {
            foreach (var c in m_components)
            {
                if (c.GetType().IsTypeOrSubclassOf(_type))
                    yield return c;
            }
        }
        else if (m_componentsDict.TryGetValue(_type, out var tList))
        {
            foreach(var c in tList)
                yield return c;
        }
    }*/

    public IEnumerable<BCBase> BuildingComponents<T, T1>(bool _checkBaseTypes = false)
    {
        if(_checkBaseTypes)
        {
            foreach (var c in m_components)
            {
                if (c is T || c is T1)
                    yield return c;
            }
        }
        else
        {
            if (m_componentsDict.TryGetValue(typeof(T), out var tList))
            {
                foreach(var bc in tList)
                {
                    yield return bc;
                }
            }
            if (m_componentsDict.TryGetValue(typeof(T1), out var tList1))
            {
                foreach(var bc in tList1)
                {
                    yield return bc;
                }
            }
        }
    }
    
    public IEnumerable<BCBase> BuildingComponents(params Type[] _types)
    {
        foreach(var type in _types)
        {
            if (m_componentsDict.TryGetValue(type, out var tList) && tList.Count > 0)
            {
                foreach(var item in tList)
                    yield return item;
            }
        }
    }

    public int GetBuildingComponentCount()
    {
        return m_components.Count;
    }

    public bool HasValidEntrance()
    {
        var nav = Nav; 
        if(nav != null && nav.HasDoor == false)
        {
            return false;
        }

        return true;
    }
    public int GetBuildingComponentCount(Type _type, bool _checkBaseTypes = false)
    {
        int count = 0;
        if (_checkBaseTypes)
        {
            foreach (var c in m_components)
            {
                if (c.GetType().IsTypeOrSubclassOf(_type))
                    count++;
            }
        }
        else if (m_componentsDict.TryGetValue(_type, out var cList))
            count = cList.Count;
        return count;
    }
    public List<BCBase> GetComponentsWithInfo(MAComponentInfo _info)
    {
        List<BCBase> result = new List<BCBase>();
        var comps = BuildingComponents(_info?.m_classType, false);
        foreach (var c in comps)
        {
            if(c.m_info == _info)
                result.Add(c);
        }
        return result;
    }

    public bool IsInDistrict(string _district)
    {
        return DistrictID.ToLower().Equals(_district.ToLower().Trim());
    }
    public bool HasComponentWithInfo(MAComponentInfo _info)
    {
        var comps = BuildingComponents(_info?.m_classType, false);
        foreach (var c in comps)
        {
            if(c.m_info == _info)
                return true;
        }
        return false;
    }
    
    public int GetBuildingComponentCount(MAComponentInfo _info, bool _matchByClass = false)
    {
        if(_info == null || _info.m_classType == null)
            return 0;
        var count = 0;
        if (m_componentsDict.TryGetValue(_info.m_classType, out var cList) == false) return 0;
        if(_matchByClass)
            return cList.Count;
            
        foreach(var c in cList)
        {
            var i = c.GetInfo();
            if (i == _info)
                count++;
        }
        return count;
    }
    public bool HasBuildingComponent<TComponent>()  where TComponent : BCBase => m_componentsDict.ContainsKey(typeof(TComponent));
    public bool HasBuildingComponent(Type _type) => m_componentsDict.ContainsKey(_type);

    public List<BCBase> GetComponentsWithWorker(MAWorker _worker)
    {
        var results = new List<BCBase>();
        foreach (var c in m_components)
        {
            if(c.IsAllocated(_worker))
                results.Add(c);
        }

        return results;
    }
    public void GetUniqueWorkersAllocated(List<MAWorker> _workers)
    {
        foreach (var c in m_components)
        {
            foreach(var cb in c.GetWorkersAllocated())
            {
                var worker = cb as MAWorker;
                if(_workers.Contains(worker) == false)
                {
                    _workers.Add(worker);
                }
            }
        }
    }
    
    public bool ConsumePower(ref float _powerNeeded)
    {
        if(_powerNeeded <= 0) return true;
        
        var powerToTake = Mathf.Min(PowerAvailable, _powerNeeded);
        _powerNeeded -= powerToTake;
        PowerAvailable -= powerToTake;
		
        powerToTake = Mathf.Min(NextPower, _powerNeeded);
        _powerNeeded -= powerToTake;
        NextPower -= powerToTake;
		
        return _powerNeeded <= 0;
    }
    
    #endregion
#region Workers
    public void EjectOccupants()
    {
        var workers = GetWorkersPresent();
        foreach (var c in workers)
        {
            c.SetMoveToPosition(DoorPosOuter, false, PeepActions.Ejected);
            Leave(c);
        }
        var heroes = GetHeroesPresent();
        foreach(var hero in heroes)
        {
            EjectHero(hero as MAHeroBase);
        }
    }

    public void EjectHero(MAHeroBase _hero)
    {
        if(_hero == null) return;
        Leave(_hero);
        _hero.ResetHealthRecoveryRate();
        _hero.CharacterUpdateState.ApplyState(CharacterStates.Eject);
        _hero.transform.position = DoorPosInner;
        _hero.gameObject.SetActive(true);
    }

    public bool WorkerArrivesToHide(MAWorker _worker)
    {
        if (_worker == null) return false;

        foreach (var c in BuildingComponents<BCBedroom>())//bedroom?
        {
            if (c.Arrive(_worker))
            {
                _worker.MASetAsHiding(false);
                return true;
            }
        }

        return false;
    }
    public bool WorkerArrivesToWork(MACharacterBase _worker)
    {
        if (_worker == null || _worker.Job == null || _worker.Job.Building != this) return false;

        return _worker.Job.Arrive(_worker);
    }
    
    /*public bool IsWorkerAllocated(MAWorker _worker)
    {
        foreach (var c in BuildingComponents<BCBedroom, BCWorkers>())
        {
            if (c.IsAllocated(_worker))
            {
                return true;
            }
        }
        
        return false;
    }*/
    public bool WorkerArrivesToRest(MACharacterBase _worker)
    {
        if (_worker == null || _worker.Home == null || _worker.Home.Building != this) return false;
        
        return _worker.Home.Arrive(_worker);
    }
    
    public bool WorkerArrivesWaitingForHome(MAWorker _worker)
    {
        if (_worker == null) return false;
        foreach (var c in BuildingComponents<BCActionTavern>())//bedroom?
        {
            if (c.Arrive(_worker))
            {
                return true;
            }
        }

        return false;
    }

    public bool IsInhabited() => m_isInhabited;

    public string GetMissingComponentsStr()
    {
        var missing = GetMissingComponents();

        if (missing.Count == 0) return null;

        string missingTxt = "";
        bool first = true;
        foreach (var m in missing)
        {
            if (!first) missingTxt += ", ";
            missingTxt += m.m_title;
            first = false;
        }

        return missingTxt;
    }

    public List<(string,string)> GetBlockData()
    {
        var dti = NGDesignInterface.Get(m_stateData.m_buildingDesign);
        
        List<(string,string)> values = new();
        
        var blocks = GetComponentsInChildren<Block>();
        Dictionary<NGBlockInfo, int> blockDic = new();
        foreach(var block in blocks)
        {
            if(blockDic.ContainsKey(block.BlockInfo) == false)
                blockDic[block.BlockInfo] = 1;
            else
                blockDic[block.BlockInfo]++;
        }
        
        foreach(var block in blockDic)
        {
            var name = block.Key.m_displayName;
            if(name.IsNullOrWhiteSpace()) continue;
            int valueOfBlock = (int)dti.GetPriceOfRemovingBlock(block.Key) * block.Value;
            
            values.Add(($"{name} x {block.Value}", $"£{valueOfBlock}"));
        }
        
        return values;
    }
        
    public List<(string, string)> GetComponentData(MABuildingComponentInfo.EType _type)
    {
        if(_type == MABuildingComponentInfo.EType.Blocks) return GetBlockData();
        
        List<(string,string)> values = new();
        Dictionary<MAComponentInfo, List<BCBase>> partLines = new Dictionary<MAComponentInfo, List<BCBase>>();
        foreach (var p in m_components)
        {
            bool isAction = p as BCActionBase;
            if (isAction && _type != MABuildingComponentInfo.EType.ActionComponents) continue;
            if (!isAction && _type != MABuildingComponentInfo.EType.OtherComponents) continue;

            var key = p.GetInfo();
            if (key == null)
                continue;
            if (partLines.ContainsKey(key))
                partLines[key].Add(p);
            else
                partLines[key] = new List<BCBase>() { p };
        }
        return values;
    }
    
    public List<MAComponentInfo> GetMissingComponents()
    {
        List<MAComponentInfo> missing = new();
        foreach(var c in m_components)
        {
            c.GetMissingComponents(missing);
        }
        return missing;
    }
    
    public List<BCBase> GetInvalidComponents()
    {
        List<BCBase> invalidComponents = new();
        foreach(var maBuildingComponent in m_components)
        {
            if (maBuildingComponent.IsValidInBuilding() == false)
            {
                invalidComponents.Add(maBuildingComponent);
            }
        }
        return invalidComponents;
    }

    public bool AddToHomePermanently(MACharacterBase _character, bool _moveToBuilding = true)
    {
        if (_character == null) return false;

        if(_character.Home && _character.Home.Building != this)
            _character.DeallocateHome();

        Type bedroomType;
        if(_character as MAWorker) bedroomType = typeof(BCBedroom);
        else if(_character as MAHeroBase) bedroomType = typeof(BCGuildBedroom);
        else return false;

        // Make sure we don't double allocate
        foreach (var c in BuildingComponents(new[] { bedroomType }))
        {
            if(c.IsAllocated(_character))
            {
                _character.Home = c;
                if(_moveToBuilding)
                    _character.SetMoveToBuilding(this, PeepActions.ReturnToRest);
                return true;
            }
        }
        
        foreach (var c in BuildingComponents(new[] { bedroomType }))
        {
            if (c.Allocate(_character))
            {
                _character.Home = c;
                if(_moveToBuilding)
                    _character.SetMoveToBuilding(this, PeepActions.ReturnToRest);
                return true;
            }
        }
        return false;
    }
    
    public BCActionBase GetNextJob(MAWorker _worker)
    {
        if(_worker == null) return null;
        
        foreach (var c in ActionComponents)
        {
            if(c as BCBedroom) continue;
            if(c.IsAllocated(_worker)) return c;
        }
        foreach (var c in ActionComponents)
        {
            if(c as BCBedroom) continue;
            if(c.GetFreeSlots() > 0) return c;
        }
        return null;
    }
    
    public bool AddWorkerToWorkPermanently(MAWorker _worker, bool _moveToBuilding = true, bool _playHireSound = false, MAComponentInfo _restrictedJob = null)
    {
        if (_worker == null) return false;
        
        var previousJob = _worker.Job;
        if(_worker.Job)
            _worker.DeallocateJob();
        
        foreach (var c in ActionComponents)
        {
            if(c as BCBedroom) continue;
            if(c == previousJob) continue;
            if(_restrictedJob != null && c.m_info != _restrictedJob) continue;
            
            if (c.Allocate(_worker))
            {
                if(_playHireSound)
                    c.PlayWorkerHiredSound();
                _worker.Job = c;
                GameManager.Me.m_state.m_gameStats.m_hires.Increment(1);
                if (_moveToBuilding)
                {
                    _worker.SetDefaultAction();
                }
                return true;
            }
        }
        return false;
    }
    
    public int GetFreeWorkerSlots()
    {
        int total = 0;
        foreach (var c in ActionComponents)
        {
            if(c as BCBedroom) continue;
            
            total += c.GetFreeSlots();
        }

        return total;

    }
    
    public int GetFreeWorkerBedrooms()
    {
        int total = 0;
        foreach (var c in BuildingComponents<BCBedroom>())
        {
            total += c.GetFreeSlots();
        }
        return total;
    }
    
    public int GetFreeHeroBedrooms()
    {
        int total = 0;
        foreach (var c in BuildingComponents<BCGuildBedroom>())
        {
            total += c.GetFreeSlots();
        }
        return total;
    }
    
    public int GetMaxWorkerBedrooms()
    {
        int total = 0;
        foreach (var c in BuildingComponents<BCBedroom>())
        {
            total += c.GetMaxSlots();
        }
        return total;
    }
    
    public int GetMaxHeroBedrooms()
    {
        int total = 0;
        foreach (var c in BuildingComponents<BCGuildBedroom>())
        {
            total += c.GetMaxSlots();
        }
        return total;
    }
    
    public int GetMaxWorkerSlots()
    {
        var result = 0;
        foreach(var w in ActionComponents)
        {
            if(w as BCBedroom) continue;
            result+= w.GetMaxSlots();
        }
        return result;
    }

    public List<MACharacterBase> GetHeroesPresent()
    {
        List<MACharacterBase> bcBedRoomResidences = new();
        foreach (BCGuildBedroom bcGuildBedroom in BuildingComponents<BCGuildBedroom>())
        {
            bcBedRoomResidences.AddRange(bcGuildBedroom.GetHeroesPresent());
        }
        return bcBedRoomResidences;
    }

    public bool HasVisitors()
    {
        foreach(var worker in NGManager.Me.m_MAWorkerList)
        {
            if(worker.m_state == NGMovingObject.STATE.MA_HANGOUT && BCBase.IsInHangOutArea(this, worker.transform.position))
            {
                return true;
            }
        }
        return false;
    }
    
    public List<MACharacterBase> GetCharactersPresent()
    {
        var charactersPresent = GetWorkersPresent();
        charactersPresent.AddRange(GetHeroesPresent());
        return charactersPresent;
    }
    
    public int GetWorkersWorking()
    {
        int count = 0;
        foreach(var bcWorker in ActionComponents)
        {
            if(bcWorker as BCBedroom) continue;
            count += bcWorker.GetWorkersPresent().Count;
        }
        return count;
    }
    public List<MACharacterBase> GetWorkersPresent()
    {
        var bcWorkersWorkPlaces = BuildingComponents<BCActionBase>(true);// BuildingComponents<BCWorkers>(); //workPlaces
        var others = BuildingComponents<BCActionTavern>();
        
        HashSet<MACharacterBase> harvesters = new(); //working workers who are harvesting outside (not present inside building)
        
        //add workers present in the workPlace
        var allWorkersPresent = new HashSet<MACharacterBase>();
        foreach (var workPlace in bcWorkersWorkPlaces)
            foreach (var worker in workPlace.GetWorkersPresent())
                allWorkersPresent.Add(worker);

        //collect all workers with a job here
        List<MAWorker> allocatedWorkingWorkers = new();
        foreach (var workPlace in bcWorkersWorkPlaces)
            foreach (var worker in workPlace.GetWorkersAllocated())
                allocatedWorkingWorkers.Add(worker as MAWorker);
        
        //find harvesting workers among allocated working workers only (not residents)
        foreach (var allocatedWorkingWorker in allocatedWorkingWorkers) 
        {
            if(allocatedWorkingWorker.PeepAction != PeepActions.Chop || allocatedWorkingWorker.GameStatePerson.m_treeChopId < 0)
                continue;

            GameObject treeObject = TreeHolder.GetTreeObject(allocatedWorkingWorker.GameStatePerson.m_treeChopId);
            BCChopObject chopObject = treeObject.GetComponent<BCChopObject>();
            if(chopObject != null && chopObject.m_worker != null && chopObject.m_worker == allocatedWorkingWorker)
            {
                harvesters.Add(allocatedWorkingWorker);
            }
        }

        /*
        //add resident workers present
        var bcBedRoomResidences = BuildingComponents<BCBedroom>(); //residences//workPlaces
        foreach (var residence in bcBedRoomResidences)
            foreach (var worker in residence.GetWorkersPresent())
                allWorkersPresent.Add(worker);
         */
         
        //add harvesting workers (working outside, allocated but not present 'inside')
        foreach (var harvester in harvesters)
            allWorkersPresent.Add(harvester);
        
        //add other workers present
        foreach (var other in others)
            foreach (var worker in other.GetWorkersPresent())
                allWorkersPresent.Add(worker);
        
        return new List<MACharacterBase>(allWorkersPresent.ToArray());
    }
    
    public void Leave(MACharacterBase _character)
    {
        foreach (var c in BuildingComponents<BCWorkerBase>(true))
        {
            if (c.Leave(_character))
                return;
        }
    }
    
    override public bool SetWorkerPickup(NGMovingObject _object)
    {
        var worker = _object as MAWorker;
        if (worker == null) return false;
        foreach (var c in BuildingComponents<BCWorkerBase>(true))
        {
            if (c.Leave(worker))
                return true;
        }
        return false;
    }

    public float GetRestScore()
    {
        foreach (var rp in BuildingComponents<BCActionRestPlace>())
        {
            var score = rp.GetRestScore();
            if (score.IsZero() == false)
            {
                foreach (var c in BuildingComponents<BCCapacity>())
                {
                    if (c.GetFreeSlots() > 0)
                        return score;
                }
            }
        }

        return 0f;
    }

    #endregion Workers
#region Tap&Drag
    private static bool s_debugProductDrag = false;
    private static DebugConsole.Command s_cmdDbgProductDrag = new DebugConsole.Command("debugmaoutputs", _s => Utility.SetOrToggle(ref s_debugProductDrag, _s));
    private static DebugConsole.Command s_cmdFillBuilding = new DebugConsole.Command("FillStockIn", _s =>
    {
        RaycastHit hit;
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);

        if (Physics.Raycast(ray, out hit)) 
        {
            var ma = hit.collider.gameObject.GetComponentInParent<MABuilding>();
            if (ma != null)
            {
                foreach(var cmp in ma.m_actionComponents)
                {
                    var stock = cmp.GetInputStock();
                    if(stock == null) continue;
                    
                    foreach(var si in stock.Items)
                    {
                        if(si.Resource.IsAnyProduct) continue;
                        
                        stock.AddOrCreateStock(si.Resource, si.Needed);
                    }
                }
            }
        }
    });
    private static DebugConsole.Command s_fillStockOutCmd = new("fillstockout", _s =>
    {
        RaycastHit hit;
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);

        if (Physics.Raycast(ray, out hit))
        {
            var ma = hit.collider.gameObject.GetComponentInParent<MABuilding>();
            if (ma != null)
            {
                foreach(var stockOut in ma.StockOuts)
                {
                    var stockToFill = stockOut.GetStock().GetPrimaryStock;
                    if (stockToFill.IsProduct == false)
                        while (stockOut.GetStockSpace() > 0)
                            stockOut.CreateStock(stockToFill);
                }
            }
        }
    });
    
    override public bool HasDragContent()
    {
        if(HasDragComponent == false) 
            return false;
            
        foreach (var cmp in StockOuts)
        {
            if(cmp.HasDragContent()) return true;
        }
        
        foreach(var cmp in ActionComponents)
        {
            if(cmp.HasDragContent()) return true;
        }
        
        /*foreach (var s in BuildingComponents<BCBedroom>())
        {
            if(s.HasDragContent()) return true;
        }*/
        return false;
    }
    
    private GameObject GetPickupChain(NGCarriableResource _resource, ReactPickup _initialPickup = null)
    {
        int totalStockOfThisType = 0;
        foreach (var cmpStock in StockOuts)
            totalStockOfThisType += cmpStock.GetStock().GetTotalStock(_resource);
        int totalToGrab = Mathf.FloorToInt((totalStockOfThisType + 1) * MAUnlocks.Me.m_dragFromBuildingPercent) + 1;
        ReactPickup previousPickup = _initialPickup;
        ReactPickup pickup = _initialPickup;
        for (int i = 0; i < totalToGrab; ++i)
        {
            foreach (var cmpStock in StockOuts)
            {
                var nextPickup = cmpStock.TakePickup(_resource, false);
                if (nextPickup == null) continue;
                    
                if(pickup == null) pickup = nextPickup;
                if(previousPickup != null) previousPickup.m_nextInChain = nextPickup;
                previousPickup = nextPickup;
                break;
            }
        }
            
        if(pickup != null)
            return pickup.gameObject;
        return null;
    }
    
    override public GameObject GetDragContent()
    {
        if(HasDragComponent == false) 
            return null;
            
        GameObject stockOutPickup = null;
        var bestStockDestination = GetBestStockDestination(GetOutputStock(true));
        if(bestStockDestination.Empty)
        {
            foreach (var cmp in StockOuts)
            {
                var obj = cmp.GetDragContent();
                if (obj)
                {
                    var pickup = obj.GetComponent<ReactPickup>();
                    stockOutPickup = GetPickupChain(pickup.Contents, pickup);
                    break;
                }
            }
        }
        else
        {
            stockOutPickup = GetPickupChain(bestStockDestination.m_supplyWhat);
        }
        
        if(stockOutPickup != null)
            return stockOutPickup;
        
        // Do factories first
        foreach (var component in ActionComponents)
        {
            if(component is not BCFactory) continue;
            
            var obj = component.GetDragContent();
            if (obj) return obj.gameObject;
        }
        
        foreach (var component in ActionComponents)
        {
            if(component is BCFactory) continue;
            
            var obj = component.GetDragContent();
            if (obj) return obj.gameObject;
        }
        
        //Check for idle workers
        foreach (var s in BuildingComponents<BCBedroom>())
        {
            var obj = s.GetDragContent();
            if(obj != null) return obj;
        }
        return null;
    }

    override public void OnStandardClick(PointerEventData _eventData)
    {
        if (CheckNuke(_eventData)) return;
        if (IsDisabledByPlants)
        {
            PlantClick();
            return;
        }
        if (GameManager.Me.TownManagementMode == ETownManagementMode.MoveBuildings)
        {
            PickUpBuilding();
            return;
        }
        if (GameManager.ClickConsumed) return;

        switch (_eventData.button)
        {
            case PointerEventData.InputButton.Right: 
                TryShowContextMenu();
                break;
                
            case PointerEventData.InputButton.Left:
                // any building with no Action component should run its ClickToAnimates instead
                if (m_actionComponents.Count == 0)
                {
                    foreach (var clickToAnimate in Visuals.GetComponentsInChildren<ClickToAnimate>())
                        clickToAnimate.Click();
                }
                        
                if(HasTapComponent)
                {
                    m_holdDuration += NGManager.Me.m_buildingClickDurationAdd;
                    UpdateSpeedupMultiplier();
			
                    ++GameManager.Me.m_state.m_gameInfo.m_numBuildingClicks;
	    
                    NextPower += m_speedupMultiplier * NGManager.Me.m_buildingClickAddsHowMuch;

                    DoComponentWork(TapWorkStage.Tap);
		
                    StartDecay();
                }
                break;
        }

        var balloon = NGBalloonManager.CheckBalloonClicked(_eventData.pointerCurrentRaycast.gameObject);
        if(balloon)
        {
            //PDM:ToDo pass to balloon checker
            //Debug.LogError($"Clicked Balloon");
        }

    }
    
    public enum TapWorkStage
    {
        Tap,
        PressStart,
        Press,
        PressEnd,
    }
    
    private int componentTapWorkStartingIndex = 0;
    
    public bool DoComponentWork(TapWorkStage _stage)
    {
        bool hasDoneWork = false;
        string cantMakeReason = null;
        int highestPriority = -1;
        float factoryResourcesRequired = float.MaxValue;
        
        List<BCActionBase> cantMakes = new();
        for(int i = 0; i < m_actionComponents.Count; i++)
        {
            var c = m_actionComponents[i];//(componentTapWorkStartingIndex+i) % m_actionComponents.Count];
            if(c.UpdateTapWork() == false)
            {
                if(c.CantMakePriority > highestPriority)
                {
                    if(c is BCFactory == false || c.GetInputStock().GetRemainingNeededToProduce() < factoryResourcesRequired)
                    {
                        cantMakes.Add(c);
                    }
                }
            }
            else
            {
                hasDoneWork = true;
            }
        }
        
        if(hasDoneWork == false)
        {
            cantMakes.Sort((a,b) => a.m_uid > b.m_uid ? 1 : -1);
            foreach(var c in cantMakes)
            {   
                var reason = c.GetCantMakeReason();
                if(reason.IsNullOrWhiteSpace() == false)
                {
                    cantMakeReason = $"<b>{c.Title}</b> {reason}";
                }
            }
        }
        if(hasDoneWork == false && cantMakeReason.IsNullOrWhiteSpace() == false)
           UIInfoMessage.Create(UIInfoMessage.Location.Hand, gameObject, cantMakeReason, null, null, 1.5f);
        
            
        componentTapWorkStartingIndex++;
        
        // If no components didn't create any audio, use default
        if(m_workAudioTriggers.Count == 0)
        {
            switch(_stage)
            {
                case TapWorkStage.PressStart:
                    QueueAudio(hasDoneWork ? NGManager.Me.m_didWorkAudio :  NGManager.Me.m_failedToDoWorkAudio);
                    break;
                case TapWorkStage.PressEnd:
                case TapWorkStage.Tap:
                    QueueAudio(hasDoneWork ? NGManager.Me.m_didWorkEndAudio :  NGManager.Me.m_failedToDoWorkAudio);
                    break;
            }
        }
        return hasDoneWork;
    }

    
    public override void OnBeginLongPress(PointerEventData _eventData)
    {
        if (IsDisabledByPlants)
        {
            PlantClick();
            return;
        }

        m_holdWorkFirstUpdate = true;
        
        if(HasTapComponent)
        {
            switch (_eventData.button)
            {
                case PointerEventData.InputButton.Left:
                    DoComponentWork(TapWorkStage.PressStart);
                    m_inLongPress = true;
                    PlayerHandManager.Me.StartBuildingSpecificAnimation(BuildingSpecificHandAnimation());
                    PlayerHandManager.Me.SetBuildingSpecificAnimationSpeed(GetHandSpeed());
                    EndDecay();
                    break;
            }
        }

        base.OnBeginLongPress(_eventData);
    }

    private float GetHandSpeed() => m_speedupMultiplier / 2f;
    
    public override void OnUpdateLongPress(PointerEventData _eventData, float _longPressTime)
    {
        if(HasTapComponent)
        {
            m_holdDuration += Time.deltaTime;
            UpdateSpeedupMultiplier();
		
            NextPower += m_speedupMultiplier * NGManager.Me.m_buildingClickHoldAddsHowMuch * MAUnlocks.Me.m_tapHoldMultiplier;
		
            DoComponentWork(TapWorkStage.Press);
            
            PlayerHandManager.Me.SetBuildingSpecificAnimationSpeed(GetHandSpeed());
        }
        
        m_holdWorkFirstUpdate = false;
        base.OnUpdateLongPress(_eventData, _longPressTime);
    }

    public override void OnEndLongPress(PointerEventData _eventData)
    {
        if(HasTapComponent)
        {
            switch (_eventData.button)
            {
                case PointerEventData.InputButton.Left:
                    DoComponentWork(TapWorkStage.PressEnd);
                    ++GameManager.Me.m_state.m_gameInfo.m_numBuildingHolds;
                    PlayerHandManager.Me.EndBuildingSpecificAnimation();
                    QueueAudio(NGManager.Me.m_didWorkEndAudio);
                    QueueAudio(NGManager.Me.m_didWorkAudio);
                    StartDecay();
                    break;
            }
        }

        base.OnEndLongPress(_eventData);
    }


    override public void OnBeginDrag(PointerEventData _eventData)
    {
        foreach (var c in m_components)
        {
            c.OnBeginDrag(_eventData);
        }
    }
    #endregion
#region Work
    public MACharacterBase GetAvailableWorker()
    {
        foreach (var comp in m_components)
        {
            var worker = comp.GetAvailableWorker();
            if (worker)
            {
                return worker;
            }
        }
        return null; 
    }

    public bool InLongPress => m_inLongPress;
    private bool m_holdWorkFirstUpdate, m_holdWorkDone;
    private bool m_inLongPress = false;
    private bool m_hasTapComponent;
    private bool m_hasDragComponent;
    public bool HasTapComponent => m_hasTapComponent;
    public bool HasDragComponent => m_hasDragComponent;
    
    public void BeginHoldWork()
    {
        m_holdWorkFirstUpdate = true;
        m_holdWorkDone = false;
    }
    
    public void QueueAudio(AkEventHolder _event)
    {
        if (_event == null) return;
        if(string.IsNullOrEmpty(_event.Name)) return;
        if(m_workAudioTriggers.Contains(_event)) return;
        
        m_workAudioTriggers.Add(_event);
    }
    public List<AkEventHolder> m_workAudioTriggers = new();

    public float GetBedroomMultiplier()
    {
        var total = 1f;
        foreach (var c in m_components)
        {
            total *= c.GetRestMultiplier();
        }
        return total;
    }
    
    public (float healMultiplier, float trainMultiplier) GetHeroesGuildMultipliers()
    {
        (float heal, float train) values = (1f,1f);
        foreach (var c in BuildingComponents<BCActionHeroesGuild>())
        {
            values.heal *= c.m_healMultiplier;
            values.train *= c.m_trailMultiplier;
        }
        return values;
    }

    public bool m_canHighlight = true;
    public override bool IsHighlightable() => IsInteractable() && m_canHighlight;
    
    public bool IsInteractable()
    {
        if (enabled == false) return false;
        if (IsPermanentlyLocked) return false;
        return true;
    }

    #endregion Work
    
    #region Combat
    public bool ContainsDamageInteractionArea(MACharacterBase _attacker, out MADamageArea damageAreaOut)
    {
        damageAreaOut = null;
        bool containsAny = false;
        foreach (BCBase bcBase in m_components)
        {
            if (bcBase.ContainsDamageInteractionArea(_attacker, out MADamageArea damageAreaInComp))
            {
                if(damageAreaInComp != null)
                {
                    damageAreaOut = damageAreaInComp;
                }
                containsAny = true;
            }
        }
        return containsAny;
    }
    
    private HashSet<Collider> m_tempColliderSet = new();
    /// <returns>sorted list (by nearest point-distance) of damage areas contained in/around this building</returns>
    public List<(Collider area, Vector3 closestPos, float sqDist, bool isInside)> GetDamageInteractionAreas(MACharacterBase _attacker)
    {
        var attackerPos = _attacker.transform.position;
        HashSet<Collider> colliderAreas = m_tempColliderSet;
        colliderAreas.Clear();
        foreach (var component in m_components)
        {
            var damageAreas = component.GetDamageInteractionAreas(null);
            foreach (var damageArea in damageAreas)
            {
                colliderAreas.Add(damageArea);
            }
            
        }
        List<(Collider area, Vector3 closestPos, float sqDist, bool isInside)> areas = new();
        foreach (var colliderArea in colliderAreas)
        {
            Vector3 pos = colliderArea.ClosestPointOnBounds(attackerPos);
            float distSq = (pos - attackerPos).xzSqrMagnitude();
            int iIndex = areas.Count > 0 ? areas.Count : 0;
            for (int i = areas.Count -1; i >= 0; i--)
            {
                if (areas[i].sqDist > distSq)
                {
                    iIndex = i;
                }
            }
            areas.Insert(iIndex, (colliderArea, pos, distSq, Mathf.Approximately(0f,distSq)));
        }
        return areas;
    }  
#endregion

#region Stock
    public List<string> GetCompatibleGatherTypes()
    {
        List<string> types = new();

        foreach(var cmp in BuildingComponents<BCActionGatherer>())
        {
            if(types.Contains(cmp.m_treeHolderType))
                continue;
            types.Add(cmp.m_treeHolderType);
        }
        return types;
    }
    
    public BCActionGatherer GetActionGatherer(TreeHolder _type)
    {
        if(this == null) return null;
        if(_type == null) return null;
        
        foreach(var cmp in BuildingComponents<BCActionGatherer>())
        {
            if(cmp.m_treeHolderType == _type.m_treeType)
            {
                return cmp;
            }
        }
        return null;
    }
    
    public NGCarriableResource ConsumeStockForStockOut()
    {
        foreach(var comp in m_actionComponents)
        {
            if(comp.OutputHasDestination) continue;
            
            var stock = comp.ConsumeForStockOut();
            if(stock != null)
                return stock;
        }
        return null;
    }

    public NGCarriableResource ConsumeStockForStockIn()
    {
        foreach(var comp in m_actionComponents)
        {
            if(comp.OutputHasDestination)
            {
                var stock = comp.ConsumeForStockOut();
                if(stock != null)
                    return stock;
            }
        }
        return null;
    }
    
    public bool ConsumeStock(NGCarriableResource _resource)
    {
        foreach(var action in m_components)
        {
            if(action.ConsumeStock(_resource))
                return true;
        }
        return false;
    }
    
    public bool ConsumeStockNeeded(NGStock _destStock, BCBase _toCmp)
    {
        return ConsumeStockNeededWithInfo(_destStock, _toCmp) != null;
    }

    public NGCarriableResource ConsumeStockNeededWithInfo(NGStock _destStock, BCBase _toCmp, BCActionProducer _toProducer = null)
    {
        var toOrderBase = _toCmp as BCActionOrderBase;
        foreach (var fromCmp in m_components)
        {
            if(fromCmp == _toCmp) continue;
            
            foreach(var destStockItem in _destStock.Items)
            {
                if(destStockItem.Needed <= 0) continue;
                
                var lookingFor = destStockItem.Resource;
                if(lookingFor.IsAnyProduct)
                {
                    // We need to try and remove any product here
                    var found = fromCmp.ConsumeOrderProduct(toOrderBase);
                    if(found != null)
                    {
                        _destStock.AddOrCreateStock(found, 1);
                        return found;
                    }
                }
                else if(fromCmp.ConsumeStock(lookingFor))
                {
                    destStockItem.Stock++;
                    return lookingFor;
                }
                
                if(_toProducer != null)
                {
                    var fromProducer = fromCmp as BCActionProducer;
                    if(fromProducer != null && fromProducer.TryMoveInputStock(lookingFor, destStockItem, _toProducer))
                    {
                        destStockItem.Stock++;
                        return lookingFor;
                    }
                }
            }
        }
        return null;
    }

    public List<NGCarriableResource> m_externalStockRequired = new();
    public List<NGCarriableResource> m_outputResources = new();
    
    public bool StockRefreshRequired { get; set; }
    private void RefreshStockRequirements()
    {
        var consumedResources = GetStockTypesConsumed();
        bool hasFactory = HasBuildingComponent<BCFactory>();
        
        // Add all the consumed resources to the stock-ins so that peeps can deliver to them
        foreach(var stockIn in StockIns)
        {   
            var stock = stockIn.GetStock();
            
            stock.RemoveEmptyStockAndClearNeededToProduce();
            
            foreach(var res in consumedResources)
            {
                stock.AddOrCreateStock(res, 0, 1f);
            }
            
            if(hasFactory)
            {/*
                stock.AddOrCreateStock(NGCarriableResource.GetInfo(NGCarriableResource.c_flour), 0, 1f);
                stock.AddOrCreateStock(NGCarriableResource.GetInfo(NGCarriableResource.c_metal), 0, 1f);
                stock.AddOrCreateStock(NGCarriableResource.GetInfo(NGCarriableResource.c_fabric), 0, 1f);
                */
            }
        }
        
        m_externalStockRequired.Clear();
        foreach(var action in m_actionComponents)
        {
            action.UpdateOutputDestinations();
            action.UpdateExternalResourcesRequired(m_externalStockRequired);
            
            // Remove any uncessery stock
            var factory = action as BCFactory;
            if(factory != null)
                MoveFactoryStock(factory);
                
            BCActionProducer producer = action as BCActionProducer;
            if(producer && producer.OutputHasDestination == false)
            {
                if(producer.OutputResource != null &&
                    producer.OutputResource.IsNone == false &&
                    m_outputResources.Contains(producer.OutputResource) == false)
                {
                    m_outputResources.Add(producer.OutputResource);
                }
            }
        }

        if (false)
        {
            // Move items from stock in to stock out
            foreach (var stockIn in StockIns)
            {
                foreach (var item in stockIn.GetStock().Items)
                {
                    if (item.m_neededToProduce > 0) continue;

                    var res = item.Resource;
                    for (int i = item.Stock; i > 0; --i)
                    {
                        if (TryAddToStockOut(res))
                            stockIn.ConsumeStock(res);
                        else if(res.IsTopLevelResource == false || hasFactory == false) // Don't eject items if we have a factory
                        {
                            stockIn.TakePickup(res, false, (_o) => stockIn.FlyPickup(_o));
                        }
                    }
                }
            }
        }
        
        // Try move items from stock out to stock in
        if(false)
        {
            foreach(var stockOut in StockOuts)
            {
                foreach(var item in stockOut.GetStock().Items)
                {
                    for(int i = item.Stock; i > 0; --i)
                    {
                        foreach(var stockIn in StockIns)
                        {
                            if(stockIn.GetResourceRequirement(item.Resource) > 0 && stockIn.CreateStock(item.Resource))
                            {
                                stockOut.ConsumeStock(item.Resource);
                                break;
                            }
                        }
                    }
                }
            }
        }
        
        //RebalanceFactoryStock();
    }
    
    private List<BCFactory> GetFactoryListByPriority()
    {
        List<BCFactory> factoryList = new();
        bool added = false;
        foreach(var f1 in m_actionComponents)
        {
            if(f1 as BCFactory == null) continue;
             
            for(int i = 0; i < factoryList.Count; ++i)
            {
                var f2 = factoryList[i];
                
                if(f1.m_workersAllocated.Count > f2.m_workersAllocated.Count)
                {
                    factoryList.Insert(i, f1 as BCFactory);
                    added = true;
                    break;
                }
            }
            
            if(added == false)
                factoryList.Add(f1 as BCFactory);
        }
        return factoryList;
    }
    
    public void RebalanceFactoryStock()
    {
        var factories = GetFactoryListByPriority();
        
        for(int i = 0; i < factories.Count; ++i)
        {
            var to = factories[i];
            
            for(int j = factories.Count-1; j > i; --j)
            {
                var from = factories[j];
                if(MoveInputStock(to, from))
                {
                    break;
                }
            }
        }
        
        // Update all stock visuals
        foreach(var f in factories)
        {
            f.RefreshStockVisuals();
        }
    }
    
    private bool MoveInputStock(BCActionProducer _to, BCActionProducer _from)
    {
        var toInput = _to.GetInputStock();
        var fromInput = _from.GetInputStock();
        
        if(_from.HasStartedMaking == false)
        {
            foreach(var item in toInput.Items)
            {
                for(int i = 0; i < item.Needed; ++i)
                {
                    if(fromInput.Consume(item.Resource))
                    {
                        item.Stock++;
                    }
                    else
                    {
                        break;
                    }
                }
            }
        }
        return toInput.CanMake();
    }
    
    private void MoveFactoryStock(BCFactory _factory)
    {
        var inputStock = _factory.GetInputStock();
        if(inputStock == null || Order.IsNullOrEmpty() || Order.HasPlayerDesigned == false) return;
        
        foreach(var item in inputStock.Items)
        {
            if(item.Stock < item.m_neededToProduce) continue;
            
            while(item.Stock > item.m_neededToProduce)
            {
                // Move these items to stock in, or for now destroy
                bool movedToStockIn = false;
                foreach(var stockIn in StockIns)
                {
                    stockIn.CreateStock(item.Resource);
                    item.Stock--;
                    movedToStockIn = true;
                    break;
                }
                
                if(movedToStockIn == false)
                {
                    // We could eject it here if needed, destroy it for now
                    item.Stock--;
                }
            }
        }
        _factory.RefreshStockVisuals();
    }
    
    private bool TryAddToStockOut(NGCarriableResource _resource)
    {
        foreach(var stockBase in StockOuts)
        {
            if(stockBase.CreateStock(_resource))
                return true;
        }
        return false;
    }
    
    private bool CheckResourceIsAllowedHere(NGCarriableResource _resource)
    {
        if(_resource == null) return false;
        
        if(_resource.IsProduct)
        {
            var product = _resource.GetProduct();
            var order = product?.GetLinkedOrder();
            if(order != null && order.CanDeliverTo(this) == false)
            {
                return false;
            }
        }
        return true;
    }
    
    public bool HasSpaceForStock(NGCarriableResource _resource, bool _draggedFromThisBuilding = false)
    {
        if(_resource.IsNone) return false;
        if(CheckResourceIsAllowedHere(_resource) == false && _draggedFromThisBuilding == false) return false;
        
        foreach(var cmp in m_components)
        {
            if(cmp.GetResourceRequirement(_resource) > 0)
            {
                return true;
            }
            
            if(_draggedFromThisBuilding && (cmp as BCStockOut)?.GetStockSpace() > 0)
            {
                return true;
            }
        }
        return false;
    }
    
    public class BestStockDestinationCheck
    {
        public bool Empty => m_building == null;
        public MABuilding m_building = null;
        public float m_priority = float.MaxValue;
        public NGCarriableResource m_supplyWhat = null;

        public bool Compare(float _priority, NGCarriableResource _resource, MABuilding _building)
        {
            if(_priority > m_priority) return false;
            
            m_building = _building;
            m_priority = _priority;
            m_supplyWhat = _resource;
            return true;
        }
    }
    
    public BestStockDestinationCheck GetBestStockDestination(NGStock _stockToDeliver = null)
    {
        BestStockDestinationCheck bestStockDestination = new();
        
        var stockToDeliver = _stockToDeliver ?? GetOutputStock();
        if (stockToDeliver.GetTotalStock() == 0)
            return bestStockDestination;
            
        var doorPos = DoorPosOuter;
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            if (b == this || b.IsWithinOpenDistrict() == false) continue;
            
            var (resource, priority) = b.GetStockDeliveryPriority(stockToDeliver);
            if(resource == null) continue;
            
            var pathBreak = b.GetComponentInParent<PathBreak>();
            if (pathBreak != null)
            {
                if (pathBreak.RepairOrder == 1)
                { 
                    bestStockDestination.Compare(priority, resource, b);
                }
            }
            else
            {
                var dist = (b.DoorPosOuter - doorPos).xzSqrMagnitude();
                bestStockDestination.Compare(dist * priority, resource, b);   
            }
        }
        return bestStockDestination;
    }
    
    public bool AcceptsTopLevelResources => m_hasFactoryAndStockIn;
    private bool m_hasFactoryAndStockIn = false;
    
    // Higher the priority the better
    public (NGCarriableResource resource, float priorityMultiplier) GetStockDeliveryPriority(NGStock _supplyAvailable)
    {
        float bestPriority = 0;
        NGCarriableResource bestResource = null;
        
        foreach (var comp in m_components)
        {
            foreach(var item in _supplyAvailable.Items)
            {
                if(item.Stock <= 0) continue;
                
                var res = item.Resource;
                var priority = comp.GetResourceRequirement(res, true);
                if(priority > bestPriority)
                {
                    bestPriority = priority;
                    bestResource = res; 
                }
            }
        }
        
        // Otherwise find a factory without an order within a certain distance
        /*if(bestResource == null && m_hasFactoryAndStockIn)
        {
            foreach(var item in _supplyAvailable.Items)
            {
                var res = item.Resource;
                if(res.IsTopLevelResource)
                {
                    return (res, 10000);
                }
            }
        }*/
        // Flip priority so that it can be used to multiply the distance, higher is less preferable
        // 1 -> 2 (1 == best, 2 == worst)
        return (bestResource, Mathf.Max(1, 1-bestPriority)+1);
    }

    public ReactPickup TakeItemForDelivery(NGCarriableResource _item, Action<GameObject> _onComplete = null)
    {
        foreach(var cmp in m_components)
        {
            if(cmp.OutputHasDestination) continue;
            
            var item = cmp.TakePickup(_item, true, _onComplete);
            if(item)
            {
                return item;
            }
        }
        return null;
    }
    
    public int GetInStockSpace()
    {
        var result = 0;
        foreach (var s in StockIns)
        {
            result += s.GetStockSpace();
        }
        return result;
    }
    
    public int GetOutStockSpace()
    {
        var result = 0;
        foreach (var s in StockOuts)
        {
            result += s.GetStockSpace();
        }
        return result;
    }

    public bool HasStockInSpace()
    {
        foreach(var stockIn in StockIns)
        {
            if(stockIn.GetStockSpace() > 0)
                return true;
        }
        return false;
    }
    
    // Note this only counts stock in and stock outs not action component stock
    public int GetTotalStockCount()
    {
        var result = 0;
        foreach(var cmp in StockIns)
        {
            result += cmp.GetStock().GetTotalStock();
        }
        foreach(var cmp in StockOuts)
        {
            result += cmp.GetStock().GetTotalStock();
        }
        return result;
    }
    public int GetStockCount(NGCarriableResource _resource)
    {
        var result = 0;
        foreach(var s in StockIns)
            result += s.GetStock().GetStock(_resource);
            
        foreach(var s in StockOuts)
            result += s.GetStock().GetStock(_resource);

        return result;
    }

    private bool m_shouldJiggle = true;
    public bool ShouldJiggle { get => m_shouldJiggle; set => m_shouldJiggle = value; }
    
    float m_lastJolt = 0;
    public void JoltBlocks(float _joltBy)
    {
        if (m_shouldJiggle == false || (DesignTableManager.Me != null && DesignTableManager.Me.m_isInDesignGlobally && DesignTableManager.Me.m_designGloballyFocusBuilding == this)) return;
        if (_joltBy < .001f && m_lastJolt < .001f) return;
        if (_joltBy < 0) _joltBy = 0;
        m_lastJolt = _joltBy;
        const float c_joltRange = .125f;
        var range = _joltBy * c_joltRange;
        foreach (var block in m_blockHolder.GetComponentsInChildren<Block>())
            if (block.GetComponent<BaseBlock>() == null)
                SetRendererJolt(block, new Vector3(0, UnityEngine.Random.Range(-range, range), 0));
    }
    
    public static void SetRendererJolt(Block _block, Vector3 _jolt)
    {
        var foundationRenderers = new HashSet<Renderer>();
        if (_block.m_foundations != null)
            foreach (var foundationBlock in _block.m_foundations)
                if (foundationBlock != null)
                    foreach (var renderer in foundationBlock.GetComponentsInChildren<Renderer>())
                        foundationRenderers.Add(renderer);
        foreach (var renderer in _block.m_toVisuals.GetComponentsInChildren<Renderer>())
        {
            if (foundationRenderers.Contains(renderer)) continue;
            bool haveLocalDirection = false;
            Vector3 localDirection = Vector3.zero;
            foreach (var mat in renderer.materials)
            {
                if (mat.HasProperty("_Jolt"))
                {
                    if (haveLocalDirection == false)
                    {
                        localDirection = renderer.transform.InverseTransformVector(_jolt);
                        haveLocalDirection = true;
                    }
                    mat.SetVector("_Jolt", localDirection);
                }
            }
        }
    }
    
    public bool AddPickup(ReactPickup _pickup)
    {
        if(_pickup == null || _pickup.Contents == null) return false;
        
        if(CheckResourceIsAllowedHere(_pickup.Contents) == false && _pickup.DraggedFrom != this) return false;
        
        
        foreach(var c in m_actionComponents)
        {
            if(c is BCFactory) continue;
            
            if(c.AddPickup(_pickup))
                return true;
        }
        
        var sortedFactory = GetFactoryListByPriority();
        foreach(var factory in sortedFactory)
        {
            if(factory.AddPickup(_pickup))
                return true;
        }
        
        foreach(var c in m_components)
        {
            if (c.IsAction) continue;
            if(c.AddPickup(_pickup))
                return true;
            if(_pickup.DraggedFrom == this && (c as BCStockOut)?.CreateStock(_pickup.Contents) == true)
                return true;
        }
        return false;
    }

    public NGStock GetOutputStock(bool _onlyStockOut = false)
    {
        var stock = new NGStock();
        foreach (var c in m_stockOuts)
        {
            stock += c.GetStock();
        }
        
        if(_onlyStockOut == false)
        {
            foreach(var c in m_actionComponents)
            {
                stock += c.GetStock();
            }
        }
        
        return stock;
    }
    
    public List<NGCarriableResource> GetStockTypesConsumed()
    {
        var result = new List<NGCarriableResource>();
        foreach(var cmp in m_actionComponents)
        {
            var inputStock = cmp.GetInputStock();
            if(inputStock == null) continue;
            
            foreach(var item in inputStock.Items)
            {
                if(item.m_neededToProduce > 0f && result.Contains(item.Resource) == false)
                {
                    result.Add(item.Resource);
                }
            }
        }
        return result;
    }

    #endregion
#region Utilites

    public void OnLastBuildHelper(GameObject _block)
    {
        AudioClipManager.Me.PlaySound("PlaySound_BuildingComplete", GameManager.Me.gameObject);
    }

    public static Dictionary<MAComponentInfo,int> CountAllComponents()
    {
        Dictionary<MAComponentInfo, int> count = new();
        foreach(var c in MAComponentInfo.s_componentInfos.Values)
        {
            if(c == null) continue;
            count[c] = 0;
        }
            
        foreach(var b in NGManager.Me.m_maBuildings)
        {
            foreach(var c in b.m_components)
            {
                if(c.m_info == null) continue;
                count[c.m_info]++;
            }
        }
        
        // Add wild blocks
        foreach(Transform wild in DesignTableManager.Me.m_wildBlockHolder)
        {
            foreach(var bc in wild.GetComponents<BCBase>())
            {
                count[bc.m_info]++;
            }
        }
        return count;
    }
    
    public static int GetAllBlockComponentCount(MAComponentInfo _whichComponent)
    {
        int count = 0;
        foreach(var b in NGManager.Me.m_maBuildings)
        {
            count += b.GetBuildingComponentCount(_whichComponent);
        }

        // Add wild blocks
        foreach(Transform wild in DesignTableManager.Me.m_wildBlockHolder)
        {
            foreach(var bc in wild.GetComponents<BCBase>())
            {
                if(bc.m_info == _whichComponent)
                {
                    count++;
                }
            }
        }
        
        return count;
    }

    public bool TryAssignJob(NGMovingObject _o, MAComponentInfo _restrictedJob = null, bool _updateCharacterState = true)
    {
        MAWorker worker = _o as MAWorker;
        if(worker != null && worker.CanAssignJob)
        {
            if(GetFreeWorkerSlots() > 0)
            {
                // Auto assign this worker a home if they don't have one
                if(worker.Home == null)
                {
                    var closestHome = InputUtilities.GetClosestFreeHome(worker);
                    if(closestHome != null) 
                        closestHome.AddToHomePermanently(worker, false);
                }
                
                if (!AddWorkerToWorkPermanently(worker, _updateCharacterState, true, _restrictedJob))
                {
                    if(_updateCharacterState) worker.SetDefaultAction();
                }
                return true;
            }
        }
        return false;
    }
    
    public bool TryAssignBedroom(NGMovingObject _o, bool _updateCharacterState = true)
    {
        MAWorker worker = _o as MAWorker;
        if(worker != null)
        {
            if(worker.Home && worker.Home.Building == this) return false;
            
            if(GetFreeWorkerBedrooms() > 0)
            {
                if(!AddToHomePermanently(worker))
                {
                    if(_updateCharacterState) worker.SetDefaultAction();
                }
                else
                {
                    if(_updateCharacterState) worker.SetMoveToBuilding(this, PeepActions.ReturnToRest);
                }
                return true;
            }
        }
        MAHeroBase hero = _o as MAHeroBase;
        if (hero != null)
        {
            if(AddToHomePermanently(hero, false))
            {
                hero.SetToGuard(DoorPosOuter, hero.CharacterSettings.m_guardModeOverrideRadius);
                if(_updateCharacterState) MACharacterStateFactory.ApplyCharacterState(hero.DefaultState, hero);
            }
            
            return true;
        }
        return false;
    }
    
    public override bool ApplySpecialDropHandling(NGMovingObject _o, SpecialHandlingAction _restrictedAction)
    {
        bool value = base.ApplySpecialDropHandling(_o, _restrictedAction);
        
        // Update the quick tip
        ShowQuickTip();
        
        return value;
    }

    public static bool s_showResourcesInComponents = false;
    
    public bool ShowQuickTip()
    {
        var draggingWorker = InputUtilities.GetCurrentDraggingWorker();
        SpecialHandlingAction restrictedAction = null;
        if(draggingWorker != null)
        {
            restrictedAction = draggingWorker.GetComponent<CharacterPickupBehaviour>()?.GetRestrictedAction();
        }
        
        var messageString = "";
        
        if(IsPaused)
        {
            messageString += MAMessageManager.GetTMPString("Paused");
        }
        else
        {
            foreach(var action in m_components)
            {
                if(action.ShowWarning)
                {
                    messageString += MAMessageManager.GetTMPString("Warning");
                    break;
                }
            }   
        }
        
        var displayType = restrictedAction == null ? BCBase.CombinedStat.ValueDisplay.BuildingTipOnlyFreeSlots : BCBase.CombinedStat.ValueDisplay.BuildingTip;
        if((restrictedAction == null && GetFreeWorkerSlots() > 0) || restrictedAction == SpecialHandlingAction.AssignJob)
        {
            BCBase.CombinedStat stat = null;
            foreach (var c in ActionComponents)
            {
                if(c as BCBedroom) continue;
                c.GetCombinedValue(ref stat);
            }
            if(messageString.Length > 0) messageString += $" ";
            messageString += stat?.GetValue(displayType);
        }
        
        if((restrictedAction == null && GetFreeWorkerBedrooms() > 0) || restrictedAction == SpecialHandlingAction.AssignHome)
        {
            BCBase.CombinedStat stat = null;
            foreach (var c in BuildingComponents<BCBedroom>()) c.GetCombinedValue(ref stat);
            if(messageString.Length > 0) messageString += $" ";
            messageString += stat?.GetValue(displayType);
        }

        if(IsTraining) messageString += $" " + MAMessageManager.GetTMPString("HeroTraining");
        if(IsHealing) messageString += $" " + MAMessageManager.GetTMPString("HeroHealing");
        
        if((restrictedAction == null && GetFreeHeroBedrooms() > 0) || InputUtilities.GetCurrentDraggingHero())
        {
            BCBase.CombinedStat stat = null;
            foreach (var c in BuildingComponents<BCGuildBedroom>()) c.GetCombinedValue(ref stat);
            if(messageString.Length > 0) messageString += $" ";
            messageString += stat?.GetValue(displayType);
        }
        
        if (messageString.IsNullOrWhiteSpace() == false)
        {
            m_tipGUI = MAQuickTipGUI.Create(this, m_balloonHolder, messageString.TrimStart());
            CardHolder?.UpdatePosition(this);
            return true;
        }
        
        m_tipGUI?.DestroyMe();
        m_tipGUI = null;
        CardHolder?.UpdatePosition(this);
        return false;
    }
    public MAQuickTipGUI m_tipGUI;
    //static OptimisationCheck s_optimisationCheck = new OptimisationCheck("MABuilding.FindBuilding");
    public static MABuilding FindBuilding(string _name, bool _includeOutOfRange = false)
    {
        MABuilding result = null;
        if(_name.ToLower() == "lastbuilt")
        {
            int largestId = 0;
            foreach(var building in NGManager.Me.m_maBuildings)
            {
                if(building.m_linkUID > largestId)
                {
                    largestId = building.m_linkUID;
                    result = building;
                }
            }
        }
        else if (_name.IsNullOrWhiteSpace() == false)
        {
            //s_optimisationCheck.StartCheckNew();
            result = s_buildingCache.GetValueOrDefault(_name, null);
            //s_optimisationCheck.EndCheckNew();
            if (_includeOutOfRange == false && result != null && result.InOwnedDistrictState == EInOwnedDistrictState.NotInOwnedDistrict)
                result = null;
            //var resultNew = result;
            //s_optimisationCheck.StartCheckOld();
            if (true) ; else
            if (int.TryParse(_name, out var id))
            {
                result = NGManager.Me.m_maBuildings.Find(o => o.m_linkUID == id);
                if (_includeOutOfRange && result == null)
                {
                    result = NGManager.Me.m_NGCommanderListOutOfRange.Find(o => o.m_linkUID == id) as MABuilding;
                }
            }
            else
            {
                result = null;
                var searchFor = _name.Trim();
                foreach (var b in NGManager.Me.m_maBuildings)
                {
                    if(b.Name.Equals(searchFor))
                    {
                        result = b;
                        break;
                    }
                    if(b.name.Equals(searchFor))
                        result = b;
                }
                if (_includeOutOfRange && result == null)
                {
                    foreach (var b in NGManager.Me.m_NGCommanderListOutOfRange)
                    {
                        if(b.Name.Equals(searchFor))
                        {
                            result = b as MABuilding;
                            if(result != null)
                                break;
                        }

                        if (b.name.Equals(searchFor))
                        {
                            result = b as MABuilding;                        
                        }
                    }
                }
            }
            //s_optimisationCheck.EndCheckOld();
            //s_optimisationCheck.AddTest(result == resultNew);
        }
        return result;
    }
    
    public static List<MABuilding> FindBuildingsByName(string _name, bool _includeOutOfRange = false)
    {
        List<MABuilding> results = null;
        if (_name.IsNullOrWhiteSpace() == false)
        {
            if (_includeOutOfRange == false)
            {
                results = NGManager.Me.m_maBuildings.FindAll(o => o.Name.ToLower() == _name.ToLower());
                if (results.Count == 0)
                    results = NGManager.Me.m_maBuildings.FindAll(o => o.name.ToLower().Contains(_name.ToLower()));
            }
            else
            {
                var resultsComm = NGManager.Me.m_NGCommanderListOutOfRange.FindAll(o => o.Name.ToLower() == _name.ToLower());
                if (resultsComm.Count == 0) resultsComm = NGManager.Me.m_NGCommanderListOutOfRange.FindAll(o => o.name.ToLower().Contains(_name.ToLower()));
                if (resultsComm.Count > 0)
                    results = new List<MABuilding>();
                foreach (var result in resultsComm)
                {
                    results.Add((MABuilding)result);
                }
                return results;
            }
        }
        return results;
    }
    
    public float GetProductScore()
    {
        float result = 0f;
        foreach (var p in BuildingComponents<BCFactory>())
        {
            result += p.GetProductScore();
        }
        m_debugProductScore = result;
        return result;
    }

    public string GetDefaultDrawerSet()
    {
        string best = "";
        foreach (var c in m_actionComponents)
        {
            var cType = c.GetType();
            foreach (var kvp in MAComponentInfo.s_componentInfos)
            {
                if (kvp.Value.m_classType == cType)
                {
                    var set = kvp.Value.m_defaultDrawerSet;
                    if (set != null)
                    {
                        set = set.Split(':')[0];
                        if (cType.ToString().EndsWith(kvp.Value.m_name))
                            return set;
                        best = set;
                    }
                }
            }
        }
        return best;
    }

    public string GetDescription()
    {
        List<string> functionalComponents = new();
        foreach(var ac in ActionComponents)
        {
            var title = ac.m_info?.DisplayTitle;
            if(title.IsNullOrWhiteSpace() || functionalComponents.Contains(title)) continue;
            functionalComponents.Add(title);
        }
        
        if(functionalComponents.Count > 0)
        {
            string functionalStr = "";
            for(int i = 0; i < functionalComponents.Count; ++i)
            {
                if(i != 0 && i == (functionalComponents.Count-1))
                    functionalStr += $" and {functionalComponents[i]}";
                else if(i == 0)
                    functionalStr += functionalComponents[i];
                else
                    functionalStr += $", {functionalComponents[i]}";
                    
            }
            
            return $"This building is a functional {functionalStr}.";
        }
        return "This building has no functional actions";
    }
    
    public string GetActionComponentBasedTitle()
    {
        string value = "";
        List<string> actions = new ();
        
        foreach(var ac in ActionComponents)
        {
            var title = ac.m_info?.DisplayTitle;
            if(actions.Contains(title) || title.IsNullOrWhiteSpace()) continue;
            
            actions.Add(title);
        }
        
        for(int i = 0; i < actions.Count; ++i)
        {
            if(value.IsNullOrWhiteSpace())
                value += actions[i];
            else if(i < (actions.Count-1))
                value += $", {actions[i]}";
            else
                value  += $" & {actions[i]}";
        }
        return value;
    }
    
    override public string GetBuildingTitle()
    {
        if(m_title.IsNullOrWhiteSpace() == false)
            return m_title;
        if(m_componentsDict.Count == 0)
            return "Empty Plot";
            
        var value = GetActionComponentBasedTitle();
        return value.IsNullOrWhiteSpace() ? "Building" : value;
    }

    public string GetBuildingPopupText(float timeOver)
    {
        var extendTime = 2f;
        var txt = $"<b>{GetBuildingTitle()}</b>";
        if(HasTapComponent)
            txt += "\n(<LMB>Hold to speed up)";
        if(HasDragComponent && HasDragContent())
            txt += "\n(<LMB>Drag item)";
       
        if (timeOver > extendTime)
        {
            var additionalTxt = "";
            if (HasBuildingComponent<BCStockIn>())
            {
                foreach (var s in StockIns)
                {
                    var stockinfo = s.GetStock();
                    foreach (var item in stockinfo.Items)
                    {
                        additionalTxt += $"\nStock In: {item.m_carriableResourceName} x {item.m_stock}";
                    }
                }
            }
            if (HasBuildingComponent<BCStockOut>())
            {
                foreach (var s in StockOuts)
                {
                    var stockinfo = s.GetStock();
                    foreach (var item in stockinfo.Items)
                    {
                        additionalTxt += $"\nStock Out: {item.m_carriableResourceName} x {item.m_stock}";
                    }
                }
            }
            if(HasBuildingComponent<BCFactory>() && HasActiveOrder == false)
                additionalTxt+= $"\n<color=yellow>Needs Order</color>";
            if(additionalTxt.IsNullOrWhiteSpace() == false)
                txt += $"\n{additionalTxt}";
        }
        return txt.ReplaceInputTokens();
    }

    public List<BCBase> GetBlockComponents(Type _type)
    {
        m_componentsDict.TryGetValue(_type, out var result);
        if (result == null)
            return new List<BCBase>();
        return result;
    }
    
    public bool HasAvailableOrder()
    {
        if(m_cardHolder == null) return false;
        
        foreach(var segment in m_cardHolder.m_segments)
        {
            foreach(var slot in segment.m_slots)
            {
                var slotInfo = slot.SlotInfo;
                if(slotInfo != null && slotInfo.m_order.IsNullOrEmpty() == false && slotInfo.m_order.IsAvailable)
                {
                    return true;
                }
            }
        }
        return false;
    }
        
    public List<MAOrder> GetOrdersDisplayed(OrderSlots.OrderDisplayedType _type = OrderSlots.OrderDisplayedType.All)
    {
        List<MAOrder> allOrdersDisplayed = new();
        
        foreach(var ac in m_actionComponents)
        {
            var orderBase = ac as BCActionOrderBase;
            if(orderBase == null) continue;
            
            var ordersDisplayed = orderBase.SlotManager.GetOrdersDisplayed(_type);
            if(ordersDisplayed != null && ordersDisplayed.Count > 0)
                allOrdersDisplayed.AddRange(ordersDisplayed);
        }
        return allOrdersDisplayed;
    }
    
    public float DefenseValueMax
    {
        get
        {
            float cumulativeDefenseValueMax = 0;
            foreach(var block in GetComponentsInChildren<Block>())
            {
                cumulativeDefenseValueMax += block.DefenseValueMax;
            }
            return cumulativeDefenseValueMax;
        }
    }

    public string BuildingSpecificHandAnimation()
    {
        if (m_actionComponents.Count == 0) return null;
        foreach(var action in m_actionComponents)
            if(action.Block && action.Block.m_handAnimation != null)
                return action.Block.m_handAnimation;
        return null;
    }

    public string GetBuildingInfoString()
    {
        var playerName = "Peter Molyneux"; //ToDo:Get Actual player name
        var date = DateTime.Now;
        var result = "";
        result = $"{GetBuildingTitle()} was created by {playerName} on {DateTime.Now.ToString("dd MMM yyyy")}\n";
        result += $"It has {m_components.Count} Components ";
        var bedroomSlots = GetMaxWorkerBedrooms();
        if(bedroomSlots > 0) 
            result+=$"which can sleep {bedroomSlots} {Utility.GetPural("worker",bedroomSlots)}";
        var workerSlots = GetMaxWorkerSlots();
        if (workerSlots > 0)
        {
            result += (bedroomSlots > 0) ? ", and " : "which ";
            result += $" has space for {workerSlots} {Utility.GetPural("worker", workerSlots)}.";
        }
        result += "\n";
        /*var maxInputStock = GetMaxStock(typeof(BCStockIn));
        if (maxInputStock > 0)
        {
            //result += $"It has space for {maxInputStock} of {GetInputStockNames()} as input.";
        }
        var maxOutputStock = GetMaxStock(typeof(BCStockOut));
        if (maxOutputStock > 0)
        {
  
            //result += $"It has space for {maxOutputStock} of {GetOutputStockNames()} as output.";
        }

        if (maxInputStock > 0 || maxOutputStock > 0)
            result += "\n";*/
        if (HasTapComponent)
        {
            result += "You can Tap or Tap hold this building to make it work.";
            if (HasDragComponent)
                result += "and drag to get resources.";
        }
        else if (HasDragComponent)
        {
            result += "You can drag to get resources.";
        }

        return result;
    }

    public string GetBuildingInfoStringByColumn()
    {
        var playerName = "Peter Molyneux"; //ToDo:Get Actual player name
        var date = DateTime.Now;
        var result = "";
        result = $"{GetBuildingTitle()} was created by {playerName} on {DateTime.Now.ToString("dd MMM yyyy")}.\n";
        result += $"It has {m_components.Count} Components:\n";
        if(GetMaxWorkerBedrooms() > 0)
            result += $"Bedrooms: {GetMaxWorkerBedrooms()}\n"; 
        if(GetMaxWorkerSlots() > 0)
            result += $"Workers: {GetMaxWorkerSlots()}\n";
        //if (GetHotspotStockNames().IsNullOrWhiteSpace() == false)
          //  result += $"Mine Stock: {GetHotspotStockNames()}";
        //if(GetMaxInputStock() > 0)
          //  result += $"{GetMaxInputStock()} Input Stock: {GetInputStockNames()}\n";
        //if(GetMaxOutputStock() > 0)
            //result += $"{GetMaxOutputStock()} Output Stock: {GetOutputStockNames()}\n";
        var controls = "";
        if (HasTapComponent)
            controls = "Tap, Tap Hold";
        if (HasDragComponent)
            controls += (controls.IsNullOrWhiteSpace()) ? "Drag" : " & Drag";
        if(controls.IsNullOrWhiteSpace() == false)
            result += $"Controls: {controls}\n";
        return result;
    }

    public void SetEntranceCollidersDisabled(bool _value, NGMovingObject _obj)
    {
        var entrances = BuildingComponents<BCEntrance>();
        foreach(var entrance in entrances)
        {
            if(_value)
                entrance.DisableMovingObjectCollisions(_obj);
            else
                entrance.ResetMovingObjectCollisions(_obj);
        }
    }

    public string GetDebugInfo()
    {
        string s = "";
        foreach (var cmp in m_components)
            s += $"<color=#ffffff>{cmp.GetType().ToString().Replace("MABuildingComponent", "")}\n</color><color=#e0ffe0><size=70%>  {cmp.GetComponentInParent<Block>(true).name}</size></color>\n  <color=#e0e0ff>{cmp.GetDebugInfo()}</color>\n";
        return s;
    }
    
    #endregion
    
    #region Factory

    override public void SetProduct(GameState_Product _product)
    {
        if(Order.IsNullOrEmpty() == false)
            Order.SetProduct(_product);
        
        foreach (var f in BuildingComponents<BCFactory>())
        {
            f.OnProductChanged();
        }
    }

    override public NGProductInfo GetProductLineInfo()
    {
        foreach (var f in BuildingComponents<BCFactory>())
        {
            var info = f.GetProductLineInfo();
            if (info != null)
                return info;
        }
        return null;
    }

    override public GameState_Product GetProduct()
    {
        var product = Order?.GameProduct;
        
        if(product != null)
        {
            product.m_placeOfManufacture = m_linkUID;
        }
        return product;
    }

    private static DebugConsole.Command s_designWeaponCmd = new ("designweapon", _s => Debug_AssignOrder("weaponorder:0010"));
    private static DebugConsole.Command s_assignOrderCmd = new("assignorder", _s => Debug_AssignOrder(_s));

    public static void Debug_AssignOrder(string _s)
    {
        if (string.IsNullOrEmpty(_s)) _s = "weaponorder:0010";
        var orderInfo = MAOrderInfo.GetInfo(_s);
        if (orderInfo == null)
        {
            Debug.LogError($"Invalid order string: {_s}");
            return;
        }
        var order = new MAOrder(orderInfo);
        foreach (var building in NGManager.Me.m_maBuildings)
        {
            if(building.HasBuildingComponent<BCFactory>())
            {
                building.ReceiveOrder(order);
                break;
            }
        }
    }

    private GameState_Product m_debugPlaceholderRemoveThis = null;
    
    public bool ReceiveOrder(MAOrder _maOrder)
    {
        var previousOrder = Order;
        if(previousOrder.IsValid)
        {
            Debug.Log($"{GetType().Name} - ReceiveOrder - old: {previousOrder.OrderInfo.Faction?.m_factionName} {previousOrder.TaskDescription} - new: {(_maOrder != null? $"{_maOrder.OrderInfo.Faction?.m_factionName} {_maOrder.TaskDescription}" : "null")}");
            RemoveOrder(previousOrder);
        }
 
        if(_maOrder.IsNullOrEmpty() == false && _maOrder.IsValid)
        {
            _maOrder.Assign(this);
            Order = _maOrder;
            if(_maOrder.HasPlayerDesigned)
                SetProduct(_maOrder.GameProduct);
            else
                DesignProduct();
            return true;
        }
        return false;
    }

    
    override public MAOrder Order 
    { 
        get
        {
            if(m_currentOrder == null)
            {
                GameManager.Me.m_state.m_orders.TryGetValue(m_stateData.m_orderId, out m_currentOrder);
                if(m_currentOrder == null) m_currentOrder = MAOrder.EmptyOrder;
            }
            return m_currentOrder;
        } 
        set
        {
            m_currentOrder = value;
            m_stateData.m_orderId = value.IsNullOrEmpty() ? -1 : value.OrderId;
        }
    }
    private MAOrder m_currentOrder;

    public bool HasActiveOrder
    {
        get
        {
            MAOrder order = Order;
            return order.IsValid && order.IsComplete == false;
        }
    }
    
    #endregion
#region Create&Save&Load
    public static MABuilding Create(MABuilding _prefab, Transform _holder, Vector3 _pos)
    {
        var go = Instantiate(_prefab, _holder);
        go.transform.position = _pos;

        return go.GetComponent<MABuilding>();
    }
  
    public static MABuilding Create(Transform _holder)
    {
        var go = Instantiate(NGManager.Me.m_MABuildingPrefab.gameObject, _holder);
        return go.GetComponent<MABuilding>();
    }
    public void StartQuestOrder(MAOrderInfo _orderInfo)
    {
        var comp = BuildingComponents<BCActionQuestOrder>();
        foreach (var questOrder in comp)
        {
            questOrder.Sequence.Insert(_orderInfo, false, OrderSequence.AddAction.PushToFront);
        }
    }
    
    public float GetWorkerSpeedMultiplier()
    {
        return 1f + m_speedupMultiplier * NGManager.Me.m_buildingWorkerSpeedMultiplier;
    }
    
    public void OnDesignTableVisibilityChanged(bool _visible, GameObject _block)
    {
        if (_visible && m_temporarilyHidden)
        {
            ShowOrHideRenderers(_block.transform, true);
        }
    }
    
    private bool m_temporarilyHidden = false; public bool IsTemporarilyHidden => m_temporarilyHidden;
    private HashSet<Renderer> m_hiddenRenderers = new();
    private HashSet<Collider> m_hiddenColliders = new();

    private void ShowOrHideRenderers(Transform _root, bool _hide)
    {
        if (_hide)
        {
            foreach (var rnd in _root.GetComponentsInChildren<Renderer>())
            {
                if (rnd == null) continue;
                if (rnd.enabled == false) continue;
                rnd.enabled = false;
                m_hiddenRenderers.Add(rnd);
            }
            foreach (var cll in _root.GetComponentsInChildren<Collider>())
            {
                if (cll == null) continue;
                cll.gameObject.layer = GameManager.c_layerIgnoreRaycast;
                m_hiddenColliders.Add(cll);
            }
        }
        else
        {
            foreach (var rnd in m_hiddenRenderers)
            {
                if (rnd == null) continue;
                rnd.enabled = true;
            }
            foreach (var cll in m_hiddenColliders)
            {
                if (cll == null) continue;
                cll.gameObject.layer = 0;
            }
        }
    }

    public void SetTemporarilyHidden(bool _hide)
    {
        m_temporarilyHidden = _hide;
        ShowOrHideRenderers(Visuals, _hide);
        if (_hide == false)
        {
            m_hiddenRenderers.Clear();
            m_hiddenColliders.Clear();
        }
        CheckCardHolderVisibility();
    }

    void OnDestroy()
    {
        DestroyMe();
    }
    
    public void DesignProduct()
    {
        DesignTableManager.Me.StartDesignInPlace(this, DesignTableManager.DESIGN_CATEGORY.PRODUCT, (accepted) => accepted);
    }
    
    public bool RemoveOrder(MAOrder _orderToRemove)
    {
	    var currentOrder = Order;
	    if(currentOrder.IsValid && currentOrder.OrderId == _orderToRemove.OrderId)
	    {
		    _orderToRemove.OnOrderRemoved();
			Order = MAOrder.EmptyOrder;
            foreach(var factory in BuildingComponents<BCFactory>())
            {
                factory.OnProductChanged();
            }
		    return true;
	    }
	    return false;
    }
    
    public static void Load(GameState_Building _s)
    {
        if (_s.m_id <= 0)
        {
            Debug.LogError("Building has no link");
            return;
        }

        var ma = NGManager.Me.m_maBuildings.Find(b => b.m_linkUID == _s.m_id);
        if (ma == null)
        {
            Debug.LogError("Unable to find linked building");
            return;
        }

        if (_s.m_name.IsNullOrWhiteSpace() == false)
            ma.Name = _s.m_name;

        ma.m_title = _s.m_title;
        
        // Load the component data
        if(ma.Design != null)
        {
            var blocks = ma.Visuals.GetComponentsInChildren<Block>();
            bool requiresRebuild = false;
            
            foreach(var block in blocks)
            {
                requiresRebuild |= block.LoadComponents(GameManager.Me.m_state.m_maComponentData, ma.Design.m_componentIds);
            }
            
            if(!requiresRebuild)
            {
                foreach(var cmp in ma.m_components)
                {
                    if(cmp.m_uid == 0 && cmp.AllowSave)
                    {
                        requiresRebuild = true;
                        break;
                    }
                }
            }
        
            if(requiresRebuild)
            {
                Debug.LogError($"Rebuilding Building component ids due to uninitialised id(s). Building: {ma.m_title}");
                ma.Design.m_componentIds.Clear();
                Block.GenerateComponentInfo(blocks.ToList(), ma.Design.m_componentIds);
            }
            
            ma.m_actionComponents.Sort((a,b) => a.m_uid > b.m_uid ? 1 : -1);
        }
        
        ma.CheckCache();
    }

    public static void ResetComponentIDs()
    {
        var bigList = new List<NGCommanderBase>();
        bigList.AddRange(NGManager.Me.m_maBuildings);
        bigList.AddRange(NGManager.Me.m_NGCommanderListOutOfRange);
        
        GameManager.Me.m_state.m_highestComponentId = 0;
        GameManager.Me.m_state.m_maComponentData.Clear();
        
        foreach(var b in bigList)
        {
            if(b == null || b.Design == null || b.Visuals == null) continue;
            
            var blocks = b.Visuals.GetComponentsInChildren<Block>();
            
            SDictionary<int,ArrayWrapper<long>> cmpIds = new();
            Block.GenerateComponentInfo(blocks.ToList(), cmpIds, true);
            b.Design.m_componentIds = cmpIds;
        }
        
        DesignTableManager.ResetWildBlockIds();
        
        GameManager.Me.m_state.PreSaveBuildings(false, true);
    }
    
    public static void LoadAll(List<GameState_Building> _saves)
    {
        foreach (var save in _saves)
            Load(save);
        
        // Delay activations until after load
        foreach(var b in NGManager.Me.m_maBuildings)
        {
            b.ActivateAllComponents();
        }
        
        // Refresh component info list, this will remove all the deleted blocks
        GameManager.Me.m_state.PreSaveBuildings(true, true);
    }
    
    public static void SaveAllComponents(SDictionary<long,SaveMABuildingComponent> _sComponents, bool _includeOutOfRange = false)
    {
        _sComponents.StartMultipleOperations();
        
        foreach(var b in NGManager.Me.m_maBuildings)
        {
            b.SaveComponents(_sComponents);
        }
        
        if(_includeOutOfRange)
        {
            foreach(var com in NGManager.Me.m_NGCommanderListOutOfRange)
            {
                (com as MABuilding)?.SaveComponents(_sComponents);
            }
        }
        
        // Save out wild block components
        DesignTableManager.SaveWildBlocks(_sComponents);

        _sComponents.EndMultipleOperations();
    }
    
    private void SaveComponents(SDictionary<long,SaveMABuildingComponent> _sComponents)
    {
        foreach(var c in m_components)
        {
            if(c == null) continue;
            
            c.Save(_sComponents);
        }
    }
    
    private static Dictionary<long, BCBase> s_allComponents = null;
    private static int s_lastComponentsLookup = 0;
    
    public static void RebuildComponentMap()
    {
        if(s_allComponents == null)
            s_allComponents = new();
        else
            s_allComponents.Clear();
            
        foreach(var b in NGManager.Me.m_maBuildings)
        {
            b.AddAllComponents(s_allComponents);
        }
            
        foreach(var b in NGManager.Me.m_NGCommanderListOutOfRange)
        {
            (b as MABuilding)?.AddAllComponents(s_allComponents);
        }
            
        DesignTableManager.GetAllWildBlockComponents(s_allComponents);
    }
    
    // TODO, force rebuild if something changes
    public static BCBase LookupComponent(long _uid)
    {
        if(_uid <= 0) return null;
        
        // Build list
        if(s_allComponents == null)
        {
            RebuildComponentMap();
        }
        
        s_allComponents.TryGetValue(_uid, out var value);
        return value;
    }
    
    private void AddAllComponents(Dictionary<long, BCBase> _all)
    {
        foreach(var c in m_components)
        {
            _all[c.m_uid] = c;
        }
    }

    public static MABuilding CreateFake(string _name, Vector3 _position, Transform _parent, Vector3 _localOffset, float _triggerRadius = .5f, List<Vector3> _previousPositions = null)
    {
        var go = new GameObject(_name);
        go.transform.SetParent(_parent);
        go.transform.position = _position + _localOffset;
        go.transform.localRotation = Quaternion.identity;
        var vis = new GameObject("Visuals");
        vis.transform.SetParent(go.transform);
        var fakeBuilding = go.AddComponent<MABuilding>();
        fakeBuilding.m_stateData = new GameState_Building();
        fakeBuilding.m_stateData.m_x = _position.x; fakeBuilding.m_stateData.m_z = _position.z;
        fakeBuilding.InitialiseBuildingData(fakeBuilding.m_stateData, null, vis);
        fakeBuilding.Name = "Fake";
        fakeBuilding.m_balloonHolder = vis.transform;
        var nav = go.AddComponent<BuildingNav>();
        nav.SetFakeBuilding();
        if (_triggerRadius > 0)
        {
            void AddCollider(Vector3 _position)
            {
                var cll = go.AddComponent<SphereCollider>();
                cll.isTrigger = true;
                cll.radius = _triggerRadius;
                cll.center = _position - go.transform.position;
            }
            AddCollider(go.transform.position);
            if (_previousPositions != null)
                for (int i = 0; i < _previousPositions.Count; ++i)
                    AddCollider(_previousPositions[i] + _localOffset);
        }
        return fakeBuilding;
    }
#endregion

    public override int GetHashCode()//TS- See MABuildingSupport.GetBuildingsWithComponents as a hashSet useage example
    {
        return m_linkUID;
    }
}

#region Editor
#if UNITY_EDITOR
[CustomEditor(typeof(MABuilding))]
public class MABuildingEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        MABuilding building = (MABuilding)target;

        if(Application.isPlaying)
        {
            if(GUILayout.Button($"Destroy"))
            {
                Block[] blocks = building.Visuals.GetComponentsInChildren<Block>();

                float damage = 100;
                Vector3 forceDir = Vector3.up * 10f;
                building.ApplyDamageEffect(IDamageReceiver.DamageSource.Debug, damage, forceDir);
                foreach(Block block in blocks)
                {
                    if(block.IsMABase)
                        continue;
					
                    Rigidbody rbBlock = block.gameObject.GetComponent<Rigidbody>();
                    if(rbBlock == null)
                    {
                        rbBlock = block.gameObject.AddComponent<Rigidbody>();
                    }
                    
                    rbBlock.linearVelocity = forceDir +
                                       new Vector3(UnityEngine.Random.Range(-1, 1), UnityEngine.Random.Range(0, 1), UnityEngine.Random.Range(-1, 1)) * 10f;
                    rbBlock.angularVelocity = new Vector3(UnityEngine.Random.Range(-10, 10), UnityEngine.Random.Range(-10, 10), UnityEngine.Random.Range(-10, 10));

                    List<BCBase> workerBases = building.m_components.FindAll(x => x is BCWorkerBase);
                    foreach(var maWorkerBase in workerBases)
                    {
                        var workersPresent = maWorkerBase.GetWorkersPresent();
                        foreach(var maWorker in workersPresent)
                        {
                            maWorkerBase.Deallocate(maWorker);
                        }
                    }

                    DesignTableManager.ConvertToWildBlock(block.gameObject);
                }
            }
            if(GUILayout.Button("Add Required Stock To Produce"))
            {
                Debug.LogError("TODO");
            }
            
            MAOrder order = building.Order;
            if(order.IsValid)
            {
                if(GUILayout.Button($"Complete Order"))
                {
                    order.Complete();
                }
                if(GUILayout.Button($"Cancel Order"))
                {
                    order.Return();
                }
            }
            if(GUILayout.Button($"Reset Health"))
            {
                building.HealthNorm = 1f;
            }
            if(building.m_stateData != null && GUILayout.Button($"{(building.m_stateData.m_lockedFromPlayer ? "AllowPlayerToEdit" : "LockFromPlayer")}"))
            {
                building.m_stateData.m_lockedFromPlayer = !building.m_stateData.m_lockedFromPlayer;
                building.UpdateBuildSign(); 
            }
        }
    }
}
#endif
#endregion
