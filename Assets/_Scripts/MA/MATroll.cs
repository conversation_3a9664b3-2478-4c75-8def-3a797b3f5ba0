using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif
public class MATroll : MA<PERSON><PERSON>ie
{
	[Header("MATroll")]
	[Range(1f,8f)]
	[SerializeField] private float m_tempTrollScaleOfZombieContent = 3f;

	protected override float[] NavCostsThreat => GlobalData.s_trollCosts;

	override public string HumanoidType => "Troll";
	protected override bool TakesVengeance => true;

	protected override void Awake()
	{
		base.Awake();
	}

	public override void Activate(MACreatureInfo _creatureInfo, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation, bool _addToCharacterLists = true)
	{
		base.Activate(_creatureInfo, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);

		//m_contentRoot.localScale *= m_tempTrollScaleOfZombieContent;
	}	
}

#if UNITY_EDITOR
[CustomEditor(typeof(MATroll))]
public class MATrollEditor : MACreatureBaseEditor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
	}
}
#endif