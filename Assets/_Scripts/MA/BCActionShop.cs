using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCActionShop : BCActionOrderBase
{
    [KnackField] public float m_workerSellSpeed = 1;
    public override int MaxCardSlots { get { return 1; } }
    public override float SlotLockDuration { get { return 0; } }
    

    public override SpecialHandlingAction GetSpecialHandlingAction(NGMovingObject _obj, SpecialHandlingAction _restrictedAction)
    {
        MAWorker worker = _obj as <PERSON><PERSON><PERSON><PERSON>;
        if (worker != null)
        {
            if (_restrictedAction == null || _restrictedAction == SpecialHandlingAction.CollectClothing)
                if (<PERSON><PERSON>ollec<PERSON>(worker))
                    return SpecialHandlingAction.CollectClothing;
        }
        return null;
    }

    virtual public void SetDesign(MACharacterBase _character, GameState_Design _design)
    {
        _character.SetArmourDesign(_design);
    }

    protected bool HasCollectable(MACharacterBase _character)
    {
        return GetSuitableResource(_character) != null;
    }
    
    public NGCarriableResource GetSuitableResource(MACharacterBase _character)
    {
        foreach(var component in Building.m_components)
        {
            foreach(var item in component.GetStock().Items)
            {
                if(item.m_stock <= 0) continue;
                
                var resource = item.Resource;
                var product = resource.GetProduct();
                if(product == null)
                    continue;
                    
                if(CanAcceptResource(resource) == false)
                    continue;
                    
                product.GetLinkedOrder(out var order);
                if(order.IsNullOrEmpty() || order.OrderInfo == null)
                    continue;
                
                var collectType = "";
                if(order.OrderInfo.m_collectType.IsNullOrWhiteSpace() == false)
                {
                    collectType = order.OrderInfo.m_collectType.ToLower();
                }
                
                switch(collectType)
                {
                    case "maleworker":
                        if(_character as MAWorker && _character.Gender == MAWorkerInfo.WorkerGender.Male)
                            return resource;
                        break;
                    
                    case "femaleworker":
                        if(_character as MAWorker && _character.Gender == MAWorkerInfo.WorkerGender.Female)
                            return resource;
                        break;
                        
                    case "malehero":
                        if(_character as MAHeroBase && _character.Gender == MAWorkerInfo.WorkerGender.Male)
                            return resource;
                        break;
                    
                    case "femalehero":
                        if(_character as MAHeroBase && _character.Gender == MAWorkerInfo.WorkerGender.Female)
                            return resource;
                        break;
                        
                    default:
                        if(_character as MAHeroBase)
                            return resource;
                        break;
                }
            }
        }
        return null;
    }

    public virtual bool AutoStartSequence => true;
    public virtual bool RepeatSequence => true;
    
    protected virtual string OrderInfoBlockName => "ShopOrder";
    
    [Save] bool m_hasLoadedSequence = false;
    [Save] bool m_sequenceStarted = false;
    private bool m_runOutOfOrders = false;
    
    //override public List<NGCarriableResource> GetInputActionStockType() => new List<NGCarriableResource>(){NGCarriableResource.GetInfo("Product:30")};
    
    public void StartSequence()
    {
        m_sequenceStarted = true;
    }
    
    public override void OnOrderSequenceEmpty()
    {
        if(AutoStartSequence == false && m_sequenceStarted == false)
            return;
        if(m_hasLoadedSequence && RepeatSequence == false)
            return;
            
        m_hasLoadedSequence = true;
        m_sequenceStarted = true;
        
        var orderInfos = MAOrderInfo.GetInfosByBlockName(OrderInfoBlockName);
        if(orderInfos == null || orderInfos.Count == 0 || m_runOutOfOrders)
            return;
        
        // Find unused orders
        foreach(var orderInfo in orderInfos)
        {
            // Skip orders we have already completed
            bool completed = false;
            foreach(var historicOrder in GameManager.Me.m_state.m_orderDataHistory)
            {
                if(orderInfo.m_name.Equals(historicOrder.OrderInfoIndexer))
                {
                    completed = true;
                    break;
                }
            }
            if(!completed)
                Sequence.Insert(orderInfo, false);
        }
        
        m_runOutOfOrders = true;
    }

    virtual public bool Collect(NGMovingObject _o)
    {
        var character = _o as MACharacterBase;

        var suitableResource = GetSuitableResource(character);
        if(suitableResource == null || Building.ConsumeStock(suitableResource) == false)
            return false;

        var design = suitableResource.GetProduct()?.Design;
        if (design == null)
            return false;

        SetDesign(character, design);
        RegisterUniqueDressedCharacter(character, design);
        
        if(character is MAWorker)
            (character as MAWorker).SetDefaultAction();
        return true;
    }

    private readonly string[] c_clothingNames = { "Clothing", "Clothing Female" };
    private void RegisterUniqueDressedCharacter(MACharacterBase _character, GameState_Design _design)
    {
        if (GameManager.Me.m_state.m_uniquePeopleDressed != null && GameManager.Me.m_state.m_uniquePeopleDressed.Contains(_character.m_ID)) return;
        
        var partsNew = DesignUtilities.GetDesignData(_design.m_design);
        if (partsNew.Length <= 1) return;
        
        for (int i = 1; i < partsNew.Length; i++)
        {
            var block = NGBlockInfo.GetInfo(partsNew[i].m_blockID);
            if (c_clothingNames.Contains(block.m_nGProductInfo) == false) continue;
            
            var state = GameManager.Me.m_state;
            bool designWasManufactured = false;
            for (int j = 0; j < state.m_products.Count; j++)
            {
                if (state.m_products[j] != null)
                {
                    if (state.m_products[j].HasDesign &&
                        state.m_products[j].m_design.m_design.IsNullOrWhiteSpace() == false &&
                        state.m_products[j].m_placeOfManufacture > 0)
                    {
                        designWasManufactured = true;
                        break;
                    }
                }
                else
                {
                    Debug.LogError($"{GetType().Name} - Dress Check - saved products list GameState.m_products Index '{j}' has gameStateProduct == null");
                }
            }
            if (designWasManufactured == false) continue;
            
            var drawerInfos = MADrawerInfo.GetInfos(block.m_mADrawerInfos);
            foreach (MADrawerInfo maDrawerInfo in drawerInfos)
            {
                if (c_clothingNames.Contains(maDrawerInfo.m_drawerName) == false) continue;
                
                if (state.m_uniquePeopleDressed != null)
                {
                    Array.Resize(ref state.m_uniquePeopleDressed, state.m_uniquePeopleDressed.Length + 1);
                    state.m_uniquePeopleDressed[^1] = _character.m_ID;
                }
                else state.m_uniquePeopleDressed = new [] { _character.m_ID };
                break;
            }
        }
    }
    
    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (m_info?.id, () => new BCShopPanel(m_info));
    public override bool CombineGUI => false;
}

public class BCShopPanel : BCUIPanel
{
    protected BCActionShop m_shop;
    
    private string m_blockID;
    public override bool ShowWarning => m_shop?.ShowWarning ?? false;
    public override string SpriteBlockID => m_blockID;
    public override string GetPrimaryText()
    {
        var desc = "";
        
        if(desc.IsNullOrWhiteSpace() == false) desc += "\n\n";
        
        desc += m_shop?.m_info?.m_description;
        
        if(desc.IsNullOrWhiteSpace() == false) desc += "\n\n";
        desc += $"<b>Stock</b>";
        
        bool hasItems = false;
        foreach(var item in m_shop.GetStock().Items)
        {
            if(item.m_stock <= 0) continue;
            
            var res = item.Resource;
            
            var product = res.GetProduct();
            if(product == null || product.GetLinkedOrder(out var order) == false)
                continue;
            
            desc += $"\n{item.m_stock} x {order.OrderInfo.m_name}";
            
            hasItems = true;
        }
        
        if(hasItems == false) desc += $"\nNone";
        
        return desc; 
    }
    public BCShopPanel(MAComponentInfo _info) : base(_info) { }

    public override void AddComponent(BCBase _component)
    {
        if(_component is BCActionShop)
        {
            m_blockID = _component.Block.BlockID;
            m_shop = _component as BCActionShop;
            m_all.Add(_component);
        }
    }
    
    public override string GetDescription()
    {
        int validOrders = 0;
        foreach(var slot in m_shop.Slots)
        {
            if(slot.m_order.IsNullOrEmpty() == false)
            {
                validOrders++;
            }
        }
        return $"{validOrders} Order(s) Available";
    }
}
