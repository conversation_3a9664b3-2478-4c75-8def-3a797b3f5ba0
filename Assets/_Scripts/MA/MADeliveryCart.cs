using System.Collections.Generic;
using Cans.Analytics;
using UnityEngine;
using VehicleStates;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class MADeliveryCart : MAVehicle
{
    private List<GameState_Product> Cargo => VehicleData.m_products;
    
    public override int FreeCargoSpace => MaxCargoSpace - Cargo.Count;
    public override int MaxCargoSpace => 999;
    public override string HoldingTransformName => "ProductHolder";
    
    protected override void Start()
    {
        base.Start();
        VehicleBaseState vehicleState = VehicleState;
        if(vehicleState == null || vehicleState.State == VehicleStateFactory.VehicleState.kNone)
            VehicleStateFactory.ApplyState(VehicleStateFactory.VehicleState.kInitialSpawn, this);
    }

    public override void SetNavAnimatorValues(float _speed, float _sideSpeed, float _idleness)
    {
        SetAnimatorFloat("Speed", _speed);
    }

    protected override bool WantKinematic(bool _isTerrainVisible)
    {
        return !_isTerrainVisible;
    }
    
    override protected void Update()
    {
        base.Update();
        if(GameManager.Me.DataReady && Home == null && DesignTableManager.Me.IsInDesignGloballyActively == false)
        {
            DestroyMe();
        }
    }
    
    private bool CanDeliverHere(MAOrder _order)
    {
        return true; // carts now have a 1 2 1 relationship with dispatch
        if(Cargo.Count == 0) return true;
        
        return Cargo[0].GetLinkedOrder() == _order;
    }
    
    public bool AddOrderItemToVanCargo(NGCarriableResource _resource)
    {
        if(IsFull) return false;
        
        // We do this to get rid of any potential old stock that slipped the net
        if(_resource.IsFromCompletedOrder())
            return true;
            
        var product = _resource.GetProduct();
        var order = product.GetLinkedOrder();
        
        if(order.IsNullOrEmpty() || order.IsValid == false)
        {
            Debug.LogError($"{GetType().Name} - Cargo place of manufacture: {product.m_placeOfManufacture} cannot be linked to a factory and therefore cannot be linked to an order");
            return false;
        }
        
        if(CanDeliverHere(order) == false)
            return false;
        
        BCActionDispatch.SellItem(product);
        
        var uniqueProduct = new GameState_Product(product);
        Cargo.Add(uniqueProduct);
        CreateCargoVisuals(uniqueProduct);
        
        order.ItemDispatched();
        order.ItemDelivered();
        
        if(IsFull || order.IsComplete)
        {
            if(VehicleState.State != VehicleStateFactory.VehicleState.kMovingOffMap)
                VehicleStateFactory.ApplyState(VehicleStateFactory.VehicleState.kMovingOffMap, this);
        }

        return true;
    }
    
    public override void OnArrivedAtFinalDestination()
    {
        var dispatchBuildings= NGManager.Me.m_maBuildings.FindAll(x =>
            x.m_componentsDict.ContainsKey(typeof(BCActionDispatch)));
        HandOverCargo();
    }

    protected override void SetVehicleData_Internal(GameState_MAVehicle _vehicleData)
    {
        if(_vehicleData.m_products == null) _vehicleData.m_products = new List<GameState_Product>();
        
        base.SetVehicleData_Internal(_vehicleData);
    }

    public override void PostLoad(bool _justCreated=false)
    {
        base.PostLoad(_justCreated);
        
        foreach(var cargoItem in VehicleData.m_products)
        {
            CreateCargoVisuals(cargoItem);
        }
    }

    private static Vector3[] s_stockPositions = new []
    {
        new Vector3(-0.65f,0,0.6f),
        new Vector3(0.65f,0,0.6f),
        new Vector3(-0.65f,0,-0.7f),
        new Vector3(0.65f,0,-0.7f)
    };
    
    protected override NGReactPickupAny CreateCargoVisuals(GameState_Product _productData)
    {
        int cargoIndex = Cargo.IndexOf(_productData);
        
        if(cargoIndex < 0)
            cargoIndex = Cargo.Count;
        
        PickupSetup pickupSetup = new()
        {
            m_holder = GlobalData.Me.m_pickupsHolder,
            m_quantity = 1,
            m_killPickup = true,
            m_onComplete = (_o) =>
            {
                NGReactPickupAny pickupProduct = _o.GetComponent<NGReactPickupAny>();
                pickupProduct.AssignToCarrier(this, false, false);

                if(pickupProduct.GetComponent<Pickup>() == null)
                    pickupProduct.gameObject.AddComponent<Pickup>();

                pickupProduct.m_holder = this;
                pickupProduct.transform.localScale = Vector3.one;
                pickupProduct.transform.rotation = Quaternion.identity;
                
                int heightIndex = cargoIndex/s_stockPositions.Length;
                float height = heightIndex * ManagedBlock.GetTotalVisualBounds(pickupProduct.gameObject).extents.y;
                pickupProduct.transform.localPosition = s_stockPositions[cargoIndex%s_stockPositions.Length] + new Vector3(0, height, 0);
                pickupProduct.SetCollisionStyle(COLLISIONSTYLE.NOPHYSICS);
            },
        };
        
        if(Home?.Building == null)
        {
            // What to do here?
            return null;
        }
        return NGReactPickupAny.Create(Home.Building, _productData.ResourceType, pickupSetup);
    }
    
    public override void HandOverCargo()
    {
        base.HandOverCargo();
        
        List<NGReactPickupAny> pickups = new();
        Visuals.HoldingTransform.GetComponentsInChildren<NGReactPickupAny>(false, pickups);
        
        foreach(var pickup in pickups)
        {
            Destroy(pickup.gameObject);
        }
        
        Cargo.Clear();
    }
    
    public bool ContainsCargo(string _productId)
    {
        return Cargo.FindIndex(x => x.m_uniqueID == _productId) != -1;
    }
}
#if UNITY_EDITOR
[CustomEditor(typeof(MADeliveryCart))]
public class MADeliveryCartEditor : MAVehicleEditor { }
#endif