using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MADesignPriceSheetLine : MonoBehaviour
{
    public class Descriptor
    {
        public string m_name;
        public string m_resources;
        public float m_price;
        public float m_rating;
        public float m_cost;
        public bool m_bold;
        
        public Descriptor(string _name) { m_name = _name; }
        public Descriptor(string _name, string _resources, float _cost, float _price, float _rating, bool _bold = false)
        { m_name = _name; m_resources = _resources; m_cost = _cost; m_price = _price; m_rating = _rating; m_bold = _bold; }
        
        public virtual void Activate(MADesignPriceSheetLine _sheet, bool _bottomLine)
        {
            _sheet.Activate(m_name, m_resources, m_cost, m_price, m_rating, _bottomLine, m_bold);
        }
    }
    
    public class TitleDescriptor : Descriptor
    {
        public string m_col1;
        public string m_col2;
        public string m_col3;
        
        public TitleDescriptor(string _name, string _col1, string _col2 = null, string _col3 = null) : base(_name) { m_col1 = _col1; m_col2 = _col2; m_col3 = _col3; }
        
        public override void Activate(MADesignPriceSheetLine _sheet, bool _bottomLine)
        {
            _sheet.Activate(m_name, m_col1, m_col2, m_col3, m_rating, _bottomLine);
        }
        
    }
    
    public TMP_Text m_name;
    public TMP_Text m_resources;
    public TMP_Text m_cost;
    public TMP_Text m_price;
    public Image m_bottomLine;
    public Transform m_starHolder;
    public Button m_actionButton;
    public NGBlockInfo m_block;
    
    public void EnableActionButton()
    {
        m_actionButton.enabled = true;
    }
    public void OnActionButtonClick()
    {
        if(m_block != null)
        {
            var drawers = MADrawerInfo.GetInfosByDrawerName(m_block.m_mADrawerInfos);
            
            if(drawers.Count == 0) return;
            
            foreach(var draw in drawers)
            {
                if(DesignTableManager.Me.IsValidForType(draw.m_drawerName))
                {
                    DesignTableManager.Me.OpenDrawSet(draw.m_drawerType, draw.m_drawerName, draw.m_drawerTitle);
                    break;
                }
            }
        }
    }
    
    void Activate(string _name, string _resources, float _cost, float _price, float _rating, bool _bottomLine, bool _bold)
    {
        string star = "️";
        
        m_name.text = _name;
        m_resources.text = _resources;
        
        if(_price < 0)
            m_price.text = $"<color=red>{GlobalData.CurrencySymbol}{_price:F2}</color>";
        else
            m_price.text = $"{GlobalData.CurrencySymbol}{_price:F2}";
            
        if(_cost < 0)
            m_cost.text = $"<color=red>{GlobalData.CurrencySymbol}{_cost:F2}</color>";
        else
            m_cost.text = $"{GlobalData.CurrencySymbol}{_cost:F2}";
            
            
        if(_bold)
        {
            m_cost.text = $"<b>{m_cost.text}</b>";
            m_price.text = $"<b>{m_price.text}</b>";
        }
        /*
        var rating = (int)(5 * _rating);
        for(int i = 0; i < m_starHolder.childCount; i++)
            m_starHolder.GetChild(i).gameObject.SetActive(i < rating);*/
        m_bottomLine.gameObject.SetActive(_bottomLine);

    }
    
    void Activate(string _name, string _col1, string _col2, string _col3, float _rating, bool _bottomLine)
    {
        string star = "️";
        
        m_name.text = _name;
        m_resources.text = _col1;
        m_cost.text = _col2;
        m_price.text = _col3;
        
        m_cost.gameObject.SetActive(_col2 != null);
        m_price.gameObject.SetActive(_col3 != null);

        /*var rating = (int)(5 * _rating);
        for(int i = 0; i < m_starHolder.childCount; i++)
            m_starHolder.GetChild(i).gameObject.SetActive(i < rating);*/
        m_bottomLine.gameObject.SetActive(_bottomLine);
    }

    public static MADesignPriceSheetLine Create(Transform _holder, Descriptor _desc, bool _bottomLine)
    {
        var prefab = Resources.Load<MADesignPriceSheetLine>("_Prefabs/Dialogs/MADesignPriceSheetLine");
        var go = Instantiate(prefab, _holder);
        var instance = go.GetComponent<MADesignPriceSheetLine>();
        _desc.Activate(instance, _bottomLine);
        return instance;
    }
}
