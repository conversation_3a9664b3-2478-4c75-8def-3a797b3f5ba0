using System;
using System.Collections.Generic;
using MACharacterStates;
using UnityEngine;
using Follow = MACharacterStates.Follow;

public static class CharacterStates
{
	public const string Idle = "Idle";
	public const string Spawn = "Spawn";
	public const string Eject = "Eject";
	public const string RoamForTarget = "RoamForTarget";
	public const string LookForTarget = "LookForTarget";
	public const string ChaseTarget = "ChaseTarget";
	public const string PatrolTownWalls = "PatrolTownWalls";
	public const string ClimbBuilding = "ClimbBuilding";
	public const string AttackFar = "AttackFar";
	public const string AttackMoveCloser = "AttackMoveCloser";
	public const string AttackClose = "AttackClose";
	public const string AttackCooldown = "AttackCooldown";
	public const string Stagger = "Stagger";
	public const string Petrified = "Petrified";
	public const string KnockedDown = "KnockedDown";
	public const string Dying = "Dying";
	public const string Dead = "Dead";
	public const string Unconscious = "Unconscious";
	public const string UnconsciousDead = "UnconsciousDead";
	public const string GoingHome = "GoingHome";
	public const string Despawn = "Despawn";
	public const string PatrolToWaypoint = "PatrolToWaypoint";
	public const string Waiting = "Waiting";
	public const string GestureAndContinue = "WaitingForAnim";
	public const string Possessed = "Possessed";
	public const string ReturnToPatrol = "ReturnToPatrol";
	public const string WorkerState = "WorkerState";
	public const string Collect = "Collect";
	public const string HeldByPlayer = "HeldByPlayer";
	public const string GoToWaypointIgnoringTargets = "GoToWaypointIgnoringTargets";
	public const string GuardLocation = "GuardLocation";
	public const string SmashWall = "SmashWall";
	public const string JumpWall = "JumpWall";
	public const string StandUp = "StandUp";
	public const string Interaction = "Interaction";
	public const string ReturnToGuardLocation = "ReturnToGuardLocation";
	public const string Follow = "Follow";
	public const string EatPickup = "EatPickup";
	public const string Flee = "Flee";
	public const string PeeOnDecoration = "PeeOnDecoration";
	public const string PooAtPosition = "PooAtPosition";
	public const string Mourn = "Mourn";
	public const string CreatureHarvesting = "CreatureHarvesting";
	public const string WaitingToTeleport = "WaitingToTeleport";
	public const string FollowLeader = "FollowLeader"; // this is the tag follow system which allows for some free will, as opposed to the Follow system above (animals)
}

public static class MACreatureStateLibrary
{
	public static Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary = new()
	{
		{ CharacterStates.Idle, _characterBase => new Idle(CharacterStates.Idle, _characterBase) },
		{ CharacterStates.AttackCooldown, _characterBase => new AttackCoolDown(CharacterStates.AttackCooldown, _characterBase) },
		{ CharacterStates.RoamForTarget, _characterBase => new RoamForTarget(CharacterStates.RoamForTarget, _characterBase) },
		{ CharacterStates.LookForTarget, _characterBase => new LookForTarget(CharacterStates.LookForTarget, _characterBase) },
		{ CharacterStates.ChaseTarget, _characterBase => new ChaseTarget(CharacterStates.ChaseTarget, _characterBase) },
		{ CharacterStates.PatrolTownWalls, _characterBase => new PatrolTownWalls(CharacterStates.PatrolTownWalls, _characterBase) },
		{ CharacterStates.AttackFar, _characterBase => new AttackFar(CharacterStates.AttackFar, _characterBase) },
		{ CharacterStates.AttackMoveCloser, _characterBase => new AttackMoveCloser(CharacterStates.AttackMoveCloser, _characterBase) },
		{ CharacterStates.AttackClose, _characterBase => new AttackClose(CharacterStates.AttackClose, _characterBase) },
		{ CharacterStates.KnockedDown, _characterBase => new KnockedDown(CharacterStates.KnockedDown, _characterBase) },
		{ CharacterStates.Dying, _characterBase => new Dying(CharacterStates.Dying, _characterBase) },
		{ CharacterStates.Dead, _characterBase => new Dead(CharacterStates.Dead, _characterBase) },
		{ CharacterStates.GoingHome, _characterBase => new GoingHome(CharacterStates.GoingHome, _characterBase) },
		{ CharacterStates.Spawn, _characterBase => new Spawn(CharacterStates.Spawn, _characterBase) },
		{ CharacterStates.Despawn, _characterBase => new Despawn(CharacterStates.Despawn, _characterBase) },
		{ CharacterStates.Waiting, _characterBase => new Waiting(CharacterStates.Waiting, _characterBase) },
		{ CharacterStates.GestureAndContinue, _characterBase => new Waiting(CharacterStates.GestureAndContinue, _characterBase) },
		{ CharacterStates.Possessed, _characterBase => new Possessed(CharacterStates.Possessed, _characterBase) },
		{ CharacterStates.SmashWall, _characterBase => new SmashWall(CharacterStates.SmashWall, _characterBase) },
		{ CharacterStates.StandUp, _characterBase => new StandUp(CharacterStates.StandUp, _characterBase) },
		{ CharacterStates.Unconscious, _characterBase => new Unconscious(CharacterStates.Unconscious, _characterBase) },
		{ CharacterStates.PatrolToWaypoint, _characterBase => new PatrolToWaypoint(CharacterStates.PatrolToWaypoint, _characterBase) },
		{ CharacterStates.GuardLocation, _characterBase => new GuardLocation(CharacterStates.GuardLocation, _characterBase) },
		{ CharacterStates.ReturnToPatrol, _characterBase => new ReturnToPatrol(CharacterStates.ReturnToPatrol, _characterBase) },
		{ CharacterStates.ReturnToGuardLocation, _characterBase => new ReturnToGuardLocation(CharacterStates.ReturnToGuardLocation, _characterBase) },
		{ CharacterStates.Stagger, _characterBase => new Stagger(CharacterStates.Stagger, _characterBase) },
		{ CharacterStates.Petrified, _characterBase => new Petrified(CharacterStates.Petrified, _characterBase) },
		{ CharacterStates.CreatureHarvesting, _characterBase => new CreatureHarvesting(CharacterStates.CreatureHarvesting, _characterBase) },
		{ CharacterStates.FollowLeader, _characterBase => new FollowLeader(CharacterStates.FollowLeader, _characterBase) },
	};
}
public static class MAWorkerStateLibrary
{
	public static Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary = new()
	{
		{ CharacterStates.Idle, _characterBase => new Idle(CharacterStates.Idle, _characterBase) },
		{ CharacterStates.Dying, _characterBase => new Dying(CharacterStates.Dying, _characterBase) },
		{ CharacterStates.Dead, _characterBase => new Dead(CharacterStates.Dead, _characterBase) },
		{ CharacterStates.WorkerState, _characterBase => new WorkerState(CharacterStates.WorkerState, _characterBase) },
		{ CharacterStates.StandUp, _characterBase => new StandUp(CharacterStates.StandUp, _characterBase) },
		{ CharacterStates.Unconscious, _characterBase => new Unconscious(CharacterStates.Unconscious, _characterBase) },
		{ CharacterStates.KnockedDown, _characterBase => new KnockedDown(CharacterStates.KnockedDown, _characterBase) },
		{ CharacterStates.Follow, _characterBase => new MACharacterStates.Follow(CharacterStates.Follow, _characterBase) },
	};
}

public static class MAHeroStateLibrary
{
	public static Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary = new()
	{
		{ CharacterStates.Idle, _characterBase => new Idle(CharacterStates.Idle, _characterBase) },
		{ CharacterStates.AttackCooldown, _characterBase => new AttackCoolDown(CharacterStates.AttackCooldown, _characterBase) },
		{ CharacterStates.ChaseTarget, _characterBase => new ChaseTarget(CharacterStates.ChaseTarget, _characterBase) },
		{ CharacterStates.LookForTarget, _characterBase => new LookForTarget(CharacterStates.LookForTarget, _characterBase) }, 
		{ CharacterStates.RoamForTarget, _characterBase => new RoamForTarget(CharacterStates.RoamForTarget, _characterBase) },
		{ CharacterStates.PatrolTownWalls, _characterBase => new PatrolTownWalls(CharacterStates.PatrolTownWalls, _characterBase) },
		{ CharacterStates.AttackFar, _characterBase => new AttackFar(CharacterStates.AttackFar, _characterBase) },
		{ CharacterStates.AttackClose, _characterBase => new AttackClose(CharacterStates.AttackClose, _characterBase) },
		{ CharacterStates.KnockedDown, _characterBase => new KnockedDown(CharacterStates.KnockedDown, _characterBase) },
		{ CharacterStates.Dying, _characterBase => new Dying(CharacterStates.Dying, _characterBase) },
		{ CharacterStates.Dead, _characterBase => new Dead(CharacterStates.Dead, _characterBase) },
		{ CharacterStates.UnconsciousDead, _characterBase => new UnconsciousDead(CharacterStates.UnconsciousDead, _characterBase) },
		{ CharacterStates.Unconscious, _characterBase => new Unconscious(CharacterStates.Unconscious, _characterBase) },
		{ CharacterStates.GoingHome, _characterBase => new GoingHome(CharacterStates.GoingHome, _characterBase) },
		{ CharacterStates.Spawn, _characterBase => new HeroSpawn(CharacterStates.Spawn, _characterBase) },
		{ CharacterStates.Eject, _characterBase => new HeroEjected(CharacterStates.Eject, _characterBase) },
		{ CharacterStates.Despawn, _characterBase => new Despawn(CharacterStates.Despawn, _characterBase) },
		{ CharacterStates.GoToWaypointIgnoringTargets, _characterBase => new GoToWaypointIgnoringTargets(CharacterStates.GoToWaypointIgnoringTargets, _characterBase) },
		{ CharacterStates.ReturnToGuardLocation, _characterBase => new ReturnToGuardLocation(CharacterStates.ReturnToGuardLocation, _characterBase) },
		{ CharacterStates.GuardLocation, _characterBase => new GuardLocation(CharacterStates.GuardLocation, _characterBase) },
		{ CharacterStates.HeldByPlayer, _characterBase => new HeroHeldByPlayer(CharacterStates.HeldByPlayer, _characterBase) },
		{ CharacterStates.PatrolToWaypoint, _characterBase => new PatrolToWaypoint(CharacterStates.PatrolToWaypoint, _characterBase) },
		{ CharacterStates.ReturnToPatrol, _characterBase => new ReturnToPatrol(CharacterStates.ReturnToPatrol, _characterBase) },
		{ CharacterStates.Waiting, _characterBase => new Waiting(CharacterStates.Waiting, _characterBase) },
		{ CharacterStates.GestureAndContinue, _characterBase => new Waiting(CharacterStates.GestureAndContinue, _characterBase) },
		{ CharacterStates.Possessed, _characterBase => new Possessed(CharacterStates.Possessed, _characterBase) },
		{ CharacterStates.Collect, _characterBase => new Collect(CharacterStates.Collect, _characterBase) },
		{ CharacterStates.StandUp, _characterBase => new StandUp(CharacterStates.StandUp, _characterBase) },
		{ CharacterStates.Interaction, _characterBase => new Interaction(CharacterStates.Interaction, _characterBase) },
		{ CharacterStates.Stagger, _characterBase => new Stagger(CharacterStates.Stagger, _characterBase) },
		{ CharacterStates.WaitingToTeleport, _characterBase => new WaitingToTeleport(CharacterStates.WaitingToTeleport, _characterBase) },
		{ CharacterStates.FollowLeader, _characterBase => new FollowLeader(CharacterStates.FollowLeader, _characterBase) },
	};
}
public static class MAAnimalStateLibrary
{
	public static Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary = new()
	{
		{ CharacterStates.Idle, _characterBase => new Idle(CharacterStates.Idle, _characterBase) },
		{ CharacterStates.Spawn, _characterBase => new Spawn(CharacterStates.Spawn, _characterBase) },
		{ CharacterStates.Despawn, _characterBase => new Despawn(CharacterStates.Despawn, _characterBase) },
		{ CharacterStates.RoamForTarget, _characterBase => new RoamForTarget(CharacterStates.RoamForTarget, _characterBase) },
		{ CharacterStates.Possessed, _characterBase => new Possessed(CharacterStates.Possessed, _characterBase) },
		{ CharacterStates.GoingHome, _characterBase => new GoingHome(CharacterStates.GoingHome, _characterBase) },
		{ CharacterStates.GuardLocation, _characterBase => new GuardLocation(CharacterStates.GuardLocation, _characterBase) },
		{ CharacterStates.ReturnToGuardLocation, _characterBase => new ReturnToGuardLocation(CharacterStates.ReturnToGuardLocation, _characterBase) },
		{ CharacterStates.PatrolToWaypoint, _characterBase => new PatrolToWaypoint(CharacterStates.PatrolToWaypoint, _characterBase) },
		{ CharacterStates.Follow, _characterBase => new MACharacterStates.Follow(CharacterStates.Follow, _characterBase) },
		{ CharacterStates.Flee, _characterBase => new Flee(CharacterStates.Flee, _characterBase) },
		{ CharacterStates.Dying, _characterBase => new Dying(CharacterStates.Dying, _characterBase) },
		{ CharacterStates.Dead, _characterBase => new Dead(CharacterStates.Dead, _characterBase) },
		{ CharacterStates.FollowLeader, _characterBase => new FollowLeader(CharacterStates.FollowLeader, _characterBase) },
	};
}

public static class MADogStateLibrary
{
	public static Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary = new()
	{
		{ CharacterStates.Idle, _characterBase => new Idle(CharacterStates.Idle, _characterBase) },
		{ CharacterStates.Spawn, _characterBase => new Spawn(CharacterStates.Spawn, _characterBase) }, //comment these in and call SetToGuard at any point to allow dog to move around a radius. also change default state after possessed from idle to roamforTarget
		{ CharacterStates.RoamForTarget, _characterBase => new DogRoamTown(CharacterStates.RoamForTarget, _characterBase) },
		{ CharacterStates.PatrolToWaypoint, _characterBase => new PatrolToWaypoint(CharacterStates.PatrolToWaypoint, _characterBase) },
		{ CharacterStates.ReturnToGuardLocation, _characterBase => new ReturnToGuardLocation(CharacterStates.ReturnToGuardLocation, _characterBase) },
		{ CharacterStates.ReturnToPatrol, _characterBase => new ReturnToPatrol(CharacterStates.ReturnToPatrol, _characterBase) },
		{ CharacterStates.GuardLocation, _characterBase => new DogGuardLocation(CharacterStates.GuardLocation, _characterBase) },
		{ CharacterStates.Possessed, _characterBase => new Possessed(CharacterStates.Possessed, _characterBase) },
		{ CharacterStates.Follow, _characterBase => new DogFollow(CharacterStates.Follow, _characterBase) },
		{ CharacterStates.EatPickup, _characterBase => new EatPickup(CharacterStates.EatPickup, _characterBase) },
		{ CharacterStates.Flee, _characterBase => new Flee(CharacterStates.Flee, _characterBase) },
		{ CharacterStates.PeeOnDecoration, _characterBase => new InteractWithDecoration(CharacterStates.PeeOnDecoration, _characterBase)
			{
				m_animClip = MAAnimal.c_pissAnimation,
				m_speed = _characterBase.CharacterGameState.WalkSpeed,
				m_maxDistanceBetween = _characterBase.CharacterSettings.m_peeRange,
				m_delayAfterInteractComplete = 1.5f,
				m_interact = OnPee,
				m_onFinished = OnPeeFinished,
			}
		},
		{ CharacterStates.PooAtPosition, _characterBase => new MACharacterStates.InteractAtPosition(CharacterStates.PooAtPosition, _characterBase)
			{
				m_animClip = MAAnimal.c_pooAnimation,
				m_speed = _characterBase.CharacterGameState.WalkSpeed,
				m_range = _characterBase.CharacterSettings.m_pooRadius,
				m_delayAfterInteractComplete = 1.5f,
				m_interact = OnPoo,
				m_onFinished = OnPooFinished,
			}
		},
		{ CharacterStates.LookForTarget, _characterBase => new LookForTarget(CharacterStates.LookForTarget, _characterBase) },
		{ CharacterStates.ChaseTarget, _characterBase => new ChaseTarget(CharacterStates.ChaseTarget, _characterBase) },
		{ CharacterStates.AttackCooldown, _characterBase => new AttackCoolDown(CharacterStates.AttackCooldown, _characterBase) },
		{ CharacterStates.AttackClose, _characterBase => new DogAttackClose(CharacterStates.AttackClose, _characterBase) },
		{ CharacterStates.Mourn, _characterBase => new Mourn(CharacterStates.Mourn, _characterBase) },
		{ CharacterStates.KnockedDown, _characterBase => new KnockedDown(CharacterStates.KnockedDown, _characterBase) },
		{ CharacterStates.StandUp, _characterBase => new StandUp(CharacterStates.StandUp, _characterBase) },
		{ CharacterStates.FollowLeader, _characterBase => new FollowLeader(CharacterStates.FollowLeader, _characterBase) },
		{ CharacterStates.Waiting, _characterBase => new Waiting(CharacterStates.Waiting, _characterBase) },
		{ CharacterStates.GestureAndContinue, _characterBase => new WaitForAnimationEnd(CharacterStates.GestureAndContinue, _characterBase) },
	};

	private static void OnPoo(MACharacterBase _c, InteractAtPosition _state)
	{
		var pos = _c.m_nav.OriginalTargetPosition;
		_c.m_nav.StopNavigation();
		_state.LookAt(pos.PerpendicularXZ(), 120f);
	}
	private static void OnPooFinished(MACharacterBase _c, InteractAtPosition _state)
	{
		_c.CharacterGameState.m_lastDayPooed = DayNight.Me.m_day;
		_c.StopCurrentAnimation(false);
		_c.m_nav.SetAnimatorSpeed(Vector3.zero);
	}
	
	private static void OnPeeFinished(MACharacterBase _c, TargetObject _t, InteractWithDecoration _state)
	{
		_c.CharacterGameState.m_lastDayPeed = DayNight.Me.m_day;
		_c.StopCurrentAnimation(false);
		_c.m_nav.SetAnimatorSpeed(Vector3.zero);
	}
	private static void OnPee(MACharacterBase _c, TargetObject _t, InteractWithDecoration _state)
	{
        var tPos = _t.transform.position;
        var toTarget = (tPos - _c.transform.position);
        _c.m_nav.StopNavigation();
        var perpDir = toTarget.PerpendicularXZ() * 10f;
        perpDir *= _c.CharacterSettings.m_peeToRight ? -1f : 1f; //-1 defines on pee-direction, at this time anim lifts right leg
        _state.LookAt(tPos + perpDir, 120f);
	}
}

public static class MAWerewolfStateLibrary
{
	private static void SafeAdd(string _stateId, Func<MACharacterBase, CharacterBaseState> _returnState)
	{
		if (StateLibrary.TryAdd(_stateId, _returnState) == false)
		{
			Debug.LogError($"WARNING! StateLibrary.Add({ _stateId }, _returnState) failed. Exists Already as: '{StateLibrary[_stateId]?.Invoke(null).GetType().Name}");
		}
	}

	public static Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary = new();
	
	static MAWerewolfStateLibrary()
	{
		SafeAdd(CharacterStates.Idle, _characterBase => new Idle(CharacterStates.Idle, _characterBase));
		SafeAdd(CharacterStates.AttackCooldown, _characterBase => new AttackCoolDown(CharacterStates.AttackCooldown, _characterBase));
		SafeAdd(CharacterStates.ChaseTarget, _characterBase => new ChaseTarget(CharacterStates.ChaseTarget, _characterBase));
		SafeAdd(CharacterStates.LookForTarget, _characterBase => new LookForTarget(CharacterStates.LookForTarget, _characterBase));
		SafeAdd(CharacterStates.RoamForTarget, _characterBase => new  WerewolfRoamForTarget(CharacterStates.RoamForTarget, _characterBase));
		SafeAdd(CharacterStates.PatrolTownWalls, _characterBase => new PatrolTownWalls(CharacterStates.PatrolTownWalls, _characterBase));
		SafeAdd(CharacterStates.AttackFar, _characterBase => new AttackFar(CharacterStates.AttackFar, _characterBase));
		SafeAdd(CharacterStates.AttackClose, _characterBase => new AttackClose(CharacterStates.AttackClose, _characterBase));
		SafeAdd(CharacterStates.Dying, _characterBase => new Dying(CharacterStates.Dying, _characterBase));
		SafeAdd(CharacterStates.Dead, _characterBase => new Dead(CharacterStates.Dead, _characterBase));
		SafeAdd(CharacterStates.GoingHome, _characterBase => new GoingHome(CharacterStates.GoingHome, _characterBase));
		SafeAdd(CharacterStates.Spawn, _characterBase => new Spawn(CharacterStates.Spawn, _characterBase));
		SafeAdd(CharacterStates.Despawn, _characterBase => new Despawn(CharacterStates.Despawn, _characterBase));
		SafeAdd(CharacterStates.PatrolToWaypoint, _characterBase => new PatrolToWaypoint(CharacterStates.PatrolToWaypoint, _characterBase));
		SafeAdd(CharacterStates.Waiting, _characterBase => new Waiting(CharacterStates.Waiting, _characterBase));
		SafeAdd(CharacterStates.GestureAndContinue, _characterBase => new Waiting(CharacterStates.GestureAndContinue, _characterBase));
		SafeAdd(CharacterStates.ReturnToPatrol, _characterBase => new ReturnToPatrol(CharacterStates.ReturnToPatrol, _characterBase));
		SafeAdd(CharacterStates.Unconscious, _characterBase => new Unconscious(CharacterStates.Unconscious, _characterBase));
		SafeAdd(CharacterStates.StandUp, _characterBase => new StandUp(CharacterStates.StandUp, _characterBase));
		SafeAdd(CharacterStates.Stagger, _characterBase => new Stagger(CharacterStates.Stagger, _characterBase));
		SafeAdd(CharacterStates.KnockedDown, _characterBase => new KnockedDown(CharacterStates.KnockedDown, _characterBase));
		SafeAdd(CharacterStates.JumpWall, _characterBase => new JumpWall(CharacterStates.JumpWall, _characterBase));
	}
}