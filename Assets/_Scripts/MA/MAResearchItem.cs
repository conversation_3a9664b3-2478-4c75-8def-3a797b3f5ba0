using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

/*public class MAResearchItem : MonoBehaviour
{
    public Transform m_lineHolder;
    public TMP_Text m_name;
    public Image m_icon;
    
    private MAResearchInfo m_info;
    private MAResearchItem m_fromItem;
    private int m_index;
    public void Activate(MAResearchItem _fromItem, MAResearchInfo _info, int _index)
    {
        m_info = _info;
        m_fromItem = _fromItem;
        m_index = _index;
        if(_info.IconSprite)
            m_icon.sprite = _info.IconSprite;
        m_name.text = m_info.m_name;
        m_info.m_displayedItem = this;
        if (m_fromItem)
        {
            transform.position = MAResearchManager.Me.SetPosition(m_fromItem, m_index);
            MAResearchManager.Me.DrawConnection(this,m_fromItem);
        }
        for (var i = 0; i < _info.m_linkToList.Count; i++)
        {
            var ri = _info.m_linkToList[i];
            if (ri.m_displayedItem != null)
            {
                MAResearchManager.Me.DrawConnection(this, ri.m_displayedItem);
                continue;
            };
            MAResearchItem.Create(transform.parent, this, ri, i+1);
        }
    }
  
  
    void DrawConnection(Vector3 _from, Vector3 _to)
    {
        var line = new GameObject("Line", typeof(LineRenderer));
        line.transform.SetParent(m_lineHolder);
        var lr = line.GetComponent<LineRenderer>();
        lr.positionCount = 2;
        lr.SetPosition(0, _from);
        lr.SetPosition(1, _to);
    }

    
    public static MAResearchItem Create(Transform _holder, MAResearchItem _fromItem, MAResearchInfo _info, int _index, Vector3 _offset = default)
    {
        var prefab = Resources.Load<MAResearchItem>("_Prefabs/Research/MAResearchItem");
        var instance = Instantiate(prefab, _holder);
        instance.transform.localPosition = _offset;
        instance.Activate(_fromItem, _info, _index);
        return instance;
    }
}*/
