using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif
public class MAZombie : MACreatureBase
{
	//[<PERSON><PERSON>("MAZombie")]
	override public string HumanoidType => "Zombie";
	public override EDeathType DeathCountType => EDeathType.Enemy;
	
	protected override void Awake()
	{
		base.Awake();
	}

	public override void DestroyedTarget()
	{
		base.DestroyedTarget();
	}

	public override void DoPossessedAction()
	{
		MACharacterStateFactory.ApplyCharacterState(CharacterStates.AttackClose, this);
	}
}

#if UNITY_EDITOR
[CustomEditor(typeof(MAZombie))]
public class MAZombieEditor : MACreatureBaseEditor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();

		MAZombie z = target as MAZombie;
		if(GUILayout.Button("PlayClip"))
		{
			z.PlayAnim("Attack", null, z.CreatureInfo.m_attackRate);
		}
	}
}
#endif