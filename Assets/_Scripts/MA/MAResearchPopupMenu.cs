using TMPro;
using UnityEngine;

public class MAResearchPopupMenu : MonoSingleton<MAResearchPopupMenu>
{
    public TMP_Dropdown m_dropDown;
    public TMP_Text m_title;
    public RectTransform m_newItemHolder;
    public RectTransform m_editItemHolder;

    private MAResearchItemUI m_overItem;
    void Activate(MAResearchItemUI _overItem)
    {
        m_overItem = _overItem;
        Vector2 mousePosition = Input.mousePosition;
        GetComponent<RectTransform>().position = mousePosition; 
        if (m_overItem == null)
        {
            m_editItemHolder.gameObject.SetActive(false);
            m_newItemHolder.gameObject.SetActive(true);
            m_title.text = "Create Research Item";
            m_dropDown.options.Clear();
            m_dropDown.options.Add(new TMP_Dropdown.OptionData("Select New"));
            foreach(var ri in MAResearchInfo.s_factionResearchDict[MAResearchManagerUI.m_faction])
            {
                if(MAResearchManagerUI.Me.IsInfoAssigned(ri))
                    continue;
                var od = new TMP_Dropdown.OptionData(ri.m_name);
                m_dropDown.options.Add(od);
            }
            m_dropDown.options.Add(new TMP_Dropdown.OptionData("CREATE NEW"));
        }
        else
        {
            m_editItemHolder.gameObject.SetActive(true);
            m_newItemHolder.gameObject.SetActive(false);
            m_title.text = $"Edit {m_overItem.m_info.m_name} Item";
        }
    }
    
    public void OnDropDownChanged(int _index)
    {
        var value = m_dropDown.value;
        var found = m_dropDown.options.Find(o => o.text == m_dropDown.options[value].text);
        switch (found.text)
        {
            case "CREATE NEW":
                var newInfo = MAResearchInfo.AddNewInfo(MAResearchManagerUI.m_faction);
                var riUInew = MAResearchItemUI.Create(MAResearchManagerUI.Me.m_researchItemHolder, transform.localPosition, Vector3.one, MAResearchItemUI.ItemState.Create, newInfo);
                MAResearchManagerUI.Me.m_researchItems.Add(riUInew);
                riUInew.CreateItemWindow(transform.localPosition);
                MAResearchManagerUI.Me.Refresh();
                break;
            default:
                var info = MAResearchInfo.GetInfo(found.text);
                if (info == null) return;
                if(MAResearchManagerUI.Me.IsInfoAssigned(info))
                    return;

                var riUI = MAResearchItemUI.Create(MAResearchManagerUI.Me.m_researchItemHolder, transform.localPosition, Vector3.one, MAResearchItemUI.ItemState.Create, info);
                MAResearchManagerUI.Me.m_researchItems.Add(riUI);
                MAResearchManagerUI.Me.Refresh();
                break;
        }
        Destroy(gameObject);
    }

    public void ClickedConnect()
    {
        m_overItem.CreateDragConnector(true);
        Destroy(gameObject);
    }

    public void ClickedEdit()
    {
        m_overItem.CreateItemWindow(transform.localPosition);
        Destroy(gameObject);
    }
    public void ClickedDelete()
    {
        m_overItem.DestroyMe();
        Destroy(gameObject);
    }
    public static MAResearchPopupMenu Create(Transform _holder, MAResearchItemUI _overItem)
    {
        var prefab = Resources.Load<MAResearchPopupMenu>("_Prefabs/Research/MAResearchPopupMenu");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_overItem);
        return instance;
    }
}
