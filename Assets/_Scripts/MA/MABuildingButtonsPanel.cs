using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MABuildingButtonsPanel : NGBuildingInfoGUIComponentBase
{
    public GameObject m_stopProductionButton;
    public GameObject m_ejectButton;
    
    public TMP_Text m_stopProductionButtonText;
    
    public void Activate(MABuilding _building)
    {
        //base.Activate();
        m_building = _building;
        m_maBuilding = _building;
        RefreshView();
    }
    void RefreshView()
    {
        bool showActionButtons = (m_maBuilding is MASpecialMABuilding) == false;
        
        m_ejectButton.SetActive(showActionButtons);
        m_stopProductionButton.SetActive(showActionButtons);
        m_stopProductionButtonText.text = m_building.IsPaused ? "Resume Production" : "Pause Production";
    }
    
    public void OnClickStop()
    {
        m_building.TogglePaused();
        RefreshView();
    }
    
    public void OnClickEjectWorkers()
    {
        (m_building as MABuilding)?.EjectOccupants();
       NGBuildingInfoGUI.DestroyCurrent();
    }
    
    public void OnClickZoom()
    {
        MAParser.MoveCamera(m_maBuilding.GetCentalPosition(), 30f);
        NGBuildingInfoGUI.DestroyCurrent();
    }
    
    public static MABuildingButtonsPanel Create(Transform _holder, MABuilding _building)
    {
        var prefab = Resources.Load<MABuildingButtonsPanel>("_Prefabs/Dialogs/MABuildingButtonsPanel");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_building);
        return instance; 
    } 
}
