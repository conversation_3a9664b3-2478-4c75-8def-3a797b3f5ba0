
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;

public class MAUndead : MACreatureBase
{
	//[Header("MAUndead")]
	override public string HumanoidType => "Undead";
	public override EDeathType DeathCountType => EDeathType.Enemy;

	override protected string m_defaultWeaponStrikeType => c_weaponType_Club;
	override protected string m_defaultWeaponDesign => "1|Weapon_Bow_TEST@|0|";

	// This is the projectile as held by the character, before being fired.
	[SerializeField]
	GameObject m_heldProjectilePrefab;
	[SerializeField]
	string m_projectileAttachPoint;

	GameObject m_heldProjectile;
	
	protected override void Awake()
	{
		base.Awake();
	}

	public override void Activate(MACreatureInfo _creatureInfo, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation, bool _addToCharacterLists = true)
	{
		base.Activate(_creatureInfo, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);

		if (m_heldProjectilePrefab != null)
		{
			m_heldProjectile = GameObject.Instantiate(m_heldProjectilePrefab);
			m_heldProjectile.SetActive(false);

			Transform t = m_heldProjectile.transform;
			Transform attachTrans = transform.FindChildRecursiveByName(m_projectileAttachPoint);
			t.SetParent(attachTrans, false);
		}

		m_onRangeAttackFired += OnProjectileFired;
		m_onShowProjectile += OnShowProjectile;
	}

	private void OnProjectileFired()
	{
		if (m_heldProjectile != null)
		{
			m_heldProjectile.SetActive(false);
		}
	}

	private void OnShowProjectile()
	{
		if (m_heldProjectile != null)
		{
			m_heldProjectile.SetActive(true);
		}
	}

	public override void DestroyedTarget()
	{
		base.DestroyedTarget();
	}

	public override void DoPossessedAction()
	{
		
	}

	public override bool SetDead()
	{
		// When dead Undead (that was weird to write) ragdoll, we split their 
		// joints so they crumble into bones.
		m_ragdollController.ScheduleBreakJoints();
		return base.SetDead();
	}
}
#if UNITY_EDITOR
[CustomEditor(typeof(MAUndead))]
public class MAUndeadEditor : MACreatureBaseEditor
{
}
#endif