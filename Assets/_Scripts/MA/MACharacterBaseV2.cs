using UnityEngine;
/*
 I need a struture designed for a unity system that will allow the following:
 - I have characters in the project that have the following features
    - They have a navigation agent that controls their movement
    - They have need to saved and loaded so that there exact state can be restored
    - They play animations based on their state and movement
- I am planning the following types of characters
    - Workers who move for and to work locations and perform tasks
    - Zombies who move towards and attack workers
    - Werewolves who move towards and attack workers and have jump animations
    - Trolls who move towards and attack workers and have jump animations
    - QuestGivers who move to locations and give quests
- each of these characters will have behavours that are unique to them but also share some common features
 */
public class MACharacterBaseV2 : MonoBehaviour
{
    public bool m_stoppedForBuildingPlacement;
    [HideInInspector] public NavAgent m_nav;

    virtual protected bool HasAgentArrived()
    {
        return m_nav.TargetReached;
    }
}
