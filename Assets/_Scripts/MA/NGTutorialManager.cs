#if CHOICES
using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using System.Runtime.InteropServices;
using UnityEngine;
using UnityEngine.UI;

public class NGTutorialManager : MonoSingleton<NGTutorialManager>
{
    private const string c_AwardCeremonyResults = "AwardCeremonyResults";
    
#if UNITY_EDITOR
    [Header("Debug Tutorial")]

    List<string> m_debugActiveTutorials = new List<string>();
    [Dropdown("m_debugActiveTutorials")]
    public string m_debugSelectedTutorial;
    [Multiline(20)]
    public string m_debugTutorialDisplay;
    public bool m_debugShowActiveTutorials = true;
    public bool m_debugShowDormantTutorials = true;
    public bool m_debugShowInactiveTutorials = false;
#endif
    [Header("Triggers")] 
    public bool m_dispatchTrigger = false;
    public bool m_interactiveLetterSent = false;
    public bool m_inBoardroom = false;
    public int m_drawerTrigger = 0;
    public bool m_partTrigger = false;
    public bool m_externalTrigger = false;
    public bool m_clickTrigger = false;
    public bool m_animationTrigger = false;
    public bool m_confirmTrigger = false;
    public bool m_spaceTrigger = false;
    public bool m_finishedTrigger = false;
    public string m_rewardTrigger = "";
    public string m_rewardCardHereTrigger = "";
    public bool m_fakeAward = false;
    public bool m_paidTrigger = false;
    public bool m_avatarFinished = true;
    public bool m_readytomake = false;
    public bool m_awardCardComing = false;
    public bool m_unlockAwardsCard = false;
    public bool m_joinAwardCompetition = false;
    public bool m_townSatisfactionTrigger = false;
    public bool m_cultureTrigger = false;
    public bool m_pickUpOptionsTrigger = false;
    public bool m_vaultTrigger = false;
    public bool m_choicesTrigger = false;
    public bool m_roadsTrigger = false;
    public bool m_designCompetitionsTrigger = false;
    public bool m_designCompEnteredTrigger = false;
    public bool m_createDesignClickedTrigger = false;
    public bool m_voteButtonClickedTrigger = false;
    public bool m_buyMoreResourcesTrigger = false;
    public bool m_awardEntryButton = false;
    public bool m_isPastDesignTableTutorial = false;
    public bool m_forceRedesignTrigger = false;
    public bool m_isForceRedesignTriggerActive = true;
    public bool m_researchLabTrigger = false;
    public bool m_treeChopped = false;
    public bool m_townManagementUIOpened = false;
    public bool m_buildingInfoGUIInfoPanelOpened = false;
    public bool m_tapHereForExcitingChallengeClicked = false;
    public int m_playerAwardCeremonyResult = -1;
    public int m_playerDesignCompResult = -1;
    public List<string> cardUnlocks;
    public bool m_hideMentor = true;
    public bool m_hideVC = true;


    [Header("Prefabs")]
    public GameObject m_mobileTutorialPrefab;
    public Transform m_mobileTutorialHolder;
    public GameObject m_tutorialGesturePrefab;
    public Transform m_foundTransform;
    public Transform m_closedDrawTransform;
    public Transform m_openDrawTransform;

    public GameObject m_contract;
    public Transform m_contractHolder;

    public GameObject m_townLighting;
    public GameObject m_tutorial_Objective;
    public Transform m_objectiveHolder;

    public enum AnimClip { Onboarding_Start }
    public NGTutorialGesture m_NGTutorialGesturePrefab;
    public List<NGTutorialMessageBase.TutorialMessageSetup> m_NGTutorialMessagePrefabs;
    public int CurrentIndex { get => GameManager.Me.m_state.m_tutorialMasterIndex; set => GameManager.Me.m_state.m_tutorialMasterIndex = value; }
    public bool IsAtEnd => CurrentIndex < 0 || CurrentIndex >= NGTutorial.s_masterTutorial.Count;

    public bool m_disabled = false;
    public List<string> m_checkRootDirectories = new List<string>()
        {"CharacterHolder", "BuildingHolder", "DecorationHolder", "BuildingPlacement", "Misc"};

    public bool ForceReset = false;
    public Animator m_newBoardroomAnimator;
    public Animator m_mentorAnimator;
    public Animator m_vcAnimator;
    public Animator m_dollyAnimator;
    public Camera m_boardroomCamera;
    public GameObject m_vcCanvas;
    public GameObject m_mentorCanvas;
    public GameObject m_boardroomPictures;
    public Image m_paper00;
    public Image m_paper01;
    public Image m_paper02;


    private int m_vcChoice = 0;
    private bool m_loaded = false;
    private bool m_forceDrop = false;
    private float m_totalPart = 0;
    private int m_minSteps = 0;
    private int m_maxSteps = 99;
    private int m_idealParts;
    private int m_maxParts;
    private GameObject m_objective;
    private int m_productIdealPartsPerDesign;
    private int m_productMaxPartsPerDesign;
    private BuildingHighlight m_buildingHighlight;
    private List<Type> m_restrictedPickupBuildingTypes = new List<Type>();
    private Coroutine m_dismissMessageCoroutine = null;
    private bool m_isRestartPending = false;
    private string m_restartPendingSection = "";
    private NGBusinessFlow m_restartPendingBusinessFlow = null;
    private MAGameFlow m_restartPendingGameFlow = null;
    private bool m_awardCeremonySectionComplete = false;
    private bool m_awardUnlockCardSectionComplete = false;
    private GameObject m_mobileTutorialPopup;

#if OldBusinessFlow
    private static DebugConsole.Command s_showtutstatus = new DebugConsole.Command("showtut", _s =>
    {
        GameManager.SetConsoleDisplay(() => $"Tutorial - {Me.GetDebugSummary()}");
    });

    public string GetDebugSummary()
    {
        var s = $"fin:{m_finishedTrigger}";
        if (m_drawerTrigger != 0) s += $" drwr:{m_drawerTrigger}";
        if (m_partTrigger) s += " part";
        if (m_externalTrigger) s += " ext";
        if (m_clickTrigger) s += " click";
        if (m_animationTrigger) s += " anim";
        if (m_confirmTrigger) s += " confirm";
        if (m_spaceTrigger) s += " space";
        if (m_rewardTrigger != "") s += " reward:"+ m_rewardTrigger;
        if (m_rewardCardHereTrigger != "") s += " reward:" + m_rewardCardHereTrigger;
        if (m_paidTrigger) s += " paid";
        if (m_avatarFinished) s += " avatar";
        if (m_readytomake) s += " rdy2make";
        if (m_unlockAwardsCard) s += " unlkAwCard";
        if (m_fakeAward) s += " fake award";
        if (m_joinAwardCompetition) s += " joinAC";
        if (m_townSatisfactionTrigger) s += " townSat";
        if (m_cultureTrigger) s += " culture";
        if (m_pickUpOptionsTrigger) s += " pickupOpt";
        if (m_vaultTrigger) s += " vault";
        if (m_choicesTrigger) s += " choices";
        if (m_roadsTrigger) s += " roads";
        if (m_designCompetitionsTrigger) s += " designComp";
        if (m_designCompEnteredTrigger) s += " designCompEntered";
        if (m_createDesignClickedTrigger) s += " createDesignClicked";
        if (m_voteButtonClickedTrigger) s += " voteButtonClicked";
        if (m_buyMoreResourcesTrigger) s += " buyMoreResources";
        if (m_awardEntryButton) s += " awardButton";
        if (m_isPastDesignTableTutorial) s += " pastDTab";
        if (m_forceRedesignTrigger) s += " forceRedes";
        if (m_isForceRedesignTriggerActive) s += " forceRedesAct";
        if (m_researchLabTrigger) s += " rlab";
        return s;
    }
#endif
    public void RestrictPickupFromBuildingType(Type _type) { if (!m_restrictedPickupBuildingTypes.Contains(_type)) m_restrictedPickupBuildingTypes.Add(_type); }
    public void ClearRestrictedPickupBuildings(Type _type) { m_restrictedPickupBuildingTypes.Remove(_type); }

    public bool ForceDropStuff { get { return m_forceDrop; } set { m_forceDrop = value; } }

    public bool ReadyToMake { get { return m_readytomake; } set { m_readytomake = value; } }
    [System.Serializable]
    public class TutorialInteractions
    {
        public bool m_townInteractions = false;
        public bool m_mineInteractions = false;
        public bool m_smelterInteractions = false;
        public bool m_factoryInteractions = false;
        public bool m_firstTimeAwards = false;
    }

    public TutorialInteractions m_interactions;
    public bool IsPastDesignTableTutorial { get { return m_isPastDesignTableTutorial; } set { m_isPastDesignTableTutorial = value; } }
    public void FirePaidTrigger() { m_paidTrigger = true; }
    public void FireResearchLabOpen() { m_researchLabTrigger = true; }
    public void FireExternalTrigger() { m_externalTrigger = true; }
    public void FireOpenDrawerTrigger(int _i) { m_drawerTrigger |= _i; }
    public void FireAddPartTrigger(float _cost) { m_partTrigger = true; m_totalPart = _cost; UpdateTutorialObjectives(m_totalPart); }
    public void FireRemovePartTrigger(float _cost) { m_totalPart = _cost; UpdateTutorialObjectives(m_totalPart); }
    public void FireDragTrigger() { m_clickTrigger = true; }
    public void FireClickTrigger() { m_clickTrigger = true; }
    public void ClearClickTrigger() { m_clickTrigger = false; }
    public void ClearPartTrigger() { m_partTrigger = false; }
    public void FireConfirmTrigger() { m_confirmTrigger = true; }
    public void FireAnimationTrigger() { m_animationTrigger = true; }
    public void FireSpaceTrigger() { m_spaceTrigger = true; }
    public void FireRewardCardHereTrigger(string _name) { m_rewardCardHereTrigger = _name; }
    public void FireRewardTrigger(string _name) { m_rewardTrigger = _name; }
    public void FireFakeAwards()
    {
        NGTutorialManager.Me.RestartDialogTutorial("AwardCeremony");
        m_fakeAward = true;
    }
    public void FireUnlockAwardCard() { m_unlockAwardsCard = true; }
    public void ActivateAwardEntryButton() { m_awardEntryButton = true; }
    public void FireAwardCardComing() { m_awardCardComing = true; }
    public void JoinAwardCompetition() { m_joinAwardCompetition = true; }
    public void FireTownSatisfactionTrigger() { m_townSatisfactionTrigger = true; }
    public void FireCultureTrigger() { m_cultureTrigger = true; }
    public void FirePickUpOptionsTrigger() { m_pickUpOptionsTrigger = true; }
    public void FireVaultTrigger() { m_vaultTrigger = true; }
    public void FireChoicesTrigger() { m_choicesTrigger = true; }
    public void FireRoadsTrigger() { m_roadsTrigger = true; }
    public void FireDesignCompetitionsTrigger() { m_designCompetitionsTrigger = true; }

    public void SendInteractiveLetter()
    {
        m_interactiveLetterSent = true;
    }
    public void TutorialHasEnded()
    {
        m_finishedTrigger = true; 
        CurrentIndex = NGTutorial.s_masterTutorial.Count;
    }
    public void ClearAllTriggers() 
    { 
        m_researchLabTrigger = false; 
        m_paidTrigger = false; 
        m_partTrigger = false; 
        m_animationTrigger = false; 
        m_externalTrigger = false;
        m_fakeAward = false;
        m_rewardTrigger = "";
        m_rewardCardHereTrigger = "";
        m_drawerTrigger = 0; 
        m_clickTrigger = false; 
        m_confirmTrigger = false; 
        m_spaceTrigger = false;
        m_townSatisfactionTrigger = false; 
        m_cultureTrigger = false; 
        m_pickUpOptionsTrigger = false; 
        m_vaultTrigger = false; 
        m_choicesTrigger = false; 
        m_roadsTrigger = false;
        m_designCompetitionsTrigger = false;
        m_designCompEnteredTrigger = false;
        m_createDesignClickedTrigger = false;
        m_voteButtonClickedTrigger = false;
        m_buyMoreResourcesTrigger = false;     
    }

    public bool ResearchLabTrigger { get { return m_researchLabTrigger; } }
    public bool AwardEntryButton { get { return m_awardEntryButton; } }
    public bool JoinedAwardCompetition { get { return m_joinAwardCompetition; } }
    public bool AwardCardComing { get { return m_awardCardComing; } }
    public bool UnlockAwardsCard { get { return m_unlockAwardsCard; } }
    public bool PaidTrigger { get { return m_paidTrigger; } }
    public bool ExternalTrigger { get { return m_externalTrigger; } }
    public int OpenDrawerTrigger { get { return m_drawerTrigger; } }
    public bool SpaceTrigger { get { return m_spaceTrigger; } }
    public bool AddPartTrigger { get { return m_partTrigger; } }
    public bool AvatarFinished { get { return m_avatarFinished; } set { m_avatarFinished = value; } }
    public float NumPartsTrigger { get { return m_totalPart; } set { m_totalPart = value; } }
    public bool ClickTrigger { get { return m_clickTrigger; } }
    public string RewardCardHereTrigger { get { return m_rewardCardHereTrigger; } }
    public string RewardTrigger { get { return m_rewardTrigger; } }
    public bool FakeAwards { get { return m_fakeAward; } set { m_fakeAward = value; } }
    public bool TownSatisfactionTrigger { get { return m_townSatisfactionTrigger; } }
    public bool CultureTrigger { get { return m_cultureTrigger; } }
    public bool PickUpOptionsTrigger { get { return m_pickUpOptionsTrigger; } }
    public bool VaultTrigger { get { return m_vaultTrigger; } }
    public bool ChoicesTrigger { get { return m_choicesTrigger; } }
    public bool RoadsTrigger { get { return m_roadsTrigger; } }
    public bool DesignCompetitionsTrigger { get { return m_designCompetitionsTrigger; } }
    public bool ForceRedesign { get { return m_forceRedesignTrigger; } }
    public bool ConfirmTrigger { get { return m_confirmTrigger; } }
    public bool TutorialEnded { get { return m_finishedTrigger; } }
    public bool IsInBoardroom { get { return m_inBoardroom; } set { m_inBoardroom = value; } }
    public bool DispatchTrigger { get { return m_dispatchTrigger; } set { m_dispatchTrigger = value; } }
    public int AwardCeremonyResult { get { return m_playerAwardCeremonyResult; } set { m_playerAwardCeremonyResult = value; } }
    public int DesignCompResult { get { return m_playerDesignCompResult; } set { m_playerDesignCompResult = value; } }
    public int VCChoice { get { return m_vcChoice; } set { m_vcChoice = value; } }
    public List<Type> RestrictedPickupBuildings { get { return m_restrictedPickupBuildingTypes; } }

    public void FireMentorAnimation() { if (m_mentorAnimator != null) m_mentorAnimator.SetTrigger("mentor"); }
    public void FireVCAnimation() { if(m_vcAnimator != null) m_vcAnimator.SetTrigger("vc"); }
    public int IdealNumberParts { get { return m_productIdealPartsPerDesign; } set { m_productIdealPartsPerDesign = value; } }
    public int MinNumberParts { get { return m_minSteps; } }
    public int MaxNumberParts { get { return m_maxSteps; } set { m_maxSteps = value; } }
    public BuildingHighlight BuildingName { get { return m_buildingHighlight; } set { m_buildingHighlight = value; } }

    public bool TownInteractions { set { m_interactions.m_townInteractions = value; } get { return m_interactions.m_townInteractions; } }
    public bool MineInteractions { set { m_interactions.m_mineInteractions = value; } get { return m_interactions.m_mineInteractions; }
    }
    public bool SmelterInteractions { set { m_interactions.m_smelterInteractions = value; } get { return m_interactions.m_smelterInteractions; }
    }
    public bool FactoryInteractions { set { m_interactions.m_factoryInteractions = value; } get { return m_interactions.m_factoryInteractions; }
    }
    public void ResetConditionalTimer() { GameManager.Me.m_state.m_tutorialConditionalTimer = -1; }
    public void ResetResearchTimer() { GameManager.Me.m_state.m_tutorialResearchLabTimer = -1; }

    public bool FirstTimeAwardCeremony { get { return m_interactions.m_firstTimeAwards; } set { m_interactions.m_firstTimeAwards = value; } }
    public void ResetTutorial(string _section, int _currentIndex)
    {
        for (int i = _currentIndex; i < NGTutorial.s_masterTutorial.Count; i++)
        {
            var line = NGTutorial.s_masterTutorial[i];
            NGTutorial.s_masterTutorial[i].State = NGTutorial.TutState.CheckTrigger;

            if (line.m_section == _section) continue;
            if (line.m_triggerCommands.Contains("EndTutorialPhase"))
                return;
        }
    }
    public void TestTriggerBusinessStageCelebration()
    {
        if(NGManager.Me.m_businessStageCelebrationPrefab.activeSelf == false)
            NGManager.Me.m_businessStageCelebrationPrefab.SetActive(true);
    }
    public bool AnimationTrigger()
    {
        if (m_animationTrigger)
        {
            m_animationTrigger = false;
            return true;
        }
        return false;
    }
    public bool HasTutorialPhaseBeenPlayed(string _section, int _currentIndex)
    {
        var line = NGTutorial.s_masterTutorial[_currentIndex];
        if (line.State != NGTutorial.TutState.CheckTrigger)
            return true;

        return false;
    }
    public static NGBusinessFlow m_parseFlowLine;
    public static MAGameFlow m_parseGameFlowLine;

    public bool TutorialPhaseHasBeenPlayed(string _section)
    {
        for (int i = 0; i < NGTutorial.s_masterTutorial.Count; i++)
        {
            var line = NGTutorial.s_masterTutorial[i];
            if (line.m_section == _section)
            {
                return HasTutorialPhaseBeenPlayed(_section, i);
            }
        }
        
        if(NGTutorial.s_dialogDict.TryGetValue(_section, out var state))
        {
            return state.m_state == NGTutorial.DictState.DictStates.Dormant;
        }
        
        foreach (var d in NGTutorial.s_dialogDict)
        {
            if (_section.Contains(d.Key))
            {
                if (d.Value.m_state == NGTutorial.DictState.DictStates.Dormant)
                {
                    return true;
                }
            }
        }

        return false;
    }

    public void TutorialRestarted(string _section, NGBusinessFlow _flow, MAGameFlow _gameFlow)
    {
        if (!GameManager.Me.LoadComplete)
        {
            m_restartPendingSection = _section;
            m_restartPendingBusinessFlow = _flow;
            m_restartPendingGameFlow = _gameFlow;
            m_isRestartPending = true;
            return;
        }

        m_finishedTrigger = false;
        ClearAllTriggers();
        var bracketSplit = _section.Split('(', ')');
        if (bracketSplit.Length > 1)
        {
            m_parseFlowLine = _flow;
            m_parseGameFlowLine = _gameFlow;
           // var result =MAKnackSupport.CallFuncFromString(_section, "TutorialRestarted");
            //var result = (bool)ReactReflection.DecodeLine(NGTutorialInterface.Me, _section);
            if (_flow != null)
            {
 //               _flow.m_waitForTutorial = result;
 //               if(result)
 //                   _flow.MyState = NGBusinessFlow.State.WaitingForTutorial;
            }
            if(_gameFlow != null)
            {
                //_gameFlow.SetWaitForTutorial(result);
            }
            return;
        }
        for (int i = 0; i < NGTutorial.s_masterTutorial.Count; i++)
        {
            var line = NGTutorial.s_masterTutorial[i];
            if (line.m_section == _section)
            {
                CurrentIndex = i;
                ResetTutorial(_section, i);
                return;
            }
        }
    }

    public void ShowMentorPose(string _pose, bool _hide) 
    {
 //        if (_hide)
 //        {
 // //           NGAnimatedPoseManager amgr = m_vcCanvas.GetComponent<NGAnimatedPoseManager>();
 //            if (amgr != null)
 //                amgr.ShowPose("");
 //        }
 // //       NGAnimatedPoseManager mgr = m_mentorCanvas.GetComponent<NGAnimatedPoseManager>();
 //        if (mgr != null)
 //            mgr.ShowPose(_pose);
    }

    public void ShowVCPose(string _pose, bool _hide) 
    {
        /*if (_hide)
        {
//            NGAnimatedPoseManager amgr = m_mentorCanvas.GetComponent<NGAnimatedPoseManager>();
            if (amgr != null)
                amgr.ShowPose("");
        }
 //       NGAnimatedPoseManager mgr = m_vcCanvas.GetComponent<NGAnimatedPoseManager>();
        if(mgr != null)
            mgr.ShowPose(_pose);

        if (NGUnlocks.Vault && NGValutGUI.Me != null)
        {
            if (_pose.IsNullOrWhiteSpace())
                NGValutGUI.Me.Show();
            else
                NGValutGUI.Me.Hide();
        }*/
    }

    private List<NGTutorialGesture> m_activeGestures = new List<NGTutorialGesture>();
    public void AddGesture(NGTutorialGesture gesture)
    {
        m_activeGestures.Add(gesture);
    }
    public void KillGestures()
    {
        for (int i = m_activeGestures.Count - 1; i >= 0; i--)
        {
            if (m_activeGestures[i] != null)
                m_activeGestures[i].DestroyMe();
        }
        m_activeGestures.Clear();
    }
    public void GestureComplete(NGTutorialGesture gesture)
    {
        if (m_activeGestures.Count == 1)
            return;

        bool found = false;
        for (int i = 0; i < m_activeGestures.Count; i++)
        {
            if (found)
            {
                m_activeGestures[i].Play();
                break;
            }
            if (m_activeGestures[i] == gesture)
                found = true;
        }

        m_activeGestures.Remove(gesture);
        gesture.DestroyMe();
    }

    public void FireForceRedesign()
    {
        if (m_isForceRedesignTriggerActive)
        {
            m_forceRedesignTrigger = true;
            m_isForceRedesignTriggerActive = false;
        }
    }

    public void ResetForceRedesign()
    {
        m_isForceRedesignTriggerActive = true;
    }

    public void ClearForceRedesign()
    {
        m_forceRedesignTrigger = false;
    }
    
    public bool IsAfterSection(string _section)
    {
        if (CurrentIndex >= NGTutorial.s_masterTutorial.Count) return true;
        for (int i = 0; i < CurrentIndex; i++)
        {
            var line = NGTutorial.s_masterTutorial[i];
            if (line.m_section == _section)
                return true;
        }

        return false;
    }

    private static DebugConsole.Command s_showtutstate = new("tutstate", _s => {
        GameManager.SetConsoleDisplay(() => Me.CurrentTutorialStateString());
    });

    private string CurrentTutorialStateString()
    {
        NGTutorial current = null;
        if (CurrentIndex < NGTutorial.s_masterTutorial.Count)
            current = NGTutorial.s_masterTutorial[CurrentIndex];
        foreach (var d in NGTutorial.s_dialogDict)
        {
            if (d.Value.m_state == NGTutorial.DictState.DictStates.Activated)
                current = d.Value.m_lines[d.Value.m_currentIndex];
        }
        if (current == null) return "No tutorial in progress";
        return $"{current.m_sectionIndex}.{current.m_section}\n<size=20%>{current.m_trigger}  {current.State}</size>";
    }


    public void EndDesignTableSection()
    {
        if (!m_isPastDesignTableTutorial)
        {
            m_isPastDesignTableTutorial = true;
            if (CurrentIndex >= NGTutorial.s_masterTutorial.Count) return;

            var line = NGTutorial.s_masterTutorial[CurrentIndex];
            if (line.m_trigger == "WaitForDesignConfirm()")
                return;

            NGTutorial.s_masterTutorial[CurrentIndex].State = NGTutorial.TutState.Dormant;
            CurrentIndex++;
            do
            {
                line = NGTutorial.s_masterTutorial[CurrentIndex];
                if (line.m_trigger == "WaitForDesignConfirm()")
                    break;

                NGTutorial.s_masterTutorial[CurrentIndex].State = NGTutorial.TutState.Dormant;
                CurrentIndex++;
            } while (CurrentIndex < NGTutorial.s_masterTutorial.Count);
        }
    }

    public bool TownInteractionsAvailable(NGCommanderBase _building)
    {
        bool active = true;

        /*if (_building as NGBikeFactory != null)
            active = FactoryInteractions;
        if (_building as NGSmelter != null)
            active = SmelterInteractions;
        if (_building as NGMine != null)
            active = MineInteractions;
*/
        return active;
    }

    public bool IsDesignTableButtonInteractable()
    {
        bool active = m_avatarFinished;
        active &= NGTutorialManager.Me.IsAfterSection("Town");
        return active;
    }

    public bool IsDesignTableFullyUnlocked()
    {
        bool active = m_avatarFinished;
        active &= NGTutorialManager.Me.IsAfterSection("BusinessFlow");
        return active;
    }

    public void DeactivateTutorialObjectives() 
    {
        if(m_objective != null)
        {
            Destroy(m_objective);
            m_objective = null;
        }
    }

    public void ActivateTutorialObjectives(string _name, bool _mode, int _min, int _max) 
    {
        m_idealParts = NGTutorialManager.Me.IdealNumberParts;
        m_maxParts = NGTutorialManager.Me.MaxNumberParts;
        m_minSteps = m_idealParts;
        m_maxSteps = _max;

        m_objective = Instantiate(m_tutorial_Objective, m_objectiveHolder);
        TutorialObjectiveMgr mgr = m_objective.GetComponent<TutorialObjectiveMgr>();
        mgr.Initialise(_name, 0, m_idealParts, _mode);

//        m_minSteps = 0;
//        m_maxSteps = NGTutorialManager.Me.IdealNumberParts;
//        mgr.Initialise(_name, 0, NGTutorialManager.Me.IdealNumberParts, _mode);
    }

    public void UpdateTutorialObjectives(float _cost) 
    {
        if (m_objective == null) return;
        TutorialObjectiveMgr mgr = m_objective.GetComponent<TutorialObjectiveMgr>();
        var parts = DesignTableManager.GetCurrentDesignParts();
        mgr.UpdateSteps(parts.Length);
//        mgr.UpdateSteps(_cost); 
 
        if (parts.Length >= m_minSteps)
            DesignTableManager.Me.ActivateConfirmButton();
        else
            DesignTableManager.Me.DeactivateConfirmButton();
    } 

    public void AnimationEnds()
    {
        FireAnimationTrigger();
    }

    public void AnimationClipEnd(AnimClip _clip) 
    {
        switch (_clip)
        {
            case AnimClip.Onboarding_Start:
                FireAnimationTrigger();
                break;
            default:
                break;
        }
    }

    public void ClearBanners()
    {
        if (NGTutorial.m_parseTutorialLine != null)
        {
            if(NGTutorial.m_parseTutorialLine.ShowingMessage)
                NGTutorial.m_parseTutorialLine.DestroyMessage();
        }
        NGTutorialManager.Me.ShowMentorPose("", false);
        NGTutorialManager.Me.ShowVCPose("", false);
/*        if (CurrentIndex < NGTutorial.s_masterTutorial.Count)
        {
            var line = NGTutorial.s_masterTutorial[CurrentIndex];
            if (line.ShowingMessage)
                line.DestroyMessage();
        }*/
    }
public void TownActive(bool on)
    {
        GameManager.Me.m_lightsAndCameras.SetActive(on);
    }

    public TutorialAudioType m_type;
    public enum TutorialAudioType
    {
        Nickname,
        Product,
        NGMine,
        NGResource,
        NGRawResourceType,
        NGSmelter,
    }
    
    public void EndTutorialEarly()
    {
        if (CurrentIndex >= NGTutorial.s_masterTutorial.Count) return;
        m_finishedTrigger = false;
        var line = NGTutorial.s_masterTutorial[CurrentIndex];
        if (line.m_trigger == "WaitForExternal()")
            return;

        NGTutorialManager.Me.m_newBoardroomAnimator.Play("Idle2");
        if (AudioClipManager.Me.IsPlaying(NGTutorial.m_audioClip))
            AudioClipManager.Me.StopVO(NGTutorial.m_audioClip);

        if (NGProductInfo.OwnedProductLines.Count > 0)
        {
            AudioClipManager.Me.SetSoundSwitchVO("VO_Nickname", NGPlayer.Me.PlayerNickname);
            if (CurrentIndex <= 284)
            {
                TownActive(true);
                OnBoardingManager.Me.CloseVCOffice();
                IsInBoardroom = false;
                do
                {
                    line = NGTutorial.s_masterTutorial[CurrentIndex];
                    if (line.m_triggerCommands.Contains("CreateAvatar"))
                        break;
                    CurrentIndex++;
                    NGTutorialManager.Me.ClearAllTriggers();
                } while (CurrentIndex < NGTutorial.s_masterTutorial.Count);
            }
            else
            {
                ClearBanners();
                TownActive(true);
                OnBoardingManager.Me.CloseVCOffice();
                m_finishedTrigger = true;
                CurrentIndex = NGTutorial.s_masterTutorial.Count;
                DesignTableManager.Me.ActivateConfirmButton();
                m_isPastDesignTableTutorial = true;
            }
            TownInteractions = true;
            MineInteractions = true;
            SmelterInteractions = true;
            FactoryInteractions = true;
            //ClearRestrictedPickupBuildings(typeof(NGMine));
            //ClearRestrictedPickupBuildings(typeof(NGClayMine));
            //ClearRestrictedPickupBuildings(typeof(NGSmelter));
            //ClearRestrictedPickupBuildings(typeof(NGBikeFactory));
            ReadyToMake = true;
            DispatchTrigger = true;
        }
        else
        {
            do
            {
                line = NGTutorial.s_masterTutorial[CurrentIndex];
                if (line.m_triggerCommands == "LoadProductLine()")
                    break;
                CurrentIndex++;
                NGTutorialManager.Me.ClearAllTriggers();
            } while(CurrentIndex < NGTutorial.s_masterTutorial.Count);

            NGPlayer.Me.PlayerNickname = "Einstein";
            AudioClipManager.Me.SetSoundSwitchVO("VO_Nickname", NGPlayer.Me.PlayerNickname);
            DispatchTrigger = false;
        }
    }

    public void PlayAudioClip(string _clip)
    {
        if (GameManager.Me.IsOKToPlayUISound())
        {
            if(NGTutorial.m_audioClip != 0)
            {
                if(AudioClipManager.Me.IsPlaying(NGTutorial.m_audioClip))
                    AudioClipManager.Me.StopVO(NGTutorial.m_audioClip);
            }
            string clipName = NGTutorialManager.Me.DecodeAudio(_clip);
            string clip = "PlaySound_" + clipName;
            NGTutorial.m_audioClip = AudioClipManager.Me.PlayVO(clip);
        }
    }

    public string   DecodeAudio(string _clip)
    {
        NGCommanderBase cb;
        NGBuildingInfoManager.NGBuildingInfo info;
        string output = "";
        /*NGTutorialInterface.TutorialDataType t;
        
        var parts = _clip.Split('[');
        if (parts.Length == 1)
        {
            output = _clip;
        }
        else
        {
            output = parts[0];
            for (int i = 1; i < parts.Length; i++)
            {
                var p = parts[i];
                var subPart = p.Split(']');
                if (Enum.TryParse(subPart[0], true, out t) != false)
                {
                    switch (t)
                    {
                        case NGTutorialInterface.TutorialDataType.Nickname:
                            output += "Nickname_" + NGPlayer.Me.PlayerNickname;
                            break;
                        case NGTutorialInterface.TutorialDataType.Product:
 //                           output += c_gameInterface.Me.PRODUCT_TITLE;
                            break;
                        case NGTutorialInterface.TutorialDataType.ProductSingle:
 //                           output += c_gameInterface.Me.PRODUCT_TITLE_SINGULAR;
                            break;
                        case NGTutorialInterface.TutorialDataType.NGMine:
                            cb = NGManager.Me.m_NGCommanderList.Find(rC => rC is NGMine && rC is not NGClayMine) as NGCommanderBase;
                            info = NGBuildingInfoManager.NGBuildingInfo.GetBuildingInfo(cb.Name);
                            output += info.m_title;
                            break;
                        case NGTutorialInterface.TutorialDataType.NumParts:
                            output += NGTutorialManager.Me.IdealNumberParts;
                            break;
                        case NGTutorialInterface.TutorialDataType.NGResource:
                            // switch (c_gameInterface.Me.PRODUCT)
                            // {
                            //     case "Bicycles":
                            //         output += "metal"; break;
                            //     case "Teddy":
                            //         output += "cloth"; break;
                            //     case "Burgers":
                            //         output += "ingredients"; break;
                            //     case "Cakes":
                            //         output += "ingredients"; break;
                            // }
                            break;
                        case NGTutorialInterface.TutorialDataType.NGRawResourceType:
                            // switch (c_gameInterface.Me.PRODUCT)
                            // {
                            //     case "Bicycles":
                            //         output += "ore"; break;
                            //     case "Teddy":
                            //         output += "cotton"; break;
                            //     case "Burgers":
                            //         output += "crops"; break;
                            //     case "Cakes":
                            //         output += "crops"; break;
                            // }
                            break;
                        case NGTutorialInterface.TutorialDataType.NGSmelter:
                            cb = NGManager.Me.m_NGCommanderList.Find(rC => rC is NGSmelter) as NGCommanderBase;
                            info = NGBuildingInfoManager.NGBuildingInfo.GetBuildingInfo(cb.Name);
                            output += info.m_title;
                            break;
                    }
                }
                if (subPart.Length > 1)
                {
                    output += subPart[1];
                }
            }
        }*/

        return output;
    }

    public void GoTo(float _desiredIndex)
    {
        do
        {
            var line = NGTutorial.s_masterTutorial[CurrentIndex];
            if (line.m_index == _desiredIndex)
                break;
            NGTutorial.s_masterTutorial[CurrentIndex].State = NGTutorial.TutState.Dormant;
            CurrentIndex++;
        } while (CurrentIndex < NGTutorial.s_masterTutorial.Count);
        GameManager.Me.m_state.m_tutorialTimer = -1;
        NGTutorialManager.Me.ClearAllTriggers();
        NGTutorial.s_masterTutorial[CurrentIndex].State = NGTutorial.TutState.CheckTrigger;
    }

    private void ResetTutorialStep(int _index)
    {
        NGTutorial.s_masterTutorial[_index].State = NGTutorial.TutState.CheckTrigger;
        if(NGTutorial.s_masterTutorial[_index].m_conditionalLine > 0f)
        {
            int i = _index;
            do
            {
                if (NGTutorial.s_masterTutorial[i].m_index == NGTutorial.s_masterTutorial[_index].m_conditionalLine)
                {
                    NGTutorial.s_masterTutorial[i].State = NGTutorial.TutState.CheckTrigger;
                    break;
                }
                i++;
            } while (i < NGTutorial.s_masterTutorial.Count);
        }
    }

    private void ResetAllDialogTutorials()
    {
        foreach (var d in NGTutorial.s_dialogDict)
            ResetDialogTutorial(d.Key);
    }

    public void ResetDialogTutorial(string _section)
    {
        if (NGTutorial.s_dialogDict.ContainsKey(_section))
        {
            NGTutorial.s_dialogDict[_section].m_state = NGTutorial.DictState.DictStates.WaitingToActivate;
            NGTutorial.s_dialogDict[_section].m_currentIndex = 0;
            //Debug.LogError("Reset Tutorial " + _section + " to WaitingToActivate");

            foreach (var l in NGTutorial.s_dialogDict[_section].m_lines)
            {
                l.m_state = NGTutorial.TutState.CheckTrigger;
            }
        }
    }

    public void EndDialogTutorial(string _section)
    {
        ClearBanners();

        if (NGTutorial.s_dialogDict.ContainsKey(_section))
        {
            //Debug.LogError("EndDialogTutorial " + _section + " to Dormant");
            NGTutorial.s_dialogDict[_section].m_state = NGTutorial.DictState.DictStates.Dormant;
            NGTutorial.s_dialogDict[_section].m_currentIndex = NGTutorial.s_dialogDict[_section].m_lines.Count-1;
            foreach (var l in NGTutorial.s_dialogDict[_section].m_lines)
            {
                l.m_state = NGTutorial.TutState.Dormant;
            }
        }
    }

    public void RestartDialogTutorial(string _section)
    {
        if (NGTutorial.s_dialogDict.ContainsKey(_section))
        {
            NGTutorial.s_dialogDict[_section].m_state = NGTutorial.DictState.DictStates.Activated;
            NGTutorial.s_dialogDict[_section].m_currentIndex = 0;
            //Debug.LogError("Restart Tutorial " + _section + " to Activated");
            foreach (var l in NGTutorial.s_dialogDict[_section].m_lines)
            {
                l.m_state = NGTutorial.TutState.CheckTrigger;
            }
        }
    }

    public bool HasAwardCeremonyResultsEnded()
    {
        NGTutorial.DictState state;
        if(!NGTutorial.s_dialogDict.TryGetValue(c_AwardCeremonyResults, out state)) return true;
        return state.m_state == NGTutorial.DictState.DictStates.Dormant || state.m_currentIndex >= state.m_lines.Count;
    }
    
    private void CheckDialogTutorials()
    {
        bool inDialog = false;

        foreach (var d in NGTutorial.s_dialogDict)
        {
            if (d.Value.m_state != NGTutorial.DictState.DictStates.WaitingToActivate && d.Value.m_hasRepeat == false)
            {
                if (d.Value.m_currentIndex < d.Value.m_lines.Count)
                {
                    inDialog = true;
                    switch (d.Key)
                    {
                        case "AwardCeremony":
                            ResetDialogTutorial(d.Key);
                            RestartDialogTutorial("AwardsUnlockCard");

                            m_awardCeremonySectionComplete = false;
                            m_awardUnlockCardSectionComplete = false;
                            break;
                        case c_AwardCeremonyResults:
                            if (d.Value.m_currentIndex < 3)
                            {
                                ResetDialogTutorial(d.Key);
                                ResetDialogTutorial("AwardCeremony");
                                RestartDialogTutorial("AwardsUnlockCard");

                                m_awardCeremonySectionComplete = false;
                                m_awardUnlockCardSectionComplete = false;
                            }
                            else
                            {
                                EndDialogTutorial(d.Key);
                            }
                            break;
                        case "BuildHouse":
                            EndDialogTutorial(d.Key);
                            break;
                        case "ChallengeReminder":
                            EndDialogTutorial(d.Key);
                            break;
                        case "DesignCompetition":
                            EndDialogTutorial(d.Key);
                            break;
                        case "BuildResearchLabAddWorker":
                            RestartDialogTutorial(d.Key);
                            break;
                        case "AwardsUnlockCard":
                            RestartDialogTutorial(d.Key);

                            m_awardCeremonySectionComplete = false;
                            m_awardUnlockCardSectionComplete = false;
                            break;
                        default:
                            RestartDialogTutorial(d.Key);
                            break;
                    }
                }
                else
                {
                    switch (d.Key)
                    {
                        case "AwardCeremony":
                            m_awardCeremonySectionComplete = true;
                            break;
                        case "AwardsUnlockCard":
                            m_awardUnlockCardSectionComplete = true;
                            break;
                    }
                }
            }
        }

        if (!inDialog)
        {
#if OldBusinessFlow
            if (!m_awardCeremonySectionComplete || !m_awardUnlockCardSectionComplete)
            {
                var bflow = NGBusinessFlow.CurrentFlow;
                if (bflow.Decision != null && bflow.Decision.m_name == "CompleteAwardsEvent" && bflow.Decision.IsTriggered == false)
                {
                    ResetDialogTutorial(c_AwardCeremonyResults);
                    ResetDialogTutorial("AwardCeremony");
                    RestartDialogTutorial("AwardsUnlockCard");
                    m_awardCeremonySectionComplete = false;
                    m_awardUnlockCardSectionComplete = false;
                }
            }
#endif
        }
    }

    public bool IsDialogActivelyPlaying(string _name)
    {
        foreach (var d in NGTutorial.s_dialogDict)
        {
            if (d.Key == _name && d.Value.m_state != NGTutorial.DictState.DictStates.WaitingToActivate)
            {
                if (d.Value.m_currentIndex == 0 && d.Value.m_lines[d.Value.m_currentIndex].m_state == NGTutorial.TutState.CheckTrigger)
                {
                    return false;
                }
                if (d.Value.m_currentIndex < d.Value.m_lines.Count && d.Value.m_lines[d.Value.m_currentIndex].m_state != NGTutorial.TutState.Dormant)
                {
                    return true;
                }
            }
        }

        return false;
    }

    public bool IsDialogPlaying(string _name)
    {
        foreach (var d in NGTutorial.s_dialogDict)
        {
            if (d.Key == _name && d.Value.m_state != NGTutorial.DictState.DictStates.WaitingToActivate)
            {
                if (d.Value.m_currentIndex < d.Value.m_lines.Count && d.Value.m_lines[d.Value.m_currentIndex].m_state != NGTutorial.TutState.Dormant)
                {
                    return true;
                }
            }
        }

        return false;
    }

    public bool IsDialogPlaying()
    {
        bool inDialog = false;

        foreach (var d in NGTutorial.s_dialogDict)
        {
            if (d.Value.m_state != NGTutorial.DictState.DictStates.Dormant && d.Value.m_state != NGTutorial.DictState.DictStates.WaitingToActivate && d.Value.m_hasRepeat == false)
            {
                if (d.Value.m_currentIndex < d.Value.m_lines.Count)
                {
                    inDialog = true;
                }
            }
        }

        return inDialog;
    }

    private void CheckUnloadBoardroom()
    {
        if (TutorialPhaseHasBeenPlayed("EndBoardroom"))
            OnBoardingManager.Me.CloseVCOffice();
    }
    public void SetSavedSceneLocation()
    {
        if (GameManager.IsVisitingInProgress) return;

        m_finishedTrigger = false;
        if (IsAtEnd)
        {
            AudioClipManager.Me.SetSoundSwitchVO("VO_Nickname", NGPlayer.Me.PlayerNickname);

            m_finishedTrigger = true;
            ReadyToMake = true;
            DispatchTrigger = true;
            m_isPastDesignTableTutorial = true;
            TownInteractions = true;
            MineInteractions = true;
            SmelterInteractions = true;
            FactoryInteractions = true;
            //ClearRestrictedPickupBuildings(typeof(NGMine));
            //ClearRestrictedPickupBuildings(typeof(NGClayMine));
            //ClearRestrictedPickupBuildings(typeof(NGSmelter));
            //ClearRestrictedPickupBuildings(typeof(NGBikeFactory));

            if (NGBusinessDecisionManager.Me?.m_flowStarted == false)
                NGBusinessDecisionManager.Me?.StartFlow();

            CheckDialogTutorials();
            CheckUnloadBoardroom();
            return;
        }

        bool inTown = false;
        bool onTable = false;
        DispatchTrigger = false;

        // GL - force VO switch before first VO plays to avoid intermittent switch failure
        AudioClipManager.Me.SetSoundSwitchVO("VO_Product", "Cakes");
        
        for (int i = 0; i < CurrentIndex; i++)
        {
            var line = NGTutorial.s_masterTutorial[i];
            if (line.m_triggerCommands.Contains("DeactivateTutorialObjective"))
                inTown = true;
            if (line.m_triggerCommands.Contains("LoadVCOffice"))
                inTown = false;
            if (line.m_triggerCommands.Contains("CloseVCOffice"))
                inTown = true;
            if (line.m_triggerCommands.Contains("LoadDesignTable"))
                onTable = true;
            if (line.m_triggerCommands.Contains("SendDispatch"))
                DispatchTrigger = true;
            if (line.m_triggerCommands.Contains("KillBalloons"))
                AudioClipManager.Me.SetSoundSwitchVO("VO_Nickname", NGPlayer.Me.PlayerNickname);

            if (line.m_triggerCommands.Contains("LockTownInteractions"))
            {
                NGTutorialManager.Me.TownInteractions = false;
                NGTutorialManager.Me.MineInteractions = false;
                NGTutorialManager.Me.SmelterInteractions = false;
                NGTutorialManager.Me.FactoryInteractions = false;
                onTable = false;
            }
            if (line.m_triggerCommands.Contains("UnlockTownInteractions"))
            {
                NGTutorialManager.Me.TownInteractions = true;
                NGTutorialManager.Me.MineInteractions = true;
                NGTutorialManager.Me.SmelterInteractions = true;
                NGTutorialManager.Me.FactoryInteractions = true;
                ReadyToMake = true;
                onTable = false;
            }
            if (line.m_triggerCommands.Contains("UnlockMineInteractions"))
            {
                NGTutorialManager.Me.MineInteractions = true;
                onTable = false;

                // set VO_Resource on gamemanager for tutorial sounds
                NGProductInfo info = NGProductInfo.GetInfo(NGProductInfo.OwnedProductLines[0]);
                string mineType = info.PrimaryMaterial.ToString();
                if (mineType == "CLOTH") mineType = "COTTON";
                AudioClipManager.Me.SetSoundSwitchVO("VO_Resource", mineType);
            }
            if (line.m_triggerCommands.Contains("UnlockSmelterInteractions"))
            {
                NGTutorialManager.Me.SmelterInteractions = true;
                onTable = false;
            }
            if (line.m_triggerCommands.Contains("UnlockFactoryInteractions"))
            {
                NGTutorialManager.Me.FactoryInteractions = true;
                onTable = false;
            }
            if (line.m_triggerCommands.Contains("LoadProductLine"))
            {
                if (NGProductInfo.OwnedProductLines.Count > 0)
                {
                    NGProductInfo info = NGProductInfo.GetInfo(NGProductInfo.OwnedProductLines[0]);
                    m_productIdealPartsPerDesign = info.m_idealPartsPerDesign;
                    AudioClipManager.Me.SetSoundSwitchVO("VO_Product", info.Name);
                    NGTutorialManager.Me.IdealNumberParts = info.m_idealPartsPerDesign;
                    //NGFactory factory = NGManager.Me.m_NGCommanderList.Find(rC => rC is NGBikeFactory) as NGFactory;
//                    NGTutorialManager.Me.MaxNumberParts = (int)factory.MaxInputs;
                }
                else
                {
                    CurrentIndex = i;
                    ResetTutorialStep(CurrentIndex);
                    break;
                }
            }
        }

        if (inTown)
        {
            IsInBoardroom = false;
            if (onTable)
            {
                do
                {
                    var line = NGTutorial.s_masterTutorial[CurrentIndex];
                    if (line.m_triggerCommands.Contains("LoadDesignTable"))
                        break;
                    ResetTutorialStep(CurrentIndex);
                    CurrentIndex--;
                } while (CurrentIndex > 0);
                ResetTutorialStep(CurrentIndex);
                ClearAllTriggers();
            }
            else
            {
                m_isPastDesignTableTutorial = true;
                var line = NGTutorial.s_masterTutorial[CurrentIndex];

                if (line.m_trigger.Contains("WaitForExternal") && line.m_index < 500)
                {
                    CurrentIndex--;
                    do
                    {
                        line = NGTutorial.s_masterTutorial[CurrentIndex];
                        if (line.m_triggerCommands.Contains("Load"))
                            break;
                        ResetTutorialStep(CurrentIndex);
                        CurrentIndex--;
                    } while (CurrentIndex > 0);
                    ResetTutorialStep(CurrentIndex);
                    ClearAllTriggers();
                }
            }
        }
        else
        {
            //TownActive(false);
            //OnBoardingManager.Me.LoadVCOffice();
            //IsInBoardroom = true;
            ResetMasterTutorial();
            ClearAllTriggers();
            CheckUnloadBoardroom();
            return;
        }

        do
        {
            var line = NGTutorial.s_masterTutorial[CurrentIndex];

            if (line.m_section != "")
            {
                if(CurrentIndex > 0)
                {
                    if(NGTutorial.s_masterTutorial[CurrentIndex-1].m_section != line.m_section)
                    {
                        break;
                    }
                }
                else
                {
                    break;
                }
            }
                
            ResetTutorialStep(CurrentIndex);
            CurrentIndex--;
        } while (CurrentIndex > 0);

        ResetTutorialStep(CurrentIndex);
        ClearAllTriggers();
        CheckUnloadBoardroom();
    }

    private void Start()
    {
        m_loaded = false;
        GameManager.Me.m_state.m_tutorialTimer = -1;
        GameManager.Me.m_state.m_tutorialConditionalTimer = -1;
    }

    void Update()
    {
        if (GameManager.Me.LoadComplete == false || m_disabled || GameManager.IsVisitingInProgress) return;
        
        if (ForceReset)
        {
            ResetMasterTutorial();
            ResetAllDialogTutorials();
        }
        else if(m_isRestartPending && !string.IsNullOrEmpty(m_restartPendingSection))
        {
            TutorialRestarted(m_restartPendingSection, m_restartPendingBusinessFlow, m_restartPendingGameFlow);
            m_isRestartPending = false;
            m_restartPendingSection = "";
            m_restartPendingBusinessFlow = null;
            m_restartPendingGameFlow = null;
        }

        UpdateMasterTutorial();
        UpdateTriggerTutorial();
        UpdateDialogTutorial();
        UpdateKeyboardTriggers();
        DebugShowActiveTutorials();
    }
    void DebugShowActiveTutorials()
    {
#if OldBusinessFlow
        if (m_debugSelectedTutorial.Equals(NGBalance.NoneEntry) == false)
        {
            var section = m_debugSelectedTutorial.Split(':');
            if (NGTutorial.s_dialogDict.ContainsKey(section[0]))
            {
                var line = NGTutorial.s_dialogDict[section[0]];
                switch (line.m_state)
                {
                    case NGTutorial.DictState.DictStates.Dormant:
                    case NGTutorial.DictState.DictStates.WaitingToActivate:
                        line.Restart(null, null);
                        break;
                    case NGTutorial.DictState.DictStates.Activated:
                        line.m_state = NGTutorial.DictState.DictStates.Dormant;
                        break;
                }
            }
        }
        m_debugActiveTutorials.Clear();
        m_debugActiveTutorials.Add(NGBalance.NoneEntry);
        m_debugSelectedTutorial = NGBalance.NoneEntry;
        m_debugTutorialDisplay = "";
        if (NGBusinessDecisionManager.Me && NGBusinessDecisionManager.Me.m_flowStarted == false)
        {
            m_debugTutorialDisplay += "NGBusinessDecisionManager.FlowStarted = false\n";
        }
        else
        {
            foreach (var gf in MAGameFlow.m_activeFlows)
            {
                m_debugTutorialDisplay += $"GameFlow: {gf.m_blockName}[{gf.m_blockIndex}] is {gf.State.ToString()}";
                switch (gf.State)
                {
                    case MAGameFlow.GameFlowState.WaitingForDecision:
                        if(gf.m_decisions != null && gf.m_decisions.Count > 0) 
                            foreach(var d in gf.m_decisions)
                                m_debugTutorialDisplay+= $"Decision: {d.m_name} {d.m_initialValue} {d.m_targetValue} {d.GetValue()} ";
                        m_debugTutorialDisplay += "\n";
                    break;
                    default:
                        m_debugTutorialDisplay += "\n";
                        break;
                }
            }

            m_debugTutorialDisplay += "NGBusinessDecisionManager.FlowStarted = true\n";
        }

        if (CurrentIndex > 0 && CurrentIndex < NGTutorial.s_masterTutorial.Count)
        {
            m_debugTutorialDisplay+= $"MASTER: index={NGTutorial.s_masterTutorial[CurrentIndex].m_index:F1} \n";
            
        }
        else
            m_debugTutorialDisplay+= $"MASTER: At End\n";
        foreach (var dd in NGTutorial.s_dialogDict)
        {
            var tutorial = dd.Value;
            var text = "";
            bool gotText = false;
            switch (tutorial.m_state)
            {
                case NGTutorial.DictState.DictStates.Activated:
                    text= $"{dd.Key}:[{tutorial.m_currentIndex}] index={(tutorial.m_currentIndex < tutorial.m_lines.Count ? tutorial.m_lines[tutorial.m_currentIndex].m_sectionIndex : -1)} ACTIVE";
                    if (m_debugShowActiveTutorials)
                        gotText = true;
                                                
                    break;                        
                case NGTutorial.DictState.DictStates.Dormant:
                    text= $"{dd.Key}:[{tutorial.m_currentIndex}] Dormant";
                    if (m_debugShowDormantTutorials)
                        gotText = true;
                    break;
                case NGTutorial.DictState.DictStates.WaitingToActivate:
                    text= $"{dd.Key}: inactive";
                    if (m_debugShowInactiveTutorials)
                        gotText = true;
                    break;
            }
            if (text != "")
            {
                if(gotText)
                    m_debugTutorialDisplay += $"{text}\n";
                m_debugActiveTutorials.Add(text);
            }
        }

        foreach (var tt in NGTutorial.s_triggerDict)
        {
            foreach (var line in tt.Value)
            {
                if (line.m_index <= 0f) continue;
                if (line.m_state == NGTutorial.TutState.Dormant && m_debugShowDormantTutorials == false) continue;
                if (line.m_state != NGTutorial.TutState.CheckTrigger || m_debugShowInactiveTutorials)
                {
                    m_debugTutorialDisplay += $"TRIGGER {line.m_section} index={line.m_index} {line.m_state}\n";
                }
            }
        }
#endif
    }

    void UpdateKeyboardTriggers()
    {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
        if (Input.GetKeyUp(KeyCode.Space))
            NGTutorialManager.Me.FireSpaceTrigger();
        if (Input.GetKeyUp(KeyCode.Escape) && (Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift)))
            NGTutorialManager.Me.EndTutorialEarly();
#endif
    }

    private static DebugConsole.Command s_showvic = new DebugConsole.Command("showvic", _s =>
    {
        var line = NGTutorial.s_masterTutorial[47];
        line.State = NGTutorial.TutState.Triggered;
        var state = line.Process();
    });
    void UpdateMasterTutorial()
    {
        if (IsAtEnd) return;
        var line = NGTutorial.s_masterTutorial[CurrentIndex];
        var state = line.Process();
        switch (state)
        {
            case NGTutorial.TutState.CheckTrigger:
                UpdateMasterTutorialConditional(line);
                UpdateConditional(line);
                break;
            case NGTutorial.TutState.Dormant:
                NextMasterTutorialLine();
                break;
            default:
                CheckSubItemTriggers();
                break;
        }
    }
    // ReSharper disable Unity.PerformanceAnalysis
    void UpdateTriggerTutorial()
    {
        foreach (var d in NGTutorial.s_triggerDict)
        {
            foreach (var line in d.Value)
            {
                if (line.Type == NGTutorial.TutorialType.FromFlow) continue; // Dont process FromFlows
                var state = line.Process();
                switch (state)
                {
                    case NGTutorial.TutState.Dormant:
                        CheckDormant(line);
                        break;
                    case NGTutorial.TutState.CheckShownMessage:
 /*                       if (line.m_messageType.Contains("Subtitles"))
                        {
                            if (!line.m_audioID.IsNullOrWhiteSpace())
                            {
                                string clipName = NGTutorialManager.Me.DecodeAudio(line.m_audioID);
                                string clip = "PlaySound_" + clipName;

                                m_dismissMessageCoroutine = StartCoroutine(DismissMessageAfterAudio(clip));
                            }
                            else
                            {
                                m_dismissMessageCoroutine = StartCoroutine(DismissMessageAfterTime(5.0f));
                            }
   
                        }*/

                        break;
                }
            }
        }
    }

    public void TriggerDismissAudio(string _audioID, string _messageType="Subtitles")
    {
        if (_messageType.Contains("Subtitles"))
        {
            if (_audioID.IsNullOrWhiteSpace() == false)
            {
                string clipName = NGTutorialManager.Me.DecodeAudio(_audioID);
                string clip = "PlaySound_" + clipName;

                m_dismissMessageCoroutine = StartCoroutine(DismissMessageAfterAudio(clip));
            }
            else
            {
                m_dismissMessageCoroutine = StartCoroutine(DismissMessageAfterTime(5.0f));
            }
   
        }
    }
    
    void UpdateDialogTutorial()
    {
        foreach (var d in NGTutorial.s_dialogDict)
        {
            d.Value.Update();
        }        
    }
    void CheckDormant(NGTutorial _line)
    {
        switch (_line.Type)
        {
            case    NGTutorial.TutorialType.Repeat:
                if(_line.State ==  NGTutorial.TutState.Dormant)
                    _line.State = NGTutorial.TutState.CheckTrigger;

                break;
            case    NGTutorial.TutorialType.OneShot:
                break;
            default:
                if (_line.m_index >= 10000f)
                    _line.State = NGTutorial.TutState.CheckTrigger;
                break;
        }
    }
        
    public void CancelDismissMessageCoroutine()
    {
        if (m_dismissMessageCoroutine != null)
        {
            StopCoroutine(m_dismissMessageCoroutine);
            m_dismissMessageCoroutine = null;
        }
    }

    private IEnumerator DismissMessageAfterTime(float _time)
    {
        yield return new WaitForSeconds(_time);

        if(NGTutorial.m_showingMessage != null && NGTutorial.m_showingMessage.IsSubtitle)
        {
            NGTutorial.m_showingMessage.DestroyMe();
        }
    }

    private IEnumerator DismissMessageAfterAudio(string _clip)
    {
        yield return new WaitForSeconds(0.5f);

        while(AudioClipManager.Me.IsPlayingVO(_clip))
        {
            yield return null;
        }

        if (NGTutorial.m_showingMessage != null && NGTutorial.m_showingMessage.IsSubtitle)
        {
            NGTutorial.m_showingMessage.DestroyMe();
        }
    }

    void UpdateConditional(NGTutorial _parentLine)
    {
        if (_parentLine.m_conditional.IsNullOrWhiteSpace())
            return;
        var bracketParse = _parentLine.m_conditional.Split('(', ')');
        if (bracketParse.Length != 3)
        {
            Debug.LogError($"index[{_parentLine.m_index}] bad syntax of conditional[{_parentLine.m_conditional}]");
            return;
        }

        switch (bracketParse[0].ToLower())
        { 
            case "master":
                var masterSection = NGTutorial.s_masterTutorial.Find(o =>
                    o.m_section.Equals(bracketParse[1], StringComparison.OrdinalIgnoreCase));
                if (masterSection == null)
                {
                    Debug.LogError($"index[{_parentLine.m_index}] no such conditinal section in '{bracketParse[0]}' of '{_parentLine.m_conditional}'");
                    return;
                }

                var indexStart = NGTutorial.s_masterTutorial.IndexOf(masterSection);
                for (int i = indexStart; i < NGTutorial.s_masterTutorial.Count && NGTutorial.s_masterTutorial[i].m_section.Equals(bracketParse[1], StringComparison.OrdinalIgnoreCase); i++)
                {
                    NGTutorial.s_masterTutorial[i].Process();
                }
                break;
            case "trigger":
                if (NGTutorial.s_triggerDict.ContainsKey(bracketParse[1]) == false)
                {
                    Debug.LogError($"index[{_parentLine.m_index}] no such conditinal section in '{bracketParse[0]}' of '{_parentLine.m_conditional}'");
                    return;
                }
                var triggerSection = NGTutorial.s_triggerDict[bracketParse[1]];
                foreach (var line in triggerSection)
                {
                    line.Process();
                }
                break;
            case "dialog":
                if (NGTutorial.s_dialogDict.ContainsKey(bracketParse[1]) == false)
                {
                    Debug.LogError($"index[{_parentLine.m_index}] no such conditinal section in '{bracketParse[0]}' of '{_parentLine.m_conditional}'");
                    return;
                }

                var dialogSection = NGTutorial.s_dialogDict[bracketParse[1]];
                dialogSection.UpdateConditional();
                break;
            default:
                Debug.LogError($"index[{_parentLine.m_index}] no such conditinal section of '{bracketParse[0]}'");
                return;
        }
    }
    void UpdateMasterTutorialConditional(NGTutorial _parentLine)
    {
        if (_parentLine.m_conditional.IsNullOrWhiteSpace() == false)
            return;
        var index = _parentLine.m_conditionalLine;
        if (index.IsZero()) return;
        NGTutorial line = null;
        for (index = _parentLine.m_conditionalLine; index > 0f; index = 0)
        {
            line = NGTutorial.s_masterTutorial.Find(o => o.m_index.Equals(index));
            if (line == null)
                break;

            var state = line.Process();
            switch (state)
            {
                case NGTutorial.TutState.CheckTrigger:
                    break;
                case NGTutorial.TutState.Dormant:
                    break;
                default:
                    CheckSubItemTriggers();
                    break;
            }
                        
        }
    }
    void CheckSubItemTriggers()
    {
        if (IsAtEnd) return;
        var currentIndex = (int) NGTutorial.s_masterTutorial[CurrentIndex].m_index;
        for (int i = CurrentIndex + 1; i < NGTutorial.s_masterTutorial.Count; i++)
        {
            var line = NGTutorial.s_masterTutorial[i];
            if ((int) line.m_index != currentIndex) break;
            switch (line.State)
            {
                case NGTutorial.TutState.Dormant:
                    break;
                default:
                    line.Process();
                    break;
            }
        }
    }
    void NextMasterTutorialLine()
    {
        if (IsAtEnd)
        {
            var line = NGTutorial.s_masterTutorial[CurrentIndex];
            if (line.ShowingMessage)
                line.DestroyMessage();
        }

        var currentIntIndex = (int)NGTutorial.s_masterTutorial[CurrentIndex].m_index;
        for (int i = CurrentIndex + 1; i < NGTutorial.s_masterTutorial.Count; i++)
        {
            if ((int)NGTutorial.s_masterTutorial[i].m_index != currentIntIndex)
            {
                CurrentIndex = i;
                GameManager.Me.m_state.m_tutorialTimer = -1;
                ClearClickTrigger();
                //UpdateTutorialObjectivesPercentage(CurrentIndex / NGTutorial.s_masterTutorial.Count);
                return;
            }
        }
        //We must have reached the end
        CurrentIndex = NGTutorial.s_masterTutorial.Count;
    }

    public void ResetMasterTutorial()
    {
        if(IsAtEnd == false)
        {
            var line = NGTutorial.s_masterTutorial[CurrentIndex];
            if (line.State == NGTutorial.TutState.CheckShownMessage && line.ShowingMessage)
                line.DestroyMessage();
        }
        for (int i = 0; i <= CurrentIndex && i < NGTutorial.s_masterTutorial.Count; i++)
            ResetTutorialStep(i);
        ForceReset = false;
        CurrentIndex = 0;
    }

    public void ActivateTutorial(string _name)
    {
        if (NGTutorial.s_triggerDict.TryGetValue(_name, out var line))
        {
            if (line.Count > 0 && line[0].State == NGTutorial.TutState.CheckTrigger)
            {
                line[0].State = NGTutorial.TutState.Triggered;
            }
        };
    }

    public void PostLoad()
    {
        GameManager.Me.m_state.m_tutorialTimer += Time.time;
        m_interactions = JsonUtility.FromJson<TutorialInteractions>(GameManager.Me.m_state.m_interactions);
        if (m_interactions == null)
            m_interactions = new TutorialInteractions();
    }
    public void LatePostLoad()
    {
        NGTutorial.SaveTutorial.DecodeLoad(NGTutorial.s_masterTutorial, GameManager.Me.m_state.m_tutorialMasterStates, !IsAtEnd);
        NGTutorial.SaveTutorial.DecodeLoad(NGTutorial.s_triggerTutorial, GameManager.Me.m_state.m_tutorialTriggerStates);
        NGTutorial.SaveTutorial.DecodeLoad(NGTutorial.s_dialogDict, GameManager.Me.m_state.m_tutorialDialogStates);

        NGTutorial line = NGTutorial.s_masterTutorial.Find(o => o.m_section.Equals("Conditionals"));
    }
    public void PreSave()
    { GameManager.Me.m_state.m_tutorialMasterStates = NGTutorial.SaveTutorial.MakeSave(NGTutorial.s_masterTutorial);
        GameManager.Me.m_state.m_tutorialTriggerStates = NGTutorial.SaveTutorial.MakeSave(NGTutorial.s_triggerTutorial);
        GameManager.Me.m_state.m_tutorialDialogStates = NGTutorial.SaveTutorial.MakeSave(NGTutorial.s_dialogDict);
        GameManager.Me.m_state.m_tutorialTimer -= Time.time;
        GameManager.Me.m_state.m_interactions =JsonUtility.ToJson(m_interactions);
    }
    public void PostSave()
    {
        GameManager.Me.m_state.m_tutorialTimer += Time.time;
    }
}
#endif