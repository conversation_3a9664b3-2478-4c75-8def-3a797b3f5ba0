using System.Collections.Generic;
using DG.Tweening;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;
/*
public class MAOrderCardDoubleSided  : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ointer<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, IEndDragHandler
{
    [SerializeField] private Transform m_root;
    [SerializeField] private RectTransform m_orderCardHolder;
    [SerializeField] private NGOrderTile m_orderTile = null;
    [SerializeField] private RectTransform m_orderCardInfoUIHolder;
    [SerializeField] private MAOrderCardInfoUI m_orderCardInfoUI = null;
    [SerializeField] private Transform m_tweenPositionA = null;
    [SerializeField] private Transform m_tweenPositionB = null;
    [SerializeField] private float m_dragTimeUntilFlip = 0.2f;
    [SerializeField] private float m_dragDistanceUntilFlip = 1f;

    private Vector3 m_posLocalCamSpace = new Vector3(-0.209999993f, 0.0399999991f, 0.519999981f);//Vector3(-1.23550415f,0.0579944253f,3.84244394f);
    private Quaternion m_rotGlobalCamSpace = new Quaternion(0.40f,-0.141f,0.1315f, 0.8917f);
    private float m_scaLocalCamSpace = 0.000999f;
    
    private int m_orderIdDisplayed = -1;
    private float m_dragTime = 0f;
    private Vector2 m_dragDistance = Vector2.zero;
    private bool m_canFlip = true;
    
    private BCFactory m_factory = null;
    private MABuilding m_building = null;
    
    private Transform m_originParent = null;
    private Transform m_cardHolder = null;

    public int m_tweenIndex = -1;
    private List<Transform> m_tweenTargets = new List<Transform>();

    private bool blockInput = false;

    protected virtual BCFactory Factory
    {
        get
        {
            if(m_factory == null)
            {
                DesignTableManager parentTable = DesignTableManager.Me;
                if(parentTable == null) return null;
                if(parentTable.IsInDesignMode && parentTable.IsInProductMode &&
                   parentTable.IsInBuildingMode == false && parentTable.IsInCompetitionMode == false)
                {
                    m_building = DesignTableManager.Me.Factory as MABuilding;
                    if(m_building != null && m_building.IsBeingConstructed) return null;
                    List<BCBase> factoryComps = (DesignTableManager.Me.Factory as MABuilding)
                        ?.ComponentLists<BCFactory>();
                    m_factory = factoryComps.Count > 0 ? factoryComps[0] as BCFactory : null;
                }
            }
            return m_factory;
        }
    }
    
    private void Awake()
    {
        m_cardHolder = transform.parent;
        m_originParent = m_cardHolder.parent;
        
        Transform tr = m_cardHolder;
        if(m_orderCardHolder == null) m_orderCardHolder = tr.GetComponent<RectTransform>();
        if(m_orderCardInfoUIHolder == null) m_orderCardHolder = tr.GetComponent<RectTransform>();
        if(m_tweenPositionA != null || m_tweenPositionB != null)
        {
            m_tweenPositionA.position = tr.position;
            m_tweenPositionA.rotation = tr.rotation;
            m_tweenPositionA.localScale = tr.localScale;

            m_tweenPositionB.position = Vector3.zero;
            m_tweenPositionB.localPosition = m_posLocalCamSpace;
            m_tweenPositionB.localScale = Vector3.one * m_scaLocalCamSpace;
            m_tweenPositionB.rotation = Quaternion.identity;
            m_tweenPositionB.localRotation = m_rotGlobalCamSpace;
            
            m_tweenTargets.Add(m_tweenPositionB);
            m_tweenTargets.Add(m_tweenPositionA);
        }
    }
    
    virtual protected void OnEnable()
    {
        SetupCardInfo();
    }
    
    virtual protected void Start()
    {
        DOTween.Init();
        SetupCardInfo();
    }

    private void OnDisable()
    {
        ClearOrderCard();
    }

    private void OnDestroy()
    {
        ClearOrderCard();
    }

    private float s = 180f;
    private bool firstReady = false;
    virtual protected void Update()
    {
        if(DesignTableManager.Me.IsDesignInPlaceExiting == false)
        {
            if(DesignTableManager.Me.IsInDesignInPlaceActively && firstReady == false)
            {
                firstReady = true;
                Transform originParent = m_tweenTargets[0].parent;
                m_tweenTargets[0].SetParent(originParent, true);
            }
        }
        else
        {
            m_cardHolder.SetParent(m_originParent, true);
            Destroy(gameObject);
            return;
        }

        BCFactory factory = Factory;
        if(factory == null || m_building.IsBeingConstructed) return;
        
        MAOrder order = factory.GetOrder();
        if(order.IsValid == false)// || order.IsComplete)
        {
            ClearOrderCard();
            return;
        }
        
        if(m_orderIdDisplayed != order.OrderId)
        {
            SetupOrderCard();
        }

        // if(Input.GetKeyUp(KeyCode.A))
        // {
        //    
        // }
    }

    private void SetupCardInfo()
    {
        BCFactory factory = Factory;
        if(factory != null)
        {
            SetupOrderCard();
        }
        else
        {
            ClearOrderCard();
        }
    }

    private void SetupOrderCard()
    {
        BCFactory factory = Factory;
        MAOrder order = factory.GetOrder();
        if(order.IsValid)
        {
            if(m_orderTile == null)
            {
                m_orderTile = NGOrderTile.Create(NGBusinessDecisionManager.Me.m_ngOrderTilePrefab.gameObject,
                    order.TemplateBusinessGift, null, null, m_orderCardHolder);
                AspectRatioFitter fitter = m_orderTile.gameObject.GetComponent<AspectRatioFitter>();
                if(fitter == null) fitter = m_orderTile.gameObject.AddComponent<AspectRatioFitter>();
                fitter.aspectMode = AspectRatioFitter.AspectMode.FitInParent;
                fitter.aspectRatio = 0.75f;
            }

            m_orderIdDisplayed = order.OrderId;
            
            m_orderTile.SetupOrderUI(order);
            m_orderTile.LockRoot.SetActive(false);

            if(m_orderCardInfoUI == null)
            {
                m_orderCardInfoUI = MAOrderCardInfoUI.Create(m_orderCardInfoUIHolder, m_orderTile);
                AspectRatioFitter fitter = m_orderTile.gameObject.GetComponent<AspectRatioFitter>();
                if(fitter == null) fitter = m_orderTile.gameObject.AddComponent<AspectRatioFitter>();
                fitter.aspectMode = AspectRatioFitter.AspectMode.FitInParent;
                fitter.aspectRatio = 0.75f;
            }
            else
            {
                m_orderCardInfoUI.LinkOrderTile(m_orderTile);
            }
        }
        else m_orderIdDisplayed = -1;
    }

    private void ClearOrderCard()
    {
        // if(m_orderCardHolder != null && m_orderCardHolder.childCount > 0)
        // {
        //     //NGOrderTile[] orderTiles = m_orderCardHolder.GetComponentsInChildren<NGOrderTile>(true);
        //     //if(orderTiles != null && orderTiles.Length > 0) Array.ForEach(orderTiles, x => x.DestroyMe());
        // }
        // m_orderIdDisplayed = -1;
    }

    private bool m_isFlipped = false;
    public Animator m_animator = null;
    private bool setY180 = true;
    private void Flip(bool _right)
    {
        m_animator.SetBool("Flip180", setY180);
        m_animator.SetBool("Right", _right);
        setY180 = !setY180;
        //m_isFlipped = !m_isFlipped;
//        transform.Rotate(180, 0 , 0, Space.Self);
        // Vector3 t = transform.rotation.eulerAngles;
        // if(m_dragDistance.x > 0)
        // {
        //     t = t. RotateAbout(m_rotateAxis, Mathf.Deg2Rad * 180f);
        //     //t += m_rotateAxis * 180f;
        // }
        // else
        // {
        //     t = t.RotateAbout(m_rotateAxis, Mathf.Deg2Rad * 180f);
        //     //t += m_rotateAxis * 180f;
        // }
        //transform.DOLocalRotate(t, 0.5f).SetEase(Ease.InOutSine);
    }
    
    private void Transform()
    {
        if(m_tweenTargets.Count == 0) return;
        
        if(m_tweenIndex < 0 || m_tweenIndex >= m_tweenTargets.Count) m_tweenIndex = 0;

        if(m_isFlipped)
        {
            transform.Rotate(180, 0 , 0, Space.Self);
            m_isFlipped = false;
        }
        Transform target = m_tweenTargets[m_tweenIndex];
        if(target == null) return;
        
        TransitionStart();

        Transform tr = m_cardHolder;
        if(m_tweenIndex == 0)
        {        
            Vector3 posGlobal = DesignTableManager.Me.m_designCamera.transform.TransformPoint(m_posLocalCamSpace);
            tr.DOMove(posGlobal, 0.5f).SetEase(Ease.InOutSine);
            tr.DORotate(m_rotGlobalCamSpace.eulerAngles, 0.5f).SetEase(Ease.InOutSine);
            tr.DOScale(Vector3.one * m_scaLocalCamSpace, 0.5f).SetEase(Ease.InOutSine);
        }
        else
        {
            tr.DOMove(target.position, 0.5f).SetEase(Ease.InOutSine);
            tr.DORotate(target.rotation.eulerAngles, 0.5f).SetEase(Ease.InOutSine);
            tr.DOScale(target.localScale, 0.5f).SetEase(Ease.InOutSine);
        }
        Invoke(nameof(TransitionEnd), 0.51f);
        m_tweenIndex++;
    }
    
    private void TransitionStart()
    {
        m_cardHolder.SetParent(m_originParent, true);
        blockInput = true;
    }
    
    private void TransitionEnd()
    {
        if(m_tweenIndex < 0 || m_tweenIndex >= m_tweenTargets.Count) m_tweenIndex = 0;
        if(m_tweenIndex > 0)
        {
            m_cardHolder.SetParent(DesignTableManager.Me.m_designCamera.transform, true);
        }
        blockInput = false;
    }

    public void OnPointerClick(PointerEventData eventData)
    {
        if(blockInput) return;
        AudioClipManager.Me.PlayUISound("PlaySound_DesignTableClickOnOrderCard");
        Transform();
    }
    
    public void OnBeginDrag(PointerEventData _eventData)
    {
        OnDrag(_eventData);
    }
    
    public void OnDrag(PointerEventData _eventData)
    {
        if(blockInput) return;
        
        if(m_canFlip == false)
            return;
        
        m_dragTime += Time.deltaTime;
        
        if(m_dragDistance.x.Sign() != _eventData.delta.x.Sign())
            m_dragDistance = Vector2.zero;
        
        m_dragDistance.x += _eventData.delta.x;
        m_dragDistance.y += _eventData.delta.y;
        
        
        if(m_dragTime > m_dragTimeUntilFlip && Mathf.Abs(m_dragDistance.x) > m_dragDistanceUntilFlip)
        {
            Flip(m_dragDistance.x > 0);
            m_canFlip = false;
            m_dragTime = 0f;
            m_dragDistance = Vector2.zero;
        }
    }

    public void OnEndDrag(PointerEventData _eventData)
    {
        m_canFlip = true;
        m_dragTime = 0f;
        m_dragDistance = Vector2.zero;
    }
}
*/