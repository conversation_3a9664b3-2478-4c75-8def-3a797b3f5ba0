using System.Collections;
using UnityEngine;
using Image = UnityEngine.UI.Image;
using TMPro;
using DG.Tweening;
using System;
using UnityEngine.UI;

public class MAReputationChangeDialogue : MAGUIBase
{
    public Transform m_statsLineHolder;
    public Image m_giverIcon;
    public TMP_Text m_orderFrom;
    public TMP_Text m_title;
    public TMP_Text m_closeButtonText;
    int m_previousLevel;
    int m_newLevel;
    float m_previousProgress;
    float m_newProgress;
    Action m_onClose;
    
    public Button m_closeButton;
    public GameObject m_starHolder;
    public MAOrderGiver m_giver;
    public Transform m_reputationBar;
    public Image m_reputationProgressBar;
    public TMP_Text m_reputationProgressText;
    
    public AkEventHolder m_progressBarTick;
    public AkEventHolder m_reputationIncrease;
    public AkEventHolder m_reputationDecrease;
    
    public void Activate(int _previousLevel, int _newLevel, float _previousProgress, float _newProgress, MAOrderGiver _giver, Action _onClose)
    {
        base.Activate();
        
		m_onClose = _onClose;
		m_previousLevel = _previousLevel;
		m_newLevel = _newLevel;
		m_previousProgress = _previousProgress;
		m_newProgress = _newProgress;
        m_giver = _giver;
        
        if(_giver == null)
        {
            Close();
            return;
        }
        
        RefreshView();
    }
    
    private MADesignInfoSheetLine[] m_starLines = new MADesignInfoSheetLine[5];
    private MADesignInfoSheetLine m_reputation;
    private MADesignInfoSheetLine m_nextLevelLine;
    
    private const float c_lockedAlpha = 0.5f;
    private void RefreshView()
    {
        m_statsLineHolder.DestroyChildren();
        
        if(m_newLevel == m_previousLevel)
            m_title.text = m_newProgress > m_previousProgress ? "Reputation Increase" : "Reputation Decrease";
        else
            m_title.text = m_newLevel > m_previousLevel ? "Reputation Increase" : "Reputation Decrease"; 
            
        m_orderFrom.text = $"<b>From</b> {m_giver.m_displayName}\n{m_giver.m_description}";
        
        m_giverIcon.sprite = m_giver.PortraitSprite;
        
        MADesignInfoSheetLine.Create(m_statsLineHolder, $"<b>Faction</b>", m_giver.Faction.m_title, false);
        
        m_nextLevelLine = MADesignInfoSheetLine.Create(m_statsLineHolder, $"<b>Level Progress</b>", "", false);
        m_reputation = MADesignInfoSheetLine.Create(m_statsLineHolder, $"<b>Reputation</b>", m_giver.GetReputationStars(m_previousLevel), false);
        
        m_nextLevelLine.m_count.gameObject.SetActive(false);
        m_reputationBar.transform.parent = m_nextLevelLine.m_count.transform.parent;
        m_reputationBar.transform.localPosition = Vector3.zero;
        m_reputationProgressBar.fillAmount = m_previousProgress;
        m_reputationProgressText.text = m_previousProgress.ToString("P0");
        
        m_starLines[0] = MADesignInfoSheetLine.Create(m_statsLineHolder, "   " + MAOrderBoardUI.GetStars(1), m_giver.Reputation1Bonus.GetRewardDescription(), false);
        m_starLines[1] = MADesignInfoSheetLine.Create(m_statsLineHolder, "   " + MAOrderBoardUI.GetStars(2), m_giver.Reputation2Bonus.GetRewardDescription(), false);
        m_starLines[2] = MADesignInfoSheetLine.Create(m_statsLineHolder, "   " + MAOrderBoardUI.GetStars(3), m_giver.Reputation3Bonus.GetRewardDescription(), false);
        m_starLines[3] = MADesignInfoSheetLine.Create(m_statsLineHolder, "   " + MAOrderBoardUI.GetStars(4), m_giver.Reputation4Bonus.GetRewardDescription(), false);
        m_starLines[4] = MADesignInfoSheetLine.Create(m_statsLineHolder, "   " + MAOrderBoardUI.GetStars(5), m_giver.Reputation5Bonus.GetRewardDescription(), false);
		
        for(int i = 1; i <= m_starLines.Length; i++)
        {
            if(m_previousLevel < i) m_starLines[i-1].gameObject.AddComponent<CanvasGroup>().alpha = c_lockedAlpha;
        }
        
        m_closeButtonText.text = "Skip";
        m_sequence = StartCoroutine(DoSequence());
    }
    
    private Coroutine m_sequence; 
    private static GameObject s_movingStar;
     
    private GameObject CreateMovingStar(Vector3 _pos)
    {
        var obj = new GameObject("StarImage");
        obj.transform.SetParent(m_starHolder.transform);
        var txt = obj.AddComponent<TextMeshProUGUI>();
        txt.text = MAOrderBoardUI.GetStars(1);//Mathf.Abs(m_newLevel - m_previousLevel));
        txt.fontSize = 26;
        
        var csf = obj.AddComponent<ContentSizeFitter>();
        csf.horizontalFit = ContentSizeFitter.FitMode.PreferredSize;
        csf.verticalFit = ContentSizeFitter.FitMode.PreferredSize;
        
        var rt = obj.transform as RectTransform;
        //rt.pivot = new Vector2(0, 0.5f);
        
        obj.transform.position = _pos;
        
        s_movingStar = obj;
        return obj; 
    }
    
    Vector2 CalculateTextPosition(TMP_Text _text)
    {
        TMP_TextInfo _textInfo = _text.textInfo;
        TMP_CharacterInfo currentCharInfo = _textInfo.characterInfo[0];
        Vector3 localMid = (currentCharInfo.bottomLeft + currentCharInfo.topRight) / 2;
        return _text.transform.TransformPoint(localMid);
    }
    
    private float m_progressBarChangedAmount = 0;
    private const float m_progressBarSegments = 1/100f;
    private IEnumerator UpdateProgressSequence(float _targetProgress)
    {
        m_progressBarChangedAmount = 0f;
        if(Mathf.Abs(m_previousProgress-_targetProgress) > 0.0001f)
        {
            DoPop(m_nextLevelLine.transform);
            yield return new WaitForSeconds(1f);
        }
        
        while(Mathf.Abs(m_previousProgress-_targetProgress) > 0.0001f)
        {
            var nextProgress = Mathf.Lerp(m_previousProgress, _targetProgress, 2f * Time.deltaTime);
            m_progressBarChangedAmount += Mathf.Abs(m_previousProgress - nextProgress);
            SetLevelProgress(nextProgress);
            
            if(m_progressBarChangedAmount >= m_progressBarSegments)
            {
                m_progressBarTick.Play(gameObject);
                m_progressBarChangedAmount = 0f;
            }
            yield return null;
        }
    }
    
    private IEnumerator DoLevelChangeSequence()
    {
        // Star fly down
        int direction = Mathf.Clamp(m_newLevel - m_previousLevel, -1,1);
        
        if(direction == 0)
        {
            yield return UpdateProgressSequence(m_newProgress);
            yield break;
        }
        
        int nextLevel = m_previousLevel + direction;
        bool increase = (nextLevel > m_previousLevel);
        
        yield return UpdateProgressSequence(increase ? 1 : 0);
        
        var reputationPos = CalculateTextPosition(m_reputation.m_count);
        
        var startStartPos = increase ? new Vector2(m_giverIcon.transform.position.x, m_giverIcon.transform.position.y) : reputationPos;
        var startEndPos = increase ? reputationPos : new Vector2(m_giverIcon.transform.position.x, m_giverIcon.transform.position.y);
        var star = CreateMovingStar(startStartPos);
        
        SetLevelProgress(increase ? 0 : 1);
        
        if(increase == false)
        {
            m_reputation.m_count.text = m_giver.GetReputationStars(nextLevel);
            m_reputationDecrease.Play(gameObject);
        }
        MoveToDestination(star.transform, startEndPos, 1f);
        yield return new WaitForSeconds(1f);
        
        Destroy(star);
        
        // Update stars
        if(increase)
        {
            m_reputation.m_count.text = m_giver.GetReputationStars(nextLevel);
            m_reputationIncrease.Play(gameObject);
        }
        
        DoPop(m_reputation.transform);
        
        yield return new WaitForSeconds(1.5f);
        
        for(int i = 1; i <= m_starLines.Length; i++)
        {
            if(i > m_previousLevel && i <= nextLevel)
            {
                DoPop(m_starLines[i - 1].transform);
                yield return new WaitForSeconds(0.5f);
                
                var cg = m_starLines[i-1].GetComponent<CanvasGroup>();
                Destroy(cg);
                
                yield return new WaitForSeconds(0.5f);
            }
            else if(i > nextLevel)
            {
                var cg = m_starLines[i-1].GetComponent<CanvasGroup>();
                if(cg == null)
                {
                    DoPop(m_starLines[i - 1].transform);
                    
                    yield return new WaitForSeconds(0.5f);
                    
                    cg = m_starLines[i-1].gameObject.AddComponent<CanvasGroup>();
                    cg.alpha = c_lockedAlpha;
                    
                    yield return new WaitForSeconds(0.5f);
                }
            }
        }
        m_previousLevel = nextLevel;
        
        if(m_previousLevel == m_newLevel)
            yield return UpdateProgressSequence(m_newProgress);
    }

    private void SetLevelProgress(float _progress)
    {
        m_previousProgress = _progress;
        m_reputationProgressBar.fillAmount = m_previousProgress;
        m_reputationProgressText.text = m_previousProgress.ToString("P0");
    }

    IEnumerator DoSequence() 
    {
        yield return new WaitForSeconds(1f);
        
        yield return DoLevelChangeSequence();
        
        while(m_previousLevel != m_newLevel)
        {
            yield return DoLevelChangeSequence();
        }

        yield return new WaitForSeconds(0.5f);
        m_closeButtonText.text = "Close";
        DoPop(m_closeButton.transform);
        m_sequence = null;
    }
    
    public void MoveToDestination(Transform _target, Vector3 _destination, float _duration)
    {
        _target.DOMove(_destination, _duration);
    }
    
    public static void DoPop(Transform _target, float _scaleFactor = 1.15f, float _duration = 0.8f)
    {
        _target.DOScale(Vector3.one * _scaleFactor, _duration)
            .SetEase(Ease.OutBack)
            .SetLoops(2, LoopType.Yoyo); // scale up then back
    }
    
    public static MAReputationChangeDialogue s_instance;
    
    public static void Create(int _previousLevel, int _newLevel, float _previousProgress, float _newProgress, MAOrderGiver _giver, Action _onClose)
    {      
        AudioClipManager.Me.PlaySound("PlaySound_OrdersBoard_Open", GameManager.Me.gameObject);
        if(s_instance == null)
        {
            var prefab = Resources.Load<MAReputationChangeDialogue>("React/Orders/MAReputationChangeDialogue");
            s_instance = Instantiate(prefab, NGManager.Me.m_orderBoardUI);
        }
        else if(s_instance.gameObject.activeSelf)
        {
            return;
        }

        s_instance.gameObject.SetActive(true);
        s_instance.Activate(_previousLevel, _newLevel, _previousProgress, _newProgress, _giver, _onClose);
    }
    
    private void Close()
    {        
        gameObject.SetActive(false);
        m_onClose?.Invoke();
    }
    
    private void OnSkip()
    {
        SetLevelProgress(m_newProgress);
        
        m_reputation.m_count.text = m_giver.GetReputationStars(m_newLevel);
        
        if(s_movingStar != null)
        {
            Destroy(s_movingStar);
            s_movingStar = null;
        }
        
        for(int i = 1; i <= m_starLines.Length; i++)
        {
            var cg = m_starLines[i-1].GetComponent<CanvasGroup>();
            if(i > m_newLevel)
            {
                if(cg == null)
                {
                    cg = m_starLines[i-1].gameObject.AddComponent<CanvasGroup>();
                    cg.alpha = c_lockedAlpha;
                }
            }
            else
            {
                Destroy(cg);
            }
        }
    }
    
    public void OnCloseButtonClicked()
    {
        if(m_sequence != null)
        {
            OnSkip();
            StopCoroutine(m_sequence);
            m_sequence = null;
            m_closeButtonText.text = "Close";
            return;
        }
        m_giver.TryGiveOneOffRewards();
        AudioClipManager.Me.PlaySound("PlaySound_OrdersBoardClose", GameManager.Me.gameObject);
        Close();
    }
}
