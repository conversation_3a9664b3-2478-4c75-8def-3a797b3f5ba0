
using System;
using System.Collections.Generic;
[System.Serializable]
public class MARewardOrderInfo
{
    public static List<MARewardOrderInfo> s_rewardOrderInfo = new();
    public static List<MARewardOrderInfo> GetList=>s_rewardOrderInfo;
    public string DebugDisplayName => m_name;

    public string id;
    public bool m_debugChanged;
    public string m_indexer;
    public string m_name;
    public string m_blockName;
    public string m_faction;
    [ScanField] public string m_currencyReward; //<- to pass into NGCarriableResource.GetInfo, need ScanField
    public float m_currencyRewardValue;
    [ScanField] public string m_giftReward;
    public float m_percentChance;

    public NGCarriableResource CurrencyReward;
    
    public NGCarriableResource RewardType => NGCarriableResource.GetInfo(m_currencyReward);

    private NGBusinessGift m_giftRewardInstance = null;

    public NGBusinessGift GiftReward
    {
        get
        {
            if(m_giftRewardInstance == null && m_giftReward.IsNullOrWhiteSpace() == false)
            {
                m_giftRewardInstance = NGBusinessGift.GetInfo(m_giftReward);
            }
            return m_giftRewardInstance;
        }
    }

    public static bool PostImport(MARewardOrderInfo _what)
    {
        if (_what.m_currencyReward.IsNullOrWhiteSpace() == false)
        {
            _what.CurrencyReward = NGCarriableResource.GetInfo(_what.m_currencyReward);
        }
        if(_what.m_giftReward.IsNullOrWhiteSpace() == false)
        {
            _what.m_giftRewardInstance = NGBusinessGift.GetInfo(_what.m_giftReward);
        }
        return true;
    }

    public static List<MARewardOrderInfo> LoadInfo()
    {
        s_rewardOrderInfo = NGKnack.ImportKnackInto<MARewardOrderInfo>(PostImport); 
        return s_rewardOrderInfo;
    }

    public static MARewardOrderInfo GetInfo(string _name)
    {
        return s_rewardOrderInfo.Find(x => x.m_name.Equals(_name, StringComparison.OrdinalIgnoreCase));
    }

    public string RewardText => $"name: '{m_name}' - value: '{m_currencyRewardValue}' - currency: '{m_currencyReward}' - gift: '{m_giftReward}'";
}
