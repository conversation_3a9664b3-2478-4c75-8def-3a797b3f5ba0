using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Random = System.Random;
/*
public class MAOrderCardInfoUI : MonoBehaviour
{
    [SerializeField] private Image m_factionIcon = null;
    [SerializeField] private TextMeshProUGUI m_factionName = null;
    [SerializeField] private TextMeshProUGUI m_productName = null;
    [SerializeField] private TextMeshProUGUI m_productQualityText = null;
    [SerializeField] private Image m_productQualityIcon = null;
    [SerializeField] private TextMeshProUGUI m_productQuantity = null;
    [SerializeField] private TextMeshProUGUI m_orderName = null;
    //[SerializeField] private TextMeshProUGUI m_rewardsTitle = null;
    [SerializeField] private Image m_orderBorder = null;
    [SerializeField] private Image m_productSprite = null;
    [SerializeField] private Image m_partialDesignProvided = null;

    public List<BonusEntry> m_bonusEntries = new List<BonusEntry>();
    
    [Serializable]
    public class BonusEntry
    {
        public Transform m_container;
        public Image m_qualityIcon;
        public TextMeshProUGUI m_description;
        public List<GameObject> m_rewardsDividers = new List<GameObject>();
        [NonSerialized] public List<RewardElementUI> m_rewardElements = new List<RewardElementUI>();
    }
    
    public List<GameObject> m_rewardsDividers = new List<GameObject>();
    
    public List<RewardElementUI> m_rewardElements = new List<RewardElementUI>();
    
    private NGOrderTile m_orderTile = null;
    //dont forget partial design 'parts included'
    
    private void OnEnable()
    {
        if(m_orderTile == null)
        {
            NGOrderTile orderTile = GetComponentInParent<NGOrderTile>(true);
            if(orderTile != null) LinkOrderTile(orderTile);
        }

        if(m_orderTile != null)
        {
            //NGOrderTile.SetupRewardsInfoUI(m_orderTile.Order.Rewards);
        }
    }

    public void LinkOrderTile(NGOrderTile _orderTile)
    {
        m_orderTile = _orderTile;
        
        MAOrder order = _orderTile.Order;
        
        m_factionIcon.sprite = _orderTile.m_factionIcon.sprite;
        if(order.OrderInfo.m_faction.IsNullOrWhiteSpace() == false)
        {
            m_factionName.text = order.OrderInfo.Faction.m_factionName;
        }
        m_productName.text = order.ProductDisplayName; 
        
        //m_productQualityText.text = _orderTile.m_orderQualityText.text;
        //m_productQualityIcon.sprite = _orderTile.m_orderQualityIcon.sprite;
    
        m_productQuantity.text = order.m_orderQuantity.ToString();
        m_orderName.text = order.OrderInfo.m_name;
        
        m_orderBorder.sprite = _orderTile.m_orderBorder.sprite;
        m_orderBorder.gameObject.SetActive(m_orderBorder.sprite != null);
        
        m_productSprite.sprite = _orderTile.m_image.sprite;
        
        m_partialDesignProvided.sprite = _orderTile.m_partialDesignProvided.sprite;
        m_partialDesignProvided.gameObject.SetActive(m_partialDesignProvided.sprite != null);

        int numBonuses = UnityEngine.Random.Range(0, 3);
        int i = 0;
        for(; i < m_bonusEntries.Count && i < numBonuses; i++)
        {
            int qualValTest = UnityEngine.Random.Range(0,150);
            MAOrderDataManager.QualityEntry testQual = MAOrderDataManager.Me.FindQualityEntryForValue(qualValTest);
            
            m_bonusEntries[i].m_container.GetComponentsInChildren(true, m_bonusEntries[i].m_rewardElements);
            m_bonusEntries[i].m_qualityIcon.sprite = testQual.m_sprite;
            m_bonusEntries[i].m_description.text = $"Design a {testQual.m_name} {order.ProductInfo.Name}";

            MARewardOrderInfo testReward1 = new MARewardOrderInfo();
            testReward1.m_currencyReward = "Money";
            testReward1.m_currencyRewardValue = 2500;
            
            MARewardOrderInfo testReward2 = new MARewardOrderInfo();
            testReward2.m_currencyReward = "Favor";
            testReward2.m_currencyRewardValue = 4;
            MARewardOrderInfo[] rewards = new MARewardOrderInfo[2] {testReward1, testReward2};

            //NGOrderTile.SetupRewardsInfoUI(rewards, m_bonusEntries[i].m_rewardElements, m_bonusEntries[i].m_rewardsDividers);
            m_bonusEntries[i].m_container.gameObject.SetActive(true);
        }
        
        for(; i < m_bonusEntries.Count; i++)
        {
            m_bonusEntries[i].m_container.gameObject.SetActive(false);
        }

        //NGOrderTile.SetupRewardsInfoUI(_orderTile.Order.Rewards, m_rewardElements, m_rewardsDividers);
    }
    
    public static MAOrderCardInfoUI Create(Transform _parent, NGOrderTile _orderTileToLink)
    {   
        MAOrderCardInfoUI cardInfo = Instantiate(MAOrderDataManager.Me.m_maOrderCardInfoUIPrefab, _parent);
        cardInfo.LinkOrderTile(_orderTileToLink);
        return cardInfo;
    }
}*/