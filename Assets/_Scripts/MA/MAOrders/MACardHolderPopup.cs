using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Button = UnityEngine.UI.Button;
#if false
public class MACardHolderPopup : NGBusinessGiftsPopup
{
    [SerializeField] private Button m_closeButton = null;
    
    public Transform m_container = null;

    public bool InWorldSpace
    {
        set
        {
            if(m_closeButton != null)
            {
                m_closeButton.gameObject.SetActive(!value);
            }

            AlwaysFaceCamera alwaysFaceCamera = GetComponent<AlwaysFaceCamera>();
            if(alwaysFaceCamera != null)
            {
                alwaysFaceCamera.enabled = value;
            }
        }
    }
    
    public override void Start()
    {
        base.Start();
        if(m_closeButton != null)
        {
            m_closeButton.onClick.AddListener(Close);
        }
        m_canvas2D = GameManager.Me.TownCanvas;
    }
    
    private void Update()
    {
        if (m_chooseHolder.gameObject.activeSelf)
        {
            m_chooseHeaderText.text = $"Choose {m_maxChooseGifts}";
        }
        if(m_takeAllHolder.gameObject.activeSelf == false && m_chooseHolder.gameObject.activeSelf == false)
        {
        //    DestroyMe();
        }
    }
    
    public override void DestroyMe()
    {
        if(m_container != null)
        {
            Destroy(m_container.gameObject);
        }
        else
        {
            Destroy(gameObject);
        }
    }

    private void Close()
    {
        DestroyMe();
    }
    
    public static MACardHolderPopup Fill(MACardHolderPopup _cardHolderPopup, List<NGBusinessGift> _takeAllGifts, List<NGBusinessGift> _chooseGifts = null, int _maxChooseGifts = 0, MAGameFlow _flow = null)
    {
        var takeAllGifts = _takeAllGifts ?? new List<NGBusinessGift>();
        var chooseGifts = _chooseGifts ?? new List<NGBusinessGift>();
        _cardHolderPopup.Activate(takeAllGifts, chooseGifts, _maxChooseGifts, _flow);
        return _cardHolderPopup;
    }
    
    //TODO: TS - need any of these?
    public static MACardHolderPopup CreateBuildingPopup(MABuilding _building, Vector3 _offsetPos, List<NGBusinessGift> _takeAllGifts, List<NGBusinessGift> _chooseGifts = null, int _maxChooseGifts = 0, MAGameFlow _gameFlow = null, BCBase _optionalComponent = null)
    {
        Transform tr = _building.Visuals;
        if(tr == null)
        {
            tr = _building.transform.Find("Visuals");//TODO: TS - need workaround? why is this null sometimes?
        }

        Tuple<Vector3, Block> highest = MABuildingSupport.FindBuildingHighestPoint(_building);
        Vector3 centralPos = highest.Item1;//GetCentralPosition(true);
        if(_optionalComponent != null)
        {
            float y = centralPos.y;
            centralPos = _optionalComponent.transform.position;
            centralPos.y = y;
        }
        if(float.IsNaN(centralPos.x) || float.IsNaN(centralPos.y) || float.IsNaN(centralPos.z))
        {
            centralPos = _building.transform.position;
        }
        else
        {
            centralPos += _offsetPos;
        }
        MACardHolderPopup popup = CreateWorldSpacePopup(centralPos, tr, _takeAllGifts, _chooseGifts, _maxChooseGifts, _gameFlow);
        popup.InWorldSpace = true;
        return popup;
    }

    public static MACardHolderPopup CreateWorldSpacePopup(Vector3 _position, Transform _parent, List<NGBusinessGift> _takeAllGifts, List<NGBusinessGift> _chooseGifts = null, int _maxChooseGifts = 0, MAGameFlow _gameFlow = null)
    {
        var takeAllGifts = _takeAllGifts ?? new List<NGBusinessGift>();
        var chooseGifts = _chooseGifts ?? new List<NGBusinessGift>();

        var go = Instantiate(MAOrderDataManager.Me.m_maOrderBoardWorldPrefab, _parent);
        go.transform.localPosition = Vector3.zero;
        var bgp = go.GetComponentInChildren<MACardHolderPopup>();
        Transform tr = bgp.transform;
        tr.localScale = new Vector3(0.01f, 0.01f, 0.01f);
        tr.position = _position;
        
        return Fill(bgp, takeAllGifts, chooseGifts, _maxChooseGifts, _gameFlow);
    }

    public static MACardHolderPopup CreateOrderCardRow(MACardHolderPopup _existingRow, Transform _parent, List<NGBusinessGift> _takeAllGifts,
        List<NGBusinessGift> _chooseGifts = null, int _maxChooseGifts = 0, MAGameFlow _gameFlow = null)
    {
        var takeAllGifts = _takeAllGifts ?? new List<NGBusinessGift>();
        var chooseGifts = _chooseGifts ?? new List<NGBusinessGift>();

        if(_existingRow == null)
        {
            var go = Instantiate(MAOrderDataManager.Me.m_maOrderBoardUIRowPrefab, _parent);
            _existingRow = go.GetComponentInChildren<MACardHolderPopup>(true);
        }

        _existingRow.gameObject.SetActive(true);
        _existingRow.InWorldSpace = false;
        
        return Fill(_existingRow, takeAllGifts, chooseGifts, _maxChooseGifts, _gameFlow);
    }
}
#endif