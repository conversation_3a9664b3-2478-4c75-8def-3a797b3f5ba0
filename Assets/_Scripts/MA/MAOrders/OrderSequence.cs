using UnityEngine;
using System.Collections.Generic;
using System;

public class OrderSlots
{
    public enum OrderDisplayedType { All, Unassigned, Assigned }
    
    public List<CardSlot> Slots => m_slots;
    [Save] private List<CardSlot> m_slots = new();
    
    public void Update(BCActionOrderBase _owner)
    {
        var sequence = _owner.Sequence;
        
        bool orderSlotsEnabled = _owner.OrderSlotsEnabled();
        bool readyToDispatch = _owner.ReadyToDispatch();
        bool isDelivering = _owner.IsDelivering();
        
        // Update existing slots
        int activeSlots = GetActiveSlotCount();
        for(int i = 0; i < Slots.Count; ++i)
        {
            bool remove = false;
            var slot = Slots[i];
            var order = slot.m_order;
            if (order.IsValid && slot.m_order.IsComplete)
            {
                order.Return();
                GameManager.Me.m_state.m_orderDataHistory.Add(order);
                
                // Push to the back of the sequence
                if(slot.m_order.OrderInfo.RepeatingOrder && slot.m_temporarySlot == false)
                    sequence.ReturnOrder(order.OrderInfoIndexer);

                if(slot.m_temporarySlot || activeSlots > _owner.MaxCardSlots)
                    remove = true;
                    
                isDelivering = true;
                slot.Clear();
                slot.m_lockedDuration = _owner.SlotLockDuration;
                slot.m_state = _owner.RequiresDelivery ? CardSlot.EState.Delivering : CardSlot.EState.Ready;
            }
            else if(slot.IsAvailable && (_owner.HideEmptySlots || slot.m_temporarySlot || activeSlots > _owner.MaxCardSlots))
            {
                remove = true;
            }
            
            if(remove)
            {
                if(slot.m_temporarySlot == false)
                    activeSlots--;
                Slots.RemoveAt(i--);
            }
            else
            {
                bool emptySlot = slot.Update();
                if(_owner.RequiresDelivery)
                {
                    if(emptySlot)
                    {
                        slot.m_state = isDelivering ? CardSlot.EState.Delivering : CardSlot.EState.NoOrder;
                    }
                    else if(slot.m_state != CardSlot.EState.Ready)
                    {
                        if(readyToDispatch)
                        {
                            slot.m_state = CardSlot.EState.Ready;
                            _owner.OnDeliveryOrderArrvied();
                        }
                        else
                            slot.m_state = CardSlot.EState.Arriving;
                    }
                }
            }
        }
        
        // Queue a new slot to hold the forced order
        if(sequence.NextOrderRequiresExtraSlot() && orderSlotsEnabled)
        {
            var slot = GetNextSlot(_owner, true);
            if(slot != null)
            {
                slot.m_temporarySlot = true;
                var order = sequence.GenerateNextOrderFromSequence(_owner);
                if(order.IsValid)
                    slot.SetNewOrder(order, CardSlot.EState.Ready);
            }
        }

        if(_owner.CreateDefaultSlot && _owner.Slots.Count == 0 && _owner.MaxCardSlots > 0)
        {
            GetNextSlot(_owner);
        }
        
        // Check for new
        if(orderSlotsEnabled && HasSpaceForOrder(_owner) && isDelivering == false)
        {
            var nextOrder = sequence.GenerateNextOrderFromSequence(_owner);
            if(nextOrder.IsValid)
            {
                var nextSlot = GetNextSlot(_owner);
                if(nextSlot != null)
                    nextSlot.SetNewOrder(nextOrder, readyToDispatch ? CardSlot.EState.Ready : CardSlot.EState.Arriving);
                else
                {
                    sequence.ReturnOrder(nextOrder.OrderInfoIndexer, nextOrder.m_extraSlot ? OrderSequence.AddAction.CreateExtraSlot : OrderSequence.AddAction.PushToFront);
                    Debug.LogError("Something going wrong in BCActionorderBase");
                }
            }
        }
    }
    
    public void ReturnAllOrdersToSequence(BCActionOrderBase _owner, bool _returnAllSlots = false)
    {
        foreach(var slot in m_slots)
        {
            if(slot.m_order.IsValid == false) continue;
            
            slot.m_order.Return();
            _owner.Sequence.ReturnOrder(slot.m_order.OrderInfoIndexer, slot.m_order.m_extraSlot ? OrderSequence.AddAction.CreateExtraSlot : OrderSequence.AddAction.PushToFront);
            
            slot.Clear();
        }
        
        if(_returnAllSlots)
            m_slots.Clear();
    }
    
    public void ReturnAllOrders()
    {
        foreach(var slot in m_slots)
        {
            if(slot.m_order.IsValid == false) continue;

            slot.m_order.Return();
        }
    }
    
    public CardSlot TryGetOrderSlot(MAOrder _order)
    {
        foreach(var slot in m_slots)
        {
            if(slot.m_order == _order)
                return slot;
        }
        return null;
    }
    
    /*private CardSlot CreateTemporarySlot(MAOrderInfo _info, string _orderType)
    {
        var nextOrder = OrderSequenceItem.CreateOrder(_info.m_indexer, _orderType);
        
        if(nextOrder.IsValid == false) return null;
        
        var slot = new CardSlot();
        slot.SetNewOrder(nextOrder);
        slot.m_temporarySlot = true;
        m_slots.Add(slot);
        return slot;
    }*/
    
    public void RemoveInvalidOrders()
    {
        foreach(var slot in m_slots)
        {
            if(slot.m_order.IsNullOrEmpty() == false && (slot.m_order.HasLostLinkToOrderInfo() || !slot.m_order.IsValid))
                slot.m_order = MAOrder.EmptyOrder;

            if(slot.m_order.IsNullOrEmpty() == false)
                slot.m_gift = slot.m_order?.TemplateBusinessGift;
            else
                slot.m_gift = null;
        }
    }
    
    public bool HasSpaceForOrder(BCActionOrderBase _owner)
    {
        int slots = 0;
        foreach(var slot in Slots)
        {
            if(slot.m_temporarySlot)
                continue;
            if(slot.IsAvailable)
                return true;
            slots++;
        }
        return slots < _owner.MaxCardSlots;
    }
    
    public List<MAOrder> GetOrdersDisplayed(OrderDisplayedType _type = OrderDisplayedType.All)
    {
        List<MAOrder> orders = new();
        foreach (var slot in Slots)
        {
            if (slot.m_order.IsNullOrEmpty() || slot.m_state != CardSlot.EState.Ready) continue;

            switch (_type)
            {
                case OrderDisplayedType.All:
                    orders.Add(slot.m_order);
                    break;
                    
                case OrderDisplayedType.Assigned:
                    if(slot.m_order.IsAvailable == false)
                        orders.Add(slot.m_order);
                    break;
                    
                case OrderDisplayedType.Unassigned:
                    if(slot.m_order.IsAvailable)
                        orders.Add(slot.m_order);
                    break;
            }
        }
        return orders;
    }
    
    private int GetActiveSlotCount()
    {
        int count = 0;
        foreach(var slot in m_slots)
        {
            if(slot.m_temporarySlot)
                continue;
            count++;
        }
        return count;
    }
    
    private CardSlot GetNextSlot(BCActionOrderBase _owner, bool _forceNew = false)
    {
        int slots = 0;
        foreach(var slot in m_slots)
        {
            if(slot.m_temporarySlot)
                continue;
            if(slot.IsAvailable)
                return slot;
            
            slots++;
        }
        
        CardSlot nextSlot = null;
        if(slots < _owner.MaxCardSlots || _forceNew)
        {
            nextSlot = new CardSlot();
            nextSlot.m_state = _owner.RequiresDelivery ? CardSlot.EState.NoOrder : CardSlot.EState.Ready;
            m_slots.Add(nextSlot);
        }
        return nextSlot;
    }
}

public class OrderSequence
{
    public enum AddAction { None, PushToFront, CreateExtraSlot }
    
    [Save] public List<OrderSequenceItem> m_sequence = new();
    [Save] private float m_orderSearchCooldown = 0;

    public bool HasItems => m_sequence.Count > 0;
    
    public void Update()
    {
        m_orderSearchCooldown = Mathf.Max(0, m_orderSearchCooldown - Time.deltaTime);
    }
    
    public void ReturnOrder(string _orderInfo, AddAction _action = AddAction.None)
    {
        AddAction newAction = _action == AddAction.CreateExtraSlot ? AddAction.CreateExtraSlot : AddAction.None; 
        switch(_action)
        {
            case AddAction.PushToFront:
            case AddAction.CreateExtraSlot:
                m_sequence.Insert(0, new OrderSequenceItem(_orderInfo, newAction));
                break;
            default:
                m_sequence.Add(new OrderSequenceItem(_orderInfo, newAction));
                break;
        }
    }
        
    public bool NextOrderRequiresExtraSlot()
    {
        if(m_sequence.Count == 0) return false;
        return m_sequence[0].m_addAction == AddAction.CreateExtraSlot;
    }
    
    public MAOrder GenerateNextOrderFromSequence(BCActionOrderBase _owner)
    {
        if(m_orderSearchCooldown > 0)
            return MAOrder.EmptyOrder;
            
        if(m_sequence.Count == 0)
            _owner.OnOrderSequenceEmpty();

        if(m_sequence.Count == 0)
        {
            m_orderSearchCooldown = 0;
            return MAOrder.EmptyOrder;
        }
        
        var order = m_sequence[0].CreateOrder(_owner, m_sequence[0].m_addAction == AddAction.CreateExtraSlot);
        m_sequence.RemoveAt(0);
        return order;
    }
        
    public void ResetSearchCooldown()
    {
        m_orderSearchCooldown = 0;
    }
    
    public void Insert(MAOrderInfo _order, bool _clearExisting, AddAction _action = AddAction.None)
    {
        Insert(new List<MAOrderInfo>{ _order }, _clearExisting, _action);
    }
    
    public void Insert(List<MAOrderInfo> _orders, bool _clearExisting, AddAction _action = AddAction.None)
    {
        if(_orders == null)
            return;
            
        if(_clearExisting)
            m_sequence.Clear();

        ResetSearchCooldown();

        switch(_action)
        {
            case AddAction.CreateExtraSlot:
            case AddAction.PushToFront:
                for(int i = _orders.Count-1; i >= 0; i--)
                    m_sequence.Insert(0, new OrderSequenceItem(_orders[i].m_indexer, _action)); 
                break;
                
            default:
                foreach (var o in _orders)
                    m_sequence.Add(new OrderSequenceItem(o.m_indexer, _action));
                break;
        }
    }
}

[Serializable]
public class CardSlot
{
    public enum EState
    {
        Ready,
        NoOrder,
        Arriving,
        Delivering,
    }
    
    public NGBusinessGift m_gift;
    [Save] public MAOrder m_order;
    [Save] public float m_lockedDuration = 0;
    [Save] public EState m_state;
    [Save] public bool m_temporarySlot = false;
    public bool IsAvailable => m_lockedDuration <= 0 && m_order.IsNullOrEmpty();

    public CardSlot() { m_gift = null; m_order = MAOrder.EmptyOrder; }
    public CardSlot(NGBusinessGift _gift)
    {
        m_gift = _gift;
    }
    
    public void SetNewOrder(MAOrder _order, EState _state)
    {
        m_state = _state;
        m_order = _order;
        if(_order != null)
            m_gift = _order.TemplateBusinessGift;
        else
            Clear();
    }
    
    public void Clear()
    {
        m_gift = null;
        m_order = MAOrder.EmptyOrder;
    }
    
    // Returns true if we are ready to recieve an order
    public bool Update()
    {
        if(m_lockedDuration > 0)
        {
            m_lockedDuration = Mathf.Max(0, m_lockedDuration - Time.deltaTime);
            return false;
        }
        if(m_order.IsNullOrEmpty())
            return true;
            
        return false;
    }
}

[Serializable]
public class OrderSequenceItem
{
    public OrderSequence.AddAction m_addAction;
    public string m_orderInfo;
    public MAOrder m_order;
    
    public OrderSequenceItem() {}
    
    // New order
    public OrderSequenceItem(string _orderInfo, OrderSequence.AddAction _action = OrderSequence.AddAction.None)
    {
        m_orderInfo = _orderInfo;
        m_addAction = _action;
    }
    
    // Partialy complete order
    public OrderSequenceItem(MAOrder _order, OrderSequence.AddAction _action = OrderSequence.AddAction.None)
    {
        m_order = _order;
        m_orderInfo = _order.OrderInfoIndexer;
        m_addAction = _action;
    }
    
    public MAOrder CreateOrder(BCActionOrderBase _owner, bool _extraSlot)
    {
        return CreateOrder(_owner, m_orderInfo, _owner.OrderType, _owner.SequenceType, _extraSlot, m_order);
    }
    
    public static MAOrder CreateOrder(BCBase _owner, string _orderInfo, string _orderType, string _sequenceType, bool _extraSlot, MAOrder _order = null)
    {
        if(_order == null)
        {
            var orderInfo = MAOrderInfo.GetInfo(_orderInfo);
            if(orderInfo == null)
                return MAOrder.EmptyOrder;
        
            _order = new MAOrder(orderInfo);
            if(!_order.IsValid)
                return null;
        }
        
        _order.m_extraSlot = _extraSlot;
        _order.m_sequenceType = _sequenceType;
        _order.m_orderType = _orderType;
        _order.Return();
        _order.SetOwner(_owner);
        return _order;
    }
}
