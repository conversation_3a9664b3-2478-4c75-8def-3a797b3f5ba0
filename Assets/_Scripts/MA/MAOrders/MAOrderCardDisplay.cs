using UnityEngine;
using System.Collections.Generic;
using TMPro;

public class MAOrderCardDisplay : MonoB<PERSON><PERSON><PERSON>, INGDecisionCardHolder
{
    #region INGDecisionCardHolder interface implementation
    public INGDecisionCardHolder.ECardView CardView { get { return INGDecisionCardHolder.ECardView.Simple; } }
    public void ToggleHiding(bool _hide, bool _isDueToClick = false) { }
    public bool IsHidden { get; }
    public void GiftReceived(IBusinessCard _card) {    }
    public bool ShowOrderBoardGUIOnCardClick { get { return true; } }
    public bool DragIn3D { get { return true; } }

    public Transform m_cardHolder;
    public Transform GiftsHolder() { return transform; }
    public Transform Root => m_cardHolder;
    public bool EnableCardOnUpwardDrag() { return true; }
    public Transform ParentForDraggingCard(NGDirectionCardBase _card) { return m_canvas2D.transform; }
    public Transform GetParentHolder(NGDirectionCardBase _card) { return m_cardHolder; }
    #endregion

    public Canvas m_canvas2D = null;
    
    [SerializeField] private TMP_Text m_infoText;
    [SerializeField] private RectTransform m_orderCardHolder;
    [SerializeField] private float m_overrideProductTitleMaxFontSize = 200f;
    
    private NGOrderTile m_orderTile = null;
    private GameObject m_infoTextGameObject = null;

    private int m_orderIdDisplayed = -1;
    
    public void OnCardClick() {}
    public void OnStartCardDrag() {}
    public void OnEndCardDrag() {}
    
    public void SetDefaultText()
    {
        SetInfoText("Order Board");
    }
    
    private void Awake()
    {
        if(m_orderCardHolder == null) m_orderCardHolder = transform.GetComponent<RectTransform>();
        m_infoTextGameObject = m_infoText?.gameObject;
        m_orderTile = GetComponentInChildren<NGOrderTile>(true);
        ClearAll();
    }
    
    public void ClearAll()
    {
        SetDefaultText();
        ClearCard();
    }

    public void SetInfoText(string _infoText)
    {
        if(m_infoText != null)
        {
            m_infoText.text = _infoText;
            //m_infoTextGameObject.SetActive(string.IsNullOrWhiteSpace(_infoText) == false);
        }
    }

    public void SetupOrderCard(MAOrder _order)
    {
        NGBusinessGift templateBusinessGift = _order.TemplateBusinessGift;
        if(m_orderTile == null)
        {
            m_orderTile = NGOrderTile.Create(NGBusinessDecisionManager.Me.m_ngOrderTilePrefab.gameObject,
                templateBusinessGift, null, this, m_orderCardHolder);
        }
        else
        {
            m_orderTile = NGOrderTile.Setup(m_orderTile,
                templateBusinessGift, null, this, m_orderCardHolder);
        }
    
        m_orderIdDisplayed = _order.OrderId;
        
        RectTransform rTr = m_orderTile.transform as RectTransform;
        rTr.anchoredPosition = Vector3.zero;
        m_orderTile.gameObject.SetActive(true);
        m_orderTile.SetupOrderUI(_order);
    }

    public bool IsDisplayingOrder(MAOrder _order)
    {
        return m_orderIdDisplayed == _order.OrderId;
    }
    
    public void ClearCard()
    {
        m_orderIdDisplayed = -1;
        if(m_orderTile != null)
        {
            m_orderTile.gameObject.SetActive(false);            
        }
    }
}
