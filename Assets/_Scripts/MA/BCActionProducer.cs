using System;
using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using Random = UnityEngine.Random;

public class BCActionProducer : BCActionBase
{
    [KnackField] public string m_input;
    [KnackField] public string m_output;
    [KnackField] public int m_inputQuantity;
    [KnackField] public float m_energyRequiedToMake = 2;
    [KnackField] public float m_outputQuantity = 1;
    
    public float m_nextStockCheckTime = 0;
    public NGCarriableResource OutputResource => m_outputResource;
    protected NGCarriableResource m_inputResource;
    protected NGCarriableResource m_outputResource;
    
    [SerializeField][Save] protected NGStock m_inputStock = new ();
    
    [Save]protected float m_energyLeftToMake = 0;
    
    public bool HasStartedMaking => m_energyLeftToMake < m_energyRequiedToMake;
    
    override public NGStock GetInputStock() => m_inputStock;
    
    override public int CantMakePriority => 5;
    virtual protected bool ShowSmoke => false;
    
    virtual protected string GetDescriptionPrefix()  => null;
    virtual protected string GetDescriptionPostfix()  => null;
    protected float m_nextCreateTime = 0;
    private bool m_hasCreatedOutputVisual = false;

    protected override void PostLoad()
    {
        m_nextStockCheckTime = Time.time + Random.Range(0.5f, 2f);
        base.PostLoad();
    }

    public void RefreshStockVisuals()
    {
        ClearStockVisuals(Block.m_inputStockVisuals);
        ClearStockVisuals(Block.m_outputStockVisuals);
        
        foreach(var item in m_inputStock.Items)
        {
            for(int i = 0; i < item.m_stock; ++i)
            {
                AddStockToVisuals(Block.m_inputStockVisuals, item.Resource);
            }
        }
        
        foreach(var item in m_stock.Items)
        {
            for(int i = 0; i < item.m_stock; ++i)
            {
                AddStockToVisuals(Block.m_outputStockVisuals, item.Resource);
            }
        }
    }
    
    public override string GetCantMakeReason()
    {
        if (m_building.IsPaused)
            return "production paused";

        if(m_inputStock.CanMake() == false)
        {
            string stockRequired = "";
            foreach(var i in m_inputStock.Items)
            {
                if(i.Needed > 0)
                {
                    string stockStr = $"{i.Needed} {i.Resource.m_title}";
                    if(stockRequired.IsNullOrWhiteSpace())
                        stockRequired += $"{stockStr}";
                    else
                        stockRequired += $" + {stockStr}";
                }
            }
            return $"{stockRequired} required to make";
        }

        if (m_stock.GetTotalStock() > 0)
            return "stock out full";
        
        return base.GetCantMakeReason();
    }

    override public float GetProductScore() => m_energyRequiedToMake == 0 ? 0f : 1f - Mathf.Clamp01(m_energyLeftToMake / m_energyRequiedToMake);
    
    override public float GetResourceRequirement(NGCarriableResource _resource, bool _isTest = false)
    {
        var item = m_inputStock.Find(_resource);
        if(item == null || item.Needed <= 0)
            return 0f;
        return 1f;
    }
    
    protected override void SetupStockRequirements()
    {
        base.SetupStockRequirements();
        
        m_inputResource = NGCarriableResource.GetInfo(m_input);
        m_outputResource = NGCarriableResource.GetInfo(m_output);
        
        m_inputStock.RemoveEmptyStockAndClearNeededToProduce(m_inputResource);
        m_stock.RemoveEmptyStockAndClearNeededToProduce(m_outputResource);
        
        if(m_inputResource.IsNone == false)
            m_inputStock.AddOrCreateStock(m_inputResource, 0, m_inputQuantity);
            
        if(m_outputResource.IsNone == false)
            m_stock.AddOrCreateStock(m_outputResource, 0, m_outputQuantity);
            
        RefreshStockVisuals();
    }

    override public void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        if(m_energyLeftToMake <= 0) m_energyLeftToMake = m_energyRequiedToMake;
        
        base.Activate(_indexOfComponentType, _quantityInBuilding);
    }

    public override void UpdateInternal(BuildingComponentsState _state)
    {
        base.UpdateInternal(_state);
        
        if(m_building.IsPaused == false)
        {
            UpdateProduction(_state, false);
        }
    }
    
    private float m_lastMadeTime = 0;
    private float m_nextGatherCheck = 0;
    protected bool IsReadyToProduce(out bool _doneWork, bool _isTap)
    {
        _doneWork = false;
        while(m_inputStock.CanMake() == false)
        {
            if(_isTap)
            {
                _doneWork = (Time.time - m_lastMadeTime) < 0.5f;
                return false;
            }
            bool addedStock = AskForInputStock(false); 
            _doneWork |= addedStock;
            if(addedStock == false)
            {
                return false;
            }
        }
        
        // Ensure output has space
        _doneWork = true;
        bool stockOutFull = m_stock.GetTotalStock() > 0;
        
        // Done to send idle workers out to harvest
        if(stockOutFull && Time.time >= m_nextGatherCheck)
        {
            m_nextGatherCheck = Time.time + 1.5f;
            if(Building.HasStockInSpace())
            {
                AskForInputStock(true);
            }
        }
        
        return stockOutFull == false;
    }
    
    public override bool UpdateTapWork()
    {
        return UpdateProduction(null, true);
    }
    
    public bool m_inhibitProduction = false;

    public override void PreUpdate(BuildingComponentsState _state)
    {
        base.PreUpdate(_state);
    }
    
    virtual protected bool UpdateProduction(BuildingComponentsState _state, bool _isTap)
    {
        if(m_inhibitProduction)
            return false;
        
        if(IsReadyToProduce(out var doneWork, _isTap))
        {
            TryCreateOutputVisual();
            
            var previousEnergy = m_energyLeftToMake;

            // 1. Consume power
            if (m_energyLeftToMake > 0f)
            {
                if(_isTap)
                {
                    m_building.ConsumePower(ref m_energyLeftToMake);
                }
                else
                {
                    var multiplier = Building.GetWorkerSpeedMultiplier();
                    foreach(var worker in m_workersPresent)
                    {
                        m_energyLeftToMake = Mathf.Max(0, m_energyLeftToMake - worker.ProductionPower * Time.deltaTime * multiplier);
                    }
                }
                
                m_nextCreateTime = Time.time + 0.1f;
            }
            
            AddToEnergyUsed(previousEnergy - m_energyLeftToMake, ShowSmoke);
            
            // 2. Try create output
            if(m_energyLeftToMake <= 0f && m_nextCreateTime < Time.time)
            {
                CreateItem();
            }
            return true;
        }
        return doneWork;
    }
    
    protected void TryCreateOutputVisual()
    {
        if(m_hasCreatedOutputVisual || m_energyLeftToMake >= (m_energyRequiedToMake-0.01f)) return;
        
        ClearStockVisuals(Block.m_outputStockVisuals);
        AddStockToVisuals(Block.m_outputStockVisuals, m_stock.GetPrimaryStock);
        m_hasCreatedOutputVisual = true;
    }
    
    virtual protected void CreateItem()
    {
        m_lastMadeTime = Time.time;
        m_hasCreatedOutputVisual = false;
        ClearStockVisuals(Block.m_inputStockVisuals);
        m_inputStock.ClearStock();
        m_stock.MakeStockFromNeeded();
        m_energyLeftToMake = m_energyRequiedToMake;
        
        GetComponent<Block>().PlayCreateOutputAudio();
        
        TryDeliverStock(m_stock.GetPrimaryStock);
    }
    
    public bool ConsumeInputStock(NGCarriableResource _resource)
    {
        bool consumed = m_inputStock.Consume(_resource);
        if(consumed)
        {
            RemoveStockFromVisuals(Block.m_inputStockVisuals, _resource);
        }
        return consumed;
    }
    
    virtual public bool TryMoveInputStock(NGCarriableResource _resource, NGStock.NGStockItem _toStockItem, BCActionProducer _to)
    {
        int needed = _toStockItem.Needed;
        if(needed == 0) return false;
        if(HasStartedMaking) return false;
        
        // They have workers
        if(_to.m_workersAllocated.Count > 0)
        {
            // I have workers and I have priority
            if(m_workersAllocated.Count > 0 && m_uid < _to.m_uid) return false;
        }
        // I have workers, but they dont
        else if(m_workersAllocated.Count > 0)
        {
            return false;
        }
        // Neither have workers, I have priority
        else if(m_uid < _to.m_uid)
        {
            return false;
        }
        
        return ConsumeInputStock(_resource);
    }
    
    private void TryDeliverStock(NGCarriableResource _resource)
    {
        // Try deliver directly to destination
        if(OutputHasDestination)
        {
            foreach(var action in Building.ActionComponents)
            {
                if(action.AddResource(_resource))
                {
                    ClearStockVisuals(Block.m_outputStockVisuals);
                    m_stock.ClearStock();
                    return;
                }
            }
            return;
        }
            
        // Don't deliver stock when no workers or there is another action within
        // the building that needs the stock
        if(m_workersPresent.Count == 0) return;
        
        var worker = GetAvailableWorker();
        if(worker == null || worker.IsTimeToGoHome || worker.IsTimeToHangout) return;
        
        var bestStockDestination = m_building.GetBestStockDestination(m_stock);
        SendOutToDeliver(bestStockDestination, worker);
    }
    
    public virtual bool AskForInputStock(bool _gatherOnly)
    {
        if(_gatherOnly)
            return false;
        var stockAdded = m_building.ConsumeStockNeededWithInfo(m_inputStock, this, this);
        AddStockToVisuals(Block.m_inputStockVisuals, stockAdded);
        return stockAdded != null;
    }
    
    override public bool HasDragContent()
    {
        if(OutputHasDestination) return false;
        
        var stock = GetInputStock();
        if(stock == null) return false;
        
        foreach(var item in m_stock.Items)
        {
            if(item.Stock > 0 && MAUnlocks.CanPickup(item.Resource)) return true;
        }
        return false;
    }
    
    override public GameObject GetDragContent()
    {
        if(OutputHasDestination) return null;
        
        var stock = GetInputStock();
        if(stock == null) return null;
        
        foreach(var item in m_stock.Items)
        {
            if(item.Stock > 0 && MAUnlocks.CanPickup(item.Resource))
            {
                return TakePickup(item.Resource, false)?.gameObject;
            }
        }
        return null;
    }
    
    override public NGCarriableResource ConsumeOrderProduct(BCActionOrderBase _orderDestination)
    {
        var item = m_stock.GetCompatableItemInStock(_orderDestination);
        if(item != null && ConsumeStock(item))
        {
            ClearStockVisuals(Block.m_outputStockVisuals);
            return item;
        }
        return null;
    }
    
    override public NGCarriableResource ConsumeForStockOut()
    {
        foreach(var item in m_stock.Items)
        {
            if(item.Stock > 0)
            {
                item.Stock--;
                if(item.Stock == 0)
                {
                    ClearStockVisuals(Block.m_outputStockVisuals);
                }
                
                return item.Resource;
            }
        }
        
        return null;
    }

    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (m_info?.id, () => new BCProducerPanel(m_info));
    
    public class BCFactoryPanel : BCProducerPanel
    {
        private BCFactory m_factory;
        public BCFactoryPanel(MAComponentInfo _info) : base(_info) { }
        
        public override void AddComponent(BCBase _component)
        {
            if(_component is BCFactory)
            {
                m_factory = (BCFactory)_component;
            }
            base.AddComponent(_component);
        }
        
        public override string GetDescription()
        {
            if(m_factory != null && m_factory.Order.IsNullOrEmpty())
            {
                return "Order Required";
            }
            return base.GetDescription();
        }
    }
    
    public class BCProducerPanel : BCUIPanel
    {
        public BCActionProducer m_producer;
        private MABuilding m_building;
        private string m_blockID;
        public override bool ShowWarning => m_producer?.ShowWarning ?? false;
        public override string SpriteBlockID => m_blockID;
        
        public override string GetPrimaryText()
        {
            var desc = m_producer.GetDescriptionPrefix();
            
            if(desc.IsNullOrWhiteSpace() == false) desc += "\n\n";
            
            desc += m_producer?.m_info?.m_description;
            
            if(desc.IsNullOrWhiteSpace() == false) desc += "\n\n";
            
            desc += m_producer.GetDescriptionPostfix();
            
            return desc; 
        }
        //public override string TableHeading => "Enhancements";
        
        public override Action GetShowRangeAction()
        { 
            if(m_producer == null) return null;
            var gather = m_producer as BCActionGatherer;
            if(gather == null) return null;
            
            return () =>
            {
                BCBase.HighlightRange(m_all);
                BuildingComponentVisualHelpers.AddGroundArea(m_producer.m_building.transform.position, 0, gather.m_treeRadius);
            };
        }
        
        public BCProducerPanel(MAComponentInfo _info) : base(_info) { }

        public override void AddComponent(BCBase _component)
        {
            if(_component is BCActionProducer)
            {
                m_blockID = _component.Block.BlockID;
                m_building = _component.Building;
                m_producer = (BCActionProducer)_component;
                base.AddComponent(_component);
            }
        }
        
        public override string GetDescription()
        {
            string convertFrom = "";
            string convertTo = "";
            foreach(var stock in m_producer.m_inputStock.Items)
            {
                if(stock.m_neededToProduce <= 0) continue;
                if(stock.m_neededToProduce == 1)
                    convertFrom += $"<size=150%>{stock.Resource.TextSprite}</size>";
                else
                    convertFrom += $"{stock.m_neededToProduce}x<size=150%>{stock.Resource.TextSprite}</size>";
            }
              
            foreach(var stock in m_producer.m_stock.Items)
            {
                if(stock.m_neededToProduce <= 0) continue;
                if(stock.m_neededToProduce == 1)
                    convertTo += $"<size=150%>{stock.Resource.TextSprite}</size>";
                else
                    convertTo += $"{stock.m_neededToProduce}x<size=150%>{stock.Resource.TextSprite}</size>";
            }
            
            return $"Turns {convertFrom}into {convertTo}";
        }
    }
}