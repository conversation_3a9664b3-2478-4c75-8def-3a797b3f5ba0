using System;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using Random = UnityEngine.Random;

public class MAAnimalHouse : MonoBehaviour
{
    [Serializable]
    public class CreatureSpawnChance
    {
        public string m_creatureInfo = "Chicken";
        public float m_probability = 1f;
    }

    public List<CreatureSpawnChance> m_animalTypes = new();
    
    public int m_maxAnimals = 1;
    public float m_spawnTimerRangeMin = 1f;
    public float m_spawnTimerRangeMax = 5f;
    public float m_returnTimerRangeMin = 6f;
    public float m_returnTimerRangeMax = 15f;
    public float m_returnChance = 0.5f;
    
    [SerializeField]
    public List<MAAnimal> m_animals = new List<MAAnimal>();
    
    private MATimer m_dayNightCheckTimer = new MATimer();
    private MATimer m_spawnTimer = new MATimer();
    private MATimer m_returnTimer = new MATimer();

    private MABuilding m_maBuilding = null;
    public MABuilding Building => m_maBuilding;
    
    private bool m_loaded = false;
    
    // public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
    // {
    //     base.Activate(_indexOfComponentType, _quantityInBuilding);
    //     SetSpawnTimer();
    //     SetReturnTimer();
    // }

    public bool IsCompatibleAnimal(MACreatureInfo _info) => m_animalTypes.FindIndex(x => MACreatureInfo.GetInfo(x.m_creatureInfo) == _info) > -1;

    public void ReturnAnimal(MAAnimal _animal)
    {
        if (_animal != null)
        {
            var state = _animal.CharacterUpdateState;
            MACharacterStateFactory.ApplyCharacterState(CharacterStates.GoingHome, _animal);
            state = state != null ? _animal.ScheduledState : _animal.CharacterUpdateState;
            state.OnStateFinished -= OnFinishedGoingHome;
            state.OnStateFinished += OnFinishedGoingHome;
        }
    }
    
    private void Update()
    {
        if (GameManager.Me.LoadComplete == false || IntroControl.IsInIntro) return;
        
        if (m_loaded == false)
        {   
            SetSpawnTimer();
            SetReturnTimer();
            m_maBuilding = GetComponentInParent<MABuilding>(true);
            
            if(m_maBuilding != null)
            {
                foreach (MAAnimal maAnimal in NGManager.Me.m_MAAnimalList)
                {
                    if (maAnimal.IsPossessed) continue;
                    if (maAnimal.GameState.m_homeComponentId > 0)
                    {
                        int iHomeComp = m_maBuilding.m_components.FindIndex(x =>
                            x.m_uid == maAnimal.GameState.m_homeComponentId);
                        if (iHomeComp > -1 && IsCompatibleAnimal(maAnimal.CreatureInfo))
                        {
                            m_animals.Add(maAnimal);
                        }
                    }
                    else if (maAnimal.GameState.m_homeComponentId == -1) //<- normally 0 means 'none' but we set this to -1 if an animal's house was destroyed.
                    {
                        if (IsCompatibleAnimal(maAnimal.CreatureInfo))
                        {
                            m_animals.Add(maAnimal);
                            maAnimal.Home = m_maBuilding.ActionComponents[0]; //at time of writing this seems to be the way home component id is assigned, see Activate()
                        }
                    }
                }
            }

            m_loaded = true;
        }
        
        for(int i = m_animals.Count - 1; i >= 0; i--)
        {
            if (m_animals[i] == null)
            {
                m_animals.RemoveAt(i);
            }
        }

        if (m_dayNightCheckTimer.IsFinished)
        {
            foreach (var animal in m_animals)
            {
                if (MACreatureControl.Me.IsValidTimeOfDay(animal.CreatureInfo) == false)
                {
                    if (animal.IsOrWillBeInState(CharacterStates.GoingHome) == false &&
                        animal.IsOrWillBeInState(CharacterStates.FollowLeader) == false &&
                        animal.IsOrWillBeInState(CharacterStates.Despawn) == false &&
                        animal.IsOrWillBeInState(CharacterStates.Spawn) == false)
                    {
                        ReturnAnimal(animal);
                    }
                }
            }

            SetDayNightReturnTimer();
        }

        if (m_spawnTimer.IsFinished)
        {
            if (m_animals.Count < m_maxAnimals)
            {
                SpawnAnimal();
            }

            SetSpawnTimer();
        }

        if (m_returnTimer.IsFinished)
        {
            if (Random.Range(0f, 1f) < m_returnChance)
            {
                var animalsToManage = m_animals.FindAll(x => 
                    x.CharacterUpdateState.State != CharacterStates.GoingHome &&
                    x.CharacterUpdateState.State != CharacterStates.FollowLeader &&
                    x.CharacterUpdateState.State != CharacterStates.Despawn &&
                    x.CharacterUpdateState.State != CharacterStates.Spawn);
                
                int i = Random.Range(-1, animalsToManage.Count);
                if (i != -1)
                {
                    ReturnAnimal(animalsToManage[i]);
                }
            }
            SetReturnTimer();
        }
    }

    private void OnFinishedGoingHome(string _state, MACharacterBase _character)
    {
        if (_state == CharacterStates.GoingHome)
        {
            _character.CharacterUpdateState.OnStateFinished -= OnFinishedGoingHome;
            m_animals.Remove(_character as MAAnimal);
        }
    }
    
    public void SpawnAnimal()
    {
        if(m_maBuilding == null) return;
        
        float total = 0f;
        int count = 0;

        CreatureSpawnChance[] types = new CreatureSpawnChance[m_animalTypes.Count];
        for (int i = 0; i < m_animalTypes.Count; i++)
        {
            var typeInfo = MACreatureInfo.GetInfo(m_animalTypes[i].m_creatureInfo);
            if (typeInfo != null)
            {
                if(MACreatureControl.Me.IsValidTimeOfDay(typeInfo) == false) continue;
                total += m_animalTypes[i].m_probability;
                types[count] = m_animalTypes[i];
                count++;
            }
        }

        if (count > 0)
        {
            MACreatureInfo creatureInfo = null;
            float ran = Random.Range(0, total);
            float step = 0f;
            for (int i = 0; i < count; i++)
            {
                step += types[i].m_probability;
                if (step > ran)
                {
                    creatureInfo = MACreatureInfo.GetInfo(types[i].m_creatureInfo);
                    break;
                }
            }
					
            var character = MACreatureControl.Me.SpawnNewCreatureAtBuilding(creatureInfo, m_maBuilding);
            var animal = character as MAAnimal;
            if(animal != null)
            {
                m_animals.Add(animal);
                animal.SetToGuard(m_maBuilding.DoorPosOuter, animal.CharacterSettings.m_guardModeOverrideRadius);
            }
            else if(character != null)
            {
                Debug.LogError("Spawned character is not an animal");
                character.DestroyMe();
            }
        }
    }
    
    public void SpawnAnimal(string _creatureInfoName)
    {
        MACreatureInfo creatureInfo = MACreatureInfo.GetInfo(_creatureInfoName);
        SpawnAnimal(creatureInfo);
    }
    
    public void SpawnAnimal(MACreatureInfo _creatureInfo)
    {
        var character = MACreatureControl.Me.SpawnNewCreatureAtBuilding(_creatureInfo, m_maBuilding);
        var animal = character as MAAnimal;
        if(animal != null)
        {
            m_animals.Add(animal);
            animal.SetToGuard(m_maBuilding.DoorPosOuter, animal.CharacterSettings.m_guardModeOverrideRadius);
        }
        else if(character != null)
        {
            Debug.LogError("Spawned character is not an animal");
            character.DestroyMe();
        }
    }

    private void OnDestroy()
    {
        for (var i = m_animals.Count-1; i >= 0; i--)
        {
            MAAnimal maAnimal = m_animals[i];
            if (maAnimal == null)
            {
                m_animals.RemoveAt(i);
            }
            else
            {
                m_animals[i].GameState.m_homeComponentId = -1;
            }
        }
    }

    private void SetSpawnTimer()
    {
        float max = Mathf.Max(m_spawnTimerRangeMin,m_spawnTimerRangeMax);
        float min = Mathf.Max(m_spawnTimerRangeMin,m_spawnTimerRangeMax);
        m_spawnTimer.Set(UnityEngine.Random.Range(min, max));
    }

    private void SetReturnTimer()
    {
        float max = Mathf.Max(m_returnTimerRangeMin,m_returnTimerRangeMax);
        float min = Mathf.Max(m_returnTimerRangeMin,m_returnTimerRangeMax);
        m_returnTimer.Set(UnityEngine.Random.Range(min, max));
    }
    
    private void SetDayNightReturnTimer()
    {
        m_dayNightCheckTimer.Set(0.3f);
    }
}



#if UNITY_EDITOR
[CustomEditor(typeof(MAAnimalHouse))]
public class MAAnimalHouseEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        MAAnimalHouse animalHouse = (MAAnimalHouse)target;

        if (Application.isPlaying)
        {
            foreach (var animalType in animalHouse.m_animalTypes)
            {
                MACreatureInfo creat = MACreatureInfo.GetInfo(animalType.m_creatureInfo);
                if (animalHouse.IsCompatibleAnimal(creat))
                {
                    if (GUILayout.Button($"Spawn Animal: {animalType}"))
                    {
                        animalHouse.SpawnAnimal(creat);
                    }
                }
            }
            if (GUILayout.Button($"Spawn All Animals'"))
            {
                for (int i = 0; i < animalHouse.m_animalTypes.Count; i++)
                {
                    animalHouse.SpawnAnimal(animalHouse.m_animalTypes[i].m_creatureInfo);
                }
            }
            if (GUILayout.Button($"Spawn All Probable Animals '"))
            {
                for (int i = 0; i < animalHouse.m_maxAnimals; i++)
                {
                    animalHouse.SpawnAnimal();
                }
            }
            if (GUILayout.Button($"Return All Animals'"))
            {
                foreach (var animal in NGManager.Me.m_MAAnimalList.FindAll(x => animalHouse.Building.m_components.Contains(x.Home)))
                {
                    if (animal != null)
                    {
                        MACharacterStateFactory.ApplyCharacterState(CharacterStates.GoingHome, animal);
                    }
                }
            }
        }
    }
}
#endif