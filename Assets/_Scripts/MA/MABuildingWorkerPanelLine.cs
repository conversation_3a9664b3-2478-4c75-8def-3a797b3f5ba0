using System.Collections;
using System.Collections.Generic;
using System.Text.RegularExpressions;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MABuildingWorkerPanelLine : MonoBehaviour
{
    public TMP_Text m_name;
    public TMP_Text m_col2;
    public TMP_Text m_col3;
    public TMP_Dropdown m_col3Dropdown;
    public Button m_actionButton;
    public Button m_viewButton;
    public BCUIPanel m_owner;
    public bool m_allowJobChange;
    public Image m_background;

    //public TMP_Text m_actionText;
    
    private MACharacterBase m_character;
    
    public void OnClickAction()
    {
        var action = GetWorkerActionButton(m_character);
        
        if(action.action == null) return;
        
        action.action();
    }
    
    public void OnDropdownChanged()
    {
        if(m_character == null)
            return;
        var options = m_col3Dropdown.options;
        if(options.Count == 0 || m_character.Job == null || m_character.Job.Building == null)
            return;
            
        var worker = m_character as MAWorker;
        if(worker == null || worker.CanAssignJob == false)
            return;
        
        var selected = options[m_col3Dropdown.value];
        var jobOption = selected as JobOptionData;
        if(jobOption == null || jobOption.m_info == null || m_character.Job.m_info == jobOption.m_info)
            return;
            
        var building = m_character.Job.Building;
        building.TryAssignJob(worker, jobOption.m_info);
        
        Refresh();
        if(m_owner != null)
            m_owner.m_forceUpdateTableLines = true;
    }
    
    public void OnClickLine()
    {
        if(m_character == null) return;
        
        NGBuildingInfoGUI.DestroyCurrent();
        
        MACharacterInfoUI.Create(m_character);
    }
    
    public void OnClickView()
    {
        if(m_character == null) return;
        
        NGBuildingInfoGUI.DestroyCurrent();
        
        MACharacterInfoUI.Create(m_character);
    }

    public static (string description, System.Action action) GetWorkerActionButton(MACharacterBase _character)
    {
        if(_character == null) return (null,null);
        
        if(_character is MAWorker worker)
        {
            switch(_character.PeepAction)
            {
                case PeepActions.Resting:
                case PeepActions.ReturnToRest:
                    if(_character.Job != null)
                    {
                        return ("Work", worker.RecallToWork);
                    }
                    break;
                    
                default:
                    if(_character.Home != null)
                    {
                        return ("Rest", worker.RecallToHome);
                    }
                    break;
            }
        }
        return (null, null);
    }
    
    private float m_nextUpdateTime = 0;
    public void Update()
    {
        if(Time.time > m_nextUpdateTime)
        {
            Refresh();
            m_nextUpdateTime = Time.time + 1f; 
        }
    }
    
    private void UpdateDropdownValues()
    {
        var jobs = new List<MAComponentInfo>() { };
        int selected = 0;
        
        if(m_character != null)
        {
            var job = m_character.Job;
            if(job != null)
            {
                jobs.Add(job.m_info);
                selected = jobs.Count-1;
            
                if(job.Building != null)
                {
                    foreach(var action in job.Building.ActionComponents)
                    {
                        if(jobs.Contains(action.m_info)) continue;
                        if(action is BCBedroom) continue;
                        if(action.GetFreeSlots() <= 0) continue;
                        
                        jobs.Add(action.m_info);
                    }
                }
            }
        }
        
        var values = new List<TMP_Dropdown.OptionData>();
        foreach(var j in jobs)
        {
            values.Add(new JobOptionData(j));
        }
        m_col3Dropdown.options = values;
        m_col3Dropdown.value = selected;
    }
    
    private class JobOptionData : TMP_Dropdown.OptionData
    {
        public MAComponentInfo m_info;
        
        public static string GetName(MAComponentInfo _info)
        {
            if(_info == null) return "None";
            return _info.GetJobTitle();
        }
        public JobOptionData(MAComponentInfo _info) : base(GetName(_info))
        {
            m_info = _info;
        }
    }
    
    private void UpdateCol3(bool _showDropdown, bool _showText)
    {
        if(m_col3.gameObject.activeSelf != _showText) m_col3.gameObject.SetActive(_showText);
        if(m_col3Dropdown.gameObject.activeSelf != _showDropdown) m_col3Dropdown.gameObject.SetActive(_showDropdown);
    }
    
    public void SetIsTitle(bool _value)
    {
        m_viewButton.enabled = _value == false;
        var color = m_background.color;
        color.a = _value ? 0.1f : 0.04f;
        m_background.color = color;
    }
        
    private void Refresh()
    {
        if(m_character == null) return;
        
        m_name.text = m_character.Name;
        
        if(m_character is MAWorker)
        {
            if(m_owner is BCWorkerPanel || m_owner is BCResidentsPanel)
                m_col2.gameObject.SetActive(false);
            else
                m_col2.text = MACharacterInfoUI.GetStateName(m_character);
            if(m_allowJobChange)
                UpdateCol3(true, false);
            else
                UpdateCol3(false, true);
            UpdateDropdownValues();
            
            m_col3.text = JobOptionData.GetName(m_character.Job?.m_info);
        }
        else
        {
            m_col2.text = $"{m_character.Experience:N0}/{m_character.GetExperienceRequiredForNextLevel():N0}";
            m_col3.text = m_character.NormalizedHealth.ToString("P0");
            UpdateCol3(false, true);
        }
    }
    
    void Activate(MACharacterBase _character, bool _allowJobChange, BCUIPanel _panel)
    {
        m_character = _character;
        m_allowJobChange = _allowJobChange;
        m_owner = _panel;
        Refresh();
    }
    
    public static MABuildingWorkerPanelLine Create(Transform _holder, MACharacterBase _character, bool _allowJobChange = false, BCUIPanel _panel = null)
    {
        var prefab = Resources.Load<MABuildingWorkerPanelLine>("_Prefabs/Dialogs/MABuildingWorkerPanelLine");
        var go = Instantiate(prefab, _holder);
        var instance = go.GetComponent<MABuildingWorkerPanelLine>();
        instance.Activate(_character, _allowJobChange, _panel);
        instance.SetIsTitle(false);
        return instance;
    }
    
    public static MABuildingWorkerPanelLine Create(Transform _holder, string _col1, string _col2 = "", string _col3 = "")
    {
        var prefab = Resources.Load<MABuildingWorkerPanelLine>("_Prefabs/Dialogs/MABuildingWorkerPanelLine");
        var go = Instantiate(prefab, _holder);
        var instance = go.GetComponent<MABuildingWorkerPanelLine>();
        
        instance.m_name.text = $"{_col1}";
        instance.m_col2.text = $"{_col2}";
        instance.m_col3.text = $"{_col3}";
        
        instance.UpdateCol3(false, false);
        
        instance.SetIsTitle(false);
        
        //instance.m_actionButton.gameObject.SetActive(false);
        
        return instance;
    }
    
    public static MABuildingWorkerPanelLine CreateTitle(Transform _holder, string _col1, string _col2, string _col3)
    {
        var prefab = Resources.Load<MABuildingWorkerPanelLine>("_Prefabs/Dialogs/MABuildingWorkerPanelLine");
        var go = Instantiate(prefab, _holder);
        var instance = go.GetComponent<MABuildingWorkerPanelLine>();
        
        instance.m_name.text = $"<b>{_col1}</b>";
        instance.m_col2.text = $"<b>{_col2}</b>";
        instance.m_col3.text = $"<b>{_col3}</b>";
        
        instance.UpdateCol3(false, true);
        
        instance.SetIsTitle(true);
        
        //instance.m_actionButton.gameObject.SetActive(false);
        
        return instance;
    }
    
    public static MABuildingWorkerPanelLine CreateWorkerTitle(Transform _holder)
    {
        var prefab = Resources.Load<MABuildingWorkerPanelLine>("_Prefabs/Dialogs/MABuildingWorkerPanelLine");
        var go = Instantiate(prefab, _holder);
        var instance = go.GetComponent<MABuildingWorkerPanelLine>();
        
        instance.m_name.text = "<b>Name</b>";
        instance.m_col2.text = "<b>State</b>";
        instance.m_col3.text = "<b>Job</b>";
        
        instance.UpdateCol3(false, true);
        instance.SetIsTitle(true);
        //instance.m_actionButton.gameObject.SetActive(false);
        
        return instance;
    }
    
    public static MABuildingWorkerPanelLine CreateHeroTitle(Transform _holder)
    {
        var prefab = Resources.Load<MABuildingWorkerPanelLine>("_Prefabs/Dialogs/MABuildingWorkerPanelLine");
        var go = Instantiate(prefab, _holder);
        var instance = go.GetComponent<MABuildingWorkerPanelLine>();
        
        instance.m_name.text = "<b>Name</b>";
        instance.m_col2.text = "<b>Experience</b>";
        instance.m_col3.text = "<b>Health</b>";
        
        instance.UpdateCol3(false, true);
        
        instance.SetIsTitle(true);
        
        //instance.m_actionButton.gameObject.SetActive(false);
        
        return instance;
    }
}
