using UnityEngine;

public class BCAmmoStock : BCBase
{
    [KnackField] public string m_defaultAmmoBlock = null;
    
    public string GetAmmoBlock()
    {
        if(m_defaultAmmoBlock.IsNullOrWhiteSpace() == false)
            return m_defaultAmmoBlock;
            
        foreach(var hinge in Block.m_toHinges.GetComponentsInChildren<SnapHinge>())
        {
            if(hinge.m_isAmmo)
            {
                return GetAmmoBlock(hinge);
            }
        }
        return null;
    }
    
    private string GetAmmoBlock(SnapHinge _ammoHinge)
    {
        foreach(var block in Building.GetComponentsInChildren<Block>())
        {
            if(block == Block)
                continue;
            
            var hinges = block.GetComponentsInChildren<SnapHinge>();
            foreach(var hinge in hinges)
            {
                var d2 = (_ammoHinge.transform.position - hinge.transform.position).sqrMagnitude;
                
                if(d2 < 0.001)
                {
                    return block.BlockID;
                }
            }
        }
        return null;
    }
}