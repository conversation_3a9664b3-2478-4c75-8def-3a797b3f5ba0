using System.Collections.Generic;
using UnityEngine;

public class MAStockPositions : MonoBehaviour
{
    [System.Serializable]
    public class StockPositionPads
    {
        public Transform m_resourceHolder;
        public MeshRenderer m_pad;
        public int m_capacity = -1;
        
        private List<ReactPickup> m_allStock = new();
        
        public int Count => m_allStock.Count;
        
        public ReactPickup NextItem => m_allStock.Count > 0 ? m_allStock[0] : null;
        public NGCarriableResource GetNextProduct()
        {
            for(int i = m_allStock.Count-1; i >= 0; i--)
            {
                var item = m_allStock[i];
                if(item.m_contents.IsProduct && item.IsReady)
                    return item.m_contents;
            }
            return null;
        }

        public bool TryPopPickup(NGCarriableResource _type)
        {
            int removedFrom = -1;
            
            for(int i = m_allStock.Count-1; i >= 0; --i)
            {
                var pickup = m_allStock[i];
                if(pickup.m_contents == _type)
                {
                    pickup.gameObject.SetActive(false);
                    pickup.DestroyMe();
                        
                    m_allStock.RemoveAt(i);
                    removedFrom = i;
                    break;
                }
            }

            // Shift stock down
            if(removedFrom > -1)
            {
                for(int i = removedFrom; i < m_allStock.Count; i++)
                {
                    SetPosition(m_resourceHolder, m_allStock[i], i);
                }
            }
            
            return removedFrom != -1;
        }
        
        public float GetMaxY()
        {
            if(m_allStock.Count == 0)
                return 0;
                
            var item = m_allStock[m_allStock.Count-1]; 
            return (item.transform.position.y + item.TopOffset.y) - m_resourceHolder.transform.position.y;
        }
        
        public void PushPickup(ReactPickup _pickup)
        {
            Transform tr = _pickup.transform;
            tr.SetParent(m_resourceHolder);
            tr.localRotation = Quaternion.identity;
            m_allStock.Add(_pickup);
            
            SetPosition(m_resourceHolder, _pickup, m_allStock.Count-1);
        }
        
        public void DestroyAll()
        {
            foreach(var stock in m_allStock)
            {
                stock.gameObject.SetActive(false);
                stock.DestroyMe();
            }
            m_allStock.Clear();
        }
        
        private void SetPosition(Transform _holder, ReactPickup _pickup, int _position)
        {
            if(_position == 0)
                _pickup.transform.position = _holder.position + _pickup.BottomOffset;
            else
            {
                var previous = m_allStock[_position-1];
                _pickup.transform.position = previous.transform.position + previous.TopOffset + _pickup.BottomOffset;
            }
        }
        
        public int Space()
        {
            if(m_capacity == -1) 
                return -1;
            return m_capacity - Count;
        }
    }
    
    public List<StockPositionPads> m_pads =  new ();
    public float m_stockPositionScale = 1.5f;
    private int m_lastPopPadIndex = -1;
    public float m_maxY;
    
    void Awake()
    {
        foreach (Transform t in transform)
        {
            t.localScale = new Vector3(m_stockPositionScale, m_stockPositionScale, m_stockPositionScale);
            m_pads.Add(new StockPositionPads() { m_resourceHolder = t });
        }
        var block = GetComponentInParent<Block>();
        var inputPads = block?.m_toVisuals.Find("Pads2");
        if (inputPads != null)
        {
            var messrs = inputPads.GetComponentsInChildren<MeshRenderer>();
            int count = 0;
            for (var i = 0; i < messrs.Length; i++)
            {
                var mr = messrs[i];
                if (mr.name.Contains("_Pad"))
                    if (count < m_pads.Count)
                        m_pads[count++].m_pad = mr;
            }
        }
    }
    
    public NGCarriableResource GetNextProduct()
    {
        var pads = GetPadsSortedByItemCount();
        foreach(var pad in pads)
        {
            var item = pad.GetNextProduct();
            if(item != null)
                return item;
        }
        return null;
    }
    
    private List<StockPositionPads> GetPadsSortedByItemCount()
    {
        m_pads.Sort((a,b) => 
        { 
            if(a.Count > b.Count)
                return -1;
            return 1; 
        });
        return new List<StockPositionPads>();
    }
    
    
    private StockPositionPads GetBestPadWithSpace()
    {
        StockPositionPads bestPad = null;
        int bestCount = int.MaxValue;
        foreach(var pad in m_pads)
        {
            int padItemCount = pad.Count;
            if(padItemCount >= pad.m_capacity && pad.m_capacity >= 0) continue;
            if(padItemCount >= bestCount) continue;
            
            bestPad = pad;
            bestCount = padItemCount;
        }
        return bestPad;
    }

    private void UpdateMaxY()
    {
        m_maxY = 0;
        foreach(var pad in m_pads)
        {
            m_maxY = Mathf.Max(pad.GetMaxY(), m_maxY);
        }
    }
    
    public bool PushStock(ReactPickup _pickup)
    {
        StockPositionPads bestPad = GetBestPadWithSpace();
        
        if (bestPad == null)
        {
            Debug.LogError($"No Pad found for {_pickup.Contents.m_name}");
            return false;
        }

        bestPad.PushPickup(_pickup);
        
        UpdateMaxY();

        return true;
    }
    
    public bool PopStock(NGCarriableResource _resource)
    {
        for(int i = 1; i <= m_pads.Count; i++)
        {
            var index = (m_lastPopPadIndex + i) % m_pads.Count;
            if(m_pads[index].TryPopPickup(_resource))
            {
                m_lastPopPadIndex = index;
                UpdateMaxY();
                return true;
            }
        }
        Debug.LogError($"No Pad with Pickup found for {_resource.m_name}");
        return false;
    }
    
    public ReactPickup GetNextItem()
    {
        StockPositionPads bestPad = null;
        int highestPadCount = 0;
        foreach(var pad in m_pads)
        {
            int padItemCount = pad.Count;
            if(padItemCount <= highestPadCount) continue;

            bestPad = pad;
            highestPadCount = padItemCount;
        }
        
        if(bestPad != null)
        {
            return bestPad.NextItem;
        }
        return null;
    }

    public void ClearStock()
    {
        foreach (var pad in m_pads)
        {
            pad.DestroyAll();
        }
        
        UpdateMaxY();
    }
    
    public void SetCapacity(int _capacity)
    {
        foreach(var pad in m_pads)
        {
            pad.m_capacity = _capacity;
        }
    }

    public int GetStockSpace()
    {
        var total = 0;
        foreach (var pad in m_pads)
        {
            var space = pad.Space();
            if(space == -1) return -1;
            
            total += space;
        }
        return total;
    }
    
}


