using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class MAResearchDragConnector : MonoBehaviour
{
    private MAResearchItemUI m_item;
    MAUILine m_line;
    Canvas parentCanvas;
    private RectTransform rectTransform;
    private bool m_clickToAttach;
    void Start()
    {
        rectTransform = GetComponent<RectTransform>(); // Get the RectTransform component
    }

    void Update()
    {
        MAResearchItemUI overReseachItem = null;
        // Convert mouse position to Canvas coordinates
        Vector2 mousePos = Input.mousePosition;
  
        if (RectTransformUtility.ScreenPointToLocalPointInRectangle(transform.parent.GetComponent<RectTransform>(), mousePos, null, out Vector2 anchoredPos))
        {
            rectTransform.anchoredPosition = anchoredPos;
            var over = Utility.GetUIObjectAt(Input.mousePosition);
            if(over)
            {
                var overRI = over.GetComponentInParent<MAResearchItemUI>();
                if(overRI)
                    overReseachItem = overRI;
            }
        }

        if (m_clickToAttach == false)
        {
            if (Input.GetMouseButton(0) == false)
            {
                if (overReseachItem && overReseachItem != m_item)
                {
                    m_item.AddConnection(overReseachItem);
                    MAResearchManagerUI.Me.Refresh();
                }

                DestroyMe();
            }
        }
        else
        {
            if (Input.GetMouseButtonDown(0))
            {
                ReleasedOver(overReseachItem);
            }
        }
    }
    
    public void DestroyMe()
    {
        m_line.DestroyMe();
        Destroy(gameObject);
    }
    public void ReleasedOver(MAResearchItemUI _over)
    {
        if (_over == null)
        {
            DestroyMe();
            return;
        }
        if (_over != m_item)
        {
            m_item.AddConnection(_over);
            MAResearchManagerUI.Me.Refresh();
            Debug.LogError($"Assigned {_over.m_info.m_name} to {m_item.m_info.m_name}");
        }
        else
        {
            Debug.LogError($"Same Item");
        }
        DestroyMe();
    }
    void Activate(MAResearchItemUI _item, bool _clickToAttach)
    {
        m_item = _item;
        m_clickToAttach = _clickToAttach;
        m_line = MAUILine.Create(gameObject, null, m_item.gameObject, null, MAResearchManagerUI.Me.m_lineHolder, 1f, Color.red,true);
    }
    public static MAResearchDragConnector Create(Transform _holder, MAResearchItemUI _item, bool _clickToAttach)
    {
        var prefab = Resources.Load<MAResearchDragConnector>("_Prefabs/Research/MAResearchDragConnector");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_item, _clickToAttach);
        return instance;
    }
}
