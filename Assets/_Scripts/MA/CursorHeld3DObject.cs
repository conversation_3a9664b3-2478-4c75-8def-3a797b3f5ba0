using System;
using UnityEngine;

public class CursorHeld3DObject : MonoBehaviour
{
    [Range(0.1f, 20f)] [SerializeField] private float m_nearDistance = 15f;
    [Range(0.1f, 100f)] [SerializeField] private float m_planeDist = 45f;

    [SerializeField] private RectTransform m_2DContent = null;
    [SerializeField] private Transform m_3DContent = null;
    [SerializeField] private string m_failSafeCameraName = "TownCamera";
    public RectTransform Content2D => m_2DContent;
    public Transform Content3D => m_3DContent;
    private Canvas m_canvas = null;
    private Vector2 m_backupCardHolderLocalPos;

    public RectTransform m_cardHolder;
    
    public RectTransform Attach2DContent(RectTransform _2DContent)
    {
        gameObject.SetActive(true);
        _2DContent.parent = m_2DContent;
        return m_2DContent;
    }    
    
    public RectTransform AttachCard(NGDirectionCardBase _cardBase)
    {
        gameObject.SetActive(true);
        if(_cardBase != null)
        {
            m_backupCardHolderLocalPos = m_cardHolder.localPosition;
            _cardBase.transform.parent = m_cardHolder;
            m_cardHolder.localPosition = _cardBase.m_3DDragOffsetPos;
        }
        return m_cardHolder;
    }

    public void ResetCardHolder()
    {
        gameObject.SetActive(false);
        m_cardHolder.localPosition = m_backupCardHolderLocalPos;
    }    
    
    public Transform Attach3DContent(Transform _3DContent)
    {
        gameObject.SetActive(true);
        _3DContent.parent = m_3DContent;
        return _3DContent.parent;
    }    
    
    private void Awake()
    {
        gameObject.SetActive(false);
        m_backupCardHolderLocalPos = m_cardHolder.localPosition;
        SetupRefs();
    }

    private void OnEnable()
    {
        SetupRefs();
        UpdatePosition();
    }

    private void OnDisable()
    {
        m_cardHolder.localPosition = m_backupCardHolderLocalPos;
    }

    private void Start()
    {
        SetupRefs();
    }


    private void Update()
    {
        UpdatePosition();
    }
    
   /* [SerializeField] */private bool m_useRB = false;
    private void UpdatePosition()
    {
        /*Vector3 forward = m_transform.forward;
        Vector3 planeOrigin = (m_useRB ? GetComponent<Rigidbody>().position : m_transform.position) + forward * m_planeDist;
        Vector3 planeNormal = forward;
        Plane plane = new Plane(planeNormal, planeOrigin);
        plane.Raycast(ray, out var distance);*/
        Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);
        Vector3 point = ray.origin + (ray.direction * m_nearDistance);
        if(m_useRB)
        {
            GetComponent<Rigidbody>().MovePosition(point);
        }
        else
        {
            transform.position = point;
        }
    }
    
    private void SetupRefs()
    {
        if(m_canvas == null) m_canvas = m_2DContent.GetComponent<Canvas>();
        m_canvas.worldCamera = Camera.main;
    }
}
