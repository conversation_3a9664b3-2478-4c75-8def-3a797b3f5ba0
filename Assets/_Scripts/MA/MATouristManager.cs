using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;

public class MATouristManager : MonoSingleton<MATouristManager>
{
    public int m_maxNumTourists = 10;
    public Transform m_touristSpawnPoint;
    public Transform m_challangeSpawnPoint;
    public float m_touristSpawnInterval = 10f;
    float m_touristSpawnTimer = 0f;
    public int m_sessionMin = 3;
    public int m_sessionMax = 5;    
    public List<MATourist> m_tourists = new List<MATourist>();
    public List<NGDecoration> m_decorations = new List<NGDecoration>();
    public MABuilding m_touristRecievePadPrefab;
    bool m_loadComplete = false;
    public bool m_enableTourist = false;

    private void Start()
    {
        StartCoroutine(AfterLoad());
    }

    public IEnumerator AfterLoad()
    {
        yield return new WaitUntil(() => ( (GameManager.Me && GameManager.Me.LoadComplete)) );
        var decorations = NGDecorationInfoManager.Me.m_decorationHolder.GetComponentsInChildren<NGDecoration>(); 
        m_decorations.AddRange(decorations);
        m_touristSpawnTimer = m_touristSpawnInterval+Time.time;
        m_loadComplete = true;
    }

    void Update()
    {
        if (m_enableTourist == false) return;
        if (Time.time > m_touristSpawnTimer && m_tourists.Count < m_maxNumTourists && m_loadComplete)
        {
            m_touristSpawnTimer = m_touristSpawnInterval+Time.time;
            var pos = m_touristSpawnPoint.position.GroundPosition();
            var sessions = Random.Range(m_sessionMin, m_sessionMax);
            MATourist tourist = MATourist.Create(MAWorkerInfo.GetInfo("TouristMale"), pos, sessions, null);
            
            if(tourist != null)
            {        
                m_tourists.Add(tourist);
            }

        }
    }
    public void ToggleTourist(bool _enable)
    {
        m_enableTourist = _enable;
    }
}
