using System.Collections;
using System.Collections.Generic;
using UnityEngine;
[System.Serializable]
public class MATimer
{
    [Save] public float m_length;
    [Save] public float m_timer;
    public MATimer() {}
    public MATimer(float _length) => Set(_length);
    public void Set(float _length)
    {
        m_length = _length;
        Set();
    }

    public void Set() => m_timer = Time.time + m_length;
    public bool IsFinished =>Time.time >= m_timer;
    public float TimeRemaining => m_timer - Time.time;
}
