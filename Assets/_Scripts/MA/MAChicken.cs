using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

/*
 I have writing  Game in unity, I Have A prefab called MAChickenHen. This prefab contains A NavAgent, An animator, and a script called MAChicken. The script needs to control the behaviour of the chicken. So when the prefab is instantiated, 
 the chicken should walk around randomly. The chicken should also have a peck animation that plays every 5-10 seconds. The chicken should also have a peck sound that plays every time the peck animation plays. The chicken should also tend to group around a spawn point in a radius of m_chicken radius.
 the chicken needs to be aware of objects in the scene and avoid them. The chicken should also be aware of the player and run away from the player when the player gets too close. The chicken should also be aware of the player's dog and run away from the dog when the dog gets too close.
 */
public class MAChicken : MAAnimal
{
    public MAPossessionSettings m_customChickenPossessionSettings = null;
    
    override public string HumanoidType => "Chicken";

    public float m_proximityRadius = 2f;
    public float m_chickenRadius = 10f;
    
    private bool m_isFleeing = false;

    private MATimer m_updateRunAway = new MATimer();
    
    public override bool IsFleeing => m_isFleeing;

    public bool m_isSpooked = false;
    public float m_spookedExtraRange = 10.0f;
    
    public override string InitialState
    {
        get => string.IsNullOrWhiteSpace(m_gameState?.m_initialState) ? CharacterStates.Spawn : m_gameState.m_initialState;
        set => m_gameState.m_initialState = value;
    }
    
    public override string DefaultState
    {
        get => string.IsNullOrWhiteSpace(m_gameState?.m_defaultState) ? CharacterStates.RoamForTarget : m_gameState.m_defaultState;
        set => m_gameState.m_defaultState = value;
    }

    protected override void Awake()
    {
        base.Awake();
        
        m_kickVelocity = 4f;
        m_contentRootName = "";
        m_headMainBoneName = "CHICKEN_ Head";
        m_headBoneName = "CHICKEN_Comb1";
        m_neckBoneName = "CHICKEN_ Neck2";
        m_toeBoneName = "CHICKEN_ R Toe0";
    }

    protected override void Update()
    {
        base.Update();
        if (CharacterUpdateState != null && (CharacterUpdateState.State == CharacterStates.Idle ||
                                             CharacterUpdateState.State == CharacterStates.GuardLocation || 
                                             CharacterUpdateState.State == CharacterStates.Waiting ||
                                             m_isSpooked))
        {
            CheckProximity();
        }
    }
    
    public override void Activate(MACreatureInfo _info, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation,
        bool _addToCharacterLists = true)
    {
        base.Activate(_info, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);
    }

    public override void SetupPossessed(bool _possessedOn)
    {
        base.SetupPossessed(_possessedOn);
        if (m_customChickenPossessionSettings != null)
        {
            GameManager.Me.m_possessionSettingsOverride = m_customChickenPossessionSettings;
        }
    }
    bool m_isBeingDestroyed = false;
    public override bool CanInteract(NGMovingObject _chr)
    {
        if (_chr != this && _chr is not MAAnimal)
        {
            if (m_isBeingDestroyed)
            {
                Debug.LogWarning($"{GetType().Name} - [{m_ID}] - trying to interact with object but is being destroyed ");
                return false;
            }

            float distanceXZSq = transform.position.DistanceXZSq(_chr.transform.position);
            if (m_characterMaxInteractionScanRadius * m_characterMaxInteractionScanRadius > distanceXZSq)
            {
                if (c_defaultInteractRange * c_defaultInteractRange > distanceXZSq)
                {
                    return true;
                }
            }
        }
        return false;
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        m_isBeingDestroyed = true;
    }

    protected override void InitialiseGameState()
    {
        base.InitialiseGameState();
        m_gameState.m_creatureInfoName = "Chicken";
        m_gameState.m_creatureInfoName = m_creatureInfo.m_name;
    }

    void CheckProximity()
    {
        var spookedRange = m_isSpooked ? m_spookedExtraRange : 0f;
        if (m_updateRunAway.IsFinished)
        {
            NGMovingObject fleeFrom = null;
            if(CanCheckForThreats)
            {
                OnCheckedThreats();
                /*
                 // If we want to do this, i'd suggest having the attacker applying the state directly
                 MADog dog = _o as MADog;
                   if (dog != null && dog.HasCombo())
                   {
                       var attack = dog.CurrentAttack ?? dog.GetNextAttackAvailableInCombo();
                       extraRange = attack?.AttackRadius ?? 0f;
                   }
                }*/

                var nearChrs = AgentPresenceManager.Me.GetTemporaryArrayOfCharactersNear(transform.position, m_proximityRadius + spookedRange, out var chrs, this);
                for (int i = 0; i < nearChrs; ++i)
                {
                    var attacker = chrs[i];
                    if (attacker == null || attacker is MAChicken) continue;
                    
                    fleeFrom = attacker;
                    break;
                }
            }
            
            if (fleeFrom != null)
            {
                m_isFleeing = true;
                RunAwayFrom(fleeFrom.m_transform.position);
            }
            else
            {
                m_isFleeing = false;
                m_gameState.m_speed = m_gameState.WalkSpeed;
            }
        }
    }
    
    private void RunAwayFrom(Vector3 threatPosition)
    {
        m_updateRunAway.Set(0.5f);
        m_gameState.m_speed = m_gameState.AttackSpeed;
        var seflPos = m_transform.position;
        Vector3 directionAway =seflPos - threatPosition;
        Vector3 newTarget = directionAway.normalized * Mathf.Clamp(m_chickenRadius, 1, m_chickenRadius);
        if (GlobalData.IsPerfectNavable(GlobalData.Me.GetNavAtPoint(newTarget)) == false)
        {
            var r = ScanForNavigableRandomPos(seflPos, 1f, m_chickenRadius, false, GlobalData.AllowNavType.AnyPerfectNav);
            if(r != null) newTarget = (Vector3)r;
        }

        m_nav.SetNavCosts(NavigationCosts);
            
        SetMoveToPosition(newTarget);
        
/*        NavMeshHit hit;
        if (NavMesh.SamplePosition(newTarget, out hit, m_chickenRadius, NavMesh.AllAreas))
        {
            navAgent.SetDestination(hit.position);
            animator.SetBool("isRunning", true);
        }*/
    }

    public void RunAwayFromThreat(Vector3 threatPosition)
    {
        m_isFleeing = true;
        RunAwayFrom(threatPosition);
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(MAChicken))]
public class MAChickenEditor : MACharacterBaseEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
    }
}
#endif