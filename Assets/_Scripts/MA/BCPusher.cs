using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCPusher : BCStockBase
{
    public float m_pushForce = 5f;
    public GameObject m_pushBlock;
    public Transform m_spawnPoint;
    public Animator m_anim;
    override protected void Awake()
    {
        base.Awake();
        var block = GetComponent<Block>();
        if (block)
        {
            var pb = block.m_toVisuals.Find("Pusher/PushCube");
            if (m_pushBlock == null && pb)
                m_pushBlock = pb.gameObject;
            var sp = block.m_toVisuals.Find("Pusher/SpawnPoint");
            if (m_spawnPoint == null && sp)
                m_spawnPoint = sp;
            m_anim = GetComponentInChildren<Animator>();
        }
    }

    override public int CreateStockBlock(NGCarriableResource _resource, int _count = 1)
    {
        for(int i = 0; i < _count; i++)
        {
            MABuildingSupport.CreatePickupFromResource(m_building, _resource, m_spawnPoint, false);
        }
        m_anim.SetTrigger("Fire");
        return _count;
    }
    //override public ReactPickup GetItemForDelivery() => null;

    public void AnimFireNow()
    {
        var objects = m_spawnPoint.GetComponentsInChildren<NGMovingObject>();
        foreach (var o in objects)
        {
            var pickup = o as ReactPickup;
            if (pickup)
            {
                pickup.SetCollisionStyle(NGMovingObject.COLLISIONSTYLE.DEFAULT);
                var item = m_stock.Find(pickup.m_contents);
                if (item != null && item.m_stock > 0)
                    item.Stock--;
            }
            var rb = o.GetComponentInChildren<Rigidbody>();
            if (rb)
            {
                rb.isKinematic = false;
            }
            else
            {
                rb = pickup.gameObject.AddComponent<Rigidbody>();
            }
            rb.AddForce(m_spawnPoint.up*m_pushForce, ForceMode.VelocityChange);

            o.transform.SetParent(GlobalData.Me.m_pickupsHolder, true);
        }
    }


}
