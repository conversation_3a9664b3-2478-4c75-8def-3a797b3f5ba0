using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using NUnit.Framework;
using UnityEngine;
using Cans.Analytics;
using NUnit.Framework.Constraints;
using AnalyticsEvent = Cans.Analytics.AnalyticsEvent;

public class MAGameFlow
{
    public const string MainBlockName = "Main"; 

    public static List<MAGameFlow> m_activeFlows = new ();
    public static Dictionary<string, List<NGBusinessFlow>> m_activeFlowss = new ();
    public static object UpdatingControlObject { get { return MAGameFlow.m_updatingFlow?.m_controlObject?.m_value; } }
    public static MAGameFlow m_updatingFlow = null;
    public static bool m_pauseAllFlows = false;
    public static MAGameFlow CurrentFlow => m_activeFlows.Count > 0 ? m_activeFlows[0] : null;
    public static MAGameFlow GetFlow(string _blockName) { return m_activeFlows.Find(o => _blockName == o.m_blockName); }
    
    public enum GameFlowState
    {
        WaitingToActivate,
        WaitingForTrigger,
        Triggered,
        WaitingForGiftEnvelope,
        TriggeredGiftEnvelope,
        WaitingRecieveGift,
        WaitingForDecision,
        WaitingForOtherFlow,
        WaitForEvent,
        WaitingForExitFunction,
        WaitingForAudio,
        WaitingForMessage,
        WaitingForFTP
    }
    public MAGameFlow(NGBusinessFlow _flow, int _listIndex = 0, ControlObjectContainer _controlledObject = null)
    {
        Activate(_flow, _listIndex, _controlledObject);
    }
    public GameFlowState m_state = GameFlowState.WaitingForTrigger;
    public NGBusinessFlow m_flow;
    public int m_listIndex = 0;
    public string m_blockIndex;
    public List<NGBusinessDecision> m_decisions = new();
    public List<NGBusinessGift> m_takeAllGifts = new();
    public List<NGBusinessGift> m_chooseGifts = new();
    public int m_chooseMaxGifts;
    public float m_giftCostMultiplier;

    public string m_enterFunctions;    
    public string m_exitFunctions;    
    public string m_enterWaitForTrigger;
        
    public string m_tutorialPhase;
    
    public string m_message;
    public string m_blockName;
    
    public static bool m_audioPlaying = false;
    
    private MAGameFlowDialog m_dialog;
    private bool m_doneExitFunction = false;
    private int m_decisionWaitCount = 0;
    public string m_waitingForOtherFlow = "";
    public ControlObjectContainer m_controlObject;
    public Action m_clickedGameFlowDialogAction;
    private bool m_atEndOfFlow = false;
    public int GiftsCount => m_takeAllGifts.Count + m_chooseMaxGifts;
    public GameFlowState State { get { return m_state;  } set { m_state = value; } }

    public static MAGameFlow StartFlow(string _flowName, int _startIndex = 0, ControlObjectContainer _controlObject = null)
    {
        if(NGBusinessFlow.s_flowsDict.TryGetValue(_flowName,out var flow) == false) return null;
        if(flow.IsNullOrEmpty()) return null;
        if(m_activeFlowss.ContainsKey(flow[0].m_blockName) == false)
        {
            m_activeFlowss.Add(flow[0].m_blockName, flow);
        }
        return new MAGameFlow(flow[_startIndex], _startIndex, _controlObject);
    }
    public static MAGameFlow StartFlow(List<NGBusinessFlow> _flow, int _startIndex = 0, ControlObjectContainer _controlObject = null)
    {
        if(_flow.IsNullOrEmpty()) return null;
        if(m_activeFlowss.ContainsKey(_flow[0].m_blockName) == false)
        {
            m_activeFlowss.Add(_flow[0].m_blockName, _flow);
        }
        return new MAGameFlow(_flow[_startIndex], _startIndex, _controlObject);
    }
    //activates the flow
    public void Activate(NGBusinessFlow _flow, int _listIndex, ControlObjectContainer _controlObject = null)
    {
        m_controlObject = _controlObject;
        GameManager.Me.RegisterFlowIndex(_flow.m_indexer);
        m_flow = _flow;
        if(m_dialog == null)    
            m_dialog = MAGameFlowDialog.Create(this);
        m_dialog.ShowNothing = true;
        m_listIndex = _listIndex;
        State = GameFlowState.WaitingToActivate;
        m_blockIndex = m_flow.m_blockIndex;
        m_decisions = new();
        if (m_flow.m_businessDecision.IsNullOrWhiteSpace() == false && m_flow.m_businessDecision.Equals(NGBusinessDecision.None) == false)
        {
            foreach (var d in m_flow.m_businessDecision.Split(';', '\n', '|'))
            {
                var decision = NGBusinessDecision.GetInfo(d);
                if (decision == null)
                {
                    Debug.LogError(MAParserSupport.DebugColor($"{m_blockName}[{m_blockIndex}]: no such decision as '{d}'"));
                    continue;
                }
                var tDecision = (NGBusinessDecision)decision.Clone();
                tDecision.m_targetValue*=m_flow.m_decisionValueMultiplier;
                m_decisions.Add(tDecision);
            }
        }

        m_takeAllGifts = GetBusinessGifts(m_flow.m_takeAllGifts);
        m_chooseGifts = GetBusinessGifts(m_flow.m_chooseGifts);
        m_chooseMaxGifts = m_flow.m_chooseMaxGifts;
        m_giftCostMultiplier = m_flow.m_giftCostMultiplier;
        m_enterFunctions = m_flow.m_enterFunctions;
        m_exitFunctions = m_flow.m_exitFunctions;
        m_enterWaitForTrigger = m_flow.m_enterWaitForTrigger;

        m_tutorialPhase = m_flow.m_tutorialPhase;
        m_message = m_flow.m_message;
        m_blockName = m_flow.m_blockName;
        //m_activeFlows.FindAll(o => o.m_flow == m_flow && o.m_listIndex == _listIndex).ForEach(o => m_activeFlows.Remove(o));
        if(m_activeFlows.Contains(this) == false)
            m_activeFlows.Add(this);
    }
    public List<MAGameFlow> m_triggerFlows = new();
    void UpdateTriggerFlows()
    {
        for (var i = m_triggerFlows.Count - 1; i >= 0; i--)
        {
            var tf = m_triggerFlows[i];
            tf.Update();
            if (tf.m_atEndOfFlow)
            {
                m_triggerFlows.Remove(tf);
            }
        }
    }
    public void Update()
    {
        if(m_flow.m_pauseThisFlow)
            return;
        UpdateTriggerFlows();
        if(m_flow.m_debugBreakHere)
        {
            Debug.DebugBreak();
        }
        m_updatingFlow = this;
        switch (State)
        {
            case GameFlowState.WaitingToActivate:
                if (m_enterWaitForTrigger.IsNullOrWhiteSpace() == false)
                {
                    if (MAParserSupport.TryParse(m_enterWaitForTrigger, out var triggered, $"Flow Trigger {m_blockName}[{m_blockIndex}") == false)
                    {
                        Debug.LogError(MAParserSupport.DebugColor($"[{m_enterWaitForTrigger}] Not Implemented Flow Trigger {m_blockName}[{m_blockIndex}]"));
                        break;
                    }
                    //var triggered =MAKnackSupport.CallFuncFromString(m_enterWaitForTrigger, $"Flow Trigger[{m_index}");
                    if(triggered == false)
                        break;
                }
                State = GameFlowState.Triggered;
                break;
            case GameFlowState.Triggered:
                if(ActivateEnterFunctions())
                    break;
                if(ActivateMessage())
                    break;
                if(ActivateDecision())
                    break;
                if (ActivateGifts())
                    break;
                if (ActivateTutorialPhase())
                    break;
                //If we reach here then we must move to the next flow
                MoveToNextFlow();
                break;
            case GameFlowState.WaitingForDecision:
                StateWaitingForDecision();
                break;
            case GameFlowState.WaitingForGiftEnvelope:
                break;
            case GameFlowState.TriggeredGiftEnvelope:
                NGBusinessRewardsSequence.Create(m_takeAllGifts);
                NGBusinessGiftsPanel.CreateOrCall(m_takeAllGifts, m_chooseGifts, m_chooseMaxGifts);
                State = GameFlowState.WaitingRecieveGift;
                break;
            case GameFlowState.WaitingRecieveGift:
                break;
            case GameFlowState.WaitForEvent:
                if(MAMessageManager.Me.m_waitForEvents.Count == 0)
                {
                    MoveToNextFlow();
                }
                break;
            case GameFlowState.WaitingForOtherFlow:
                if(m_activeFlows.Find(o => o.m_flow.m_blockName == m_waitingForOtherFlow) == null)
                {
                    m_waitingForOtherFlow = "";
                    State = GameFlowState.Triggered;
                }
                break;
            case GameFlowState.WaitingForMessage:
                if (MAMessage.IsReadyToDisplay())
                {
                    m_message = "";
                    State = GameFlowState.Triggered;
                }
                break;
        }
        m_updatingFlow = null;
    }
    
//    public void TappedEnvelope()
    public void TappedBanner(MAGameFlow _flow)
    {
        m_message = "";
        State = GameFlowState.Triggered;
    }

    bool ActivateEnterFunctions()
    {
        if (m_enterFunctions.IsNullOrWhiteSpace()) return false;
 
        if(MAParserSupport.TryParse(m_enterFunctions, out var triggered, $"Flow Trigger {m_blockName}[{m_blockIndex}]") == false)
        {
            Debug.LogError(MAParserSupport.DebugColor($"[{m_enterFunctions}] Not Implemented Flow Trigger {m_blockName}[{m_blockIndex}]"));
            return false;
        }

        m_enterFunctions = "";
        return false;
    }
    bool ActivateMessage()
    {
        if (m_message.IsNullOrWhiteSpace() == false)
        {
            if (m_flow.m_toMessage == null)
            {
                Debug.LogError(MAParserSupport.DebugColor($"No message function for {m_blockName}[{m_blockIndex}]"));
                return false;
            }

            if (MAMessage.IsReadyToDisplay() == false)
            {
                return true;
            }
            m_flow.m_toMessage.Display();
            State = GameFlowState.WaitingForMessage;
            return true;
        }
        return false;
    } 

    bool ActivateDecision()
    {
        if (m_decisions.Count > 0)
        {
            foreach (var d in m_decisions)
            {
                d.Activate();
            }
            m_decisionWaitCount = 0;
            m_dialog.TriggerTheDecision();
            State = GameFlowState.WaitingForDecision;
            return true;
        }
        return false;
    }
    bool ActivateGifts()
    {
        if (m_takeAllGifts.IsNullOrEmpty() == false || m_chooseGifts.IsNullOrEmpty() == false)
        {
            m_dialog.TriggerTheEnvelope();
            State = GameFlowState.WaitingForGiftEnvelope;
            return true;
        }
        return false;
    }
    bool ActivateTutorialPhase()
    {
        return false;
        if (m_tutorialPhase.IsNullOrWhiteSpace() == false)
        {
            if(MAParserSupport.TryParse(m_tutorialPhase, out var result, $"Flow Trigger {m_blockName}[{m_blockIndex}]") == false)
            {
                Debug.LogError(MAParserSupport.DebugColor($"[{m_tutorialPhase}] Not Implemented Flow Trigger {m_blockName}[{m_blockIndex}]"));
                return false;
            }
            //var result = MAKnackSupport.CallFuncFromString(m_tutorialPhase, $"GameFlow Tutorial[{m_index}");
            if (result)
            {
                State = GameFlowState.WaitingForOtherFlow;
                return true;
            }
            return result;
        }
        return false;
    }

    bool StateWaitingForDecision(bool forceComplete = false)
    {
        bool isComplete = true;
        foreach(var d in m_decisions)
        {
            var value = d.GetValue();
            if (value > 0)
            {
                isComplete = false;
                break;
            }            
        }
        if (isComplete || forceComplete)
        {
            if(m_decisionWaitCount == 0)
                Debug.LogError($"The business at {m_flow.m_blockName}[{m_flow.m_blockIndex}] decision completed immediately. This is not expected.");
            m_decisions = new();
            m_dialog.ShowNothing = true;
            State = GameFlowState.Triggered;
            return true;
        }
        m_decisionWaitCount++;

        return false;
    }
    public void TutorialPhaseEnded()
    {
        if (State == GameFlowState.WaitingForOtherFlow)
        {
            m_waitingForOtherFlow = "";
           // m_tutorialPhase = "";
            State = GameFlowState.Triggered;
        }
    }

    public void SetWaitForTutorial(bool _isWaitFor)
    {
        if(_isWaitFor)
            State = GameFlowState.WaitingForOtherFlow;
    }

    public static void UpdateFlows()
    {
        if(m_pauseAllFlows) return;
        for (var i = m_activeFlows.Count - 1; i >= 0; i--)
        {
            var f = m_activeFlows[i];
            f.Update();
        }
    }

    public static void UpdateTriggers()
    {
        foreach (var d in NGBusinessFlow.s_triggerFlows)
        {
            if(d.Value.Count == 0) continue;
            if(m_activeFlows.Find(o => o.m_flow.m_blockName == d.Key) != null) continue;
            if(MAParserSupport.TryParse(d.Value[0].m_enterWaitForTrigger, out var result, $"Flow Trigger {d.Key}") == false)
            {
                Debug.LogError(MAParserSupport.DebugColor($"[{d.Value}] Not Implemented Flow Trigger {d.Key}"));
                continue;
            }

            if (result)
            {
                StartFlow(d.Value);
            }
        }
    }


    public void TriggerBannerMessage()
    {
        MAGameFlow.m_updatingFlow.State = MAGameFlow.GameFlowState.WaitForEvent;
     //   m_dialog.TriggerBannerMessage();
        
    }
    public int GetDeterministicRand(NGBusinessGift _gift, int _maxInclusive)
    {
        int cardIndex = Mathf.Max(0, m_takeAllGifts.IndexOf(_gift));
        var seed = ( m_flow.m_index + cardIndex);
        var rand = new System.Random(seed.GetHashCode());
        return (int) (rand.NextDouble() * _maxInclusive);
    }
    public bool GiftRecieved(NGBusinessGift _gift, bool _isChooseGift)
    {
        if (_isChooseGift)
        {
            if (m_chooseGifts.Remove(_gift) == false)
            {
//                Debug.LogError($"Trying to remove choose gift '{_gift.m_name}' from choose gifts but it was not found");
            }
        }
        else
        {
            if(m_takeAllGifts.Remove(_gift) == false)
            {
  //              Debug.LogError($"Trying to remove take all gift '{_gift.m_name}' from take all gifts but it was not found");
            }
        }
        bool isLastCard = (m_takeAllGifts.Count == 0 && m_chooseGifts.Count == 0);
        if (isLastCard)
        {
            MoveToNextFlow();
            return true;
        }
        return false;
    }

    public void ClickedEnvelope()
    { 
        State = GameFlowState.WaitingRecieveGift;
        AddNewBusinessRewardsSeq(OnRewardSeqComplete);
    }
    
    bool CheckOkayToMoveToNextFlow()
    {
        var currentState = State;
        if (m_decisions.Count > 0)
        {
            Debug.LogError($"Flow {m_blockName}[{m_blockIndex}]: still has decisions to make");
            return false;
        }
        if (m_takeAllGifts.Count > 0)
        {
            Debug.LogError($"Flow {m_blockName}[{m_blockIndex}]: still has take all gifts to take");
            return false;
        }
        if (m_chooseGifts.Count > 0)
        {
            Debug.LogError($"Flow {m_blockName}[{m_blockIndex}]: still has choose gifts to take");
            return false;
        }
        if(m_exitFunctions.IsNullOrWhiteSpace() == false)
        {
            if(MAParserSupport.TryParse(m_exitFunctions, out var triggered, $"Flow Trigger {m_blockName}[{m_blockIndex}]") == false)
            {
                Debug.LogError(MAParserSupport.DebugColor($"[{m_exitFunctions}] Not Implemented Flow Trigger {m_blockName}[{m_blockIndex}]"));
                return false;
            }
            if (triggered)
            {
                m_exitFunctions = "";
                if (State != currentState)
                    return false;
            }
            else
            {
                State = GameFlowState.WaitingForExitFunction;            
                return false;
            }
        }

  //      if (m_audioPlaying)
  //      {
  //          State = GameFlowState.WaitingForAudio;            
  //          return false;
  //      }
        if(MAMessageManager.Me.m_waitForEvents.Count > 0)
        {
            State = GameFlowState.WaitForEvent;
            return false;
        }
        return true;
    }
    void MoveToNextFlow()
    {
        if (CheckOkayToMoveToNextFlow() == false)
        {
            return;
        }
        var nextListIndex = m_listIndex + 1;
        var checkIfInActive = m_activeFlows.Find(o => o == this);
        if( checkIfInActive == null)
        {
            Debug.LogError($"Flow {m_blockName}:[{m_blockIndex}]: could not find self in active flows");
            return;
        }
        if(m_activeFlowss.TryGetValue(m_flow.m_blockName, out var flows) == false)
        {
            Debug.LogError($"Flow {m_blockName}:[{m_blockIndex}]: could not find active flows for block '{m_flow.m_blockName}'");
            return;
        }
        if(nextListIndex >= flows.Count)
        {
            var waitingForFlow = m_activeFlows.Find(o =>  o.m_waitingForOtherFlow == m_flow.m_blockName && o.State == GameFlowState.WaitingForOtherFlow);
            if(waitingForFlow != null)
            {
                waitingForFlow.TutorialPhaseEnded();
            }

            switch (m_flow.m_flowType)
            {
                case "Flow":
                    break;
                case "TriggerRepeat":
                    break;
                case "TriggerOnce":
                    NGBusinessFlow.s_triggerFlows.Remove(m_blockName);
                    break;
            }
            m_activeFlows.Remove(this);
            m_activeFlowss.Remove(m_flow.m_blockName);
            if(m_dialog)
                m_dialog.DestroyMe();
            m_atEndOfFlow = true;
            return;
        }
        Activate(m_activeFlowss[m_flow.m_blockName][nextListIndex], nextListIndex, m_controlObject);
    }
    #region RewardSequence
    public struct RewardSequenceItem
    {
        public string m_gifts;
        public string m_giftChoices;
        public int m_ecol;
    }
    private List<RewardSequenceItem> m_rewardSequenceList;

     public void AddNewBusinessRewardsSeq(System.Action<List<NGBusinessDecisionCard>> _oncomplete)
    {
        /*if (m_rewardSequenceList == null)
            m_rewardSequenceList = new List<RewardSequenceItem>();
        
        var gifts = new List<NGBusinessGift>(m_takeAllGifts);
        gifts.AddRange(m_chooseGifts);

        var seq = NGBusinessRewardsSequence.Create(gifts, this);
        if (gifts.Count == 0)
        {
            Debug.LogError("Gifts should never be empty at this point!");
        }
        else
        {
            seq.OnComplete += _oncomplete;
        }*/
    }
     public void OnRewardSeqComplete(List<NGBusinessDecisionCard> _cards)
     {
        //NGBusinessGiftsPanel.CreateOrCall(this);
     }
     #endregion

 
    
    public void SendBusinessGiftInteractionAnalyticsEvent(NGBusinessGift _gift, string action)
    {
        if(GameManager.IsVisitingInProgress == false)
        {
            AnalyticsEvent businessGiftInteraction = AnalyticsManager.Me.Events.BusinessGiftInteraction;
            businessGiftInteraction.AddParameter(EventParams.businessGiftFlowIndex, m_flow.m_index);
            businessGiftInteraction.AddParameter(EventParams.businessGiftItemCount, m_takeAllGifts.Count);
            businessGiftInteraction.AddParameter(EventParams.businessGiftChosenReward, _gift.m_name);
            businessGiftInteraction.AddParameter(EventParams.businessGiftType, _gift.Type.ToString());
            businessGiftInteraction.AddParameter(EventParams.businessGiftQuantity, _gift.m_quantity);
            businessGiftInteraction.AddParameter(EventParams.businessGiftAction, action);
            AnalyticsManager.Me.LogEvent(businessGiftInteraction);
        }
    }
    public static List<NGBusinessGift> GetBusinessGifts(string _giftsString)
    {
        var results = new List<NGBusinessGift>();
        if (_giftsString.IsNullOrWhiteSpace())
            return results;
        var gifts = _giftsString.Split(';', '|', '\n');
        foreach (var g in gifts)
        {
            var found = NGBusinessGift.s_gifts.Find(o => o.m_name == g);
            if (found != null)
                results.Add(found);
        }
        return results;
    }
    public Sprite GetAdvisorSprite()
    {
        var advisor = NGBusinessAdvisor.GetInfo(m_flow.m_businessAdvisor);
        if (advisor != null)
        {
            var sprite = advisor.PortaitSprite;
            return sprite;
        }
        return null;
    }
    public void Abort()
    {
        m_dialog?.DestroyMe();
        m_activeFlows.Remove(this);
        m_activeFlowss.Remove(m_flow.m_blockName);
    }

    public static void Abort(string _blockName)
    {
        var flow = m_activeFlows.Find(o => o.m_blockName == _blockName);
        if(flow != null)
            flow.Abort();
    }

    #region SAVE&LOAD
    public class ControlObjectContainer
    {
        public string m_type;
        public string m_uid;
        public object m_value;
        
        public ControlObjectContainer(MAQuestBase _quest)
        {
            m_type = "Quest";
            m_uid = _quest.m_id;
            m_value = _quest;
        }

        public static ControlObjectContainer Load(SaveLoadGameFlow _s)
        {
            if(_s.m_controlObjectType.IsNullOrWhiteSpace())
                return null;
                
            switch(_s.m_controlObjectType)
            {
                case "Quest":
                    var quest = MAQuestManager.Me.GetQuest(_s.m_controlObjectUID);
                    if(quest != null)
                        return new ControlObjectContainer(quest); 
                    break;
            }
            return null;
        }
        
        public void Save(SaveLoadGameFlow _s)
        {
            _s.m_controlObjectType = m_type;
            _s.m_controlObjectUID = m_uid;
        }
    }
    [System.Serializable]
    public class SaveLoadGameFlow
    {
        public SaveLoadGameFlow(MAGameFlow _flow)
        {
            m_blockName = _flow.m_blockName;
            foreach(var d in _flow.m_decisions)
            {
                m_decisionsLeft+=$"{d.m_name},{d.m_initialValue},{d.m_isClosing}|";
                //m_decisions.Add((d.m_name, d.m_initialValue));
            }
            m_decisionsLeft = m_decisionsLeft.TrimEnd('|');
            m_blockIndex = _flow.m_blockIndex;
            m_state = _flow.State;
            foreach (var ta in _flow.m_takeAllGifts) m_takeAllGiftsLeft+=$"{ta.m_name}|";
            m_takeAllGiftsLeft = m_takeAllGiftsLeft.TrimEnd('|');
            foreach (var ta in _flow.m_chooseGifts) m_chooseGiftsLeft+=$"{ta.m_name}|";
            m_chooseGiftsLeft = m_chooseGiftsLeft.TrimEnd('|');
            m_waitingForOtherFlow = _flow.m_waitingForOtherFlow;
            m_enterFunctions = _flow.m_enterFunctions;
            m_exitFunctions = _flow.m_exitFunctions;
            m_lastFlowIndex = _flow.m_listIndex;
            _flow.m_controlObject?.Save(this);
            
            if(_flow.m_clickedGameFlowDialogAction != null)
            {
                var d = _flow.m_clickedGameFlowDialogAction;
                var mi = d.Method;
                m_clickedGameFlowDialogActionName = mi.Name;
                var nn = mi.DeclaringType.Name;
                m_clickedGameFlowDialogActionClass = mi.DeclaringType.FullName;
            }
        }

        public string m_blockName;
        public string m_blockIndex;
        public GameFlowState m_state; 
        public string m_decisionsLeft = "";
        public string m_takeAllGiftsLeft = "";
        public string m_chooseGiftsLeft = "";
        public string m_waitingForOtherFlow = "";
        public string m_enterFunctions;
        public string m_exitFunctions;
        public int m_lastFlowIndex;
        public string m_clickedGameFlowDialogActionName;
        public string m_clickedGameFlowDialogActionClass;
        public string m_controlObjectType;
        public string m_controlObjectUID;
    }
    public string Save()
    {
        var save = new SaveLoadGameFlow(this);
        var json = JsonUtility.ToJson(save);
        //var load = (SaveLoadGameFlow)JsonUtility.FromJson(json, typeof(SaveLoadGameFlow));
        return json; 
    }

    public void DestroyMe()
    {
        m_dialog?.DestroyMe();
    }

    public IEnumerator PostLoad()
    {       
        while(GameManager.Me.LoadComplete == false)
        {
            yield return null;
        }
        
        switch(State)
        {
            case GameFlowState.WaitingToActivate:
            case GameFlowState.WaitingForTrigger:
            case GameFlowState.Triggered:
            case GameFlowState.WaitForEvent:
                break;
            case GameFlowState.TriggeredGiftEnvelope:
            case GameFlowState.WaitingForGiftEnvelope:
                m_dialog.TriggerTheEnvelope();
                break;
            case GameFlowState.WaitingRecieveGift:
                ClickedEnvelope();
                break;
            case GameFlowState.WaitingForDecision:
                m_dialog.TriggerTheDecision();
                break;
        }
    }

    public static void Load(string _what)
    {
        var load = (SaveLoadGameFlow)JsonUtility.FromJson(_what, typeof(SaveLoadGameFlow));
        if (NGBusinessFlow.s_flowsDict.TryGetValue(load.m_blockName, out var flows) == false)
        {
            Debug.LogError($"Flow[{load.m_blockIndex}]: could not find active flows for block '{load.m_blockName}'");
            return;
        }
        var flow = flows.Find(f => f.m_blockIndex == load.m_blockIndex);
        if(flow == null)
        {
            Debug.LogError($"Flow[{load.m_blockIndex}]: could not find flow with index '{load.m_blockIndex}'");
            return;
        }
        if(m_activeFlowss.ContainsKey(flow.m_blockName) == false)
        {
            m_activeFlowss.Add(flow.m_blockName, flows);
        }
        var gameFlow = new MAGameFlow(flow);
        gameFlow.State = load.m_state;

        gameFlow.m_decisions = new();
        foreach(var d in load.m_decisionsLeft.Split('|'))
        {
            if (d.IsNullOrWhiteSpace())
                continue;
            var bits = d.Split(',');
            if (bits.Length != 2 && bits.Length != 3)
            {
                Debug.LogError($"Flow[{load.m_blockIndex}]: could not parse decision '{d}'");
                continue;
            }
            var decision = NGBusinessDecision.GetInfo(bits[0]);
            if (decision == null)
            {
                Debug.LogError($"Flow[{load.m_blockIndex}]: could not find decision '{bits[0]}'");
                continue;
            }
            floatinv.TryParse(bits[1], out var value);
            decision.m_initialValue = value;

            if(bits.Length == 3)
            {
                bool.TryParse(bits[2], out var isClosing);
                decision.m_isClosing = isClosing;
            }
            gameFlow.m_decisions.Add(decision);
        }
        gameFlow.m_takeAllGifts = GetBusinessGifts(load.m_takeAllGiftsLeft);
        gameFlow.m_chooseGifts = GetBusinessGifts(load.m_chooseGiftsLeft);
        gameFlow.m_waitingForOtherFlow = load.m_waitingForOtherFlow;
        gameFlow.m_enterFunctions = load.m_enterFunctions;
        gameFlow.m_exitFunctions = load.m_exitFunctions;
        gameFlow.m_listIndex = load.m_lastFlowIndex;
        gameFlow.m_controlObject = ControlObjectContainer.Load(load);
        
        if (load.m_clickedGameFlowDialogActionClass.IsNullOrWhiteSpace() == false)
        {
            var oldflow = m_updatingFlow;
            m_updatingFlow = gameFlow;
            MAParser.SetGameFlowDialogClick(load.m_clickedGameFlowDialogActionName);
            m_updatingFlow = oldflow;
        }
        NGManager.Me.StartCoroutine(gameFlow.PostLoad());
 
    }
    
    public static void SaveAll(ref SaveContainers.SaveCountryside _s)
    {
        foreach (var f in m_activeFlows)
        {
            var saveString = f.Save();
            _s.m_saveBusinessDecision.m_activeGameFlows.Add(saveString);
        }
    }
    
    public static void LoadAll(SaveContainers.SaveCountryside _l)
    {
        foreach (var f in _l.m_saveBusinessDecision.m_activeGameFlows)
        { 
            Load(f);
        }
    }
    public static void LateInitialise()
    {
        if (NGBusinessDecisionManager.Me?.m_flowStarted == false)
            NGBusinessDecisionManager.Me?.StartFlow();
    }
    #endregion SAVE&LOAD

    #region Support
    //Complete the current flow
    //SYNTAX: complete or complete=<blockName>
    private static DebugConsole.Command s_complete = new DebugConsole.Command("complete", _s =>
    {
        if(m_activeFlows.Count == 0)
        {
            Debug.LogError("No active flows");
            return;
        }
        var flow = m_activeFlows[0];
        if (_s.IsNullOrWhiteSpace() == false)
        {
            flow = m_activeFlows.Find(o => o.m_flow.m_blockName.Equals(_s));
            if(flow == null)
            {
                Debug.LogError($"No active flow with block name '{_s}'");
                return;
            }
        }
        if(flow.State == GameFlowState.WaitingForDecision)
            flow.StateWaitingForDecision(true);
    });
    
    //Skip to a specific flow line of a block
    // Syntax:
    // skip=<flowName>[,<index>] or
    // skip=index or
    // skip=<flowName> starts at start of flow
    private static DebugConsole.Command s_skip = new DebugConsole.Command("skip", _s =>
    {
        SkipFlow(_s);
    });
    public static void SkipFlow(string _s)
    {
        var bits = _s.Split(',', ';', '|',':');
        var flowName = MAGameFlow.MainBlockName;
        var index = -1f;
        if (bits.Length > 1)
        {
            flowName = bits[0].ToLower();
            floatinv.TryParse(bits[1], out index);
        }
        else
        {
            if(floatinv.TryParse(bits[0], out index) == false)
            {
                flowName = bits[0];
            }
        }

        List<NGBusinessFlow> flows = null;
        foreach (var d in NGBusinessFlow.s_flowsDict)
        {
            if(d.Key.Equals(flowName, StringComparison.OrdinalIgnoreCase))
            {
                flowName = d.Key;
                flows = d.Value;
                break;
            }
        }
        if(flows == null)
        {
            Debug.LogError($"DebugConsole: No such flow as '{flowName}'");
            return;
        }
        if(index == -1)
        {
            index = flows[0].m_index;
        }
        var flowLine = flows.Find(o => o.m_index == index);
        if (flowLine == null)
        {
            Debug.LogError($"DebugConsole: No such flow line as '{flowName}[{index}]'");
            return;
        }

        var blockFlowActive = m_activeFlows.Find(o => o.m_flow.m_blockName.Equals(flowName));
        if(blockFlowActive == null)
        {
            MAGameFlow.StartFlow(flows, flows.IndexOf(flowLine));
        }
        else
        {
            //Reset();
            blockFlowActive.Activate(flowLine, flows.IndexOf(flowLine), blockFlowActive.m_controlObject);
        }
    }
    public static bool IsFakeAwardFlowStep()
    {
        foreach (var a in m_activeFlows)
        {
            if(a.m_decisions.Find(o=>o.m_name == "CompleteAwardsEvent") != null)
                return true;
        }
        return false;
    }

    public static void Reset(string _blockName = null)
    {
        for (var i =  m_activeFlows.Count-1; i >= 0; i--)
        {
            m_activeFlows[i].Abort();
        }

        m_activeFlows = new();
        
        m_activeFlowss = new();
        if(_blockName.IsNullOrWhiteSpace() == false)
        {
            var l = NGBusinessFlow.s_flowsDict[_blockName];
            StartFlow(l);
        }
    }
    private Coroutine m_dismissMessageCoroutine = null;
    //public static NGTutorialMessageBase m_showingMessage = null;

    public void TriggerDismissAudio(string _audioID, string _messageType = "Subtitles")
    {
        if (_messageType.Contains("Subtitles"))
        {
            if (_audioID.IsNullOrWhiteSpace() == false)
            {
                string clipName = GameManager.DecodeAudio(_audioID);
                string clip = "PlaySound_" + clipName;

                m_dismissMessageCoroutine = GameManager.Me.StartCoroutine(DismissMessageAfterAudio(clip));
            }
            else
            {
                m_dismissMessageCoroutine = GameManager.Me.StartCoroutine(DismissMessageAfterTime(5.0f));
            }
   
        }
    }
    
    private IEnumerator DismissMessageAfterAudio(string _clip)
    {
        yield return new WaitForSeconds(0.5f);

        while(AudioClipManager.Me.IsPlayingVO(_clip))
        {
            yield return null;
        }

        // if (m_showingMessage != null && m_showingMessage.IsSubtitle)
        // {
        //     m_showingMessage.DestroyMe();
        // }
    }
    private IEnumerator DismissMessageAfterTime(float _time)
    {
        yield return new WaitForSeconds(_time);

        // if(m_showingMessage != null && m_showingMessage.IsSubtitle)
        // {
        //     m_showingMessage.DestroyMe();
        // }
    }


    #endregion Support

}
