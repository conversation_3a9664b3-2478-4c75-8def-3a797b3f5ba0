using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAQuickTipButton : MAGUIBase
{
   public GameObject m_highLight;
   public bool m_isHighlighting = false;
   public void ClickedButton(string _type)
   {
      switch (_type)
      {
         case "QuickTip":
            MAMessageManager.Me.ShowQuickTip();
            break;
         case "Research":
            MAResearchManagerUI.Create(NGManager.Me.m_centreScreenHolder);
            break;
         case "RoadBuild":
            RoadManager.Me.ToggleRoadBuildMode(true);
            break;
         case "HandPower":
            ActivateClicked();
            break;
         case "DesignAnywhere":
            DesignTableManager.Me.StartDesignGloballyUnfocused();
            break;
      }
      OnMouseDown();
   } 
   virtual protected void ActivateClicked()
   {
      OnMouseDown();
   }
   override public void Highlight(bool _flag)
   {
      if(m_highLight == null) return;
      m_highLight.SetActive(_flag);
      var animator = GetComponent<Animator>();
      if(animator != null)
         animator.SetBool("Highlight", _flag);
      m_isHighlighting = _flag;
   }
}
