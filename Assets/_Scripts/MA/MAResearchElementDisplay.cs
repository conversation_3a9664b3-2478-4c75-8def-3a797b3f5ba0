using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MAResearchElementDisplay : MonoBehaviour
{
 /*   //public MAResearchInfo.MAResearchElement m_element;
    public Image m_spriteImage;
    public Image m_spriteHighlight;
    public TMP_Text m_title;
    public TMP_Text m_costs;
    public Transform m_lineHolder;
    public LineRenderer m_lineRendererPrefab;
    public List<LineRenderer> m_lines = new List<LineRenderer>();
    public ParticleSystem m_goodChoiceParticleSystem;
    public ParticleSystem m_affordableParticleSystem;
    
    void Activate(MAResearchInfo.MAResearchElement _element)
    {
        m_element = _element;
        m_title.text = m_element.m_info.m_name;
        if (m_element.m_unlocked)
        {
            m_spriteHighlight.GetComponent<Outline>()?.gameObject.SetActive(true);
        } 
        else
        {
            m_spriteHighlight.GetComponent<Outline>()?.gameObject.SetActive(false);
        }
        m_spriteHighlight?.gameObject.SetActive(true);
        var costText = "";
        if (m_element.m_info.m_dollarCost > 0)
            costText += $"<sprite=0> {m_element.m_info.m_dollarCost:N0}\n";
        if(m_element.m_info.m_factionCost > 0)
            costText += $"<sprite={(int)m_element.m_info.m_faction+1}> {m_element.m_info.m_factionCost:N0}";
        m_costs.text = costText.TrimEnd('\n');
        m_spriteImage?.gameObject.SetActive(true);

        if (m_element.m_info.m_iconSprite)
            m_spriteImage.sprite = m_element.m_info.m_iconSprite;
        else
        {
            m_spriteImage.sprite = MAResearchSceneManager.Me.m_defaultFactionSprite[(int)m_element.m_info.m_faction];
        }

        if (m_element.m_info.m_linkTo.IsNullOrWhiteSpace() == false)
        {
            var split = m_element.m_info.m_linkTo.Split('|', ';', '\n');
            foreach (var lt in split)
            {
                if (lt.IsNullOrWhiteSpace()) continue;
                var line = Instantiate(m_lineRendererPrefab.gameObject, m_lineHolder);
                m_lines.Add(line.GetComponent<LineRenderer>());
            }
        }
        m_affordableParticleSystem.gameObject.SetActive(false);
        if (IsAffordable())
        {
            m_affordableParticleSystem.gameObject.SetActive(true);
        }
        m_goodChoiceParticleSystem.gameObject.SetActive(false);
    }

    public void ToggleGoodChoice(bool _toggle)
    {
        m_goodChoiceParticleSystem.gameObject.SetActive(_toggle);
    }

    public bool IsAffordable()
    {
        if (NGPlayer.Me.m_cash.Balance < m_element.m_info.m_dollarCost)
        {
            return false;
        }
        switch (m_element.m_info.m_faction)
        {
            case MAFactionInfo.FactionType.Royal:
                if (NGPlayer.Me.m_royalFavors.Balance < m_element.m_info.m_factionCost)
                {
                    return false;
                }
                break;
            case MAFactionInfo.FactionType.Lords:
                if (NGPlayer.Me.m_lordsFavors.Balance < m_element.m_info.m_factionCost)
                {
                    return false;
                }
                break;
            case MAFactionInfo.FactionType.Commoners:
                if (NGPlayer.Me.m_commonersFavors.Balance < m_element.m_info.m_factionCost)
                {
                    return false;
                }
                break;
        }

        return true;
    }

    public void DrawConnections(List<MAResearchElementDisplay> _allElements)
    {
        var split = m_element.m_info.m_linkTo.Split('|', ';', '\n');
        for (var i = 0; i < split.Length; i++)
        {
            var lt = split[i];
            if (lt.IsNullOrWhiteSpace()) continue;
            var line = m_lines[i];
            MAResearchElementDisplay to = null;
            foreach (var vv in _allElements)
            {
                
                if (vv.m_element.m_info.m_key.Equals(lt))
                {
                    to = vv;
                    break;
                }
            }
//            var to = _allElements.Find(o => o.m_element.m_info.m_key.Equals(lt));
            if (to)
            {
                Vector3[] positions = new Vector3[2];
                positions[0] = transform.position;
                positions[1] = to.transform.position;
                line.positionCount = positions.Length;
                line.SetPositions(positions);
                //line.useWorldSpace = false;
            }
            
        }
    }

    public void ClickedMe()
    {
        if (NGPlayer.Me.m_cash.Balance < m_element.m_info.m_dollarCost)
        {
            //Show not enough dialog
            return;
        }
        switch (m_element.m_info.m_faction)
        {
            case MAFactionInfo.FactionType.Royal:
                if (NGPlayer.Me.m_royalFavors.Balance < m_element.m_info.m_factionCost)
                {
                    return;
                }
                break;
            case MAFactionInfo.FactionType.Lords:
                if (NGPlayer.Me.m_lordsFavors.Balance < m_element.m_info.m_factionCost)
                {
                    return;
                }
                break;
            case MAFactionInfo.FactionType.Commoners:
                if (NGPlayer.Me.m_commonersFavors.Balance < m_element.m_info.m_factionCost)
                {
                    return;
                }
                break;
        }

        if (m_element.m_info.m_dollarCost > 0)
        {
            NGPlayer.Me.m_cash.Spend(m_element.m_info.m_dollarCost, "Research Lab", "", "Research");
        }

        if (m_element.m_info.m_factionCost > 0)
        {
            switch (m_element.m_info.m_faction)
            {
                case MAFactionInfo.FactionType.Royal:
                    NGPlayer.Me.m_royalFavors.Spend(m_element.m_info.m_factionCost, "Research Lab", "", "Research");
                    break;
                case MAFactionInfo.FactionType.Lords:
                    NGPlayer.Me.m_lordsFavors.Spend(m_element.m_info.m_factionCost, "Research Lab", "", "Research");
                    break;
                case MAFactionInfo.FactionType.Commoners:
                    NGPlayer.Me.m_commonersFavors.Spend(m_element.m_info.m_factionCost, "Research Lab", "", "Research");
                    break;
            }
        }

    }

    public void DestroyMe()
    {
        Destroy(gameObject);
    }
    public static MAResearchElementDisplay Create(MAResearchInfo.MAResearchElement _element, Transform _holder)
    {
        var go = Instantiate(NGManager.Me.maResearchElementDisplayPrefab, _holder);
        var re = go.GetComponent<MAResearchElementDisplay>();
        re.Activate(_element);
        return re;
    }*/
}
