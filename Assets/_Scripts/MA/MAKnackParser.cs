using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using Object = System.Object;
using Random = UnityEngine.Random;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using UnityEngine.UI;
#if OldBusinessFlow
public class MAKnackParser
{
    public static bool TryParse(string _pass, out bool _result, string _caller)
    {
	    _result = true;
	    var passLines = _pass.Split('\n');
  //      new string[] {"TestFunc(\"Peter\")", "TestFunc(\"Mike\", 2, 12.3)"};

        foreach (string functionString in passLines)
        {
            // Extract method name and arguments manually
            int startIndex = functionString.IndexOf('(');
            int endIndex = functionString.IndexOf(')');

            if (startIndex < 0 || endIndex < 0)
            {
                Debug.LogError(MAParserSupport.DebugColor($"Unmatched parenthesis in function string. @{_caller}"));
                return false;
            }
            string methodName = functionString.Substring(0, startIndex);
            string argumentsString = functionString.Substring(startIndex + 1, endIndex - startIndex - 1);
            
            //GetAugments
            List<string> arguments = GetArguments(argumentsString);
			if (arguments == null)
			{
				Debug.LogError(MAParserSupport.DebugColor($"Missing closing quote in {argumentsString} argument string. @{_caller}"));
				return false;
			}

            // Get all overloads with matching name
            MethodInfo[] methodInfos = typeof(MAKnackParser).GetMethods().Where(m => m.Name == methodName).ToArray();

            // Find the overload matching argument count
            MethodInfo matchingMethodInfo = null;
            foreach (MethodInfo methodInfo in methodInfos)
            {
                if (methodInfo.GetParameters().Length == arguments.Count)
                {
                    matchingMethodInfo = methodInfo;
                    break;
                }
            }

            // Handle no matching overload
            if (matchingMethodInfo == null)
            {
	            Debug.LogError(MAParserSupport.DebugColor($"Error: No matching '{methodName}' method found for {arguments.Count} arguments. @{_caller}"));
                return false;
            }

            // Parse argument values
            var argumentValues = new object[arguments.Count];
            var parameters = matchingMethodInfo.GetParameters();
            for (int i = 0; i < arguments.Count; i++)
            {
				argumentValues[i] = GetArgumentValue(parameters[i].ParameterType, arguments[i]);
				if(argumentValues[i] == null)
				{
					Debug.LogError(MAParserSupport.DebugColor($"Error: No matching '{methodName}' method found for {arguments.Count} arguments. @{_caller}"));
					return false;
				}
			}
			
			// Invoke the matching overload
			var iResult = matchingMethodInfo.Invoke(null, argumentValues);
			if(iResult == null)
	            _result = true;
			else
				_result = (bool)iResult; 
	    }
		return true;
	}

    public static List<string> GetArguments(string _argumentsString)
    {
	    
	    // Split arguments, handling nested quotes manually
	    List<string> arguments = new List<string>();
	    int currentStart = 0;
	    bool inQuote = false;
	    bool quoteFound = false;

	    for (int i = 0; i < _argumentsString.Length; i++)
	    {
		    if (_argumentsString[i] == '"' && !inQuote)
		    {
			    quoteFound = true;
			    inQuote = true;
		    }
		    else if (_argumentsString[i] == '"' && inQuote)
		    {
			    inQuote = false;
		    }
		    else if (_argumentsString[i] == ',' && !inQuote)
		    {
			    arguments.Add(_argumentsString.Substring(currentStart, i - currentStart).Trim());
			    currentStart = i + 1;
		    }
	    }

	    if (!inQuote) // Add the last argument
	    {
		    var str = _argumentsString.Substring(currentStart).Trim();
		    if(quoteFound)
			    str = str.Replace("\"","");
		    arguments.Add(_argumentsString.Substring(currentStart).Trim());
	    }
	    else
	    {
		    return null;
	    }

	    return arguments;
    }
    
    public static object GetArgumentValue(Type _type, string _argument)
	{
		if(_type == typeof(string))
		{
			return _argument;
		}
		if (_type.IsPrimitive)
	    {
		    Type underlyingType = Nullable.GetUnderlyingType(_type) ?? _type;

		    if (underlyingType == typeof(int))
		    {
			    int.TryParse(_argument, out var result);
			    return result;
		    }
		    else if (underlyingType == typeof(float))
		    {
			    floatinv.TryParse(_argument, out var result);
			    return result;
		    }
		    else if (underlyingType == typeof(double))
		    {
			    double.TryParse(_argument, out var result);
			    return result;
		    }
		    else if (underlyingType == typeof(bool) )
		    {
			    bool.TryParse(_argument, out var result);
			    return result;			    
		    }
		    else if (underlyingType == typeof(char))
		    {
			    var str = _argument;
			    if (str.Length >= 1)
				    return str[0];
		    }
	    }

		return GetGameObjectValue(_type, _argument);
	}

    public static object GetGameObjectValue(Type _type, string _argument)
    {
	    if (_type == typeof(MABuilding))
	    {
		    return MABuilding.FindBuilding(_argument.ToString());
	    }
	    else if (_type == typeof(Vector3))
	    {
	    }
	    else if (_type == typeof(MABuildingComponentBase))
	    {
		    
	    }    
	    return null;
    }
	
	
    public static void TestFunc(string _name)
    {
	    Console.WriteLine($"Name: {_name}");
    }

    public static void TestFunc(string _name, int _id, float _value)
    {
	    Console.WriteLine($"Name: {_name}, ID: {_id}, Value: {_value}");
    }
    public static void TestFunc(string _name, MABuilding _name2)
    {
	    Console.WriteLine($"Name: {_name}, Name2: {_name2.ToString()}");
    }

    static public void MoveCamera(MABuilding _building, float _distance)
	{
		MAParser.StartCameraTransition(_building.DoorPosOuter, _distance); 
	}

}
#endif