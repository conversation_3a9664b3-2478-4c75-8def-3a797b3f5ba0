using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using Combo = MAAreaSelectionCircle.KeyCodeComboActivation.KeyCodeCombo;

[Serializable]
public class MAAreaSelectionCircle
{ 
	public static readonly int HASH_TINT = Shader.PropertyToID("_Tint");
	
	[SerializeField]
	private int m_districtBoundsToleranceMargin = 4;
	
	[SerializeField] 
	private GlobalData.NavCostTypes[] m_allowedNavCostTypes = { GlobalData.NavCostTypes.Road, GlobalData.NavCostTypes.OffRoad, GlobalData.NavCostTypes.Pavement };

	[SerializeField]
	private Color m_circleColour = Color.cyan;
	[SerializeField]
	private Color m_circleColourBlocked = Color.red;

	[SerializeField]
	private int m_renderQueue = 2450;
	
	[Range(0.01f, 10f)]
	[SerializeField]
	private float m_circleLineWidth = 0.25f;
	
	[SerializeField]
	private Vector3 m_circleRotation = Vector3.right;
	
	[SerializeField]
	private float m_circleStepsPerUnit = 0.85f;

	[Range(0.1f, 30f)]
	[SerializeField]
	private float m_increaseCircleStep = 10f, m_decreaseCircleStep = 10f;

	[SerializeField]
	private KeyCodeComboActivation m_activate = new("ActivateCircle", new[] { new Combo(EKeyboardFunction.SetPatrolZone), new Combo(KeyCode.LeftShift, KeyCode.F1 )});
	
	[SerializeField]
	private KeyCodeComboActivation m_plus = new("CirclePlus", new[] { new Combo(EKeyboardFunction.IncPatrolZone)});
	
	[SerializeField]
	private KeyCodeComboActivation m_minus = new("CircleMinus", new[] { new Combo(EKeyboardFunction.DecPatrolZone)});

	private LineRenderer m_lineRenderer = null;
	private Transform m_circleTr = null;
	private MaterialPropertyBlock m_circleMaterialPropertyBlock;

	private float m_radiusMin = 0f;
	private float m_radiusMax = Single.MaxValue;
	private float m_circleRadius = 5f;
	
	private bool m_isActive = false;
	private bool m_isLocked = false;
	
	private Vector3 m_mousePosPrevious = Vector3.zero;
	private Vector3 m_camPosPrevious = Vector3.zero;
	private Quaternion m_camRotPrevious = Quaternion.identity;
	private Vector3 m_lastDrawPos = Vector3.zero;
	private Vector3 m_lastValidPos = Vector3.zero;
	
	public bool IsActive => m_isActive;
	public bool IsLocked => m_isLocked;
	
	public float Radius => m_circleRadius;
	
	public Vector3? ValidCentre => m_lastValidPos == Vector3.zero ? null : m_lastValidPos;// m_circleTr.position;

	public MAAreaSelectionCircle(float _startingRadius, float _radiusMin, float _radiusMax)
	{
		m_circleRadius = _startingRadius;
		m_radiusMin = _radiusMin;
		m_radiusMax = _radiusMax;
	}
	
	~MAAreaSelectionCircle()
	{
		FLog.Log("MAAreaSelectionCircle");
		try {
			ClearCircle();
		} catch (System.Exception e) {}
	}

	public void ClearCircle()
	{
		if (m_lineRenderer != null)
		{
			GameObject.Destroy(m_lineRenderer.gameObject);
			m_lineRenderer = null;
		}

		m_currentColour = m_circleColourBlocked;
		m_circleTr = null;
	}
	
	public void SetLocked(bool _lock)
	{
		m_isLocked = _lock;
	}
	public void Update()
	{
		//test combos:
		// string str = "";
		// if (Input.GetKeyDown(KeyCode.LeftShift))
		// {
		// 	str = "ls down";
		// }
		// if (Input.GetKeyDown(KeyCode.F1))
		// {
		// 	str += "f1 down";
		// }
		// if (Input.GetKeyUp(KeyCode.LeftShift))
		// {
		// 	str = "ls Up";
		// }
		// if (Input.GetKeyUp(KeyCode.F1))
		// {
		// 	str += "f1 Up";
		// }
		//if(str.IsNullOrWhiteSpace() == false) Debug.Log(str);

		Vector3 mousePosNow = Input.mousePosition;
		bool anyKey = Input.anyKey;
		bool toggledToActive = false;
		if (anyKey && m_isLocked == false && m_activate.IsToggled)
		{
			TogglePatrolMode();
			if (m_isActive)
			{
				toggledToActive = true;
			}
		}
		
		if (m_isActive)
		{
			if (m_isLocked == false)
			{
				bool circleChanged = false;
				if (anyKey)
				{
					if (m_plus.IsToggled || m_plus.IsDown)
					{
						circleChanged |= IncreaseCircleRadius();
					}

					if (m_minus.IsToggled || m_minus.IsDown)
					{
						circleChanged |= DecreaseCircleRadius();
					}
				}

				Color color = m_circleColourBlocked;
				Vector3 pos = m_lastDrawPos;
				if (toggledToActive || HaveTransformsChanged(mousePosNow))
				{
					circleChanged |= true;
					Ray heldCharacterRay = Camera.main.ScreenPointToRay(mousePosNow);
					if (Physics.Raycast(heldCharacterRay, out RaycastHit hitInfo, int.MaxValue,
						    1 << GlobalData.Me.m_moaTerrain.gameObject.layer))
					{
						Vector3 possiblePos = hitInfo.point;
						GlobalData.NavCostTypes navCostType = GlobalData.Me.GetNavAtPoint(hitInfo.point);
						Vector2[] dirs = new [] { Vector2.right, Vector2.down, Vector2.left, Vector2.up };
						int iDir = 0;
						float dist = 1f;
						int iLimit = 32;
						Vector3 newPossiblePos = possiblePos;
						while(m_allowedNavCostTypes.Contains(navCostType) == false && iLimit >= 0)
						{
							if (iDir > dirs.Length - 1)
							{
								iDir = 0;
								dist++;
							}
							newPossiblePos = possiblePos + dirs[iDir++].V3XZ() * dist;
							navCostType = GlobalData.Me.GetNavAtPoint(newPossiblePos);
							iLimit--;
						}
						
						if(iLimit < 0)
						{
							color = m_circleColourBlocked;
							pos = hitInfo.point;
							m_lastValidPos = Vector3.zero;
						}
						else
						{
							if (IsWithingDistrictBounds(newPossiblePos, m_districtBoundsToleranceMargin))
							{
								pos = newPossiblePos;
								color = m_circleColour;
								m_lastValidPos = pos.GroundPosition();								
							}
							else
							{
								color = m_circleColourBlocked;
								pos = hitInfo.point;
								m_lastValidPos = Vector3.zero;
								//Debug.LogError("Failed to find a position for the circle inside valid District bounds");
							}
						}
					}
				}

				if (circleChanged)
				{
					UpdateCircleArea(pos, color);
				}
			}
		}
		else
		{
			ClearCircle();
		}
	}

	private bool IsWithingDistrictBounds(Vector3 _pos, int _toleranceMargin)
	{
		for (int iX = -_toleranceMargin; iX < _toleranceMargin; iX++)
		{
			for (int iZ = -_toleranceMargin; iZ < _toleranceMargin; iZ++)
			{
				Vector3 posToCheck = _pos;
				posToCheck.x += iX;
				posToCheck.z += iZ;
				if (!DistrictManager.Me.IsWithinDistrictBounds(posToCheck)) return false;
			}
		}
		return true;
	}
	
	private bool HaveTransformsChanged(Vector3 _mousePosNow)
	{ //we don't use mouseDelta because a still mouse will never do the raycast to begin with
		if (Input.mousePosition.Approximately(m_mousePosPrevious) == false)
		{
			m_mousePosPrevious = _mousePosNow;
			return true;
		}

		Transform camTr = Camera.main.transform;
		Vector3 camPos = camTr.position;
		if (camPos.Approximately(m_camPosPrevious) == false)
		{
			m_camPosPrevious = camPos;
			return true;
		}
		
		Quaternion camRot = camTr.rotation;
		if (camRot.Approximately(m_camRotPrevious, Mathf.Epsilon * 8f) == false)
		{
			m_camRotPrevious = camRot;
			return true;
		}
		return false;
	}
	
	private void TogglePatrolMode()
	{		
		m_isActive = !m_isActive;
	}
	
	private bool IncreaseCircleRadius()
	{
		return ChangeCircleRadius(Time.deltaTime * m_increaseCircleStep);
	}
	
	private bool DecreaseCircleRadius()
	{		
		return ChangeCircleRadius(Time.deltaTime * -m_decreaseCircleStep);
	}
	
	private bool ChangeCircleRadius(float delta)
    {		
    	float newCircleRadius = m_circleRadius + delta;
    	newCircleRadius = Mathf.Clamp(newCircleRadius, m_radiusMin, m_radiusMax);
    	if (Mathf.Approximately(newCircleRadius, m_circleRadius) == false)
    	{
    		m_circleRadius = newCircleRadius;
    		return true;
    	}
        return false;
    }

	private Color m_currentColour = Color.clear;
	private void UpdateCircleArea(Vector3 _pos, Color _color, Vector3? _screen = null)
	{
		m_lastDrawPos = _pos;
		if (m_lineRenderer == null)
		{
			m_lineRenderer = GameObject.Instantiate(
				GlobalData.Me.m_terrainSelectionCirclePrefab, 
				GlobalData.Me.m_terrainCircleSelectionHolder, 
				false).GetComponent<LineRenderer>();
            //m_lineRenderer.material.renderQueue = 2500;
            m_circleTr = m_lineRenderer.transform;
        }
		
		if (m_currentColour != _color)
		{
			m_circleMaterialPropertyBlock = new MaterialPropertyBlock();
			m_lineRenderer.GetPropertyBlock(m_circleMaterialPropertyBlock);
			m_circleMaterialPropertyBlock.SetColor(HASH_TINT, _color);
			m_lineRenderer.SetPropertyBlock(m_circleMaterialPropertyBlock);
			m_currentColour = _color;
		}
		
        m_circleTr.localRotation = Quaternion.Euler(m_circleRotation.x, m_circleRotation.y, m_circleRotation.z);
        Vector3 centre = _pos;
        centre.y += 1f;
        m_circleTr.position = centre;
        int steps = Mathf.RoundToInt(2 * Mathf.PI * m_circleRadius * m_circleStepsPerUnit);
        DrawCircle(steps, m_circleLineWidth);
	}
	
	private void DrawCircle(int _steps, float _lineWidth)
	{
		DrawCircle(m_lineRenderer, m_circleRadius, _steps, _lineWidth);
	}

	public static TerrainSectionMesh DrawTerrainArea(Vector3 _position, float _radius)
	{
		var plot = TerrainSectionMesh.Create("PatrolCircle", _position, _radius * 2f * Vector3.one, .15f, GlobalData.Me.m_terrainCircleProjectionMaterial);
		//plot.m_uvRotate = dir;
		var goPlot = plot.gameObject;
		goPlot.transform.SetParent(GlobalData.Me.m_terrainCircleSelectionHolder, true);
		goPlot.name = "Guard Area";
		//goPlot.transform.localRotation = Quaternion.Euler(0, (float) dir, 0);
		return plot;
	}
	
	public static void DrawCircle(LineRenderer _lineRenderer, float _radius, int _steps, float _lineWidth)
	{
		if (_lineRenderer == null)
			return;

		Transform tr = _lineRenderer.transform;
		_lineRenderer.positionCount = _steps;
		_lineRenderer.startWidth = _lineRenderer.endWidth = _lineWidth;
		
		Vector3 pos = tr.position;
		Vector3 rot = tr.localRotation.eulerAngles;
		
		for(int currentStep = 0; currentStep < _steps; currentStep++)
		{
			float circumferenceProgress = (float)currentStep / (_steps-1);
			float currentRadian = circumferenceProgress * 2 * Mathf.PI;
    
			float xScaled = Mathf.Cos(currentRadian);
			float yScaled = Mathf.Sin(currentRadian);

			float x = _radius * xScaled;
			float y = _radius * yScaled;
			float z = 0;

			Vector3 currentPosition = new Vector3(x,y,z);
			currentPosition = currentPosition.RotateAbout(rot, 90f * Mathf.Deg2Rad);
			if(_lineRenderer.useWorldSpace)
			{
				currentPosition += pos;
			}
			_lineRenderer.SetPosition(currentStep, currentPosition);
		}
	}	
	
	public static Vector3[] GetCircle(Vector3 _pos, Vector3 _rot, float _radius, int _steps)
	{
		var circlePoints = new Vector3[_steps];
		for(int currentStep = 0; currentStep < _steps; currentStep++)
		{
			float circumferenceProgress = (float)currentStep/(_steps-1);

			float currentRadian = circumferenceProgress * 2 * Mathf.PI;
    
			float xScaled = Mathf.Cos(currentRadian);
			float yScaled = Mathf.Sin(currentRadian);

			float x = _radius * xScaled;
			float y = _radius * yScaled;
			float z = 0;

			Vector3 currentPosition = new Vector3(x,y,z);
			currentPosition = currentPosition.RotateAbout(_rot, 90f * Mathf.Deg2Rad);
			currentPosition += _pos;
			circlePoints[currentStep] = currentPosition;
		}

		return circlePoints;
	}
	
	[Serializable]
	public class KeyCodeComboActivation
	{
		[SerializeField]
		private string m_name;
		
		/// <summary>
		/// Pass-in as many single-keys or combinations of keypresses (e.g. 'a' and/or 'shift-a') as you wish
		/// </summary>
		private KeyCodeCombo[] m_keyCodeCombinations;

		public KeyCodeComboActivation(string _name, KeyCodeCombo[] _combinations)
		{
			m_name = _name;
			m_keyCodeCombinations = _combinations;
		}
		
		/// <summary>
		/// the moment a combination (or singular key) is toggled
		/// if single key:on key-up
		/// if combination: on key-up of key in a combination of down-keys)
		/// </summary>
		public bool IsToggled
		{
			get
			{
				foreach (var keyPress in m_keyCodeCombinations)
				{
					if (keyPress.KeysToggled())
					{
						return true;
					}
				}
				return false;
			}
		}		
		
		/// <summary>
		/// a combination (or singular key) is fully down
		/// </summary>
		public bool IsDown
		{
			get
			{
				foreach (var keyPress in m_keyCodeCombinations)
				{
					if (keyPress.KeysActivatedOrDown())
					{
						return true;
					}
				}
				return false;
			}
		}
		
		[Serializable]
		public class KeyCodeCombo
		{
			[SerializeField]
			private string m_name;
			
			public KeyCode[] m_keyCodes;
			public EKeyboardFunction m_keyboardFunction;
			
			private HashSet<int> m_keysDown = new();
			private int m_activateFrame = -1;
			
			public bool IsNewKeyPress => m_activateFrame == Time.frameCount;

			private bool KeyboardFunctionActivatedOrDown()
			{
				if (m_keyboardFunction.AnyDown() || m_keyboardFunction.AnyHeld())
				{
					if (m_activateFrame == -1) m_activateFrame = Time.frameCount;
					return true;
				}
				m_activateFrame = -1;
				return false;
			}
			
			private bool KeyboardFunctionToggled()
			{
				if (m_keyboardFunction.AnyClicked())
				{
					m_activateFrame = Time.frameCount;
					return true;
				}
				m_activateFrame = -1;
				return false;
			}

			public bool KeysActivatedOrDown()
			{
				if ((m_keyCodes?.Length ?? 0) == 0) return KeyboardFunctionActivatedOrDown();
				
				for(int i = 0; i < m_keyCodes.Length; i++)
				{
					if (Input.GetKeyDown(m_keyCodes[i]))
					{
						m_keysDown.Add(i);
					}
					else if (Input.GetKeyUp(m_keyCodes[i]))
					{
						m_keysDown.Remove(i);
					}
				}

				for (int i = 0; i < m_keyCodes.Length; i++)
				{
					if(m_keysDown.Contains(i) == false)
					{
						m_activateFrame = -1;
						return false;
					}
				}

				if(m_activateFrame == -1) m_activateFrame = Time.frameCount;
				return true;
			}
			
			public bool KeysToggled()
			{
				if ((m_keyCodes?.Length ?? 0) == 0) return KeyboardFunctionToggled();

				int keysRaisedThisFrame = 0;
				int keysDown = 0;
				for(int i = 0; i < m_keyCodes.Length; i++)
				{
					if (Input.GetKeyDown(m_keyCodes[i]))
					{
						m_keysDown.Add(i);
						keysDown++;
					}
					else if (Input.GetKeyUp(m_keyCodes[i]))
					{
						if (m_keysDown.Remove(i))
						{
							keysRaisedThisFrame++;
						}
					}
					else if(m_keysDown.Contains(i))
					{
						keysDown++;
					}
				}

				if (keysRaisedThisFrame > 0 && keysRaisedThisFrame + keysDown == m_keyCodes.Length)
				{
					m_activateFrame = Time.frameCount;
					return true;
				}

				m_activateFrame = -1;
				return false;
			}

			public KeyCodeCombo(EKeyboardFunction _fn)
			{
				m_keyboardFunction = _fn;
			}

			public KeyCodeCombo(params KeyCode[] _keyCodes)
			{
				m_keyCodes = _keyCodes;
				for (int i = 0; i < _keyCodes.Length; i++)
				{
					m_name += _keyCodes[i].ToString();
					if(i < _keyCodes.Length - 1)
					{
						m_name += " + ";
					}
				}
			}
		}
	}
}
