#if false
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class BCOrderBoard : BCActionBase
{
	[KnackField] public float m_orderRefresh = 60f;
	
	[Save] public static int m_heldOrder = -1;
	[SerializeField] private string m_giftTypeName = "Order";
	[SerializeField] private AlwaysFaceCamera m_alwaysFaceCamera = null;
	[SerializeField] private Balloon m_balloon = null;
	[SerializeField] private float m_scaleInDrawer = 0.26f;

	public override void Activate()
	{
		base.Activate();
		//FillOrderList();
	}

	public override string GetCantMakeReason()
	{
		return "";
	}

	protected override void Awake()
	{
		base.Awake();

		foreach(Collider componentsInChild in GetComponentsInChildren<Collider>())
		{
			componentsInChild.enabled = true;
		}
	}
}
#endif