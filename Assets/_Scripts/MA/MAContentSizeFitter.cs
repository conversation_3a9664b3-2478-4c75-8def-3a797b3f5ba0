using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

[ExecuteInEditMode]
public class MAContentSizeFitter : MonoBehaviour
{
    public RectTransform rectTransform;
    public Vector2 preferredSize;
    public bool m_fitHorizontal = true;
    public bool m_fitVertical = true;
    private bool initialized = false;

    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
    }

    private void OnEnable()
    {
        if (initialized == false)
        {
            rectTransform = GetComponent<RectTransform>();
        }

        FitSize(); 
        initialized = true;
    }

    public void SetPreferredSize(Vector2 newPreferredSize)
    {
        if (preferredSize != newPreferredSize)
        {
            preferredSize = newPreferredSize;
            FitSize();
        }
    }

    void Update()
    {
        FitSize();
    } 

    private void FitSize()
    {
        if (rectTransform != null)
        {
            var totalWidth = 0f;
            var totalHeight = 0f;
            foreach (RectTransform t in transform)
            {
                var lg = t.GetComponent<LayoutElement>();
                if(lg && lg.ignoreLayout)
                    continue;
                if( t.gameObject.activeSelf == false)
                    continue;
                totalWidth+=t.GetWidth();
                totalHeight+=t.GetHeight();
            }
            if(m_fitHorizontal)
                rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, totalWidth);
            if(m_fitVertical)
                rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, totalHeight);
//            rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, preferredSize.x);
  //          rectTransform.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, preferredSize.y);
        }
    }
}
