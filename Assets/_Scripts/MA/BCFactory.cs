using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class BCFactory : BCActionProducer
{
	public MAOrder Order => Building == null || Building.Order.IsNullOrEmpty() ? null : Building.Order;
	[KnackField] public float m_energyRequiredToMakeMultiplier = 1;

	override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (m_info?.id, () => new BCFactoryPanel(m_info));
#if UNITY_EDITOR	
	override public void DebugShowGUIDetails(GUIStyle _labelStyle, bool _showBase = true)
	{
		EditorGUILayout.LabelField($"m_productScore: ", $"{GetProductScore()}", _labelStyle);
		EditorGUILayout.LabelField($"Production Status: ", $"{GetProductStatus()}", _labelStyle);
		EditorGUILayout.LabelField($"Workers Present: ", $"{m_building.GetWorkersPresent().Count}", _labelStyle);

	}
#endif

	public override bool CombineGUI => true;
	override protected bool ShowSmoke => true;
	override public int CantMakePriority => 1;

	protected override string GetDescriptionPrefix()
	{
		string value = "";
		var order = Order;
		if (order.IsNullOrEmpty() == false)
		{
			value += $"<b>Order</b> {order.DisplayName}";
			value += $"\nManufactured\t{order.Manufactured} of {order.m_orderQuantity}";
			value += $"\nDelivered\t{order.Dispatched} of {order.m_orderQuantity}";
		}
		return value;
	}

	override public void Activate(int _indexOfComponentType, int _quantityInBuilding)
	{
		if(m_building == null || m_building.Order.IsNullOrEmpty())
			m_energyLeftToMake = m_energyRequiedToMake;
        
		base.Activate(_indexOfComponentType, _quantityInBuilding);
	}
	
	override public void PreUpdate(BuildingComponentsState _state)
	{
		if(HasStartedMaking)
		{
			_state.ProductsBeingBuilt++;
			m_inhibitProduction = false;
		}
		else
		{
			m_inhibitProduction = true;
		}
		
		base.PreUpdate(_state);
	}
	
	override public void UpdateInternal(BuildingComponentsState _state)
    {
		if(Order.IsNullOrEmpty() == false)
		{
			if(Order.IsComplete)
			{
				Building.RemoveOrder(Order);
			}
			else if((DesignTableManager.Me.IsDesignInPlaceExiting || DesignTableManager.Me.IsInDesignInPlace == false) && Order.HasDesign == false)
			{
				Building.RemoveOrder(Order);
			}
		}
		
        base.UpdateInternal(_state);
    }

	protected override void CreateItem()
	{
		base.CreateItem();
		
		Order?.ItemManufactured();
	}
    
    public override string GetCantMakeReason()
    {
	    if (m_building.IsPaused)
		    return "production paused";
		    
	    if(Order.IsNullOrEmpty())
			return "requires order";
			
	    var product = Building?.GetProduct();
	    if (product == null || product.Design == null)
			return "order requires design";
			
	    return base.GetCantMakeReason();
    }

    private void UpdateEnergyRequiredToMake(bool _updateEnergyLeft = true)
    {
		var product = Building?.GetProduct();
		
		if(product == null || product.Design == null || product.Design.m_design.IsNullOrWhiteSpace())
		{
			if(_updateEnergyLeft)
			{
				m_energyLeftToMake = m_energyRequiedToMake;
			}
			return;
	    }
			
	    NGDesignInterface.DesignScoreInterface di = NGDesignInterface.Get(product);
	    
	    m_energyRequiedToMake = di.SecondsForWorkerToBuild * MAWorkerInfo.c_baseWorkerProductionPower * m_energyRequiredToMakeMultiplier;
	    if(_updateEnergyLeft)
			m_energyLeftToMake = m_energyRequiedToMake;
    }
    
    protected override bool UpdateProduction(BuildingComponentsState _state, bool _isTap)
    {
		GameState_Product product = Building?.GetProduct();
		
		if (product == null) return false;
		if (product.Design == null || product.Design.m_design.IsNullOrWhiteSpace()) return false;
		
		if(product.GetLinkedOrder(out MAOrder order) == false || order.IsComplete) return false;
		
	    int needToMake = Mathf.Max(1, order.RemainingToManufacture);
	    
	    bool wasMaking = HasStartedMaking;
	    if(_state != null && m_inhibitProduction)
	    {
			if(_state.ProductsBeingBuilt >= needToMake)
			{
				return false;
		    }
		    m_inhibitProduction = false;
	    }
	    
		bool isReady = base.UpdateProduction(_state, _isTap);
		
	    if(_state != null && wasMaking == false)
	    {
			if(_state.ProductsBeingBuilt < needToMake && (isReady || HasStartedMaking))
				_state.ProductsBeingBuilt++;	
			else
				m_inhibitProduction = true;
	    }
	    return isReady;
    }
	
    override public NGProductInfo GetProductLineInfo()
    {
	    if (Order.IsNullOrEmpty() == false && Order.IsValid)
		    return Order.ProductInfo;
	    return null;
    }
    
    protected override void SetupStockRequirements()
    {
	    m_outputResource = NGCarriableResource.GetInfo(NGCarriableResource.c_product);
	    
		if(m_building)
			m_building.StockRefreshRequired = true;
	    
	    m_inputStock.RemoveEmptyStockAndClearNeededToProduce();
	    m_stock.RemoveEmptyStockAndClearNeededToProduce();
	    
	    var product = Building?.GetProduct();
	    
	    if(product == null || product.HasDesign == false)
	    {
		    RefreshStockVisuals();
			return;
	    }
	    
	    NGDesignInterface.DesignScoreInterface di = NGDesignInterface.Get(product);
	    var materialsRequired = di.GetMaterialsRequiredList();
	    foreach(var requirement in materialsRequired)
	    {
			m_inputStock.AddOrCreateStock(requirement.Item1, 0, requirement.Item2);
	    }
	    
	    m_stock.AddOrCreateStock(Order.GameProduct.ResourceType, 0, m_outputQuantity);
	    
	    RefreshStockVisuals();
    }

    protected override void PostLoad()
    {
	    UpdateEnergyRequiredToMake(false);
	    base.PostLoad();
    }
    
	public void OnProductChanged()
    {
		SetupStockRequirements();
		UpdateEnergyRequiredToMake();
    }
    
    override public void DisplayBuildingInfo(Transform _holder, List<BCBase> _components)
    {
	    MABuildingFactoryPanel.Create(_holder, m_building);
    }
    
    private string GetProductStatus()
	{
		var product = Building?.GetProduct();
		if(product == null) return "No Product";
		if(product.GetLinkedOrder(out MAOrder order) == false)
			return "No Order";
		if(product.HasDesign == false) return "No Design";
		var design = NGDesignInterface.Get(product);
		if(design.SecondsForWorkerToBuild == 0) return "SecondsForWorkerToBuild == 0";
		if (m_inputStock.CanMake() == false) return "Not Enough Stock";
		if (m_stock.GetTotalStock() > 0) return "Not Enough Output Space";
		return "Ready";
	}
}

#if UNITY_EDITOR
[CustomEditor(typeof(BCFactory))]
public class MABuildingComponentFactoryEditor : Editor
{
	public override void OnInspectorGUI()
	{
		// Popup ("Select Waypoint", index, optionsList);.
		base.OnInspectorGUI();
		
		if(Application.isPlaying)
		{
			BCFactory factory = (BCFactory)target;
			MAOrder order = factory.Order;
			if(order != null && order.IsValid)
			{
				if(GUILayout.Button("Complete Order"))
				{
					order.Complete();
				}
			}
		}
	}
}
#endif