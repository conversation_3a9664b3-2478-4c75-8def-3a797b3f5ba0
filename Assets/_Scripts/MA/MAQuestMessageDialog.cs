using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MAQuestMessageDialog : NGTutorialMessageBase
{
    public TMP_Text m_title;
    public TMP_Text m_message;
    public TMP_Text m_rewardText;
    public Image m_image;
    public Transform m_itemsHolder;
    public Action m_onAcceptAction;
    public Action m_onCancelAction;
    public Action m_onCloseAction;
    public Action m_onZoomAction;
    public GameObject m_closeButton;
    public ContextMenuButton m_acceptButton;
    public ContextMenuButton m_cancelButton;
    public ContextMenuButton m_zoomButton;
    
    override protected void Activate(string _preBarText, string _postBarText, string _trackFuncString, string _type, string _pose)
    {
        var fromQuest = MAParserManager.CurrentQuest;
//        var fromQuest = MAGameFlow.UpdatingControlObject as MAQuestBase;
        if (fromQuest == null)
        {
            Debug.LogError(MAParserSupport.DebugColor("MAQuestMessageDialog:, MAGameFlow.m_updatingFlow.m_controlObject is null"));
            return;
        }
        fromQuest.ShowAcceptDialog(_preBarText, _pose);
        Activate("Quest", _preBarText, fromQuest.m_questCompleteResources, fromQuest.m_questCompleteGifts, GetPoseSprite(_pose), fromQuest.OnAcceptChallenge, fromQuest.OnCancelChallenge, null, null);
        fromQuest.m_challengeText = _preBarText;
    }
    private const string c_escapeBackLabel = "QuestDialog";
    void Activate(string _title, string _message, List<MAQuestBase.QuestResourceReward> _resourceItems, List<NGBusinessGift> _giftItems, Sprite _image, Action _onAcceptAction, Action _onCancelAction, Action _onZoomAction,  Action _onCloseAction)
    {
        GameManager.Me.PushEscapeBackFunction(c_escapeBackLabel, ClickedClose);
        m_acceptButton.gameObject.SetActive(_onAcceptAction != null);
        m_cancelButton.gameObject.SetActive(_onCancelAction != null);
        m_zoomButton.gameObject.SetActive(_onZoomAction != null);
        m_closeButton.SetActive(_onCloseAction != null || (_onCancelAction == null && _onAcceptAction == null));
        m_title.text = _title;
        m_message.text = _message;
        if (_image)
        {
            m_image.sprite = _image;
        }
        m_onAcceptAction = _onAcceptAction;
        m_onCancelAction = _onCancelAction;
        m_onZoomAction = _onZoomAction;
        m_onCloseAction = _onCloseAction;
        
        m_closeButton.SetActive(_onCloseAction != null);
        if (_resourceItems != null && _giftItems != null)
        {
            m_rewardText.gameObject.SetActive(true);
            var itemElement = m_itemsHolder.GetChild(0).gameObject;

            if (_resourceItems != null)
            {
                foreach(var l in _resourceItems)
                {
                    var newItem = Instantiate(itemElement, m_itemsHolder);
                    newItem.SetActive(true);
                    var newItemTxt = newItem.GetComponent<TMP_Text>();
                    newItemTxt.enabled = true;
                    if(l.m_resource.m_name == "Money")
                    {
                        newItemTxt.text = $"{l.m_resource.TextSprite}{l.m_amount:N0}";
                    }
                    else
                    {
                        newItemTxt.text = $"{l.m_amount} x {l.m_resource.TextSprite} {l.m_resource.m_title}";
                    }
                }    
            }
            if(_giftItems != null)
            {
                foreach(var l in _giftItems)
                {
                    var newItem = Instantiate(itemElement, m_itemsHolder);
                    newItem.SetActive(true);
                    var newItemTxt = newItem.GetComponent<TMP_Text>();
                    newItemTxt.enabled = true;
                    newItemTxt.text = $"1 x {l.m_giftTitle}";
                }
            }
        }
        else
        {
            m_rewardText.gameObject.SetActive(false);
        }
        s_current = this;
    }
    
    public void ClickedAccept()
    {
        m_onAcceptAction?.Invoke();
        DestroyMe();
    }
    public void ClickedCancel()
    {
        m_onCancelAction?.Invoke();
        DestroyMe();
    }
    
    public void ClickedClose()
    {
        m_onCloseAction?.Invoke();
        DestroyMe();
    }
    
    public void ClickedZoom()
    {
        m_onZoomAction?.Invoke();
        DestroyMe();
    }
    
    override public void DestroyMe()
    {
        if (s_current != null)
        {
            GameManager.Me.PopEscapeBackFunction(c_escapeBackLabel);
            s_current = null;
        }

        base.DestroyMe();    
    }
    
    public static MAQuestMessageDialog s_current;
    
    public static MAQuestMessageDialog Create(string _title, string _message, List<MAQuestBase.QuestResourceReward> _resourceItems, List<NGBusinessGift> _giftItems, Sprite _image, Action _onAcceptAction, Action _onCancelAction, Action _onZoomAction, Action _onCloseAction = null)
    {
        if(s_current != null)
        {
            s_current.DestroyMe();
        }
        var prefab = Resources.Load<MAQuestMessageDialog>("_Prefabs/MessagePrefabs/MAQuestMessageDialogV2");
        var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
        instance.Activate(_title, _message, _resourceItems, _giftItems, _image, _onAcceptAction, _onCancelAction, _onZoomAction, _onCloseAction);
        s_current = instance;
        return instance;
    }
}
