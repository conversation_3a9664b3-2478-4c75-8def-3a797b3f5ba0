using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAMovingInfoBase
{
    [SerializeField] public string id;
    [SerializeField] public bool m_debugChanged;
    [SerializeField] public string m_name;
    [SerializeField] public string m_displayName;
    [SerializeField] public float m_lowWalkSpeed;
    [SerializeField] public float m_highWalkSpeed;
    [SerializeField] public float m_possessWalkSpeed;
    [SerializeField] public float m_possessRunSpeed;
    [SerializeField] public string m_gender;
    [SerializeField] public string m_persona;
    [SerializeField] public string m_likes;
    [SerializeField] public string m_dislikes;
    [SerializeField] public string m_prefabName;
    [SerializeField] public float m_health;
    [SerializeField] public float m_mana;
    [SerializeField] public float m_tavernCost;
    [SerializeField] public string m_description;
    [SerializeField] public string m_spritePath;
    [SerializeField] public float m_tavernCostMultiplier;
    [SerializeField] public string m_defaultArmour;
    [SerializeField] public int m_level;
    [SerializeField] public float m_healthDayRegeneration;
    
		// RW-10-MAR-25: The first string is what comes in from Knack, this
		// may have multiple possible drops concatenated. This is then split
		// into m_deathDropOptions, which is what should be used at runtime.
    [SerializeField] protected string m_deathDropFavour;
    [SerializeField] public float m_deathAlignment;
    [SerializeField] public float m_positiveInteractionAlignment;
    [SerializeField] public float m_negativeInteractionAlignment;
    
    public string[] m_deathDropOptions;
    public float m_deathDropChance;
    
    public string DebugDisplayName => m_name;
    public bool IsMale => m_gender == "Male";
    
    [ScanField] public string m_allowedInteractTypes;
    private HashSet<string> m_allowedInteractTypesCache;
    public bool IsInteractTypeAllowed(string _s)
    {
        if (m_allowedInteractTypesCache == null)
        {
            var types = m_allowedInteractTypes.Split(';', '|', '\n');
            foreach (var type in types)
                m_allowedInteractTypesCache.Add(type);
        }
        return m_allowedInteractTypesCache.Contains(_s);
    }


    public virtual HashSet<MAMovingInfoBase> GetMovingInfoTargets()
    {
        return new HashSet<MAMovingInfoBase>();
    }
    
    public float PossessWalkSpeed => m_possessWalkSpeed;
    public float PossessRunSpeed => m_possessRunSpeed;
    
    virtual public float DeathMana => m_mana;
    public float GetNewWalkingSpeed() => UnityEngine.Random.Range(m_lowWalkSpeed, m_highWalkSpeed);
    
    virtual public float GetDamageModifier(string _damgeType) => 1;
}
