using UnityEngine;
using UnityEngine.UI;

public class MAQuickDesignAnywhereButton: MAQuickTipButton
{
    public Button m_button;

    // Update is called once per frame
    void Update()
    {
        if (GameManager.Me == null || GameManager.Me.LoadComplete == false) return;
        var showButton = MAUnlocks.Me.m_designBuildings;
        if(m_button.interactable != showButton)
        {
            transform.localScale = showButton ? Vector3.one : Vector3.zero;
            m_button.interactable = showButton;
        }
       
        if (showButton == false)
        {
            m_highLight.SetActive(showButton);
        }
    }
}
