using System;
using UnityEngine;
using System.Collections.Generic;

public class MAFeedbackCondition
{
    private MAMessage m_message;
    public enum Result
    {
        None,
        Allowed,
        Denied,
    }
    
    public void AddMessage(string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        m_message = MAMessage.Create(_type.Trim(), _message.Trim(), _audioID.Trim(), _pose.Trim(), 0,
            _advisorName.Trim());
    }

    public virtual Result CheckDesign(NGDesignInterface.DesignScoreInterface _dsi) { return Result.None; }
    public virtual Result CheckBlock(Block _block) { return Result.None; }
    public virtual Result CheckBuildPlacement(Vector3 _object) { return Result.None; }
    
    public virtual void DisplayFeedback()
    {
        if (MAMessage.IsReadyToDisplay())
            m_message?.Display();
    }
}

public class RequiredComponentInDesignCondition : MAFeedbackCondition
{
    public MAComponentInfo m_componentInfo;
    public int m_requiredCount;

    public RequiredComponentInDesignCondition(string _componentName, int _count)
    {
        m_componentInfo = MAComponentInfo.GetInfo(_componentName);
        if(m_componentInfo == null)
        {
            Debug.LogError($"RequiredComponentCondition: Unable to find component with name {_componentName}");
        }
        m_requiredCount = _count;
    }

    public override Result CheckDesign(NGDesignInterface.DesignScoreInterface _dsi)
    {
        if(m_componentInfo == null)
            return Result.None;
          
        int count = 0;
        foreach(var p in _dsi.Parts)
        {
            var components = p.m_block.GetComponentInfos();
            foreach(var c in components)
            {
                if(c == m_componentInfo) count++;
            }
        }
        if(count < m_requiredCount)
            return Result.Denied;
        return Result.None;
    }
}

public class AllowBlockCondition : MAFeedbackCondition
{
    public string m_blockID;

    public AllowBlockCondition(string _blockID)
    {
        m_blockID = _blockID;
    }

    public override Result CheckBlock(Block _block)
    {
        if (_block == null)
            return Result.None;
        if(_block.BlockInfo.m_prefabName.Equals(m_blockID, StringComparison.InvariantCultureIgnoreCase))
            return Result.Allowed;
        return Result.None;
    }
}

public class DenyBlockCondition : MAFeedbackCondition
{
    public string m_blockID;

    public DenyBlockCondition(string _blockID)
    {
        m_blockID = _blockID;
    }

    public override Result CheckBlock(Block _block)
    {
        if (_block == null)
            return Result.None;
        if(_block.BlockInfo.m_prefabName.Equals(m_blockID, StringComparison.InvariantCultureIgnoreCase))
            return Result.Denied;
        return Result.None;
    }
}

public class DenyDesignScoreCondition : MAFeedbackCondition
{
    public float m_start;
    public float m_end;
    
    public DenyDesignScoreCondition(float _start, float _end)
    {
        m_start = _start;
        m_end = _end;
    }

    public override Result CheckDesign(NGDesignInterface.DesignScoreInterface _dsi)
    {
        var score = _dsi.TotalScore;
        if(score >= m_start && score <= m_end)
            return Result.Denied;
        return Result.None;
    }
}

public class AllowDesignScoreCondition : MAFeedbackCondition
{
    public float m_start;
    public float m_end;
    
    public AllowDesignScoreCondition(float _start, float _end)
    {
        m_start = _start;
        m_end = _end;
    }

    public override Result CheckDesign(NGDesignInterface.DesignScoreInterface _dsi)
    {
        var score = _dsi.TotalScore;
        if(score >= m_start && score <= m_end)
            return Result.Allowed;
        return Result.None;
    }
}

public class DenyBuildLocation : MAFeedbackCondition
{
    public enum LocationType { None, Rect, Other, }
    public Rect m_rect;
    public LocationType m_type = LocationType.None;
    
    public DenyBuildLocation(string _location)
    {
        if(_location.Contains("rect", StringComparison.OrdinalIgnoreCase))
        {
            var parseString = _location.Split('(',')');
            if (parseString.Length < 2 || Utility.TryParseRect(parseString[1], out m_rect) == false)
            {
                Debug.LogError(MAParserSupport.DebugColor($"DenyPlotLocation: Failed to parse rect: {_location}"));
                return;
            }
            m_type = LocationType.Rect;
        }
        else if(_location.Contains("other", StringComparison.OrdinalIgnoreCase))
        {
            m_type = LocationType.Other;
        }
    }
    
    public override Result CheckBuildPlacement(Vector3 _object)
    {
        switch(m_type)
        {
            case LocationType.Rect:
                var pos = _object;
                if(m_rect.Contains(new Vector2(pos.x, pos.z)))
                    return Result.Denied;
                break;
            case LocationType.Other:
                return Result.Denied;
        }
        return Result.None;
    }
}

public class AllowBuildLocation : MAFeedbackCondition
{
    public enum LocationType { None, Rect, Other, }
    public Rect m_rect;
    public LocationType m_type = LocationType.None;
    
    public AllowBuildLocation(string _location)
    {
        if(_location.Contains("rect", StringComparison.OrdinalIgnoreCase))
        {
            var parseString = _location.Split('(',')');
            if (parseString.Length < 2 || Utility.TryParseRect(parseString[1], out m_rect) == false)
            {
                Debug.LogError(MAParserSupport.DebugColor($"DenyPlotLocation: Failed to parse rect: {_location}"));
                return;
            }
            m_type = LocationType.Rect;
        }
        else if(_location.Contains("other", StringComparison.OrdinalIgnoreCase))
        {
            m_type = LocationType.Other;
        }
    }
    
    public override Result CheckBuildPlacement(Vector3 _object)
    {
        switch(m_type)
        {
            case LocationType.Rect:
                var pos = _object;
                if(m_rect.Contains(new Vector2(pos.x, pos.z)))
                    return Result.Allowed;
                break;
            case LocationType.Other:
                return Result.Allowed;
        }
        return Result.None;
    }
}

public class OnlyBlocksCondition : MAFeedbackCondition
{
    public List<string> m_blockTypes = new();

    public OnlyBlocksCondition(string _blockTypes)
    {
        var split = _blockTypes.Split(',');
        foreach(var type in split)
            m_blockTypes.Add(type);
    }

    public override Result CheckBlock(Block _block)
    {
        if (_block == null)
            return Result.None;
        bool allowed = false;
        foreach(var type in m_blockTypes)
        {
            if(_block.BlockInfo.m_buildingPartType.Contains(type))
            {
                allowed = true;
                break;
            }
        }

        if(allowed == false)
            return Result.Denied;
        return Result.None;
    }
}

public class MAFeedbackConditions
{
    public string m_id;
    public bool m_complete = false;

    public List<MAFeedbackCondition> m_conditions = new();

    public MAFeedbackConditions(string _id)
    {
        m_id = _id;
    }
    
    public MAFeedbackCondition.Result IsBuildPlacementAllowed(Vector3 _obj)
    {
        foreach(var condition in m_conditions)
        {
            switch(condition.CheckBuildPlacement(_obj))
            {
                case MAFeedbackCondition.Result.Allowed:
                    condition.DisplayFeedback();
                    m_complete = true;
                    return MAFeedbackCondition.Result.Allowed;
                    
                case MAFeedbackCondition.Result.Denied:
                    condition.DisplayFeedback();
                    return MAFeedbackCondition.Result.Denied;
            }
        }
        m_complete = true;
        return MAFeedbackCondition.Result.None;
    }

    public bool IsDesignAllowed()
    {
        if (DesignTableManager.Me == null)
            return false;
        
        foreach(var condition in m_conditions)
        {
            switch(condition.CheckDesign(DesignTableManager.Me.DesignInterface))
            {
                case MAFeedbackCondition.Result.Allowed:
                    condition.DisplayFeedback();
                    m_complete = true;
                    return true;
                case MAFeedbackCondition.Result.Denied:
                    condition.DisplayFeedback();
                    return false;
            }
        }
        m_complete = true;
        return true;
    }
    
    public bool IsBlockAllowed(Block _block, bool _playFeedback = true)
    {
        foreach (var condition in m_conditions)
        {
            switch(condition.CheckBlock(_block))
            {
                case MAFeedbackCondition.Result.Allowed:
                    condition.DisplayFeedback();
                    return true;
                case MAFeedbackCondition.Result.Denied:
                    condition.DisplayFeedback();
                    return false; 
            }
        }
        return false;
    }

    public MAFeedbackCondition AddCondition(MAFeedbackCondition _item)
    {
        // TODO
        // Make sure we're not double adding

        m_conditions.Add(_item);
        return _item;
    }

    public void AddAllowBlock(string _blockID, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        AddCondition(new AllowBlockCondition(_blockID))?.AddMessage(_advisorName, _type, _pose, _message, _audioID);
    }
    public void AddDenyBlock(string _blockID, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        AddCondition(new DenyBlockCondition(_blockID))?.AddMessage(_advisorName, _type, _pose, _message, _audioID);
    }
    public void AddOnlyBlocks(string _blockTypes, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        AddCondition(new OnlyBlocksCondition(_blockTypes))?.AddMessage(_advisorName, _type, _pose, _message, _audioID);
    }
    
    public void AddAllowDesignScore(float _start, float _end, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        AddCondition(new AllowDesignScoreCondition(_start, _end))?.AddMessage(_advisorName, _type, _pose, _message, _audioID);
    }
    
    public void AddDenyDesignScore(float _start, float _end, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        AddCondition(new DenyDesignScoreCondition(_start, _end))?.AddMessage(_advisorName, _type, _pose, _message, _audioID);
    }
    
    public void AddRequiredComponentsInDesign(string _componentName, int _count, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        AddCondition(new RequiredComponentInDesignCondition(_componentName, _count))?.AddMessage(_advisorName, _type, _pose, _message, _audioID);
    }
    
    public void AddAllowBuildLocation(string _location, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        AddCondition(new AllowBuildLocation(_location))?.AddMessage(_advisorName, _type, _pose, _message, _audioID);
    }
    
    public void AddDenyBuildLocation(string _location, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        AddCondition(new DenyBuildLocation(_location))?.AddMessage(_advisorName, _type, _pose, _message, _audioID);
    }
}