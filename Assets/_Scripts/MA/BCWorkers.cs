using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using UnityEngine;

public class BCWorkers : BCWorkerBase
{
    public float m_nextDeliverTime = 0f;
    override public string GetDebugInfo() => $"{m_workersAllocated.Count}/{m_maxWorkers}";

    public override List<MACharacterBase> GetWorkersAllocated() => m_workersAllocated;
    public override List<MACharacterBase> GetWorkersPresent() => m_workersPresent;
    
    public override void PreUpdate(BuildingComponentsState _state)
    {
        foreach(var w in m_workersPresent)
        {
            w.SetPowerAvailable(w.ProductionPower*Time.deltaTime);
        }
        base.PreUpdate(_state);
    }

    public override bool Allocate(MACharacterBase _worker)
    {
        if(base.Allocate(_worker))
        {
            _worker.Job = this;
            return true;
        }
        return false;
    }

    public override bool Arrive(MACharacterBase _worker)
    {
        if(base.Arrive(_worker))
        {
            _worker.NGSetAsWorking(false);
            return true;
        }
        return false;
    }

    override public void UpdateInternal(BuildingComponentsState _state)
    {
        base.UpdateInternal(_state);
        UpdateWorkers();
        UpdateCarryResourceToBuilding();
    } 

    void UpdateWorkers()
    {
        if(m_workersPresent.Count == 0) return;
        
        Building.m_isInhabited = true; 
        
        for (var i = m_workersPresent.Count - 1; i >= 0; i--)
        {
            var worker = m_workersPresent[i];

            if(worker.IsTimeToHangout && worker.SendToHangOut())
            {
                Leave(worker);
            }
            else if(worker.IsTimeToGoHome || worker.IsTimeToHangout)
            {
                if(m_building.WorkerArrivesToRest(worker))
                {
                }
                else if(worker.Home != null)
                {
                    worker.SetMoveToComponent(worker.Home, PeepActions.ReturnToRest);
                }
                else
                {
                    worker.SetMoveToPosition(m_building ? m_building.DoorPosOuter : GetDoorPos(), false, PeepActions.WaitingForHome);   
                }
                Leave(worker);
            }
        }
    }
    
    public void UpdateCarryResourceToBuilding()
    {
        var worker = GetAvailableWorker();
        
        if(worker != null)
        {
            var bestStockDestination = m_building.GetBestStockDestination();

            SendOutToDeliver(bestStockDestination, worker);
        }
    }
    
    private bool SendOutToDeliver(MABuilding.BestStockDestinationCheck _destination, MACharacterBase _character)
    {
        if(_destination.Empty) return false;
        
        var pickup = m_building.TakeItemForDelivery(_destination.m_supplyWhat, (o) => {
            var item = o.GetComponent<ReactPickupPersistent>();
            item.m_intendedDestination = _destination.m_building;
            item.AssignToCarrier(_character); // do this after SetMoveToBuilding so the object is enabled and animator is valid
        });
                
        if(pickup != null)
        {
            m_nextDeliverTime = Time.time + NGManager.Me.m_workerLeavesDelay;
            _character.SetMoveToBuilding(_destination.m_building, PeepActions.DeliverToInput);
            Leave(_character);
            return true;
        }
        return false;
    }

    override public MACharacterBase GetAvailableWorker()
    {
        if (Time.time < m_nextDeliverTime)
            return null;
            
        foreach(var w in m_workersPresent)
        {
            if(w.PowerAvailable <= 0f || w.IsLowEnergy()) continue;
            
            return w;
        }
        return null; 
    }
    
    override public float GetWorkerPower()
    {
        float power = 0f;
        foreach(var wp in m_workersAllocated)
        {
            power += wp.PowerAvailable;
        }
        return power;
    }
    
    /*override public bool ConsumePower(ref float _power)
    {
        var usedPower = false;
        foreach(var wp in m_workersPresent)
        {
            float powerToConsume = Mathf.Min(wp.PowerAvailable, _power);
            if(powerToConsume > 0f)
            {
                wp.ConsumePower(powerToConsume);
                _power -= powerToConsume;
            }
            
            if(_power <= 0) return true;
        }
        return false;
    }*/
    
    //override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (BCWorkerPanel.PanelID, () => new BCWorkerPanel());
}

public class BCWorkerPanel : BCUIPanel
{
    public override int Priority => 20;
    public const string PanelID = "workers";
    public List<BCWorkerBase> m_bcWorkers = new();
    private List<MAComponentInfo> m_componentTypes = new List<MAComponentInfo>();
    public override bool ForceDisableQuantityDisplay => true;
    public override bool m_forceDisableHighlightButton => true;
    private MABuilding m_building;
    
    public BCWorkerPanel() : base(PanelID, "Workers") 
    {
        // Todo, description needs to be icons
    }
    
    public override string GetPrimaryText()
    {
        string text = "";
        
        BCBase.CombinedStat stat = null;
        foreach (var c in m_bcWorkers) c.GetCombinedValue(ref stat);
        text += stat?.GetValue(BCBase.CombinedStat.ValueDisplay.Default, true);
        
        text += "\n\nView and manage workers assigned to workstations in this building.";
        
        return text;
    }
    
    public override string GetDescription()
    {
        int workers = 0;
        foreach (var c in m_bcWorkers)
        {
            workers += c.m_workersAllocated.Count;
        }
        if(workers > 1) return $"{workers} workers";
        return $"{workers} worker";
    }

    public override void AddComponent(BCBase _component)
    {
        var wc = _component as BCWorkerBase;
        if(wc != null)
        {
            m_building = wc.Building;
            m_bcWorkers.Add(wc);
            if(m_componentTypes.Contains(wc.m_info) == false)
                m_componentTypes.Add(wc.m_info);
            m_quantity += wc.m_maxWorkers;
            m_all.Add(wc);
        }
        // Dont call base
    }

    public override bool RebuildTableLines()
    {
        foreach(var character in m_characters)
        {
            if(character.Job == null || character.Job.Building != m_building)
                return true;
        }
        return m_forceUpdateTableLines;
    }

    List<MACharacterBase> m_characters = new();
    
    override public IEnumerable<System.Action> CreateTableLines(Transform _holder)
    {
        m_forceUpdateTableLines = false;
        m_characters.Clear();
        
        foreach(var type in m_componentTypes)
        {
            BCBase.CombinedStat stat = null;
            foreach(var w in m_bcWorkers)
            {
                if(w.m_info == type)
                    w.GetCombinedValue(ref stat);
            }
            int componentCount = m_building.GetBuildingComponentCount(type);
            yield return () => MADesignInfoSheetLine.Create(_holder, $"<b>{type.m_title} x{componentCount}</b>", stat?.GetValue(BCBase.CombinedStat.ValueDisplay.InfoPanel), false);
            int count = 0;
            int max = 0;
            foreach(var w in m_bcWorkers)
            {
                if(w.m_info == type)
                {
                    max += w.m_maxWorkers;
                    foreach(var worker in w.m_workersAllocated)
                    {
                        count++;
                        m_characters.Add(worker);
                        yield return () => MABuildingWorkerPanelLine.Create(_holder, worker, true, this);
                    }
                }
            }
            
            for(int i = count; i < max; i++)
            {
                yield return () => MADesignInfoSheetLine.Create(_holder, "<i>Free Slot</i>", null, false, false);
            }
        }
    }
}