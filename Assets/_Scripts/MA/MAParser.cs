using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using UnityEngine;
using Object = UnityEngine.Object;

public class MAParser
{
    public class TutorialTrigger : Attribute { }
    public class TutorialCommands : Attribute { }
    public class TutorialIf : Attribute { }
    public class BusinessCommands : Attribute { }
    public class ParserCommands : Attribute { }
    public class FunctionDescriptionAttribute : Attribute
    {
        public string Description { get; }

        public FunctionDescriptionAttribute(string description)
        {
            Description = description;
        }
    }
    [Flags]
    public enum Triggers
    {
        None = 0,
        MoveCamera = 1 << 0,
        FollowCamera = 1 << 1,
        Gesturing = 1 << 2,
        Screenshot = 1 << 3,
        CameraSequence = 1 << 4,
        LookAt = 1 << 5,
    }

    public static Vector3 m_parsedPos;
    public static Triggers m_triggers;
    public class MAQuestInfo {}
    static HashSet<string> s_shownErrors = new();
    public static void ParserError(string _s)
    {
        if (s_shownErrors.Add(_s) == false)
            return;
        Debug.LogError(MAParserSupport.DebugColor(_s));
    }
    #region Commands
    // Check(WaitForDusk(), Label(AfterDawn))
    // Check(WaitForDusk(), Execute(AfterDawnScript))
    
    [TutorialIf] public static void Check(string _conditionToWaitFor, string _labelToJumpTo)
    {
        MAParserSection maParserSection = null;
        int currentLine = 0;

        //KW: find the section and current line on load, make sure condition/label combos are unique if saving
        if (MAParserManager.m_updatingMaParserSection == null)
        {
            foreach(var section in MAParserManager.Me.m_sections)
            {
                foreach(var line in section.m_lines)
                {
                    if(line.m_line.Contains(_conditionToWaitFor) && line.m_line.Contains(_labelToJumpTo))
                    {
                        maParserSection = section;
                        currentLine = line.m_lineNumber;
                        break;
                    }
                }

                if(maParserSection != null)
                {
                    break;
                }
            }

            if(maParserSection == null)
            {
                ParserError($"Check - could not find section {_conditionToWaitFor} to jump to {_labelToJumpTo}");
                if (MAParserManager.m_updatingMaParserSection != null)
                {
                    ParserError($"in section {MAParserManager.m_updatingMaParserSection.m_fileName}");

                }
                return;
            }
        }
        else
        {
            maParserSection = MAParserManager.m_updatingMaParserSection;
            currentLine = MAParserManager.m_updatingMaParserSection.m_currentLine;
        }

        var condition = $"{_conditionToWaitFor.Trim()}|{_labelToJumpTo.Trim()}|{currentLine}";
        var found = maParserSection.m_checks.Find(o=> o.m_check == condition);
        if (found == null)
        {
            maParserSection.m_checks.Add(new MAParserSection.SectionCheck(condition));
        }
    }

    // [TutorialCommands] public static void SetDayLength(int _day, float _dayLength)
    // {
    //     MACalenderInfo.SetDayLength(_day, _dayLength);
    // }
    [TutorialCommands] public static void SetNightLength(int _day, float _dayLength)
    {
        MACalenderInfo.SetNightLength(_day, _dayLength);
    }

    [TutorialCommands] public static void SetSpawnMarkerColour(string _colour)
    {
        switch(_colour.ToLower())
        {
            case "red":
                GameManager.Me.m_state.m_spawnMarkerColour = Color.red;
                break;

            default:
                // RW-04-JUL-25: Unity 6.1 defines a load of colours by name, but we don't have that yet. This is orange.
                GameManager.Me.m_state.m_spawnMarkerColour = new Color(1f, 0.4f, 0f, 1f);
                break;
        }

				// RW-07-JUL-25: Ensure the overlay gets regenerated, since the colour may have changed.
				TowerDefenseVisualsManager.Me.DestroyOverlay(true);
    }
    [TutorialCommands] public static void DestroyCheck(string _conditionToWaitFor, string _labelToJumpTo)
    {
        var condition = $"{_conditionToWaitFor.Trim()}|{_labelToJumpTo.Trim()}|";

        foreach (var section in MAParserManager.Me.m_sections)
        {
            for(int i = section.m_checks.Count - 1; i >= 0; i--)
            {
                var found = section.m_checks.Find(o=> o.m_check == condition);
            
                if(found != null)
                {
                    section.m_checks.RemoveAt(i);
                }
            }   
        }
    }
    
    [TutorialCommands] public static void GotoLabel(string _section, string _label)
    {
        var section = MAParserManager.Me.GetSection(_section.Trim());
        if (section == null)
        {
            ParserError($"GotoLabel - could not find section {_section}");
            return;
        }
        if (section.GoToLabel(_label.Trim()) == false)
        {
            ParserError($"GotoLabel - Could not find label '{_label}'");
            return;
        }

        section.ClearCurrents();
    }
    [TutorialCommands] public static void GotoLabel(string _label)
    {
        if(MAParserManager.m_updatingMaParserSection.GoToLabel(_label.Trim()) == false)
        {
            ParserError($"GotoLabel - Could not find label '{_label}'");
            return;
        }
        MAParserManager.m_updatingMaParserSection.ClearCurrents();
    }

    [TutorialCommands] public static void Label(string _label)
    {
        Debug.Log("Label: " + _label);  
    }
    
    [TutorialCommands] public static bool WaitForMakeProduct(string _productLine)
    {
        var confirms = ProductTestingManager.GetDesignConfirms(_productLine.ToLower().Trim());
        var trackedConfirms = MAParserManager.GetOrSetTrackValue(confirms);
        return confirms > trackedConfirms;
    }
    
    [TutorialCommands] public static void ShowNightReminder(bool _flag)
    {
        CalendarUIManager.Me.ShowNightReminder = _flag;
    }
    [TutorialCommands] public static void ShowAdvisor(string _advisor, string _type, float _time)
    {
        var advisor = NGBusinessAdvisor.GetInfo(_advisor.Trim());
        if (advisor == null)
        {
            ParserError($"ShowAdvisor - Could not find advisor '{_advisor}'");
            return;
        }
        NGTutorialMessageSubtitle.Create(advisor.PortaitSprite, _type, _time);
    }
    [TutorialCommands] public static void SetResearchFaction(string _faction)
    {
        if(Enum.TryParse(_faction, out MAFactionInfo.FactionType faction) == false)
        {
            ParserError($"SetResearchFaction - Could not parse faction '{_faction}'");
            return;
        }
        MAResearchManagerUI.SetFaction(faction);
    }   
    [TutorialCommands] public static void PlayVideo(string _name, string _message)
    {
        MAVideoPlayer.Create(_name, _message);
    }   
    
    [TutorialCommands] public static void SpawnHelperSign(string _type, Vector3 _pos, string _section)
    { //Pos[-0.49;0;0.256]
        MAHelperSign.Create(_type.Trim().ToLower(), _section, _pos);
    }
    [TutorialCommands] public static void TogglePublisherCheats(bool _flag)
    {
        MAPublisherCheats.Create();
    }

    [TutorialCommands] public static void SetNightFailScript(string _sectionName)
    {
        MAParserManager.Me.m_nightFailSectionName = _sectionName;
    }

    [TutorialCommands] public static void ToggleMoneyHUD(bool _flag)
    {
       NGManager.Me.m_topRightHolder.gameObject.SetActive(_flag);
    }
    [TutorialCommands] public static void ToggleTagHUD(bool _flag)
    {
        MATagManager.Me.SetTagPanelToShow(_flag);
    }
    [TutorialCommands] public static void ShowMessageFromFile(string _title, string _file)
    {
        var textAsset = Resources.Load<TextAsset>($"Flows/Messages/{_file.Trim()}");
        if(textAsset == null)
        {
            ParserError($"ShowMessageFromFile - Could not find file '{_file}'"); 
            return;
        }
        Utility.ShowDialog(_title, textAsset.text, false, "Ok", null, null);
    }

    [TutorialCommands] public static void SaveDay()
    {
        GameManager.Me.SaveDay();
    }
    [TutorialCommands] public static void ShowMessage(string _title, string _msg)
    {
        Utility.ShowDialog(_title, _msg, false, "Ok", null, null);
    }
    [TutorialCommands] public static void QuestTrigger(string _name, Vector3 _pos, float _radius, string _triggerType, string _lookFor, string _questName, bool _debug)
    {
        var lookFor = new List<string>(_lookFor.Split(';','|').Select(x => x.Trim()));
        MAQuestTrigger.Create(_name.Trim(), _pos, _radius, _triggerType.Trim(), lookFor, _questName.Trim(), _debug);
    }
    [TutorialCommands] public static void ToggleTODHUD(bool _flag)
    {
        CalendarUIManager.Me.SetVisible(_flag);
    }

    [TutorialTrigger] public static void OnPlayedDesignTableReaction()
    {
        DesignTableManager.Me.OnReactionPlayed();
    }
    [TutorialTrigger] public static bool WaitForDesignTableReaction()
    {
        return DesignTableManager.Me.m_pendingReaction != DesignTableManager.DesignTableReaction.None;
    }
    
    [TutorialTrigger] public static bool DesignTableReaction(string _reaction)
    {
        if(Enum.TryParse<DesignTableManager.DesignTableReaction>(_reaction, true, out var reaction))
        {
            return (DesignTableManager.Me.m_pendingReaction == reaction);
        }
        return false;
    }
    
    [TutorialTrigger] public static bool WaitForCreatureDeath(int  _count)
    {
        var currentDeaths = GameManager.Me.m_state.m_creaturesDefeated;
        var trackedDeaths = MAParserManager.GetOrSetTrackValue(currentDeaths);
        if (currentDeaths - trackedDeaths < _count) return false;
        // If we remove the trakced value, it will break the check function
        //MAParserManager.RemoveTrackedValue();
        return true;
    }
    [TutorialTrigger] public static bool WaitForCameraPanPoint(string _name)
    {
        return CameraPanNode.s_lastCameraPanPoint == _name;
    }
    
    [TutorialCommands] public static void QuestTrigger(string _name, MABuilding _building, float _radius, string _triggerType, string _lookFor, string _questName, bool _debug)
    {
        if(_building == null)
        {
            ParserError($"QuestTrigger - Building is null");
            return;
        }
        QuestTrigger(_name, _building.transform.position, _radius, _triggerType, _lookFor, _questName, _debug);
    }
    [TutorialCommands] public static void QuestTrigger(string _name, NGDecoration _decoration, float _radius, string _triggerType, string _lookFor, string _questName, bool _debug)
    {
        if(_decoration == null)
        {
            ParserError($"QuestTrigger - Building is null");
            return;
        }
        QuestTrigger(_name, _decoration.transform.position, _radius, _triggerType, _lookFor, _questName, _debug);
    }
    
    [TutorialTrigger] public static bool WaitForQuestTrigger(string _name, MABuilding _building, float _radius, string _lookFor, bool _debug)
    {
       if(_building == null)
       {
           ParserError($"WaitForQuestTrigger - Building is null");
           return false;
       }
       return WaitForQuestTrigger(_name, _building.transform.position, _radius, _lookFor, _debug);
    }
    [TutorialTrigger] public static bool WaitForQuestTrigger(string _name, Vector3 _pos, float _radius, string _lookFor, bool _debug)
    {
        if (MAParserManager.CurrentQuestTrigger == null)
        {
            var lookFor = MAParserSupport.StringToList(_lookFor);

            MAParserManager.CurrentQuestTrigger = MAQuestTrigger.Create(_name.Trim(), _pos, _radius , lookFor, _debug);
        }
        return MAParserManager.CurrentQuestTrigger.m_triggered;
    }
    
    [TutorialTrigger] public static bool WaitForPossessedInRange(Vector3 _pos, float _radius)
    {
        if (GameManager.Me.PossessedCharacter != null)
        {
            var distance = (GameManager.Me.PossessedCharacter.transform.position - _pos).magnitude;
            return distance <= _radius;
        }

        return false;
    }
    
    [TutorialTrigger] public static bool WaitForPossessedInCharacterRange(MAFlowCharacter _character, float _radius)
    {
        if (GameManager.Me.PossessedCharacter != null)
        {
            if (_character != null)
            {
                var distance = (GameManager.Me.PossessedCharacter.transform.position - _character.transform.position).magnitude;
                return distance <= _radius;
            }
        }
        return false;
    }
    
    [TutorialTrigger] public static bool WaitForPossessedInCharacterRange(String _character, float _radius)
    {
        if (GameManager.Me.PossessedCharacter != null)
        {
            MACharacterBase character = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == _character);
            
            if (character != null)
            {
                var distance = (GameManager.Me.PossessedCharacter.transform.position - character.transform.position).magnitude;
                return distance <= _radius;
            }
        }
        return false;
    }

    [TutorialCommands] public static void KillQuestTrigger(string _name)
    {
        MAQuestTrigger.KillTrigger(_name.Trim());
    }

    [TutorialCommands] public static void FeedbackAllowBuildLocation(string _location, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions.AddAllowBuildLocation(_location, _advisorName, _type, _pose, _message, _audioID);
    }
    [TutorialCommands] public static void FeedbackDenyBuildLocation(string _location, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions.AddDenyBuildLocation(_location, _advisorName, _type, _pose, _message, _audioID);
    }
    
    [TutorialCommands] public static void FeedbackRequiredComponentsInDesign(string _componentName, int _count, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions.AddRequiredComponentsInDesign(_componentName, _count, _advisorName, _type, _pose, _message, _audioID);
    }
    [TutorialCommands] public static void FeedbackAllowBlock(string _blockID, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions.AddAllowBlock(_blockID, _advisorName, _type, _pose, _message, _audioID);
    }
    [TutorialCommands] public static void FeedbackDenyBlock(string _blockID, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions.AddDenyBlock(_blockID, _advisorName, _type, _pose, _message, _audioID);
    }
    [TutorialCommands] public static void FeedbackOnlyBlocks(string _blockTypes, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions.AddOnlyBlocks(_blockTypes, _advisorName, _type, _pose, _message, _audioID);
    }
    [TutorialCommands] public static void FeedbackAllowDesignScore(float _start, float _end, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions.AddAllowDesignScore(_start, _end, _advisorName, _type, _pose, _message, _audioID);
    }
    [TutorialCommands] public static void FeedbackDenyDesignScore(float _start, float _end, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions.AddDenyDesignScore(_start, _end, _advisorName, _type, _pose, _message, _audioID);
    }
    [TutorialCommands] public static bool WaitForConditions(string _designCondition)
    {
        MAParserManager.m_updatingMaParserSection.TryLoadDesignConditions(_designCondition);
        if(MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions != null && MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions.m_complete)
            MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions = null;
        return MAParserManager.m_updatingMaParserSection.m_currentFeedbackConditions == null;
    }
    
    [TutorialCommands] public static void Save(string _tag, string _functionCommand)
    {
        MAParserManager.Me.SaveFlowStep(_tag, _functionCommand)?.Execute();
    }
    [TutorialCommands] public static void ClearSaves()
    {
        GameManager.Me.m_state.m_parserCommands.Clear();
    }
    [TutorialCommands] public static void Unsave(string _tag)
    {
        MAParserManager.Me.RemoveFlowStep(_tag);
    }
    [TutorialCommands] public static void PlayAudio(string _clip, MABuilding _building)
    {
        AudioClipManager.Me.PlaySound(_clip, _building?.gameObject);
    }
    [TutorialCommands] public static void Unsave(string _tag, string _functionCommand)
    {
        MAParserSupport.TryParse(_functionCommand.Trim(), out var result);
        Unsave(_tag);
    }
    
    [FunctionDescription("Thumbs Up, Hang Loose, Loser, Peace, F Off, A Hole, Fingers Crossed, Thumbs Down, Wanker")]
    [TutorialCommands] public static void HandGesture(string _name)
    {
        if (InputUtilities.GetCurrentDragObject() != null) return;
        PlayerHandManager.Me.PlayHandGesture(_name.Trim());
    }

    [TutorialCommands] public static void UnlockRegionDrawers(string _regionName)
    {
        foreach (var drawerInfo in MADrawerInfo.GetList)
        {
            if (drawerInfo.m_district == _regionName)
            {
                GameManager.UnlockDraw(drawerInfo, true);
            }
        }
    }
    [FunctionDescription("use UnlockDrawer(Armourer:Roofs) to unlock a singe draw or UnlockDrawer(Armourer) to unlock all drawers")]
    [TutorialCommands] public static void UnlockDrawer(string _name, bool _flag)
    {
        if (_name.Contains(':') == false)
        {
            var infos = MADrawerInfo.GetInfosByDrawerName(_name);
            if (infos == null || infos.Count == 0)
            {
                ParserError($"Can't find Drawer {_name}.");
                return;
            }
            foreach (var i in infos)
            {
                GameManager.UnlockDraw(i, _flag);
            }

            return;
        }
        var info = MADrawerInfo.GetInfo(_name);
        if (info == null)
        {
            ParserError($"No such drawer as {_name}");
            return;
        }
        GameManager.UnlockDraw(info, _flag);
    }

    [TutorialCommands] public static void OpenDrawSet(string _topDrawName, string _partSet, string _subSet)
    {
        DesignTableManager.Me.OpenDrawSet(_topDrawName.Trim(), _partSet.Trim(), _subSet.Trim());
    }
    
    [TutorialCommands] public static bool IsEvil()
    {
        return AlignmentManager.Me.IsEvil;
    }
    [TutorialCommands] public static bool IsGood()
    {
        return AlignmentManager.Me.IsGood;
    }
    [TutorialCommands] public static bool IsNeutral()
    {
        return AlignmentManager.Me.IsNeutral;
    }
    [TutorialCommands] public static void ChangeAlignment(string _action, float _value)
    {
        AlignmentManager.Me.ApplyAction(_action.Trim(), _value, "");
    }
    [TutorialCommands] public static void ChangeAlignment(string _action, float _value, string _context)
    {
        AlignmentManager.Me.ApplyAction(_action.Trim(), _value, _context.Trim());
    }
    
    [TutorialCommands] public static void ClearMarketForce()
    {
        MAMarketForcesManager.Me.ClearMarketForces();
    }
    [TutorialCommands] public static void ApplyMarketForce(string _groupName)
    {
        if(_groupName.IsNullOrWhiteSpace() == false && MAUnlocks.Me.m_marketForces == false)
        {
            Unlock("m_marketForces=true");
        }
        
        MAMarketForcesManager.Me.ApplyMarketForces(_groupName.Trim());
    }
    
    [ParserCommands] public static void CharacterImmortal(NGMovingObject _character, bool _value)
    {   
        MACharacterBase cb = _character as MACharacterBase;
        if(cb == null) return;
        cb.CharacterGameState.m_immortal = _value;
    }
    [TutorialCommands] public static void CreateCharacter(string _name, MAWorkerInfo _workerInfo, MABuilding _building)
    {
        if (_building == null)
        {
            ParserError("No such building");
            return;
        }
        var existing = MAFlowCharacter.FindCharacter(_name);
        if(existing == null)
            MAFlowCharacter.Create(_name, _workerInfo, _building.DoorPosOuter, MAParserManager.m_updatingMaParserSection);
    }
    [TutorialCommands] public static void CreateCharacterInBuilding(string _name, MAWorkerInfo _workerInfo, MABuilding _building)
    {
        if (_building == null)
        {
            ParserError("No such building");
            return;
        }
        var existing = MAFlowCharacter.FindCharacter(_name);
        if(existing == null)
            MAFlowCharacter.Create(_name, _workerInfo, _building.DoorPosInner, MAParserManager.m_updatingMaParserSection, Quaternion.identity, _building);
    }
    [TutorialCommands] public static void CreateCharacter(string _name, MAWorkerInfo _workerInfo, Vector3 _pos)
    {
        var existing = MAFlowCharacter.FindCharacter(_name);
        if(existing == null)
            MAFlowCharacter.Create(_name, _workerInfo, _pos, MAParserManager.m_updatingMaParserSection);
    }
    [TutorialCommands]
    public static void CreateCharacter(string _name, MAWorkerInfo _workerInfo, Vector3 _pos, Quaternion _rot)
    {
        var existing = MAFlowCharacter.FindCharacter(_name);
        if (existing == null)
            MAFlowCharacter.Create(_name, _workerInfo, _pos, MAParserManager.m_updatingMaParserSection, _rot);
    }
    [FunctionDescription("This moves a created character to a position")]
    [TutorialCommands] public static void MoveCharacterToPosition(MAFlowCharacter _character, Vector3 _pos)
    {
        if(_character == null)
        {
            ParserError($"MoveCharacterToPosition - Could not find character.");
            return;
        }
        _character.SectionMoveToPosition(_pos);
    }
    [TutorialCommands] public static void MoveCharacterToBuilding(MAFlowCharacter _character, MABuilding _building)
    {
        if(_character == null)
        {
            ParserError($"MoveCharacterToPosition - Could not find character");
            return;
        } 
        if(_building == null)
        {
            ParserError($"MoveCharacterToPosition - Could not find building");
            return;
        }
        _character.SectionMoveToBuilding(_building);
    }
    [TutorialCommands] public static void MoveToPosition(MACharacterBase _character, Vector3 _pos, string _action)
    {
        if(_character == null)
        {
            ParserError($"MoveToPosition - Could not find character.");
            return;
        }

        if (_character.StateLibrary().ContainsKey(CharacterStates.PatrolToWaypoint) == false)
        {
            PeepActions action = PeepActions.None;
            _action = _action.Trim();
            Enum.TryParse(_action, out action);

            _character.SetMoveToPosition(_pos, false, action);
        }
        else
        {
            _character.ObjectiveWaypoint = _pos.SetYToHeight();
            MACharacterStateFactory.ApplyCharacterState(CharacterStates.PatrolToWaypoint, _character);
        }
    }

    [TutorialCommands] public static void MoveToPositionAndLookAt(MACharacterBase _character, Vector3 _pos, string _lookAtTarget)
    {
        if(_character == null)
        {
            ParserError($"MoveToPosition - Could not find character.");
            return;
        }

        PeepActions action = PeepActions.GoLookAt;

        #if UNITY_EDITOR
        if (MACharacterBase.GetLookAtTarget(_lookAtTarget, out Vector3 target) == false || target == Vector3.zero)
        {
            Debug.LogError($"MAParser - MoveToPositionAndLookAt - Valid LookAt Target? '{target}' for {_character.name}");
        }
        #endif
        
        _character.SetMoveToPosition(_pos, false, action);
        _character.CharacterGameState.m_lookAtTarget = _lookAtTarget;
    }
    [TutorialCommands]
    public static void MoveToPosition(MACharacterBase _character, Vector3 _pos)
    {
        MoveToPosition(_character, _pos, "None");
    }

    [TutorialCommands]
    public static void SetSpeed(MACharacterBase _character, float _speed)
    {
        _character.CharacterGameState.m_speed = _speed;
    }

    [TutorialCommands]
    public static void SetUseWalkSpeed(MACharacterBase _character)
    {
        SetSpeed(_character, _character.CharacterGameState.WalkSpeed);
    }

    [TutorialCommands]
    public static void SetUseRunSpeed(MACharacterBase _character)
    {
        SetSpeed(_character, _character.CharacterGameState.AttackSpeed);
    }

    [TutorialCommands] public static void MoveToBuilding(MACharacterBase _character, MABuilding _building, string _action)
    {
        if(_character == null)
        {
            ParserError($"MoveToBuilding - Could not find character");
            return;
        }
        if(_building == null)
        {
            ParserError($"MoveToBuilding - Could not find building");
            return;
        }

        PeepActions action = PeepActions.None;
        Enum.TryParse(_action, out action);

        _character.SetMoveToBuilding(_building, action);
    }

    [TutorialCommands]
    public static void MoveToBuilding(MACharacterBase _character, MABuilding _building)
    {
        MoveToBuilding(_character, _building, "None");
    }

    [TutorialCommands] public static void DestroyCharacter(MAFlowCharacter _character)
    {
        if(_character == null)
        {
            ParserError($"DestroyCharacter - Could not find character.");
            return;
        }
        _character.DestroyMe();
    }
    [TutorialCommands] public static void DestroyCharacter(MACharacterBase _character)
    {
        if(_character == null)
        {
            ParserError($"DestroyCharacter - Could not find character.");
            return;
        }
        _character.DestroyMe();
    }
    [TutorialCommands] public static void DestroyCharacter(String _name)
    {
        var existing = MAFlowCharacter.FindCharacter(_name);
        if (existing == null)
        {
            ParserError($"DestroyCharacter - Could not find character.");
            return;
        }
        existing.DestroyMe();
    }
    
    // Use when unsure if the character exists or not
    // This won't produce a warning if the character does not exist
    [TutorialCommands] public static void TryDestroyCharacter(String _name)
    {
        var existing = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == _name);
        if (existing == null)
        {
            return;
        }
        existing.DestroyMe();
    }
    
    [TutorialCommands] public static bool CharacterExists(String _name)
    {
        var existing = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == _name);
        return existing != null;
    }
    

    [TutorialTrigger] public static bool WaitForCharacterState(NGMovingObject _character, string _state)
    {
        MACharacterBase cb = _character as MACharacterBase;
        if (cb == null)
        {
            ParserError($"WaitForCharacterState - Could not find character.");
            return false;
        }
        
        if(Enum.TryParse(_state.Trim().ToUpper(), out MAFlowCharacter.STATE state) == false)
        {
            ParserError($"WaitForCharacterState - Could not parse state '{_state}'");
            return false;
        }
        
        if (state == MAFlowCharacter.STATE.IDLE)
        {
            switch(_character.m_state)
            {
                case MAFlowCharacter.STATE.IDLE:
                case MAFlowCharacter.STATE.MA_DECIDE_WHAT_TO_DO:
                    return true;
            }

            return false;
        }
        return _character.m_state == state;
    }
    
    [TutorialTrigger] public static bool WaitForCharacterState(MAFlowCharacter _character, string _state)
    {
        if(_character == null)
        {
            ParserError($"WaitForCharacterState - Could not find character.");
            return false;
        }
        if(Enum.TryParse(_state.Trim().ToUpper(), out MAFlowCharacter.STATE state) == false)
        {
            ParserError($"WaitForCharacterState - Could not parse state '{_state}'");
            return false;
        }

        if (state == MAFlowCharacter.STATE.IDLE)
        {
            switch(_character.m_state)
            {
                case MAFlowCharacter.STATE.IDLE:
                case MAFlowCharacter.STATE.MA_DECIDE_WHAT_TO_DO:
                    return true;
            }

            return false;
        }
        return _character.m_state == state;
    }
    [TutorialCommands] public static void StartQuestOrder(MABuilding _building, MAOrderInfo _order)
    {
        if(_building == null)
        {
            ParserError($"ToggleCardHolder - Building is null");
            return;
        }
        if(_order == null)
        {
            ParserError($"ToggleCardHolder - Order is null");
            return;
        }
        _building.StartQuestOrder(_order);
    }

    [TutorialCommands]
    public static void EnableBlockers(string _prefix, bool _flag)
    {
        GameState_MovementBlocker.Enable(_prefix, _flag);
    }
    
    [TutorialCommands] public static void DisablePlotExcluder(string _id) => PlotExcluder.PermanentlyDisable(_id.Trim());
    
    [TutorialCommands] public static void AddMoney(float _amount)
    {
        NGPlayer.Me.m_cash.Add(CurrencyContainer.TransactionType.Given, _amount, "Flow cash");
    }
    [TutorialCommands] public static void SpendMoney(float _amount)
    {
        NGPlayer.Me.m_cash.Spend(10000, "Flow", "", "");
    }
    [TutorialCommands] public static void CreateGodBeam(MABuilding _building, string _name, string _type)
    {
        if(_building == null)
        {
            ParserError($"CreateGodBeam - Building is null");
            return;
        }
        CreateGodBeam(_building.gameObject, _name, _type);
    }
    [TutorialCommands] public static void CreateGodBeam(GameObject _object, string _name, string _type)
    {
        if(_object == null)
        {
            ParserError($"CreateGodBeam - Object is null");
            return;
        }
        var godBeam = MAGodBeam.Create(_name.Trim(), _object, _type.Trim());
    }
    [TutorialCommands] public static void CreateGodBeam(MAFlowCharacter _character, string _name, string _type)
    {
        if(_character == null)
        {
            Debug.LogError(MAParserSupport.DebugColor($"CreateGodBeam - Can't find character."));
            return;
        }
        var godBeam = MAGodBeam.Create(_name.Trim(), _character.gameObject, _type.Trim());
    }
    [TutorialCommands]
    public static void CreateGodBeam(Vector3 _pos, string _name, string _type)
    {
        var godBeam = MAGodBeam.Create(_name.Trim(), _pos, _type.Trim());
    }
    [TutorialCommands] public static void DestroyGodBeam(string _name)
    {
        _name = _name.Trim();
        var godBeam = MAParserManager.Me.m_godBeams.Find(x => x.m_name == _name);
        if (godBeam == null)
        {
//            ParserError($"DestroyGodBeam - Could not find god beam with name '{_name}'");
            return;
        }
        godBeam.DestroyMe();
    }
    [TutorialCommands] public static void SetSkipMode(bool _flag)
    {
        MAParserManager.m_updatingMaParserSection.m_isStepping = _flag;
    }
    [TutorialCommands] public static void ToggleTourist(bool _flag)
    {
        MATouristManager.Me.ToggleTourist(_flag);
    }
    [TutorialCommands] public static void ToggleTownTorches(bool _flag)
    {
        foreach(var torch in NGManager.Me.m_decorationHolder.GetComponentsInChildren<MAFlameTorch>())
        {
            torch.Toggle(_flag);
        }
    }

    [TutorialCommands]
    public static void ToggleQuickTip(string _name, bool _flag)
    {
        var holder = NGManager.Me.m_screenLocationsHolder.GetComponentsInChildren<MAQuickTipButton>(true);
        var found = holder.FindAll(o=>o.m_name == _name);
        foreach (var f in found)
        {
            if(_flag)
                f.transform.parent.gameObject.SetActive(_flag);
            f.gameObject.SetActive(_flag);
        }
    }

    [TutorialCommands] public static void ToggleCurrencyDisplay(bool _flag)
    {
        var currencyTransform = NGManager.Me.m_topRightHolder.GetComponentsInChildren<Transform>(true).FirstOrDefault(t => t.name == "Currency");

        if (currencyTransform != null)
        {
            currencyTransform.gameObject.SetActive(_flag);
        }
    }
    [TutorialCommands] public static void ResetGameFlowDialogClick()
    {
        MAGameFlow.m_updatingFlow.m_clickedGameFlowDialogAction = null;
    }
    [TutorialCommands]
    public static void MAPauseSpawnPoint(int _id, bool _flag)
    {
        if (MASpawnPoint.s_spawnPoints.TryGetValue(_id, out var spawnPoint))
        {
            spawnPoint.m_paused = _flag;
        }
        else
        {
            Debug.LogError($"MAParser - MAPauseSpawnPoint - Could not find spawn point with id {_id}");
        }
    }
    [TutorialCommands]
    public static void MAPauseAllSpawnPoints(bool _flag)
    {
        foreach (MASpawnPoint spawnPointsValue in MASpawnPoint.s_spawnPoints.Values)
        {
            spawnPointsValue.m_paused = _flag;
        }
    }
    [TutorialCommands] public static void SetGameFlowDialogClick(string _functionName)
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;

//        var parsedQuestBase = MAGameFlow.UpdatingControlObject as MAQuestBase;
        if (parsedQuestBase == null)
        {
            ParserError($"SetGameFlowDialogClick - m_controlObject <MAQuestBase> is null");
            return;
        }     
        var method = parsedQuestBase.GetType().GetMethod(_functionName);
        if (method == null)
        {
            ParserError($"SetGameFlowDialogClick - Could not find method '{_functionName}' in class '{parsedQuestBase.GetType().Name}'");
            return;
        }
        if (parsedQuestBase != null)
        {
            var action = (Action)Delegate.CreateDelegate(typeof(Action), parsedQuestBase, method);
            MAGameFlow.m_updatingFlow.m_clickedGameFlowDialogAction = action;
        }
    }

    [TutorialCommands] public static void ClearQuestRewards()
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;
    //    var parsedQuestBase = MAGameFlow.UpdatingControlObject as MAQuestBase;
        if (parsedQuestBase == null)
        {
            ParserError($"OnQuestComplete - m_parsedQuestGiver is null");
            return;
        }
        parsedQuestBase.ClearQuestRewards();

    }

    [TutorialCommands] public static void SetQuestReward(NGCarriableResource _resource, int _amount)
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;
//        var parsedQuestBase = MAGameFlow.UpdatingControlObject as MAQuestBase;
        if (parsedQuestBase == null)
        {
            ParserError($"OnQuestComplete - m_parsedQuestGiver is null");
            return;
        }

        if (_resource == null)
        {
            ParserError($"OGCarrableResource is null");
            return;
        }
        parsedQuestBase.m_questCompleteResources.Add(new MAQuestBase.QuestResourceReward(){m_resource = _resource, m_amount = _amount});
    }

    [TutorialCommands] public static void SetQuestReward(NGBusinessGift _gift)
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;
        //var parsedQuestBase = MAGameFlow.UpdatingControlObject as MAQuestBase;

        if (parsedQuestBase == null)
        {
            ParserError($"OnQuestComplete - m_parsedQuestGiver is null");
            return;
        }
        parsedQuestBase.m_questCompleteGifts.Add(_gift);
    }

    [TutorialCommands]
    public static void GiveQuestRewards()
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;
        //var parsedQuestBase = MAGameFlow.UpdatingControlObject as MAQuestBase;

        if (parsedQuestBase == null)
        {
            ParserError($"GiveQuestRewards - m_parsedQuestGiver is null");
            return;
        }
        parsedQuestBase.GiveRewards();
    }
    
    // A version to award the quest rewards without completing the quest
    public static void GiveQuestRewardsOnly()
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;
        //var parsedQuestBase = MAGameFlow.UpdatingControlObject as MAQuestBase;

        if (parsedQuestBase == null)
        {
            ParserError($"GiveQuestRewards - m_parsedQuestGiver is null");
            return;
        }
        parsedQuestBase.GiveRewards(false);
    }

    [TutorialCommands]
    public static void Unpossess(float _zoomDistance = 1.0f)
    {
        GameManager.Me.Unpossess(true, _zoomDistance);
    }

    [TutorialCommands]
    public static void Unpossess(bool _zoomOut, float _zoomDistance = 1.0f)
    {
        GameManager.Me.Unpossess(_zoomOut, _zoomDistance);
    }
    
    [TutorialCommands]
    public static void BlockPossession(NGMovingObject _movingObject, bool _prevent)
    {
        if (_movingObject != null)
        {
            if (_movingObject.GameState == null)
            {
                Debug.LogError($"MAParser - BlockPossession - null _movingObject.GameState to set _prevent as '{_prevent}'");
                return;
            }
            _movingObject.GameState.m_blockPossession = _prevent;
        }
        else
        {
            Debug.LogError($"MAParser - BlockPossession - null _movingObject to set _prevent as '{_prevent}'");
        }
    }

    [TutorialCommands]
    public static void TriggerQuestEvent(string _event)
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;

        if (parsedQuestBase == null)
        {
            ParserError($"TriggerQuestEvent - parsedQuestBase is null");
            return;
        }
        parsedQuestBase.TriggerQuestEvent(_event);
    }
    
    public static void TriggerQuestEvent(string _quest, string _event)
    {
        var parsedQuestBase = MAQuestManager.Me.GetQuest(_quest);

        if (parsedQuestBase == null)
        {
            ParserError($"TriggerQuestEvent - parsedQuestBase is null");
            return;
        }
        parsedQuestBase.TriggerQuestEvent(_event);
    }
    
    [TutorialCommands] public static bool WaitForQuestEvent(string _event)
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;

        if (parsedQuestBase == null)
        {
            ParserError($"WaitForQuestFunction - m_controlObject <MAQuestBase> is null");
            return false;
        }
        return (bool)parsedQuestBase.WaitForQuestEvent(_event);;
    }

    [TutorialCommands]
    public static bool WaitForQuestEvent(string _quest, string _event)
    {
        var parsedQuestBase = MAQuestManager.Me.GetQuest(_quest);

        if (parsedQuestBase == null)
        {
            ParserError($"WaitForQuestFunction - m_controlObject <MAQuestBase> is null");
            return false;
        }
        return (bool)parsedQuestBase.WaitForQuestEvent(_event); ;
    }

    [TutorialCommands]
    public static bool QuestObjectiveComplete(string _event)
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;
        //var parsedQuestBase = MAGameFlow.UpdatingControlObject as MAQuestBase;

        if (parsedQuestBase == null)
        {
            ParserError($"QuestObjective - m_parsedQuestGiver is null");
            return false;
        }

        return parsedQuestBase.QuestObjectiveValue(_event) == 0.0f;
    }

    [TutorialCommands] public static void SpawnAcceptQuest(string _challange)
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;

//        var parsedQuestBase = MAGameFlow.UpdatingControlObject as MAQuestBase;
        if (parsedQuestBase == null)
        {
            ParserError($"OnQuestComplete - m_parsedQuestGiver is null");
            return;
        }
        parsedQuestBase.ShowAcceptDialog(_challange, "");
    }

    [TutorialCommands]
    public static void SpawnQuest(string _quest)
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;
        //var parsedQuestBase = MAGameFlow.UpdatingControlObject as MAQuestBase;

        if (parsedQuestBase != null)
        {
            parsedQuestBase.ActivateQuest();
            return;
        }
        ParserError($"OnQuestComplete - parsedQuestBase is null");
    }

    [TutorialCommands]
    public static void SetCurrentQuest(string _quest)
    {
        SetCurrentQuest(_quest, false);
    }

    [TutorialCommands]
    public static void SetCurrentQuest(string _quest, bool _isMain)
    {
        var questSpawnHolder = Utility.FindTransformByPath("Town/QuestSpawnHolder");

        if (questSpawnHolder != null)
        {
            var questBases = questSpawnHolder.GetComponentsInChildren<MAQuestBase>();

            if (questBases != null && questBases.Length > 0)
            {
                var questBase = questBases.Find(o => o.m_id.Equals(_quest));

                if (questBase != null)
                {
                    questBase.AddSectionFlow(MAParserManager.m_updatingMaParserSection, _isMain);
                    return;
                }
            }
        }

        ParserError($"SetCurrentQuest - could not find {_quest}");
    }

    private static DebugConsole.Command s_completeQuestCmd = new("completequest", _s => SetQuestStatus(_s, "Completed"));

    [TutorialCommands]
    public static void SetQuestStatus(string _quest, string _status)
    {
        MAQuestBase.QuestStatus questStatus = MAQuestBase.QuestStatus.Invalid;

        if(Enum.TryParse(_status, out questStatus))
        {
            var quest = MAParserManager.m_updatingMaParserSection != null ? MAParserManager.CurrentQuest : null;

            if (quest == null)
            {
                var questSpawnHolder = Utility.FindTransformByPath("Town/QuestSpawnHolder");

                if (questSpawnHolder != null)
                {
                    var questBases = questSpawnHolder.GetComponentsInChildren<MAQuestBase>();

                    if (questBases != null && questBases.Length > 0)
                    {
                        var questBase = questBases.Find(o => o.m_id.Equals(_quest));

                        if (questBase != null)
                        {
                            quest = questBase;
                        }
                    }
                }
            }

            if (quest != null)
            {
                if (quest.Status != questStatus)
                {
                    quest.SetQuestStatus(questStatus);
                }
            }
            else
            {
                ParserError($"SetQuestStatus - could not find quest:{_quest}");
            }
        }
        else
        {
            ParserError($"SetQuestStatus - could parse QuestStatus:{_status}");
        }
    }
    [TutorialCommands] static public Vector3 GetPosition(Vector3 _pos)
    {
        m_parsedPos = _pos;
        return _pos;
    }
    [TutorialCommands] static public Vector3 GetPosition(MABuilding _building)
    {
        m_parsedPos = _building.DoorPosOuter;
        return m_parsedPos;
    }
    
    [TutorialCommands] static public Vector3 GetPosition(MAWorker _worker)
    {
        m_parsedPos = _worker.transform.position;
        return m_parsedPos;
    }
    [TutorialCommands] static public Vector3 GetPosition(NGMovingObject _movingObject)
    {
        m_parsedPos = _movingObject.transform.position;
        return m_parsedPos;
    }

    [TutorialCommands] static public void OverrideDayNightControl(MABuilding _building, string _s)
    {
        var state = (DayNightControl.EOverrideState)Enum.Parse(typeof(DayNightControl.EOverrideState), _s);
        DayNightControl.SetStateOverride(_building.gameObject, state);
    }
    
    
    [TutorialCommands] static public void SpawnQuest(MAQuestInfo _quest, MABuilding _building)
    {
        
    }
    [TutorialCommands] static public void SpawnQuest(MAQuestInfo _quest, MAWorker _worker)
    {
        
    }
    [TutorialCommands] static public void SpawnQuest(MAQuestInfo _quest, NGMovingObject _character)
    {
    }
    [FunctionDescription("Sets the time of day")]
    [TutorialCommands] static public void SetTimeOfDay(string _time)
    {
        DayNight.SetClock(_time);
    }
    
    [TutorialCommands] static public void ChangeAllReputations(float _value)
    {
        foreach(var giver in GameManager.Me.m_state.m_orderGivers.m_values)
        {
            giver.m_reputationScore *= _value;        
        }
    }
    [TutorialCommands] static public bool WaitForInspectBuildings(MAComponentInfo _component, MAFlowCharacter _character, float _maxRange, string _noHouseExecute, string _goodExecuite, string _averageExecuite, string _badExecuite)
    {
        if(_component == null)
        {
            ParserError($"InspectBuildings - Component is null");
            return false;
        }
        if(_character == null)
        {
            ParserError($"InspectBuildings - Character is null");
            return false;
        }
        if(MAParserManager.CurrentInspection == null)
            MAParserManager.CurrentInspection = MAInspection.Create(_component, _character, _maxRange, _noHouseExecute, _goodExecuite, _averageExecuite, _badExecuite);
        var isFinished = MAParserManager.CurrentInspection.IsFinished;
        if (isFinished)
        {
            MAParserManager.CurrentInspection.DestroyMe();
            MAParserManager.CurrentInspection = null;
        }
        return isFinished;
    }
    [TutorialCommands] static public void ToggleDayNightCycle(bool _flag)
    {
        // if(_flag)
        //     DayNight.SetClock("-");
        // else
        //     DayNight.SetClock("");
        if (_flag)
            DayNight.UnpauseTime();
        else
            DayNight.PauseTime();
    }


    [FunctionDescription("Sets the day/night cycle fractions - note: must sum to less than 1")]
    [TutorialCommands]
    static public void SetDayNightCycleFractions(string _details)
    {
        var split = _details.Split(',');
        if (split.Length < 2)
        {
            ParserError($"SetDayNightCycleFractions - Requires 2 fractions");
            return;
        }
        if (float.TryParse(split[0], out var dayFraction) == false || float.TryParse(split[1], out var nightFraction) == false)
        {
            ParserError($"SetDayNightCycleFractions - Could not parse day/night fractions '{split[0]}' and '{split[1]}'");
            return;
        }
        if (dayFraction + nightFraction > .99f)
        {
            ParserError($"SetDayNightCycleFractions - Day and Night fractions must sum to less than 1, {dayFraction} + {nightFraction} > 1 ({dayFraction + nightFraction})");
            return;
        }
        DayNight.Me.SetDayFractions(dayFraction, nightFraction);
    }

    [FunctionDescription("Sets the day/night cycle day length in seconds")]
    [TutorialCommands]
    static public void SetDayNightCycleDayLength(string _lengthInSeconds)
    {
        if (int.TryParse(_lengthInSeconds, out var length) == false)
        {
            ParserError($"SetDayNightCycleDayLength - Could not parse day length '{_lengthInSeconds}'");
            return;
        }
        DayNight.Me.SetDayWallClockLength(length);
    }

    [TutorialCommands] static public void SetMusicState(string _state)
    {
        GameManager.Me.SetFlowMusicState(_state);
    }
    [TutorialCommands] static public void SetMusicState()
    {
        GameManager.Me.SetFlowMusicState("");
    }
    [TutorialCommands] static public void SetMusicName(string _state)
    {
        GameManager.Me.SetFlowMusicState(_state);
    }
    [TutorialCommands] static public void SetMusicName()
    {
        GameManager.Me.SetFlowMusicState("");
    }

    [TutorialCommands] static public void PlayAudioEvent(string _eventID)
    {
        AudioClipManager.Me.PlaySound(_eventID, GameManager.Me.gameObject);
    }

    [TutorialCommands] static public void SetTimeOfDay(float _tod, bool _isFrozen)
    {
        DayNight.Me.SetTimeOfDayFraction(_tod, _isFrozen);
    }

    [TutorialCommands] static public void SetTimeOfDayFade(string _startValue, string _endValue, float _time)
    {
        DayNight.SetClock(_startValue);
        FadeTimeOfDay(_endValue, _time);
    }
    [TutorialCommands] static public void SetTimeOfDayFadeThenContinue(string _startValue, string _endValue, float _time)
    {
        DayNight.SetClock(_startValue);
        FadeTimeOfDayThenContinue(_endValue, _time);
    }
    [TutorialCommands] static public void FadeTimeOfDay(string _endValue, float _time)
    {
        DayNight.Me.StartBlend(_endValue, _time, false);
    }
    [TutorialCommands] static public void FadeTimeOfDayThenContinue(string _endValue, float _time)
    {
        DayNight.Me.StartBlend(_endValue, _time, true);
    }
    [FunctionDescription("Moves the camera to a position and distance. EG: MoveCamera(Pos[0,54],6)")]
    [TutorialCommands] static public void MoveCamera(Vector3 _pos, float _distance)
    {
        GameManager.Me.CameraTransition(_pos, 1.0f, _distance, CameraTransitionComplete);
        m_triggers |= Triggers.MoveCamera;
    }
    [TutorialCommands] static public void MoveCamera(Vector3 _pos, float _incline, float _heading,float _distance)
    {
        GameManager.Me.CameraTransition(_pos, _incline,_heading,1.0f, _distance, CameraTransitionComplete);
        m_triggers |= Triggers.MoveCamera;
    }
    [FunctionDescription("Moves the camera to a building and distance. EG: MoveCamera(Building[Mine],6)")]
    [TutorialCommands] static public void MoveCamera(MABuilding _building, float _distance)
    {
        MoveCamera(_building.DoorPosOuter, _distance);
    }
    [FunctionDescription("Moves the camera to a Block in a building and distance. EG: MoveCamera(Building[Mine],6)")]
    [TutorialCommands] static public void MoveCamera(Block _block, float _distance)
    {
        MoveCamera(_block.transform.position, _distance);
    }
    [TutorialCommands] static public void MoveCamera(MAWorker _worker, float _distance)
    {
        MoveCamera(_worker.transform.position, _distance);
    }
    [TutorialCommands] static public void MoveCamera(ReactPickup _resource, float _distance)
    {
        MoveCamera(_resource.transform.position, _distance);
    }
    [TutorialCommands] static public void FollowCamera(MABuilding _building, float _distance)
    {
        StartFollowCamera(_building.gameObject, _distance);
    }
    [TutorialCommands] static public void FollowCamera(BCBase _component, float _distance)
    {
        StartFollowCamera(_component.gameObject, _distance);
    }
    [TutorialCommands] static public void FollowCamera(MAWorker _worker, float _distance)
    {
        StartFollowCamera(_worker.gameObject, _distance);
    }
    [TutorialCommands] static public void FollowCamera(ReactPickup _resource, float _distance)
    {
        StartFollowCamera(_resource.gameObject, _distance);
    }

    [TutorialCommands] static public void SetInitialCamera(Vector3 _pos, float _incline, float _heading)
    {
        GameManager.Me.m_defaultCameraResetPosition = _pos;
        GameManager.Me.m_defaultCameraResetRotation = new Vector3(_incline, _heading, 0);
    }

    [TutorialCommands] static public void SetMiddleClickCamera(Vector3 _pos, float _incline, float _heading)
    {
        GameManager.Me.m_cameraResetDisableSnapPoints = true; // switch off getting reset position from nearest CameraSnapPoint
        GameManager.Me.m_cameraResetPosition = _pos;
        GameManager.Me.m_cameraResetRotation = new Vector3(_incline, _heading, 0);
    }

    [TutorialCommands] static public void HighlightArcadiumExit()
    {
        MAResearchManagerUI.Me?.m_exitButtonNotification.Set(1,0,4);
    }
    
    [TutorialCommands] static public void Highlight(MAResearchInfo _info, bool _toggle)
    {
        if(_info == null)
        {
            ParserError($"Highlight - Could not find research info");
            return;
        }
        _info.m_highLight = _toggle;
        if(_toggle)
        {
            MAResearchManagerUI.SetFaction(_info.m_faction);
        }
        MAResearchManagerUI.Me?.RefreshNotifications();
    }

    [TutorialCommands] static public void Highlight(MABuilding _building, int _padNum, bool _toggle)
    {
        var baseBlock = _building.Visuals.GetComponentInChildren<BaseBlock>();

        var pads = baseBlock.GetComponentsInChildren<MeshRenderer>(true);
        for(int i=0; i<pads.Length; i++)
        {
            if ((i == _padNum || _padNum == -1) && _toggle)
                pads[i].material.color = Color.red;
            else
                pads[i].material.color = Color.white;
        }
    }

    [TutorialCommands] static public void Highlight(MABuilding _building, bool _toggle)
    {
        if(_toggle)
            CameraRenderSettings.Me.SetHighlight(_building);
        else
            CameraRenderSettings.Me.ClearHighlight(_building);
    }
    [TutorialCommands] static public void Highlight(Block _block, bool _toggle)
    {
        if(_toggle)
            CameraRenderSettings.Me.SetHighlight(_block.transform.position, 4F);
        else
            CameraRenderSettings.Me.ClearHighlight();
    }
    [TutorialCommands] static public void Highlight(MAWorker _worker, bool _toggle)
    {
        if(_toggle)
            CameraRenderSettings.Me.SetHighlight(_worker.transform.position, 4F);
        else
            CameraRenderSettings.Me.ClearHighlight();
    }
    [TutorialCommands] static public void Highlight(ReactPickup _pickup, bool _toggle)
    {
        if(_toggle)
            CameraRenderSettings.Me.SetHighlight(_pickup.transform.position, 4F);
        else
            CameraRenderSettings.Me.ClearHighlight();
    }
    [TutorialCommands] static public void Highlight(MAGUIBase _gui, bool _toggle)
    {
        _gui.Highlight(_toggle);
    }
    [TutorialCommands] static public void Highlight(BCBase _component, bool _toggle)
    {
        if(_toggle)
            FlashObject.Flash(_component.gameObject, new Color(1f,0f,0f,.1f), new Color(.5f,0f,0f,.4f), 0.5f);
        else
            FlashObject.Stop(_component.gameObject);
    }

    [TutorialCommands] static public void ActivateGUI(MAGUIBase _gui, bool _toggle)
    {
       _gui.Activate(_toggle);
    }

    [TutorialCommands] static public void ClearBanners()
    {
        // if (NGTutorial.m_parseTutorialLine != null)
        // {
        //     if(NGTutorial.m_parseTutorialLine.ShowingMessage)
        //         NGTutorial.m_parseTutorialLine.DestroyMessage();
        // } 
        //NGTutorialManager.Me.ShowMentorPose("", false);
        //NGTutorialManager.Me.ShowVCPose("", false);
    }
    
    [TutorialCommands] static public void ShowMessageDialog(MAMessageType _type, string _message, string _pose, string _audioID)
    {
        var message = new MAMessage()
        {
            m_type = _type,
            m_message = _message,
            m_audioID = _audioID,
            m_pose = _pose
        };
        message.Display();
    }

    [TutorialCommands] static public void ToggleReasearchCloseButton(bool _toggle)
    {
        if(MAResearchManagerUI.Me)
        {
            MAResearchManagerUI.Me.ToggleCloseButton(_toggle);
        }
    }
    [TutorialCommands] static public void InsertCharacterIntoTavernSequence(string _tavernID, string _characterType, int _count, int _priority)
    {
        BCActionTavern.AddCharacterToSequence(_tavernID, _characterType, _count, _priority);
    }

    [TutorialCommands] static public void RememberWallLength(Vector3 _pos)
    {
        var path = RoadManager.Me.m_pathSet.GetPlayerPathNearPoint(_pos);
        if (path == null)
        {
            MAParser.ParserError($"No wall found at [{_pos}]");
            return;
        }

        var initialLength = path.TotalLength();
        Remember("InitialWallLength", initialLength.ToString());
    }
    
    [TutorialCommands] static public void RemoveCharacterFromTavernSequence(string _tavernID, string _characterType)
    {
        BCActionTavern.RemoveCharacterFromSequence(_tavernID, _characterType);
    }
    
    [TutorialCommands] static public void InsertIntoTavernSequence(string _genericType, int _level, int _count, int _priority)
    {
        BCActionTavern.AddToSequence(_genericType, _level, _count, _priority);
    }
    
    [TutorialCommands] static public void RemoveFromTavernSequence(string _genericType, int _level)
    {
        BCActionTavern.RemoveFromSequence(_genericType, _level);
    }
    
    [TutorialCommands] static public void ClearTavernSequence()
    {
        BCActionTavern.ClearSequence();
    }

    [TutorialCommands] static public void AddRewardToRegionUnlockDialog(string _reward)
    {
        if(MARegionUnlockDialog.Me == null)
        {
            ParserError($"AddRewardToRegionUnlockDialog - MARegionUnlockDialog.Me is null");
            return;
        }
        MARegionUnlockDialog.Me.AddReward(_reward);
    }
    [TutorialCommands] static public void SetArcadiumPrice(MAResearchInfo _what, int _cash, int _favour)
    {
        
        if(_what == null)
        {
            ParserError($"SetArcadiumPrice - ResearchInfo is null");
            return;
        }
        _what.m_dollarCost = _cash;
        _what.m_factionCost = _favour;
    }
    [TutorialCommands] static public void LoadDispatchOrders(string _sequenceName, string _blockName)
    {
        MAOrderDataManager.Me.LoadDispatchOrders(_sequenceName, _blockName, "");
    }
    
    [TutorialCommands] static public void LoadDispatchOrder(string _sequenceName, MAOrderInfo _order)
    {
        MAOrderDataManager.Me.LoadDispatchOrder(_sequenceName, _order);
    }
    
    [TutorialCommands] static public void ClearDispatchOrderSequence()
    {
        MAOrderDataManager.Me.ClearDispatchOrderSequence();
    }
    [TutorialCommands] static public void ClearDispatchOrderSequence(string _sequence)
    {
        MAOrderDataManager.Me.ClearDispatchOrderSequence(_sequence);
    }

    [TutorialCommands] static public void SpawnFlow(string _flowName)
    {
        if(NGBusinessFlow.s_flowsDict.TryGetValue(_flowName, out var flow) == false)
        {
            ParserError($"SpawnFlow could not find flow '{_flowName}'");
            return;
        }
        MAGameFlow.StartFlow(flow);
    }
    [TutorialCommands] static public void SpawnFlow(string _flowName, string _index)
    {
        if(NGBusinessFlow.s_flowsDict.TryGetValue(_flowName, out var flow) == false)
        {
            ParserError($"SpawnFlow could not find flow '{_flowName}'");
            return;
        }
        var found = flow.Find(x => x.m_blockIndex == _index);
        if(found == null)
        {
            ParserError($"SpawnFlow could not find block '{_index}' in flow '{_flowName}'");
            return;
        }
        MAGameFlow.StartFlow(flow, flow.IndexOf(found));
    }

    [TutorialCommands] static public void SpawnWaggonTrain(string _name, MABuilding _building, string _blocks)
    {
        var blockNames = _blocks.Split(';');
        MAWaggonTrainManager.Me.AddWaggonTrainSequence(_name, _building.DoorPosOuter, blockNames.ToList());                    
    }
    [TutorialCommands] static public void SpawnWaggonTrain(string _name, Vector3 _pos, string _blocks)
    {
        var blockNames = _blocks.Split(';');
        MAWaggonTrainManager.Me.AddWaggonTrainSequence(_name, _pos, blockNames.ToList());                    
    }
    [TutorialCommands] static public void CreateSpawnPoint(string _name, MACreatureSpawnInfo _creatureSpawnInfo, MABuilding _building, Vector3 _position, float _radius)
    {
        MASpawnPoint newSpawnPoint = MASpawnPoint.Create(_name, _creatureSpawnInfo, _building, _position, _radius);
        newSpawnPoint.Enable();
    }
    [TutorialCommands] static public MADog SpawnDog(string _name, Vector3 _position)
    {
        return SpawnDog(_name, _position, null, null);
    }
    [TutorialCommands] static public MADog SpawnDog(string _name, Vector3 _position, MACharacterBase _owner)
    {
        return SpawnDog(_name, _position, _owner, null);
    }
    [TutorialCommands] static public MADog SpawnDog(string _name, Vector3 _position,  MACharacterBase _owner, MABuilding _home)
    { 
        var dog = SpawnCreature(_name, MACreatureInfo.GetInfo("Dog"), _position) as MADog;
        dog.Home = _home?.ActionComponents[0];
        FollowMovingObject(dog, _owner);
        return dog;
    }
    
    [TutorialCommands]
    static public MACharacterBase SpawnQuestCharacter(string _name, MACreatureInfo _creatureInfo, Vector3 _position, Quaternion _rot)
    {
        var character = SpawnCreature(_name, _creatureInfo, _position, Vector3.zero, _rot);
        character.GameState.m_lastLookAt = _rot != Quaternion.identity ? _rot.GetLookAtVector(_position) : default;
        character.InitialState = CharacterStates.Idle;
        character.DefaultState = CharacterStates.Idle;
        character.IsQuestCharacter = true;
        character.CharacterGameState.m_guardObjectiveWaypoint = false;
        MACharacterStateFactory.ApplyCharacterState(character.InitialState, character);
        return character;
    }
    
    [TutorialCommands] static public MACharacterBase SpawnQuestCharacter(string _name, MACreatureInfo _creatureInfo, MABuilding _building, Quaternion _rot)
    { 
        var character = SpawnCreature(_name, _creatureInfo, _building.DoorPosOuter.GroundPosition(), Vector3.zero, _rot);
        character.GameState.m_lastLookAt = _rot != Quaternion.identity ? _rot.GetLookAtVector(character.transform.position) : default;
        character.InitialState = CharacterStates.Idle;
        character.DefaultState = CharacterStates.Idle;
        character.IsQuestCharacter = true;
        character.CharacterGameState.m_guardObjectiveWaypoint = false;
        MACharacterStateFactory.ApplyCharacterState(character.InitialState, character);
        return character;
    }
    
    [TutorialCommands]
    static public void FollowMovingObject(MACharacterBase _character, NGMovingObject _moverToFollow)
    {
        _character.Leader = _moverToFollow;
    }
    
    [TutorialCommands] static public MACharacterBase SpawnCreature(string _name, MACreatureInfo _creatureInfo, MABuilding _building, Vector3 _despawnPosition = default)
    {
        string[] availableCreatureTypes = MACreatureControl.Me.CreaturePrefabNames;//TODO: type names or prefab names (depends on complexity of zombie types, sample zombie pack makes it hard to avoid inherently different prefabs per zombie)

        if(availableCreatureTypes.Contains(_creatureInfo.m_prefabName) == false)
        {
            ParserError($"MAKnackSupport - SpawnCreature - could not find knack creature type '{_creatureInfo.m_prefabName}' in CreatureTypeNames");
            return null; 
        } 
        
        //MACreatureControl.Me.ScheduleNewCreatureAtBuilding(_creatureInfo, _building);
        var character = MACreatureControl.Me.SpawnNewCreatureAtBuilding(_creatureInfo, _building);
        character.Name = _name;
        character.SetDespawnPosition(_despawnPosition);
        return character;
    }

    [TutorialCommands]
    static public MACharacterBase SpawnCreature(string _name, MACreatureInfo _creatureInfo, NamedPoint _namedpoint, Vector3 _despawnPosition = default)
    {
        string[] availableCreatureTypes = MACreatureControl.Me.CreaturePrefabNames;//TODO: type names or prefab names (depends on complexity of zombie types, sample zombie pack makes it hard to avoid inherently different prefabs per zombie)

        if (availableCreatureTypes.Contains(_creatureInfo.m_prefabName) == false)
        {
            ParserError($"MAKnackSupport - SpawnCreature - could not find knack creature type '{_creatureInfo.m_prefabName}' in CreatureTypeNames");
            return null;
        }

        MACreatureControl.Me.SpawnCreatureIfSpaceExists(_creatureInfo, _namedpoint, 0, out MACharacterBase character);
        if (character != null)
        {
            character.Name = _name;
            character.SetDespawnPosition(_despawnPosition);
        }

        return character;
    }
    
    [TutorialCommands] static public MACharacterBase SpawnCreature(string _name, MACreatureInfo _creatureInfo, Vector3 _pos, Vector3 _despawnPosition = default, Quaternion _rot = default)
    {
        string[] availableCreatureTypes = MACreatureControl.Me.CreaturePrefabNames;//TODO: type names or prefab names (depends on complexity of zombie types, sample zombie pack makes it hard to avoid inherently different prefabs per zombie)
        var isValidTime = MACreatureControl.Me.IsValidTimeOfDay(_creatureInfo);
        if(isValidTime == false)
        {
            ParserError($"MAKnackSupport - SpawnCreature - creature '{_creatureInfo.m_prefabName}' is not valid at this time of day");
            return null;
        }
        if(availableCreatureTypes.Contains(_creatureInfo.m_prefabName) == false)
        {
            ParserError($"MAKnackSupport - SpawnCreature - could not find knack creature type '{_creatureInfo.m_prefabName}' in CreatureTypeNames");
            return null;
        }
        var creature = MACreatureControl.Me.SpawnNewCreatureAtPosition(_creatureInfo, _pos, null, null, _name, _rot.eulerAngles);
        creature.GameState.m_lastLookAt = _rot != Quaternion.identity ? _rot.GetLookAtVector(_pos) : default;
        if(_despawnPosition != default)
            creature.SetDespawnPosition(_despawnPosition);
        return creature;
    }

    [TutorialCommands]
    static public List<MACharacterBase> SpawnCreatures(int _count, string _name, MACreatureInfo _creatureInfo, MABuilding _building, Vector3 _despawnPosition = default)
    {
        var creatures = new List<MACharacterBase>();
        for (int i = 0; i < _count; ++i)
        {
            var c = SpawnCreature(_name, _creatureInfo, _building, _despawnPosition);
            if (c != null)
                creatures.Add(c);
        }

        return creatures;
    }

    [TutorialCommands]
    static public List<MACharacterBase> SpawnCreatures(int _count, string _name, MACreatureInfo _creatureInfo, NamedPoint _namedpoint, Vector3 _despawnPosition = default)
    {
        var creatures = new List<MACharacterBase>();
        for (int i = 0; i < _count; ++i)
        {
            var c = SpawnCreature(_name, _creatureInfo, _namedpoint, _despawnPosition);
            if (c != null)
                creatures.Add(c);
        }

        return creatures;
    }

    [TutorialCommands]
    static public List<MACharacterBase> SpawnCreatures(int _count, string _name, MACreatureInfo _creatureInfo, Vector3 _pos, Vector3 _despawnPosition = default, Quaternion _rot = default)
    {
        var creatures = new List<MACharacterBase>();
        for (int i = 0; i < _count; ++i)
        {
            var c = SpawnCreature(_name, _creatureInfo, _pos, _despawnPosition, _rot);
            if (c != null)
                creatures.Add(c);
        }

        return creatures;
    }
    
    [TutorialCommands]static public void SetCreatureDestination(MACharacterBase _character, Vector3 _pos, float _radius)
    {
        if (_character == null)
        {
            ParserError($"SetCreatureDestination - could not find character");
            return;
        }
        _character.SetToGuard(_pos.SetYToHeight(), _radius);
        _character.SetMoveToPosition(_pos);
    }
    [TutorialCommands]static public void SetCreatureDestination(MACharacterBase _character, Vector3 _pos)
    {
        if (_character == null)
        {
            ParserError($"SetCreatureDestination - could not find character");
            return;
        }
        _character.ObjectiveWaypoint = _pos.SetYToHeight();
    }
    [TutorialCommands]static public void SetCreatureDestination(string _name, Vector3 _pos)
    {
        var nanes = MAParserSupport.StringToList(_name);
        bool gotOne = false;
        foreach(var name in nanes)
        {
            var creature = NGManager.Me.m_MACreatureList.Find(x => x.GetTypeInfo() == name);
            if(creature == null)
            {
                continue;
            }
            creature.ObjectiveWaypoint = _pos.SetYToHeight();
            gotOne = true;
        }
        if(gotOne == false)
            ParserError($"SetCreatureDestination - could not find any of the characters '{_name}'");
    }
    [TutorialCommands]static public void SetCreatureDestination(string _name, Vector3 _pos, float _radius)
    {
        var nanes = MAParserSupport.StringToList(_name);
        bool gotOne = false;
        foreach(var name in nanes)
        {
            var creature = NGManager.Me.m_MACreatureList.Find(x => x.GetTypeInfo() == name);
            if(creature == null)
            {
                continue;
            }
            creature.SetToGuard(_pos.SetYToHeight(), _radius);
            gotOne = true;
        }
        if(gotOne == false)
            ParserError($"SetCreatureDestination - could not find any of the characters '{_name}'");
    }
    [TutorialCommands]static public void SetCharacterToGoHome(string _name)
    {
        var character = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == _name);
        if(character == null)
        {
            ParserError($"SetCharacterToGoHome - could not find character '{_name}'");
            return;
        }

        character.CharacterUpdateState.ApplyState(CharacterStates.GoingHome);
    }   
    [TutorialCommands]static public void SetCharacterDespawnPosition(string _name, Vector3 _despawnPosition)
    {
        var character = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == _name);
        if(character == null)
        {
            ParserError($"SetCharacterDespawnPosition - could not find character '{_name}'");
            return;
        }

        character.SetDespawnPosition(_despawnPosition);
    }

    [TutorialCommands] static public void SpawnAnimal(string _name, MACreatureInfo _creatureInfo, Vector3 _pos)
    {
        if (_creatureInfo == null)
        {
            ParserError($"No such animal: {_name}");
            return;
        }
        MAAnimal.Create(_name, _creatureInfo, _pos);
    }

    [TutorialCommands]
    static public void SpawnAnimal(string _name, MACreatureInfo _creatureInfo, Vector3 _pos, Quaternion _rot)
    {
        if (_creatureInfo == null)
        {
            ParserError($"No such animal: {_name}");
            return;
        }
        var animal = MAAnimal.Create(_name, _creatureInfo, _pos, _rot);
        animal.GameState.m_lastLookAt = _rot != Quaternion.identity ? _rot.GetLookAtVector(_pos) : default;
    }
    
    [TutorialCommands]
    static public void SetAnimalState(MAAnimal _animal, string _state)
    {
        if(_animal == null)
        {
            Debug.LogError("Unable to find character");
            return;
        }
        if(_state.IsNullOrWhiteSpace())
        {
            Debug.LogError("Invalid character state");
            return;
        }
        _animal.CharacterUpdateState.ApplyState(_state.Trim());
    }
    
    [TutorialCommands] //Gesture means stop, play Anim, continue
    static public void PlayGesture(MACharacterBase _character, string _animSetKey)//Quaternion _rot)
    {
        if (_character == null)
        {
            ParserError($"No such character");
            return;
        }

        _character.StopForGestureAndContinue(_animSetKey);
    }
    
    [TutorialCommands] 
    public static void PlaySingleAnimation(MACharacterBase _character, string _animName)
    {
        if (_character == null)
        {
            ParserError($"No such character");
            return;
        }
        _character.PlaySingleAnimation(_animName, null);
    }

    [TutorialCommands]
    public static void PlayLoopAnimation(MACharacterBase _character, string _animName)
    {
        if (_character == null)
        {
            ParserError($"No such character");
            return;
        }

        _character.PlayLoopAnimation(_animName, null);
    }

    [TutorialCommands]
    public static void StopLoopAnimation(MACharacterBase _character)
    {
        if (_character == null)
        {
            ParserError($"No such character");
            return;
        }

        _character.StopWorkerLoopAnimation(true);
    }

    [TutorialCommands]
    public static void AddLipSyncer(string _advisor, MACharacterBase _character)
    {
        if (_character == null)
        {
            ParserError($"No such character");
            return;
        }

        LipSyncManager.Me.AddLipSyncer(_advisor, _character);
    }

    [TutorialCommands]
    public static void AddLipSyncer(string _advisor, MACharacterBase _character, float _deltaRot)
    {
        if (_character == null)
        {
            ParserError($"No such character");
            return;
        }

        LipSyncManager.Me.AddLipSyncer(_advisor, _character, _deltaRot);
    }

    [TutorialCommands]
    public static void SetGestureSet(string _advisor, string _gestureSet)
    {
        LipSyncManager.Me.SetGestureSet(_advisor, _gestureSet.Trim());
    }
    
    [TutorialCommands]
    public static void UnsetGestureSet(string _advisor)
    {
        LipSyncManager.Me.UnsetGestureSet(_advisor);
    }
    
    [TutorialCommands]
    public static void AddGesture(string _advisor, string _gestureSet, string _animation)
    {
        LipSyncManager.Me.AddGesture(_advisor, _gestureSet.Trim(), _animation.Trim());
    }

    [TutorialCommands]
    public static void AddGesture(string _advisor, string _gestureSet, string _animation, bool _disableHeadTracking)
    {
        LipSyncManager.Me.AddGesture(_advisor, _gestureSet.Trim(), _animation.Trim(), _disableHeadTracking);
    }

    [TutorialCommands] public static void DoTeleportEffect(Vector3 _pos)
    {
        if (NGManager.Me.m_teleportEndPrefab != null)
        {
            var teleportEnd = Object.Instantiate(NGManager.Me.m_teleportEndPrefab, _pos, Quaternion.identity);
            Object.Destroy(teleportEnd, 1f);
        } 
    }


    [TutorialCommands] static public void ToggleFleeing(bool _flag)
    {
        NGManager.Me.m_enableFleeing = _flag;
    }
    [TutorialCommands] static public void SpawnHorde(MACreatureInfo _creatureInfo, Vector3 _pos, float _radius, int _count)
    {
        for(int i = 0; i < _count; i++)
        {
            var pos = _pos + new Vector3(UnityEngine.Random.Range(-_radius, _radius), 0, UnityEngine.Random.Range(-_radius, _radius));
            SpawnCreature($"Horde[{i}]", _creatureInfo, pos, Vector3.zero);
        }
    }
    // UnlockBlock(MA_FARM_DOOR)
    // UnlockBlock(MA_FARM_DOOR;MA_FARM_DOOR)
    [TutorialCommands] static public void UnlockBlock(string _blockName)
    {
        var blocks = MAParserSupport.StringToList(_blockName);
        foreach(var b in blocks)
        {
            var blockInfo = NGBlockInfo.GetInfo(b);
            if(blockInfo == null)
            {
                ParserError($"UnlockBlock - could not find block '{b}'");
                continue;
            }
            GameManager.AddUnlock(b);
            //blockInfo.m_starterPack = true;
        }
    }
    [TutorialCommands] static public void CancelFollowCamera()
    {
        m_triggers&= ~Triggers.FollowCamera;
    }
    [TutorialCommands] static public void KillGestures()
    {
        m_triggers &= ~Triggers.Gesturing;
    }

    [TutorialCommands] static public void GestureDrag(MABuilding _fromBuilding, MABuilding _toBuilding, string _type)
    {
        // var gesture = NGTutorialGesture.Create(_fromBuilding.transform, _toBuilding.transform, _type, true, () => m_triggers.HasFlag(Triggers.Gesturing));
        // if (gesture)
        // {
        //     //NGTutorialManager.Me.AddGesture(gesture);
        //     m_triggers |= Triggers.Gesturing;
        // }
    }
    [TutorialCommands] static public void GestureDrag(string _fromGOName, string _toGOName, string _type)
    {
        // var gesture = NGTutorialGesture.Create(_fromGOName, _toGOName, _type, true);
        // m_triggers |= Triggers.Gesturing;
    }

    [BusinessCommands] static public void TakeScreenshot(int _index)
    {
        m_triggers |= Triggers.Screenshot;
        NGManager.Me.StartCoroutine(Screenshot(_index));
    }
    [TutorialCommands] static public bool IsRegionUnlocked(Vector3 _pos)
    {
        return DistrictManager.Me.IsWithinDistrictBounds(_pos, true);
    }
    [TutorialCommands] static public bool IsRegionNotUnlocked(Vector3 _pos)
    {
        return DistrictManager.Me.IsWithinDistrictBounds(_pos, true) == false;
    }
    [TutorialCommands] static public void UnlockRegion(string _name)
    {
        DistrictManager.Me.UnlockDistrictByID(_name);
    }

    [TutorialCommands] static public void RestrictWallBuildHandles(Vector3 _pos, float _radius)
    {
        RoadManager.Me.RestrictPathHandles(_pos, _radius);
    }

    [TutorialCommands] static public void DerestrictWallBuildHandles()
    {
        RoadManager.Me.DerestrictPathHandles();
    }
    

    [TutorialCommands] static public void ToggleKeyboardHelper(string _keyCode, string _label, bool _toggle)
    {
        if (System.Enum.TryParse(_keyCode, out KeyCode keyCode) == false)
        {
            ParserError($"No such KeyCode as '{_keyCode}'");
            return;
        }
        if (_toggle)
            KeyboardShortcutManager.Me.AddForcedShortcut(_label, keyCode);
        else
            KeyboardShortcutManager.Me.RemoveForcedShortcut(_label, keyCode);
    }
    [ParserCommands]static public bool IsUnlocked(string _what)
    {
        return MAUnlocks.IsUnlocked(_what);
    }
    [ParserCommands]static public bool IsAcquired(MAResearchInfo _what)
    {
        if (_what == null)
        {
            ParserError("No such research info");
            return false;
        }
        return _what.m_acquired;
    }
    [ParserCommands] static public bool IsNotAcquired(MAResearchInfo _what)
    {
        return IsAcquired(_what)== false;
    }

    [ParserCommands]
    static public void SetAcquired(MAResearchInfo _what, bool _flag)
    {
        if (_what == null)
        {
            ParserError("No such research info");
            return;
        }
        _what.m_acquired = _flag;
    }
    
    [ParserCommands]static public bool And(string _condition1, string _condition2)
    {
        if(MAParserSupport.TryParse(_condition1, out var result1) == false)
        {
            ParserError($"And - Could not parse condition 1'{_condition1}'");
            return false;
        }
        if(MAParserSupport.TryParse(_condition2, out var result2) == false)
        {
            ParserError($"And - Could not parse condition 2'{_condition2}'");
            return false;
        };

        return result1 && result2;
    }
    [ParserCommands]static public bool Or(string _condition1, string _condition2)
    {
        if(MAParserSupport.TryParse(_condition1, out var result1) == false)
        {
            ParserError($"And - Could not parse condition 1'{_condition1}'");
            return false;
        }
        if(MAParserSupport.TryParse(_condition2, out var result2) == false)
        {
            ParserError($"And - Could not parse condition 2'{_condition2}'");
            return false;
        };

        return result1 || result2;
    }

    [TutorialTrigger] static public bool WaitForTag(string _name)
    {
        _name = _name.Trim();
        return MATagManager.Me.IsTagOnScreen(_name);
    }
    
    [TutorialCommands] static public void Unlock(string _what)
    {
        _what = _what.Trim();
        if (NGUnlocks.Change(_what) == false)
        {
            MAUnlocks.Change(_what);
        }
    }
    [TutorialCommands] static public void SetBuildingLocked(MABuilding _building, bool _lock)
    {
        if (_building == null)
        {
            ParserError("No such building");
            return;
        }
        _building.m_stateData.m_lockedFromPlayer = _lock;
        _building.UpdateBuildSign();
    }
    [TutorialCommands] static public void ClearStock(MABuilding _building)
    {
        if (_building == null)
        {
            ParserError("No such building");
            return;
        }
        _building.DestroyStock();
    }

    [TutorialCommands] static public void EnableSaves()
    {
        GameManager.Me.SaveEnabled = true;
    }

    [TutorialCommands] static public void DisableSaves()
    {
        GameManager.Me.SaveEnabled = false;
    }

    [TutorialCommands] static public void LockBuildingBlocks()
    {
        DesignTableManager.Me.m_lockBlocksOnBuildings = true;
    }

    [TutorialCommands] static public void UnlockBuildingBlocks()
    {
        DesignTableManager.Me.m_lockBlocksOnBuildings = false;
    }
    
    [TutorialCommands] static public void FlowMessage(string _advisorName, string _title, string _message, string _audioID)
    {
        MAGameFlowMessage.Create(_advisorName, _title, _message, _audioID);
    }
    [TutorialCommands] static public void FlowAudioMessage(string _type, string _message, string _audioID, string _pose, float _audioRepeatTime)
    {
        var maMessage = MAMessage.Create(_type, _message, _audioID, _pose, _audioRepeatTime,"");
    }
    [TutorialCommands] static public void FlowBannerMessage(string _message)
    {
        NGBusinessDirectionBanner.Create(MAGameFlow.m_updatingFlow, _message, MAGameFlow.m_updatingFlow.TappedBanner);
    }
    [TutorialCommands] static public void Change(string _what)
    {
        NGUnlocks.Change(_what.Trim());
    }

    [TutorialCommands] static public void Abort()
    {
        MAParserManager.m_updatingMaParserSection.Abort();
    }
    [TutorialCommands] static public void Abort(string _what)
    {
        var section = MAParserManager.Me.FindByName(_what.Trim());
        if (section == null)
        {
          //  ParserError($"No such active section '{_what}' to abort");
            return;
        }
        MAParserManager.Me.m_sections.Remove(section);
        //section.Abort();
    }
    [TutorialCommands] static public void UnlockProductPack(string _packs)
    {
        var split = _packs.Trim().Split('\n', '|', ';', ',');

        foreach (var s in split)
        {
            var packName = s.Trim();
            if (packName.IsNullOrWhiteSpace()) continue;

            List<string> contents = ProductPacks.GetDecorationsFromPack(packName);
            foreach(string item in contents)
                GameManager.AddUnlock(item);
        }
    }
    [TutorialCommands] static public void UnlockProductLine(string _lines)
    {
        var split = _lines.Trim().Split('\n', '|', ';', ',');
        var gotOne = false;
        foreach (var s in split)
        {
            var productName = s.Trim();
            if (productName.IsNullOrWhiteSpace()) continue;
            var product = NGProductInfo.GetInfo(s);
            if (product != null)
            {
                ResearchLabRewardsController.UnlockProductLine(s);
                gotOne = true;
            }
        }
        if (gotOne == false)
        {
            ParserError($"[ Trying to UnlockProductLine but '{_lines}' not found");
            return;
        }
    }
    [TutorialCommands] static public void SetBridgePassable(string _name)
    {
        MABridgeController.SetBridgeState(_name.Trim(), MABridgeController.State.Passable);
    }
    
    [TutorialCommands] static public void ShowLandDeed(ReactDistrictTable _district)
    {
        MALandDeedDialog.Create(_district);
    }
    [TutorialCommands] static public void ShowChapterDialog(string _title, string _message, string _image)
    {
        MAChapterDialog.Create(_title, _message, _image);
    }

    [TutorialCommands] static public void ForceNextDayButton()
    {
        CalendarUIManager.Me.ReadyForEndOfDay(-1);
    }

    [TutorialCommands] static public void RemoveNextDayButton()
    {
        CalendarUIManager.Me.NotReadyForEndOfDay();
    }

    [TutorialCommands] static public void SetSpeedToNight(bool _flag)
    {
        if (_flag)
            ForceNextDayButton();
        else
            RemoveNextDayButton();
    }
    
    [TutorialCommands] static public void CreatureDestination(Vector3 _pos)
    {
        m_parsedPos = _pos;
    }
    static MABuilding m_parsedBuilding = null;
    static NGLegacyBase m_parsedObject = null;
    static NGDecoration m_parsedDecoration = null;
    static public IDamageReceiver LastDamageReceiver => m_parsedObject as IDamageReceiver;
    static public bool CreatureDestination(MABuilding _building)
    {
        m_parsedObject = _building;
        m_parsedBuilding = _building;
        m_parsedPos = _building.DoorPosOuter;
        return true;
    }

    static public bool CreatureDestination(NGDecoration _target)
    {
        m_parsedObject = _target;
        m_parsedDecoration = _target;
        m_parsedPos = _target.transform.position;
        return true;
    }
    
    // MACharacterBase m_parsedCharacter = null;
    // static public void CreatureDestination(MACharacterBase _character)
    // {
    //     m_parsedBuilding = _building;
    //     m_parsedPos = _worker.transform.position;
    // }
    static NGMovingObject m_parsedMovingObject = null;
    public static bool CreatureDestination(NGMovingObject _movingObject)
    {
        m_parsedObject = _movingObject;
        m_parsedMovingObject = _movingObject;
        m_parsedPos = _movingObject.transform.position;
        return true;
    } 
    [ParserCommands] public static void GiveGift(string _gift)
    {
        var takeAllGifts = new List<NGBusinessGift>();
        foreach(var g in _gift.Split(';','|'))
        {
            var gift = g.Trim();
            if (g.IsNullOrWhiteSpace()) continue;
               
            var giftInfo = NGBusinessGift.GetInfo(gift);
            if(giftInfo == null)
            {
                ParserError($"[ Trying to GiveGift but '{gift}' not found");
                continue;
            }
            takeAllGifts.Add(giftInfo);
        }
        
        if(takeAllGifts.Count == 0)
            return;
        NGBusinessRewardsSequence.Create(takeAllGifts, MAParserManager.m_updatingMaParserSection);
        NGBusinessGiftsPanel.CreateOrCall(takeAllGifts, null, 0, MAParserManager.m_updatingMaParserSection);
    }
    
    [ParserCommands] public static void GiveGift(string _gift, float _amount)
    {
        var giftInfo = NGBusinessGift.GetInfo(_gift.Trim());
        if(giftInfo == null)
        {
            ParserError($"[ Trying to GiveGift but '{_gift}' not found");
            return;
        }
        giftInfo.m_quantity = _amount;
        var takeAllGifts = new List<NGBusinessGift>(){giftInfo};
       
        
        NGBusinessRewardsSequence.Create(takeAllGifts, MAParserManager.m_updatingMaParserSection);
        NGBusinessGiftsPanel.CreateOrCall(takeAllGifts, null, 0, MAParserManager.m_updatingMaParserSection);
    }

    [ParserCommands] public static void SetDayTaskVisuals()
    {
        var advisors = new List<NGBusinessAdvisor>();
        var taskIDs = new List<string>();
        int taskNum = 1;
        var searchFor = new string[] {"Decision"};
        for (int i = MAParserManager.m_updatingMaParserSection.m_currentLine; i < MAParserManager.m_updatingMaParserSection.m_lines.Count; i++)
        {
            var line = MAParserSupport.ParseString(MAParserManager.m_updatingMaParserSection.m_lines[i].m_line);
            if (line.Count == 0) continue;
            var func = line[0].m_functionName;
            if (searchFor.Contains(func))
            {
                var advisor = NGBusinessAdvisor.GetInfo(line[0].m_args[0].Trim());
                if (advisor == null)
                {
                    ParserError($"[ Trying to SetDayTaskVisuals but '{line[0].m_args[0]}' not found");
                    return;
                }
                advisors.Add(advisor);
                taskIDs.Add($"Task_{taskNum}");
                taskNum++;
            }
        }

        if (advisors.Count == 0) return;
        //CalendarUIManager.Me.SetDayChallengeIcons(DayNight.Me.CurrentWorkingDay, advisors, taskIDs, -1);
    }

    [ParserCommands]
    public static void AddDayTask(string _taskID, string _advisor)
    {
        var advisor = NGBusinessAdvisor.GetInfo(_advisor.Trim());

        if (advisor == null)
        {
            ParserError($"Trying to AddDayTask but '{_advisor}' not found");
            return;
        }

        if(!CalendarUIManager.Me.AddDayTask(DayNight.Me.CurrentWorkingDay, _taskID, advisor))
        {
            ParserError($"Trying to AddDayTask but '{_taskID}' already exists");
            return;
        }
    }

    [ParserCommands]
    public static void HighlightDayTask(string _taskID, bool _isHighlighted)
    {
        if (!CalendarUIManager.Me.HighlightDayTask(_taskID, _isHighlighted))
        {
            ParserError($"HighlightDayTask: could not find task '{_taskID}'");
            return;
        }
    }

    [ParserCommands]
    public static void CompleteDayTask(string _taskID)
    {
        if (!CalendarUIManager.Me.CompleteDayTask(_taskID))
        {
            ParserError($"CompleteDayTask: could not find task '{_taskID}'");
            return;
        }
    }

    [ParserCommands] public static void Forget(string _key)
    {
        var memory = GetMemory(_key.Trim());
        if(memory != null)
            MAParserManager.Me.m_memories.Remove(memory);
    }
    [ParserCommands] public static void Remember(string _key, string _value)
    {
        _key = _key.Trim();
        _value = _value.Trim();
        var found = MAParserManager.Me.m_memories.Find(x => x.m_key == _key);
        if(found == null)
        {
            MAParserManager.Me.m_memories.Add(new MAParserManager.ParserMemory() {m_key = _key, m_value = _value});
        }
        else
        {
            found.m_value = _value;
        }
    }
    
    private static MAParserManager.ParserMemory GetMemory(string _key)
    {
        return MAParserManager.Me.m_memories.Find(x => x.m_key == _key);
    }
    
    [ParserCommands] public static string DivideMemoryObjects(string _key1, string _key2)
    {
        _key1 = _key1.Trim();
        _key2 = _key2.Trim();
        
        var val1 = GetMemory(_key1);
        var val2 = GetMemory(_key2);
        
        float value = 0f;
        if(val1 != null && val2 != null)
        {
            float f1 = float.Parse(val1.m_value);
            float f2 = float.Parse(val2.m_value);
            
            if(f2 != 0) value = f1 / f2;
        }
        return value.ToString();
    }
    [ParserCommands] public static void RememberAdd(string _key, float _value)
    {
        _key = _key.Trim();
        var found = GetMemory(_key);
        if(found == null)
        {
            MAParserManager.Me.m_memories.Add(new MAParserManager.ParserMemory() {m_key = _key, m_value = _value.ToString()});
        }
        else
        {
            found.m_value = (float.Parse(_value.ToString()) + _value).ToString();
        }
    }
    [ParserCommands] public static string Memory(string _key)
    {
        _key = _key.Trim();
        var found = GetMemory(_key);
        if(found == null)
        {
            //ParserError("Trying to Remember but '{_key}' not found in Memory");
            return "";
        }
        return found.m_value;
    }
    [ParserCommands] public static bool MemoryIs(string _key, string _is)
    {
        _key = _key.Trim();
        var found = GetMemory(_key);
        if(found == null)
        {
            return false;
        }

        if (found.m_value == _is.Trim())
            return true;
        return false;
    }
        //Where(Equals(Memory("AlwenChoice"), "Valmey"))
    [ParserCommands] public static bool Equals(string _value1, string _value2)
    {
        if (_value1.Contains('('))
        {
            MAParserSupport.TryParse(_value1, out var result);
            _value1 = result.ToString();
        }
        if (_value2.Contains('('))
        {
            MAParserSupport.TryParse(_value2, out var result);
            _value2 = result.ToString();
        }
        return _value1 == _value2;  
    }
    [ParserCommands]public bool Not(bool _value)
    {
        return !_value;
    }
    [ParserCommands] public static bool NotEquals(string _value1, string _value2)
    {
        if (_value1.Contains('('))
        {
            MAParserSupport.TryParse(_value1, out var result);
            _value1 = result.ToString();
        }
        if (_value2.Contains('('))
        {
            MAParserSupport.TryParse(_value2, out var result);
            _value2 = result.ToString();
        }
        return _value1 != _value2;  
    }
    [ParserCommands] public static bool Greater(string _value1, string _value2)
    {
        float result1 = 0;
        float result2= 0;
        if (_value1.Contains('('))
        {
            MAParserSupport.TryParseGetValue(_value1, out var result);
            if(result is Single)
                result1 = (float)result;
            else
            {
                if(float.TryParse((string)result, out result1) == false)
                {
                    ParserError($"[ Trying to compare '{_value1}' with '{_value2} but could not parse to float");
                    return false;
                };
            }
        }
        else
        {
            if(float.TryParse((string)_value1, out result1) == false)
            {
                ParserError($"[ Trying to compare '{_value1}' with '{_value2} but could not parse to float");
                return false;
            };
        }
        if (_value2.Contains('('))
        {
            MAParserSupport.TryParseGetValue(_value2, out object result);
            result2 = (float)result;
        }
        else
        {
            if(float.TryParse((string)_value2, out result2) == false)
            {
                ParserError($"[ Trying to compare '{_value1}' with '{_value2} but could not parse to float");
                return false;
            };
        }
        return result1 > result2;  
    }
    [ParserCommands] public static bool Less(string _value1, string _value2)
    {
        float result1 = 0;
        float result2= 0;
        if (_value1.Contains('('))
        {
            MAParserSupport.TryParseGetValue(_value1, out var result);
            if(result is Single)
                result1 = (float)result;
            else
            {
                if(float.TryParse((string)result, out result1) == false)
                {
                    ParserError($"[ Trying to compare '{_value1}' with '{_value2} but could not parse to float");
                    return false;
                };
            }
        }
        else
        {
            if(float.TryParse((string)_value1, out result1) == false)
            {
                ParserError($"[ Trying to compare '{_value1}' with '{_value2} but could not parse to float");
                return false;
            }
        }
        if (_value2.Contains('('))
        {
            MAParserSupport.TryParseGetValue(_value2, out object result);
            result2 = (float)result;
        }
        else
        {
            if(float.TryParse((string)_value2, out result2) == false)
            {
                ParserError($"[ Trying to compare '{_value1}' with '{_value2} but could not parse to float");
                return false;
            }
        }
        return result1 < result2;  
    }
    [ParserCommands] public static bool GreaterOrEquals(string _value1, string _value2)
    {
        return Greater(_value1, _value2) || Equals(_value1, _value2);
    }

    [ParserCommands] public static bool LessOrEquals(string _value1, string _value2)
    {
        return Less(_value1, _value2) || Equals(_value1, _value2);
    }

    [ParserCommands] public static void ClearChecks()
    {
        if (MAParserManager.m_updatingMaParserSection == null)
            return;
        MAParserManager.m_updatingMaParserSection.ClearChecks();
    }
    [ParserCommands] public static void GiveChoiceGift(string _gift, int _maxNumber)
    {
        var chooseAllGifts = new List<NGBusinessGift>();
        foreach(var g in _gift.Split(';','|'))
        {
            var gift = g.Trim();
            if (g.IsNullOrWhiteSpace()) continue;
               
            var giftInfo = NGBusinessGift.GetInfo(gift);
            if(giftInfo == null)
            {
                ParserError($"[ Trying to GiveGift but '{gift}' not found");
                return;
            }
            chooseAllGifts.Add(giftInfo);
        }
        /*
        foreach (var g in chooseAllGifts)
        {
            MAParserManager.CurrentGifts.Add(g.m_name);
        }*/
        NGBusinessRewardsSequence.Create(chooseAllGifts, MAParserManager.m_updatingMaParserSection, _maxNumber);
        NGBusinessGiftsPanel.CreateOrCall(null, chooseAllGifts, 0,MAParserManager.m_updatingMaParserSection);
    }

    [ParserCommands]
    public static bool IsPossessedType(string possessedType)
    {
        if(GameManager.Me.PossessedCharacter != null)
        {
            switch(possessedType)
            {
                case "Hero":
                    return (GameManager.Me.PossessedCharacter is MAHeroBase);
                case "Worker":
                    return (GameManager.Me.PossessedCharacter is MAWorker);
                case "Dog":
                    return (GameManager.Me.PossessedCharacter is MADog);
                case "Chicken":
                    return (GameManager.Me.PossessedCharacter is MAChicken);
            }
        }

        return false;
    }
    
    [ParserCommands]
    public static bool IsPossessed()
    {
        if(GameManager.Me.PossessedCharacter != null)
        {
            return true;
        }

        return false;
    }

    #endregion Commands
    #region ParserCommands
    
    private static (string location,string pose) GetAdvisorInfo(string _name)
    {
        switch (_name.ToLower().Trim())
        {
            case "keeper": return ("Popup:TopLeft", "KeeperBall");
            case "valmey": return ("Popup:BottomRight", "Valmey_Confident");
            case "cricket": return ("Popup:BottomLeft", "OswaldCricket");
            case "edith cotter": return ("Popup:BottomLeft", "EdithCotterSmall");
            case "thomas cotter": return ("Popup:BottomRight", "ThomasCotterSmall");
            case "garrick": return ("Popup:BottomLeft", "Garrick");
            case "gatekeeper": return ("Popup:BottomLeft", "GateKeeper");
            case "grimshaw": return ("Popup:BottomLeft", "Grimshaw");
            case "mineworker": return ("Popup:BottomLeft", "MineWorker");
            case "clivemineworker": return ("Popup:BottomLeft", "CliveMineWorker");
            case "lost boy": return ("Popup:BottomLeft", "LostBoy");
            case "lumberjack": return ("Popup:BottomLeft", "LumberjackSmall");
            case "hippy": return ("Popup:BottomLeft", "StonnedHippy");
            case "chickenfarmer": return ("Popup:BottomLeft", "ChickenFarmerSmall");
            case "piratefirstmate": return ("Popup:BottomRight", "PirateFirstMateSmall");
            case "piratecaptain": return ("Popup:BottomLeft", "PirateCaptainSmall");
            case "foremanwife": return ("Popup:BottomLeft", "ForemanWifeSmall");
            case "foremandaughter": return ("Popup:BottomRight", "ForemanDaughterSmall");
            case "executioner": return ("Popup:BottomRight", "ExecutionerSmall");
            case "joshuaredgrave": return ("Popup:BottomLeft", "AccusedJoshuaSmall");
            case "caveblockermetal": return ("Popup:BottomLeft", "CaveBlockerMetalSmall");
            case "mineengineer": return ("Popup:BottomLeft", "MineEngineerSmall");
            case "messagebottlehusband": return ("Popup:BottomLeft", "MessageBottleHusbandSmall");
            case "drunkbandit": return ("Popup:BottomLeft", "DrunkBanditTiny");

            default: return (null,null);
        }
    }
    
    [ParserCommands] public static bool MessageHold(string _advisorName, string _message, string _audioID)
    {
        var info = GetAdvisorInfo(_advisorName);
        if(info.location == null)
        {
            ParserError($"Default Message - Could not find advisor '{_advisorName}'");
            return true;
        }
        return MessageHold(_advisorName, info.location, info.pose, _message, _audioID);
    }
    
    [ParserCommands] public static bool MessageHold(string _advisorName, string _pose, string _message, string _audioID)
    {
        var info = GetAdvisorInfo(_advisorName);
        if(info.location == null)
        {
            ParserError($"Default Message - Could not find advisor '{_advisorName}'");
            return true;
        }
        return MessageHold(_advisorName, info.location, _pose, _message, _audioID);
    }
    
    [ParserCommands] public static bool Message(string _advisorName, string _message, string _audioID)
    {
        var info = GetAdvisorInfo(_advisorName);
        if(info.location == null)
        {
            ParserError($"Default Message - Could not find advisor '{_advisorName}'");
            return true;
        }
        return Message(_advisorName, info.location, info.pose, _message, _audioID);
    }
    
    [ParserCommands] public static bool Message(string _advisorName, string _pose, string _message, string _audioID)
    {
        var info = GetAdvisorInfo(_advisorName);
        if(info.location == null)
        {
            ParserError($"Default Message - Could not find advisor '{_advisorName}'");
            return true;
        }
        return Message(_advisorName, info.location, _pose, _message, _audioID);
    }
    
    [ParserCommands] public static bool Message(string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        return Message(false, _advisorName, _type, _pose, _message, _audioID);
    }
    
    [ParserCommands] public static bool MessageHold(string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        return Message(true, _advisorName, _type, _pose, _message, _audioID);
    }
    public static MAWaypointPathController.WaypointItem Waypoint(Vector3 _pos)
    {
        return new MAWaypointPathController.WaypointItem(_pos);
    }
    public static MAWaypointPathController.WaypointItem Waypoint(Vector3 _pos, List<string> _triggers)
    {
        return new MAWaypointPathController.WaypointItem(_pos, _triggers);
    }
    public static MAWaypointPathController.WaypointItem Waypoint(Vector3 _pos, string _execute)
    {
        return new MAWaypointPathController.WaypointItem(_pos, _execute);
    }
    public static MAWaypointPathController.WaypointItem Waypoint(MABuilding _building, string _execute)
    {
        return new MAWaypointPathController.WaypointItem(_building.DoorPosOuter, _execute);
    }
    public static MAWaypointPathController.WaypointItem Waypoint(NGDecoration _decoration, string _execute)
    {
        return new MAWaypointPathController.WaypointItem(_decoration.transform.position, _execute);
    }
    public static MAWaypointPathController.WaypointItem Waypoint(NamedPoint _namedPoint)
    {
        return new MAWaypointPathController.WaypointItem(_namedPoint);
    }
    public static MAWaypointPathController.WaypointItem Waypoint(NamedPoint _namedPoint, string _execute)
    {
        return new MAWaypointPathController.WaypointItem(_namedPoint, _execute);
    }
    public static bool Message(bool _holdOnScreen, string _advisorName, string _type, string _pose, string _message, string _audioID)
    {
        //_audioID = "";      //Done for voice regeneration
        _message = _message.Trim();
        _type = _type.Trim();
        _advisorName = _advisorName.Trim();

        if (MAParserManager.CurrentMessage == null)
        {
            MAParserManager.CurrentMessage = MAMessage.Create(_type, _message, _audioID.Trim(), _pose.Trim(), 0, _advisorName, _holdOnScreen);
        }
        else if(MAParserManager.CurrentMessage.m_holdOnScreen && (MAParserManager.CurrentMessage.m_holdOnScreen && MAParserManager.CurrentMessage.m_message.Equals(_message) == false || _holdOnScreen != MAParserManager.CurrentMessage.m_holdOnScreen))
        {
            if(MAParserManager.CurrentMessage.m_type.m_indexer != _type)
            {
                ParserError($"Hold message is being used between two different types of messages { _type } and {MAParserManager.CurrentMessage.m_type.m_indexer }");
                if(MAParserManager.m_updatingMaParserSection != null)
                    ParserError($"On section {MAParserManager.m_updatingMaParserSection.m_name} at line {MAParserManager.m_updatingMaParserSection.m_currentLine}");
            }
            else if(MAParserManager.CurrentMessage.m_advisor != _advisorName)
            {
                Debug.LogError($"Hold message is being used between two different advisor { _advisorName } and {MAParserManager.CurrentMessage.m_advisor}");
            }
            MAParserManager.CurrentMessage.Reactivate(_message, _audioID.Trim(), _pose.Trim(), 0, _advisorName, _holdOnScreen);
        }

        if(MAMessage.IsReadyToDisplay() == false)
            return false;
            
        if (MAParserManager.CurrentDisplayingMessage == false)
        {
            MAParserManager.CurrentMessage.Display();
            MAParserManager.CurrentDisplayingMessage = true;
            return false;
        }
        
        MAParserManager.CurrentDisplayingMessage = false;
        if(MAParserManager.CurrentMessage != null && MAParserManager.CurrentMessage.m_holdOnScreen == false)
            MAParserManager.CurrentMessage = null;
        return true;
    }

    [ParserCommands]
    public static void AddInsult(string _target, string _advisor, string _message, string _audioID, bool _isRandom)
    {
        _target = _target.Trim();
        _advisor = _advisor.Trim();
        _message = _message.Trim();
        _audioID = _audioID.Trim();

        if (Enum.TryParse(_target, out InsultTarget target) == false)
        {
            ParserError($"AddInsult - Could not parse target '{_target}'");
            return;
        }

        MACollectableManager.Me.AddInsult(target, _advisor, _message, _audioID, _isRandom);
    }

    [ParserCommands] public static bool CreateDecision(string _name, string _advisor, string _type, string _explainText, float _targetValue, string _power)
    {
        _name = _name.Trim(); 
        if (MAParserManager.CurrentDecision == null && NGBusinessDecision.GetInfo(_name) == null)
        {
            var newDecision = new NGBusinessDecision()
            {
                m_name = _name,
                m_type = _type.Trim(),
                m_explainText = _explainText.Trim(),
                m_targetValue = _targetValue,
                m_power = _power.Trim(),
                m_advisor = _advisor.Trim()
            };
            newDecision.ResolveCalls();
            NGBusinessDecision.s_decisions.Add(newDecision);
        }
        return DecisionInternal(_advisor, _name);
    }
    [ParserCommands] public static bool Decision(string _name, int _reminderInternal)
    {
        return DecisionInternal("", _name, _reminderInternal);
    }
    [ParserCommands] public static bool Decision(string _advisor, string _name, int _reminderInternal)
    {
        return DecisionInternal(_advisor, _name, _reminderInternal);
    }
    [ParserCommands] public static bool Decision(string _advisor, string _name)
    {
        return DecisionInternal(_advisor, _name);
    }
    [ParserCommands] public static bool Decision(string _advisor, string _name, string _clickAction)
    {
        var isNew = MAParserManager.CurrentDecision == null;
        var ret = DecisionInternal(_advisor, _name);
        if (isNew && MAParserManager.CurrentDecision != null)
        {
            _clickAction = _clickAction.Trim();
            var parsedQuestBase = MAParserManager.CurrentQuest;

            if (parsedQuestBase)
            {
                if (MAParserManager.CurrentDialog != null && _clickAction.IsNullOrWhiteSpace() == false)
                {
                    var method = parsedQuestBase.GetType().GetMethod(_clickAction);
                    if (method == null)
                    {
                        ParserError($"SetGameFlowDialogClick - Could not find method '{_clickAction}' in '{parsedQuestBase.GetType().Name}'");
                        return false;
                    }

                    var action = (Action) Delegate.CreateDelegate(typeof(Action), parsedQuestBase, method);
                    MAParserManager.CurrentDialog.SetClickAction(action);
                }
                return false;
            }
            else
            {
                var method = MAParserManager.CurrentDecision.GetType().GetMethod(_clickAction);
                if (method == null)
                {
                    ParserError($"SetGameFlowDialogClick - Could not find method '{_clickAction}' in '{parsedQuestBase.GetType().Name}'");
                    return false;
                }

                var action = (Action) Delegate.CreateDelegate(typeof(Action), parsedQuestBase, method);
                MAParserManager.CurrentDialog.SetClickAction(action);
                return false;
            }
          
        }

        return ret;
    }
    public static bool DecisionInternal(string _advisor, string _name, float _reminderInterval = -1)
    {
        _name = _name.Trim();
        if(MAParserManager.CurrentDecision == null) 
        {
            //CalendarUIManager.Me.StartFirstDayChallengeIcon();
            MAParserManager.CurrentDecision = NGBusinessDecision.GetInfo(_name.Trim());
            if (MAParserManager.CurrentDecision == null)
            {
                ParserError($"Decision - Could not find decision '{_name}'");
                return false;
            }
            MAParserManager.CurrentDecision.m_isClosing = false;
            MAParserManager.CurrentDecision.m_advisor = _advisor.Trim();
            MAParserManager.CurrentDialog = MAGameFlowDialog.Create(_advisor.Trim() ,MAParserManager.m_updatingMaParserSection, _reminderInterval);
            MAParserManager.CurrentDecision.Activate();
            MAParserManager.CurrentDialog.TriggerTheDecision();
        }
        var value = MAParserManager.CurrentDecision.GetValue();
        if (value > 0 && !MAParserManager.CurrentDecision.m_isClosing)
        {
            return false;
        }
        
        // Wait for dialogue to finish
        if(MAParserManager.CurrentDialog != null)
        {
            MAParserManager.CurrentDecision.m_isClosing = true;
            MAParserManager.CurrentDialog.OnComplete();
            if(MAParserManager.CurrentDialog.CanClose() == false)
                return false;
            MAParserManager.CurrentDialog.DestroyMe();            
        }
        
        MAParserManager.CurrentDialog = null;
        MAParserManager.CurrentDecision = null;
        //CalendarUIManager.Me.CompleteNextDayChallengeIcon();

        return true;
    }
    
    [ParserCommands] public static bool WaitForChoice(string _advisor, string _explain, string _video)
    {
        if(MAChoiceGUIManager.Me == null)
        {
            //ParserError($"MAChoiceGUIManager - Could not find choice dialog");
            return true;
        }
        if(MAChoiceGUIManager.m_lastChoice.IsNullOrWhiteSpace())
        {        
            MAChoiceGUIManager.Me.AddChoice(MAParserManager.m_updatingMaParserSection.m_name, _advisor.Trim(), _explain.Trim(), _video.Trim());
            return false;
        }
        return true;
    }
    [ParserCommands] public static bool WaitForChoice(string _sprite, string _explain)
    {
        if(MAChoiceGUIManager.Me == null)
        {
            ParserError($"MAChoiceGUIManager - Could not find choice dialog");
            return true;
        }
        if(MAChoiceGUIManager.m_lastChoice.IsNullOrWhiteSpace())
        {        
            MAChoiceGUIManager.Me.AddChoice(MAParserManager.m_updatingMaParserSection.m_name, _sprite.Trim(), _explain.Trim());
            return false;
        }
        return true;
    }
    [ParserCommands] public static bool BranchChoice(string _title, string _section_1, string _section_2)
    {
        return BranchChoice(_title,_section_1,_section_2, null, null);
    }
    [ParserCommands] public static bool BranchChoice(string _title, string _section_1, string _section_2, string _section_3)
    {
        return BranchChoice(_title,_section_1,_section_2, _section_3, null);
    }
    
    [TutorialTrigger] public static bool WaitForCharacterDamageCount(MAFlowCharacter _character, int _count)
    {
        if(_character == null)
        {
            //ParserError($"WaitForCharacterDamageCount - Could not find character");
            return false;
        } 
        var count = _character.m_receivedDamageHistory.Count;
        return count == _count;
    }

    [TutorialTrigger] public static bool BranchChoice(string _title, string _section_1, string _section_2, string _section_3, string _section_4)
    {
        //if(MAChoiceGUIManager.Me == null || MAChoiceGUIManager.Me.m_showing == false)
        if(MAChoiceGUIManager.Me == null) 
        {
            MAChoiceGUIManager.CreateChoice(_title.Trim(), _section_1, _section_2, _section_3, _section_4);
        }
        if(MAChoiceGUIManager.m_hasAllBanchSectionsEnded == false)
            return false;
        MAChoiceGUIManager.Me?.DestroyMe();
        return true;
    }

    [TutorialTrigger] public static bool IsCompletedOrderPercentGreater(MAOrderInfo _info, float _percent)
    {
        var order = MAOrderDataManager.Me.FindOrderByInfo(_info);
        if(order == null)
        {
            ParserError($"IsCompletedOrderPercentGreater - Could not find order with info '{_info.m_name}'");
            return false;
        }
        if (order.IsComplete == false)
        {
            return false;
        }
        if(order.NormalizedOrderReputationScore < _percent)
        {
            return false;
        }
        return true;
    }

    [ParserCommands] public static void TrackNightReward(string _type, string _challenge, float _data, string _reward)
    {
        MANightChallengeManager.Me.AddTrackedReward(_type, _challenge, _data, _reward);
    }
    [TutorialTrigger] public static bool IsOrderCompleted(MAOrderInfo _info)
    {
        var order = MAOrderDataManager.Me.FindOrderByInfo(_info);
        if(order == null)
        {
            ParserError($"IsCompletedOrderPercentGreater - Could not find order with info '{_info.m_name}'");
            return false;
        }
        return order.IsComplete;
    }

    [TutorialTrigger] public static bool IsCompletedOrderContainsTags(MAOrderInfo _info, string _tags)
    {
        var order = MAOrderDataManager.Me.FindOrderByInfo(_info);
        if(order == null)
        {
            ParserError($"IsCompletedOrderPercentGreater - Could not find order with info '{_info.m_name}'");
            return false;
        }

        if (order.IsComplete == false)
        {
            return false;
        }
        var theTags = _tags.Trim().Split(';', '|');

        var tags = order.GetDesignTags();
        foreach (var s in theTags)
        {
            if(tags.Contains(s.Trim()) == false)
            {
                return false;
            }
        }
        return true;
    }

    [TutorialTrigger] public static bool BranchChoiceNoWait(string _title, string _section_1, string _section_2)
    {
        return BranchChoiceNoWait(_title, _section_1, _section_2, null, null);
    }
    [TutorialTrigger] public static bool BranchChoiceNoWait(string _title, string _section_1, string _section_2, string _section_3)
    {
        return BranchChoiceNoWait(_title, _section_1, _section_2, _section_3, null);
    }
    [TutorialTrigger] public static bool BranchChoiceNoWait(string _title, string _section_1, string _section_2, string _section_3, string _section_4)
    {
        //if(MAChoiceGUIManager.Me == null || MAChoiceGUIManager.Me.m_showing == false)
        if(MAChoiceGUIManager.Me == null) 
        {
            MAChoiceGUIManager.CreateChoice(_title.Trim(), _section_1, _section_2, _section_3, _section_4);
        }
        if(MAChoiceGUIManager.m_hasAllBanchSectionsEnded == false && MAChoiceGUIManager.m_lastChoice.IsNullOrWhiteSpace())
            return false;
        MAChoiceGUIManager.Me?.DestroyMe();
        return true;
    }

    [ParserCommands] public static bool BranchChoiceDialog(string _title, string _details, string _section_1, string _section_2, string _section_3, string _section_4)
    {
        return BranchChoiceDialog(_title, _details, _section_1, _section_2, _section_3, _section_4, "");
    }

    [ParserCommands]
    public static bool BranchChoiceDialog(string _title, string _details, string _section_1, string _section_2, string _section_3, string _section_4, string _image)
    {
        if (MAChoiceGUIManager.Me == null)
        {
            MAChoiceGUIManager.CreateChoiceDialog(_title.Trim(), _details.Trim(), _section_1, _section_2, _section_3, _section_4, -1, _image.Trim());
        }
        if (MAChoiceGUIManager.m_hasAllBanchSectionsEnded == false)
            return false;
        MAChoiceGUIManager.Me?.DestroyMe();
        return true;
    }

    [ParserCommands] public static bool BranchChoiceWaitFor(string _title, string _section_1, string _section_2, int _mustBeSection)
    {
        if(MAChoiceGUIManager.Me == null)
        {
            MAChoiceGUIManager.CreateChoice(_title.Trim(), _section_1, _section_2, null, null, _mustBeSection);
        }
        if(MAChoiceGUIManager.m_hasAllBanchSectionsEnded == false)
            return false;
        if (_mustBeSection != -1)
        {
            if (MAChoiceGUIManager.Me && MAChoiceGUIManager.Me.m_branchSections.Length > _mustBeSection)
            {
                if (MAChoiceGUIManager.Me.m_branchSections[_mustBeSection].Equals(MAChoiceGUIManager.m_lastChoice) == false)
                {
                    return false;
                }
            }
        }
        MAChoiceGUIManager.Me?.DestroyMe();
        return true;
    }

    [ParserCommands] public static bool ReplaceChoice(string _withSection)
    {
        if(MAChoiceGUIManager.Me == null )
        {
            ParserError($"UpdateAndShowBranchChoice - Could not find choice dialog");
            return false;
        }
        MAChoiceGUIManager.Me.ReplaceChoice(MAParserManager.m_updatingMaParserSection.m_name, _withSection.Trim());
        return true;
    }

    [ParserCommands] public static bool Where(string _what)
    {
        if(_what.ToLower().Trim().Contains("decision"))
        {
            if(MAParserSupport.TryParse(_what, out var result) == false || result == true)
            {
                MAParserManager.m_updatingMaParserSection.SkipToCloseBracketOrElse();
                MAParserManager.CurrentDecision = null;
                if(MAParserManager.CurrentDialog != null)
                {
                    MAParserManager.CurrentDialog.DestroyMe();
                    MAParserManager.CurrentDialog = null;
                }
                return false;
            }
            MAParserManager.CurrentDecision = null;
            if(MAParserManager.CurrentDialog != null)
            {
                MAParserManager.CurrentDialog.DestroyMe();
                MAParserManager.CurrentDialog = null;
            }
            return true;
        }
        else
        {
            if (MAParserSupport.TryParse(_what, out var result) == false || result == false)
            {
                MAParserManager.m_updatingMaParserSection.SkipToCloseBracketOrElse();
                return false;
            }

            return true;
        }
    }

    [ParserCommands] public static bool Else()
    {
        return MAParserManager.m_updatingMaParserSection.SkipElseOrContinue();
    }
    [ParserCommands] public static bool Call(string _section)
    {
        if (MAParserManager.CurrentCall == null)
        {
            (string fileName, string sectionName) name;
            if(_section.Contains('/'))
                name = MAParserManager.GetSectionFileName(_section);
            else
                name =(MAParserManager.m_updatingMaParserSection.m_fileName, _section.Trim());
            
            var alreadyCreated = MAParserManager.Me.m_sections.Find(x => (x.m_fileName == name.fileName && x.m_name == name.sectionName));
                
            //var alreadyCreated = MAParserManager.Me.m_sections.Find(x => x.m_fileName == name.fileName);
            if(alreadyCreated != null)
            {
                MAParserManager.CurrentCall = alreadyCreated;
            }
            else
            {
                MAParserManager.CurrentCall = MAParserManager.Me.ExecuteSection(_section);
            }
        }
        if(MAParserManager.Me.m_sections.Contains(MAParserManager.CurrentCall))
        {
            return false;
        }
        MAParserManager.CurrentCall = null;
        return true;
    }
    [ParserCommands] public static void Execute(string _section)
    {
        MAParserManager.Me.ExecuteSection(_section);
    }

    [TutorialTrigger] public static bool HasFailedNightCryptCount(int _count)
    {
        return MAParserManager.Me.m_failWhatToDo.m_failCryptCount == _count;
    }
    [TutorialTrigger] public static bool HasFailedNightTasksCount(int _count)
    {
        return MAParserManager.Me.m_failWhatToDo.m_failTasksCount == _count;
    }
    [ParserCommands] public static void ClearSection(String _sectionFlow)
    {
        var section = MAParserManager.Me.m_sections.FirstOrDefault(s => s.m_name.Equals(_sectionFlow));

        if (section != null)
        {
            section.ClearCurrents();
            MAParserManager.Me.m_sections.Remove(section);
        }
    }

    #endregion ParserCommands
    #region WaitFors

    [TutorialTrigger] public static bool WaitForChoice()
    {
       return MAChoiceGUIManager.m_lastChoice.IsNullOrWhiteSpace() == false;
    }
    [TutorialTrigger] public static bool WasChoiceNotMe()
    {
        if (MAChoiceGUIManager.m_lastChoice.IsNullOrWhiteSpace()) return false;
        return MAChoiceGUIManager.m_lastChoice != MAParserManager.m_updatingMaParserSection.m_name;
    }

    [TutorialTrigger] public static void ForceEODGUI(bool _flag)
    {
        CalendarUIManager.Me.IsReadyForEndOfDay = _flag;

    }
    [TutorialTrigger] public static bool WaitForDay(int _day)
    {
        CalendarUIManager.Me.ReadyForEndOfDay(_day);
        return DayNight.Me.CurrentWorkingDay >= _day;
    }
    
    // Waits for the value to change by _amount or timeout (unless _timeout is -1)
    [TutorialTrigger] public static bool WaitForGameStateInfoValueChange(string _fieldName, int _amount)
    {
        return WaitForGameStateInfoValueChange(_fieldName, _amount, -1, null);
    }
    
    // Waits for the value to change by _amount or timeout (unless _timeout is -1)
    [TutorialTrigger] public static bool WaitForGameStateInfoValueChange(string _fieldName, int _amount, float _timeout, string _labelToJumpToOnTimeout)
    {
        // Set property
        var field = typeof(GameState_Info).GetField(_fieldName);
        if(field == null)
        {
            Debug.LogError($"No such field as {_fieldName} in GameState_Info");
            return true;
        }
        if(field.FieldType != typeof(int))
        {
            Debug.LogError($"{_fieldName} in GameState_Info is not an integer");
            return true;
        }
        
        if (MAParserManager.CurrentParserTimer < 0f)
            MAParserManager.CurrentParserTimer = Time.time + _timeout;
            
        var value = (int) field.GetValue(GameManager.Me.m_state.m_gameInfo);
        
        if(int.TryParse(MAParserManager.CurrentData, out var initialValue) == false)
        {
            initialValue = value;
            MAParserManager.CurrentData = initialValue.ToString();
        }
        
        if(value >= (initialValue + _amount))
        {
            MAParserManager.CurrentParserTimer = -1;
            return true;
        }
        if(_timeout > 0 && Time.time > MAParserManager.CurrentParserTimer)
        {
            MAParserManager.CurrentParserTimer = -1;
            GotoLabel(_labelToJumpToOnTimeout);
            return true;
        }
        return false;
    }

    /// <summary> Can be used to check if any unlock sequence and/or district unlock fly-by is active </summary>
    [TutorialTrigger]
    public static bool WaitForExpansionComplete()
    {
        return DesignTableManager.Me.IsShowingUnlockShelf == false && DistrictPolygon.IsUnlockSequenceInProgress == false;
    }
    
    // Waits for the value to change by _amount or timeout (unless _timeout is -1)
    [TutorialTrigger] public static bool WaitForGameStateInfoValue(string _fieldName, int _amount)
    {
        return WaitForGameStateInfoValue(_fieldName, _amount, -1, null);
    }
    
    // Waits for the value to change by _amount or timeout (unless _timeout is -1)
    [TutorialTrigger] public static bool WaitForGameStateInfoValue(string _fieldName, int _amount, float _timeout, string _labelToJumpToOnTimeout)
    {
        // Set property
        var field = typeof(GameState_Info).GetField(_fieldName);
        if(field == null)
        {
            Debug.LogError($"No such field as {_fieldName} in GameState_Info");
            return true;
        }
        if(field.FieldType != typeof(int))
        {
            Debug.LogError($"{_fieldName} in GameState_Info is not an integer");
            return true;
        }
        
        if (MAParserManager.CurrentParserTimer < 0f)
            MAParserManager.CurrentParserTimer = Time.time + _timeout;
            
        if (MAParserManager.CurrentParserTimer < 0f)
            MAParserManager.CurrentParserTimer = Time.time + _timeout;
            
        var value = (int) field.GetValue(GameManager.Me.m_state.m_gameInfo);
        
        if(value >= _amount)
        {
            MAParserManager.CurrentParserTimer = -1;
            return true;
        }
        
        if(_timeout > 0 && Time.time > MAParserManager.CurrentParserTimer)
        {
            MAParserManager.CurrentParserTimer = -1;
            GotoLabel(_labelToJumpToOnTimeout);
            return true;
        }
        return false;
    }
    
    [TutorialTrigger] public static bool WaitForHeroDeath()
    {
        var heroDeaths = GameManager.Me.m_state.m_gameStats.m_heroDeaths.m_currentValue;
        if(int.TryParse(MAParserManager.CurrentData, out var lastDeaths) == false)
        {
            lastDeaths = heroDeaths;
            MAParserManager.CurrentData = heroDeaths.ToString();
        }
        
        if(heroDeaths > lastDeaths)
        {
            return true;
        }
        return false;
    }
    
    [TutorialTrigger] public static bool WaitForDawn()
    {
        return DayNight.Me.m_isDawn || DayNight.Me.m_isFullDay;      
    }
    [TutorialTrigger] public static bool WaitForDusk()
    {
        return DayNight.Me.m_isDusk;      
    }
    [TutorialTrigger] public static bool WaitForFullDay()
    {
        return DayNight.Me.m_isFullDay;      
    }
    [TutorialCommands] public static void ToggleGate(Vector3 _pos, bool _open)
    {
        var gateEnum = (_open) ? GateOpener.GateCondition.Open : GateOpener.GateCondition.Closed;
        GateOpener.ToggleGateAt(_pos, gateEnum);
    }

    [ParserCommands] public static void ToggleNightChallenge(bool _flag)
    {
         MANightChallengeManager.Me.Enable(_flag); //Reenabled
         
    } 
    [TutorialTrigger] public static bool WaitForNight()
    {
        return DayNight.Me.m_isFullNight;      
    }


    [TutorialTrigger] public static bool WaitForOrderComplete(MABuilding _building)
    {
        if(_building == null)
        {
            ParserError($"WaitForOrderComplete - Could not find building");
            return true;
        }

        var ret = _building.GetOrdersDisplayed();
        return ret.Count == 0;
    }

    [TutorialTrigger] public static bool WaitForCharacterIdle(MAFlowCharacter _character)
    {
        if (_character == null)
        {
            Debug.LogError(MAParserSupport.DebugColor($"WaitForCharacterIdle - Could not find character"));
            return false;
        }
        return _character.IsIdle;
    }
    [TutorialTrigger] static public bool WaitForGiftReceived(string _gift)
    {
        if (_gift.IsNullOrWhiteSpace())
        {
            ParserError($"WaitForGiftRecived - Could not find gift '{_gift}'");
            return true;
        }
        var gift = _gift.Trim();
        var ret = NGBusinessGiftsPanel.ContainsBusinessGift(_gift);
       // var ret = MAParserManager.CurrentGifts.Contains(gift) == false;
        return !ret;
    }
    //                            WaitForBuildingToBeClean
    
    [TutorialTrigger]
    public static bool WaitForQuestActivate(MABuilding _building, float _offset)
    {
        return WaitForQuestActivate(_building, _offset, "GodMode");
    }

    [TutorialTrigger]
    public static bool WaitForQuestActivate(MAFlowCharacter _character, float _offset)
    {
        return WaitForQuestActivate(_character, _offset, "GodMode");
    }

    [TutorialTrigger]
    public static bool WaitForQuestActivate(GameObject _object, float _offset)
    {
        return WaitForQuestActivate(_object, _offset, "GodMode");
    }

    [TutorialTrigger]
    public static bool WaitForQuestActivate(MABuilding _building, float _offset, string _questInteractType)
    {
        return WaitForQuestActivate(_building.gameObject, _offset, _questInteractType);
    }

    [TutorialTrigger]
    public static bool WaitForQuestActivate(MAFlowCharacter _character, float _offset, string _questInteractType)
    {
        if (_character == null)
        {
            var errorStr = "";
            if(MAParserManager.m_updatingMaParserSection != null)
                errorStr = $"in {MAParserManager.m_updatingMaParserSection.m_name} at line {MAParserManager.m_updatingMaParserSection.m_currentLine}";
            ParserError($"WaitForQuestActivate - Could not find character {errorStr}");
            return false;
        }
        return WaitForQuestActivate(_character.gameObject, _offset, _questInteractType);
    }

    [TutorialTrigger] public static bool WaitForQuestActivate(GameObject _object, float _offset, string _questInteractType)
    {
        const string c_createdScroll = "c";
        MAQuestBase.QuestInteractType qit = MAQuestBase.QuestInteractType.GodMode;
        Enum.TryParse(_questInteractType, out qit);

        if(MAParserManager.CurrentQuestScroll == null && MAParserManager.CurrentData == c_createdScroll)
            return true;
            
        if (MAParserManager.CurrentQuestScroll == null)
        {
            // Destroy old scrolls
            foreach(var scroll in _object.GetComponentsInChildren<MAQuestScroll>())
            {
                GameObject.Destroy(scroll.gameObject);
            }
            
            MAParserManager.CurrentData = c_createdScroll;
            MAParserManager.CurrentQuestScroll = MAQuestCreateScroll.Create(_object.transform, _offset, qit);
        }

        if (MAParserManager.CurrentQuestScroll != null && MAParserManager.CurrentQuestScroll.m_beenClicked)
        {
            MAParserManager.CurrentQuestScroll.DestroyMe();
            MAParserManager.CurrentQuestScroll = null;
            return true;
        }
        return false;
    }
    
    [TutorialTrigger] static public bool WaitForTrigger(string _trigger)
    {
        if (_trigger.Contains("("))
        {
            var t = MAParserSupport.TryParse(_trigger, out var result);
            if (t == false)
            {
                ParserError($"WaitForTrigger - Could not parse '{_trigger}'");
                return false;
            }

            return result;
        }
        else
        {
            var method = typeof(MAParser).GetMethod(_trigger, BindingFlags.Static | BindingFlags.Public);
            if (method == null)
            {
                ParserError($"WaitForTrigger - Could not find method '{_trigger}'");
                return false;
            }
            return (bool)method.Invoke(null, null);
        }
    }

    [TutorialIf] public static bool IsQuestOn(MABuilding _building)
    {
        if (_building == null)
        {
            ParserError($"No building found");
            return false;
        }
        var comp = _building.GetComponentInChildren<MAQuestScroll>();
        return comp != null;
    }
    [TutorialIf] public static bool IsQuestNotOn(MABuilding _building)
    {
        return IsQuestOn(_building) == false;
    }
    [TutorialTrigger] static public bool WaitForUniqueDressedResidentsInRegion(int _count, string _searchRegion)
    {
        if (GameManager.Me.m_state.m_uniquePeopleDressed == null) return false;
        int countToFind = _count;
        foreach (int id in GameManager.Me.m_state.m_uniquePeopleDressed)
        {
            var worker = NGManager.Me.FindWorkerByID(id);
            if (worker != null && worker.IsAlive && worker.Job != null)// && worker.Home != null)
            {
                if (worker.Job.Building == null)
                {
                    Debug.LogError($"worker {worker.name} - job {worker.Job.m_uid} - Job has no building");
                    return false;
                }
                // if (worker.Home.Building == null)
                // {
                //     Debug.LogError($"worker {worker.name} - Home {worker.Home.m_uid} - Home has no building");
                //     return false;
                // }
                
                var jobDistrict = worker.Job.Building.DistrictName;
                //var homeDistrict = worker.Home.Building.DistrictName;

                if (jobDistrict == null)
                {
                    Debug.LogError($"worker {worker.name} - Job district is null");
                    return false;
                }
                // if (homeDistrict == null)
                // {
                //     Debug.LogError($"worker {worker.name} - Home district is null");
                //     return false;
                // }

                if (jobDistrict.Equals(_searchRegion, StringComparison.CurrentCultureIgnoreCase))// &&
                 //   homeDistrict.Equals(_searchRegion, StringComparison.CurrentCultureIgnoreCase))
                {
                    countToFind--;
                    if (countToFind == 0) return true;
                }
            }
        }
        return false;
    }
    [TutorialTrigger] static public bool WaitForAnyArmourOnAnyHero()
    {
        return NGManager.Me.m_MAHeroList.FindIndex(x => x.MaxArmour > 0) > -1;
    }
    [TutorialTrigger] static public bool WaitForBuildingToBeClean(MABuilding _building)
    {
        return _building.IsDisabledByPlants == false;
    }
    [TutorialTrigger] static public bool WaitForNotInDesignBuilding()
    {
        return DesignTableManager.Me.m_isInDesignGlobally == false;
    }
    [TutorialTrigger] static public bool WaitForIsInDesignTable()
    {
        return DesignTableManager.Me.IsInDesignInPlaceRoom;
    }
    [TutorialTrigger] static public bool WaitForNotInArcadium()
    {
        return MAResearchManagerUI.m_isActive == false;
    }
    [TutorialTrigger] static public bool IsInArcadium()
    {
        return MAResearchManagerUI.m_isActive;
    }
    [TutorialTrigger] static public bool WaitForAcceptQuest()
    {
        var parsedQuestBase = MAParserManager.CurrentQuest;
        //var parsedQuest = MAGameFlow.UpdatingControlObject as MAQuestBase;
        if (parsedQuestBase == null) 
        {
            ParserError($"OnQuestComplete - m_parsedQuestGiver is null");
            return false;
        }
        return parsedQuestBase.CheckQuestAccepted();
    }
    [TutorialTrigger]
    static public bool WaitForCompleteQuest(string _quest)
    {
        var quest = MAQuestManager.Me.GetQuest(_quest);

        if (quest == null)
        {
            ParserError($"WaitForCompleteQuest - quest is null");
            return true;
        }

        return quest.CheckQuestComplete();        
    }
    [TutorialTrigger] static public bool WaitForZombieAttack()
    {
        foreach(var c in NGManager.Me.m_MACreatureList)
        {
            if (!c.IsEnemy)
                continue;
            
            if (c.IsInAttackState)
                return true;
        }

        return false;
    }
    [TutorialTrigger] static public bool WaitForLastAudioComplete()
    {
        return true;
    }
    [TutorialTrigger] static public bool WaitForForever()
    {
        return false;
    }
    [TutorialTrigger] static public bool WaitForMoney(int _amount)
    {
        return NGPlayer.Me.m_cash.Balance >= _amount;
    }
    [TutorialTrigger] static public bool WaitForPartAdded(NGBlockInfo _blockInfo, int _count)
    {
        var result = false;
        if (_blockInfo == null)
        {
            ParserError($" no such _blockInfo is null");
            return true;
        }

        if (DesignTableManager.IsDesignInPlace == false || DesignTableManager.Me.IsInProductMode == false)
            return true;
        var tDesign = DesignTableManager.Me.GetDesignOnTable();
        var parts = DesignUtilities.GetDesignData(tDesign.m_design);
        var partsAdded = parts.FindAll(o => o.m_blockID.Equals(_blockInfo.id));
        return partsAdded.Count >= _count;
    }

    [TutorialTrigger] static public bool IsDead(MAFlowCharacter _character)
    {
        return _character == null || _character.m_state == NGMovingObject.STATE.MA_DEAD;
    }
    [TutorialTrigger] static public bool WaitForTime(float _time)
    {
        if (MAParserManager.CurrentParserTimer < 0f)
            MAParserManager.CurrentParserTimer = Time.time + _time;
        var result = Time.time > MAParserManager.CurrentParserTimer;
        if (result)
            MAParserManager.CurrentParserTimer = -1;
        return result;
    }
    [TutorialTrigger] static public bool WaitForTimeOfDay(string _time)
    {
        DayNight.Me.GetDHM(out int days, out int hours, out int minutes);
        var timeSplit = _time.Split(':','.');
        if (timeSplit.Length != 2)
        {
            ParserError($"Invalid time format '{_time}' expected HH:MM or HH.MM");
            return false;
        }
        var thours = int.Parse(timeSplit[0]);
        var tminutes = int.Parse(timeSplit[1]);
        return hours >= thours && minutes >= tminutes;
        float time = DayNight.StageToClock(DayNight.ClockToStage(_time));
        bool result = false;
        if (MAParserManager.m_updatingMaParserSection != null)
        {
            if (MAParserManager.CurrentParserTimer < 0f)
                MAParserManager.CurrentParserTimer = DayNight.GetFutureTime(0, time);
            result = GameManager.Me.m_state.m_gameTime.m_gameTime >= MAParserManager.CurrentParserTimer;
            if (result)
                MAParserManager.CurrentParserTimer = -1;
        }
        else
        {
            float waitUntil = DayNight.GetFutureTime(0, DayNight.ClockToFloat(_time));
            result = GameManager.Me.m_state.m_gameTime.m_gameTime >= waitUntil;

        }
        return result;
    }

    // Time is used to allow a delay for the pan to start
    [TutorialTrigger] static public bool WaitForCameraPan(float _time)
    {
        if (MAParserManager.CurrentParserTimer < 0f)
            MAParserManager.CurrentParserTimer = Time.time + _time;
            
        if (MAParserManager.CurrentParserTimer >= Time.time || GameManager.Me.CameraPanSequenceInProgress)
            return false;
            
        MAParserManager.CurrentParserTimer = -1;
        return true;
    }
    
    [TutorialTrigger] static public bool WaitForPickup(NGMovingObject _character)
    {
        var character = _character as MACharacterBase;
        if(character == null)
        {
            ParserError($"WaitForPickup - Could not find character");
            return true;
        }
        return character.CharacterUpdateState.State == CharacterStates.HeldByPlayer;
    }

    [TutorialTrigger] static public bool WaitForCharacterPatrol(NGMovingObject _character)
    {
        var character = _character as MACharacterBase;
        if(character == null)
        {
            ParserError($"WaitForPickup - Could not find character");
            return true;
        }

        return character.CharacterGameState.m_guardObjectiveWaypoint && character.CharacterGameState.m_guardAreaIsBespoke;
    }

    [TutorialTrigger] static public bool WaitForOrderArriveAtDispatch(MABuilding _building)
    {
        if (_building.HasBuildingComponent<BCActionDispatch>() == false)
        {
            ParserError($" building '{_building.name}' has no Dispatch component");
            return false;
        }

        var dispatches = _building.BuildingComponents<BCActionDispatch>();
        foreach (var c in dispatches)
        {
            if(c.SlotManager.GetOrdersDisplayed().Count > 0)
                return true;
        }
        return false;
    }
    
    [TutorialTrigger] static public bool WaitForOrderTo(MABuilding _building)
    {
        if(_building == null) 
            return true;
        return _building.Order.IsNullOrEmpty() == false;
    }
    [TutorialTrigger] static public bool WaitForOrderAssignedFromGiver(String _orderGiver)
    {
        foreach (var building in NGManager.Me.m_maBuildings)
        {
            foreach (var factory in building.BuildingComponents<BCFactory>())
            {
                if (factory != null)
                {
                    if (building.Order.IsNullOrEmpty() == false &&
                        building.Order.OrderInfo.OrderGiver.m_name == _orderGiver)
                    {
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    [TutorialTrigger] static public bool WaitForElement(MAGUIBase _gui)
    {
        return _gui != null;
    }

    [TutorialTrigger] public static bool WaitForTapHold(MABuilding _building)
    {
        if (_building == null)
        {
            ParserError($"WaitForTapHold - Could not find building");
            return false;
        }
        
        if (_building.HasTapComponent == false)
        {
            ParserError($"WaitForTapHold - building '{_building.name}' has no Tap component");
            //return false;
        }
        return _building.m_holdDuration > 0.4f; // Hold duration is set to 0.5f
    }
    [TutorialTrigger] public static bool IsHarvestGreaterThan(MABuilding _building, int _amount)
    {
        if (_building == null)
        {
            ParserError($"GetResources - Could not find building");
            return false;
        }

        var comps = _building.BuildingComponents<BCActionGatherer>();
        if (comps.Count() <= 0)
        {
            ParserError($"GetResources - building '{_building.name}' has BCActionGatherer component");
            return false;
        }

        foreach (var comp in comps)
        {
            comp.GetHarvestResourceState(out var total, out var remaining);
            if(remaining >= _amount)
                return true;
        }
        return false;
    }

    [TutorialTrigger] public static bool HasResourcesNotBeenDragged()
    {
        var mill = MABuilding.FindBuilding("Mill", false);
        var factory = MABuilding.FindBuilding("Factory", false);
        if (mill == null)
        {
            ParserError($"HasResourcesBeenDragged: Cant find mill");
        }

        if (factory == null)
        {
            ParserError($"HasResourcesBeenDragged: Cant find factory");
        }
        var millStock = mill.GetTotalStockCount();
        var factoryStock = factory.GetTotalStockCount();
        if (millStock + factoryStock < 16)
            return true;
        return false;

    }
    [TutorialTrigger] public static bool WaitForDrag(MABuilding _building)
    {
        if (_building == null)
        {
            ParserError($"WaitForTapHold - Could not find building");
            return false;
        }

        return _building.GetDragContent() != null;
    }
    
    [TutorialTrigger]
    static public bool WaitForClick(string _clickControl)
    {
        switch (_clickControl)
        {
            case "left":
            case "0":
                return Input.GetMouseButtonDown(0);
            case "right":
            case "1":
                return Input.GetMouseButtonDown(1);
            case "middle":
            case "2":
                return Input.GetMouseButtonDown(2);
            case "any":
                return Input.GetMouseButtonDown(0) | Input.GetMouseButtonDown(1) | Input.GetMouseButtonDown(2);
            default:
                ParserError($"{_clickControl} No such Click Control");
                return false;
        }
    }
    [TutorialTrigger] static public bool WaitForContextMenu()
    {
        return ContextMenuManager.Me.ContextMenuOpen;
    }
    [TutorialTrigger] static public bool WaitForGUIGone(string _guiName)
    {
        var found = MAGUIBase.Find(_guiName);
        return found == null;
    }
    [TutorialTrigger] static public bool WaitForGUIClick(MAGUIBase _gui)
    {
        if(_gui == null) return true;
        return _gui.m_mouseDown;
    }
    [FunctionDescription("Called in the form WaitForBlockPlaced(Building[22], ComponentInfo(ActionFactory, 1) use _padNum = -1 for any")]
    [TutorialTrigger] static public bool WaitForBlockPlacedOnPad(MABuilding _building, MAComponentInfo _toComponent, int _padNum)
    {
        if (_toComponent == null)
        {
            ParserError($" WaitForBlockPlacedOnPad _toComponent is null");
            return false;
        }

        var baseBlock = _building.GetComponentInChildren<BaseBlock>();
        if (baseBlock == null)
        {
            ParserError($" WaitForBlockPlacedOnPad no baseBlock found");
            return false;
        }

        var pads = baseBlock.GetHinges();
       
        for(int i = 0; i < pads.Count; i++)
        {
            if (i == _padNum || _padNum == -1)
            {
                foreach (var gl in DesignTableManager.Me.GrabList)
                {
                    var block = gl.GetComponentInChildren<Block>();
                    if (block == null)
                        continue;
                    var components=block.BlockInfo.GetComponentInfos();
                    if (components.Contains(_toComponent))
                    {
                        float distance = Vector3.Distance(gl.transform.localPosition, pads[i].transform.localPosition);
                        if (distance < 4)
                            return true;
                    }
                }
            }
        }
        return false;
    }
    [TutorialTrigger] static public bool WaitForWaggonTrain(string _name)
    {
        var result =MAWaggonTrainManager.Me.HasWaggonTrainSequenceArrived(_name);
        return result;
    }
    [TutorialTrigger] static public bool WaitForWorkerCount(int _count)
    {
        int workerCount = 0;
        foreach (var w in NGManager.Me.m_MAWorkerList)
        {
            if (w.Home != null)
                workerCount++;
        }
        var result = workerCount >= _count;
        return result;
    }

    [TutorialTrigger]
    static public bool WaitForChecksComplete()
    {
        return MAParserManager.m_updatingMaParserSection.m_checks.Count == 0;
    }
    [TutorialTrigger]
    static public bool WaitForBuildingFull(MABuilding _building)
    {
        var result = _building.GetInStockSpace() == 0;
        return result;
    }
    [TutorialTrigger] static public bool WaitForStockOutCount(NGCarriableResource _resource, int _count)
    {
        var result = false;
        int stockCount = 0;
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            foreach (var so in b.StockOuts)
            {
                var si = so.GetStock().Find(_resource);
                if (si != null)
                    stockCount += si.m_stock;
            }
        }
        result = stockCount >= _count;
        return result;           
    }
    [TutorialTrigger] static public bool WaitForStockOutCount(MABuilding _building, NGCarriableResource _resource, int _count)
    {
        var result = false;
        int stockCount = 0;
        foreach (var so in _building.StockOuts)
        {
            var si = so.GetStock().Find(_resource);
            if (si != null)
                stockCount += si.m_stock;
        }
        result = stockCount >= _count;
        return result;           
    }
    [TutorialTrigger] static public bool WaitForStockInCount(MABuilding _building, NGCarriableResource _resource, int _count)
    {
        var result = false;
        int stockCount = 0;
        foreach (var so in _building.StockIns)
        {
            var si = so.GetStock().Find(_resource);
            if (si != null)
                stockCount += si.m_stock;
        }
        result = stockCount >= _count;
        return result;           
    }
    [TutorialTrigger] static public bool WaitForDesignTablePart(int _numPartsNeeded)
    {
        string design = DesignTableManager.Me.GetDesign();
        if (string.IsNullOrEmpty(design) || design == "0|0|")
        {
            return false;
        }
        var bits = design.Split('|');
        int numParts = int.Parse(bits[0]);

        for (int i = 0; i < numParts; ++i)
        {
            var bits2 = bits[1 + i].Split('@');
            var entry = bits2[0];
            var info = NGBlockInfo.GetInfo(entry);
            if (_numPartsNeeded == 0)
                return true;
        }
        var result = numParts == _numPartsNeeded;
        return result;
    }
    [TutorialTrigger] static public bool WaitForAudioComplete(string _audioID)
    {
        if (TutorialTimer < 0f)
            TutorialTimer = Time.time + 25f;
        var result = Time.time > TutorialTimer;
        if (result)
            TutorialTimer = -1;

        string clipName = GameManager.DecodeAudio(_audioID);
        string clip = "PlaySound_" + clipName;
        var ret = !AudioClipManager.Me.IsPlayingVO(clip);
        if (ret)
            TutorialTimer = -1;
        return ret;
    }

    [TutorialTrigger] static public bool WaitForConstructBeacon()
    {
        return BCBeacon.HaveUnlockedBeaconThisSession;
    }
    
    [TutorialTrigger] static public bool IsBeaconComplete(String _name)
    {
        var building = MABuilding.FindBuilding(_name, true);
        if(building == null)
        {
            MAParser.ParserError($"No such building as {_name}");
            return false;
        }
        BCBeacon beaconChild = building.GetComponentInChildren<BCBeacon>();
        if (beaconChild != null)
        {
            return beaconChild.IsComplete();
        }

        return false;
    }

    [TutorialTrigger] static public bool WaitForDistrictUnlockFlybyFinish()
    {
        return DistrictManager.Me.LastUnlockFlybyComplete;
    }

    [TutorialTrigger] static public bool WaitForDead(MAFlowCharacter _character)
    {
        if (_character == null)
        {
            ParserError("Cannot find");
            return true;
        }
        return _character.m_state == NGMovingObject.STATE.MA_DEAD;
    }
    [TutorialTrigger] static public bool WaitForAudioCompleteOrClick(string _audioID)
    {
        if (TutorialTimer < 0f)
            TutorialTimer = Time.time + 2f;
        var result = Time.time > TutorialTimer;
        if (result)
            TutorialTimer = -1;

        string clipName = GameManager.DecodeAudio(_audioID);
        string clip = "PlaySound_" + clipName;
        var clipAudio = AudioClipManager.Me.GetClip(clip);
        if (clipAudio != null)
        {
            result |= !AudioClipManager.Me.IsPlayingVO(clip);
        }
        bool hasUserClicked = Input.GetMouseButtonDown(0) | Input.GetMouseButtonDown(1) | Input.GetMouseButtonDown(2);
        var ret = result || hasUserClicked;
        if(ret)
            TutorialTimer = -1;
        return ret;
    }
    [TutorialTrigger] static public bool WaitForAudioTriggered(string _audioID)
    {
        if (TutorialTimer < 0f)
            TutorialTimer = Time.time + 10.0f;
        var result = Time.time > TutorialTimer;
        if (result)
            TutorialTimer = -1;

        string clipName = GameManager.DecodeAudio(_audioID);
        string clip = "PlaySound_" + clipName;
        var ret = AudioClipManager.Me.IsPlayingVO(clip) || result;
        if (ret)
            TutorialTimer = -1;
        return ret;
    }

    [TutorialTrigger]
    static public bool WaitForCameraSequence(string _sequenceID)
    {
        if ((m_triggers & Triggers.CameraSequence) == 0)
        {
            m_triggers |= Triggers.CameraSequence;
            CameraPanNode.StartCameraPanSequence(_sequenceID, true, () => m_triggers &= ~Triggers.CameraSequence);
        }
        return (m_triggers & Triggers.CameraSequence) != 0;
    }

    static public bool WaitForCaveExit(string _exitID)
    {
        return string.Equals(_exitID, GameManager.Me.m_state.m_subSceneState.LastExitUsed, StringComparison.OrdinalIgnoreCase);
    }
    
    static public bool WaitForCaveEnter(string _exitID)
    {
        return string.Equals(_exitID, GameManager.Me.m_state.m_subSceneState.LastEntranceUsed, StringComparison.OrdinalIgnoreCase);
    }
    
    static public bool WaitForInSubscene(string _subscene)     
    {
        return string.Equals(_subscene, GameManager.Me.m_state.m_subSceneState.m_current, StringComparison.OrdinalIgnoreCase);
    }
    
    #endregion WaitFors

    #region Support

    public static float TutorialTimer
    {
        get => GameManager.Me.m_state.m_tutorialTimer;
        set => GameManager.Me.m_state.m_tutorialTimer = value;
    }
    public static float ParserTimer
    {
        get => GameManager.Me.m_state.m_parserTimer;
        set => GameManager.Me.m_state.m_parserTimer = value;
    }
    static public IEnumerator Screenshot(int _index)
    {
        yield return null;
        Utility.ScreenGrab(1, _index);
        yield return null;
        m_triggers &= ~Triggers.Screenshot;
    }
    static void StartFollowCamera(GameObject _followWhat, float _distance)
    {
        m_triggers |= Triggers.FollowCamera;
        NGManager.Me.StartCoroutine(FollowCameraCorroutine(_followWhat, _distance));
    }
    static IEnumerator FollowCameraCorroutine(GameObject _object, float _distance)
    {
        while (m_triggers.HasFlag(Triggers.FollowCamera))
        {
            yield return null;
            if(m_triggers.HasFlag(Triggers.MoveCamera) == false)
                StartCameraTransition(_object.transform.position, _distance);
        }
    }
    static public void StartCameraTransition(Vector3 _pos, float _distance)
    {
        GameManager.Me.CameraTransition(_pos, 1.0f, _distance, CameraTransitionComplete);
        m_triggers |= Triggers.MoveCamera;
    }
    static public void StartCameraTransition(Vector3 _pos, float _heading, float _incline, float _distance)
    {
        GameManager.Me.CameraTransition(_pos, 1.0f, _incline, _heading, _distance, CameraTransitionComplete);
        m_triggers |= Triggers.MoveCamera;
    }
    static public void StartCameraTransition(Vector3 _focus, Vector3 _pos)
    {
        GameManager.Me.CameraTransition(_focus, _pos, 1.0f, CameraTransitionComplete);
        m_triggers |= Triggers.MoveCamera;
    }
    static public void StartCameraTransition(Vector3 _pos, Quaternion _rot)
    {
        GameManager.Me.CameraTransition(_pos, _rot, 1.0f, CameraTransitionComplete);
        m_triggers |= Triggers.MoveCamera;
    }
    
    static public void StartCameraTransition(NGMovingObject _focusCharacter, Vector3 _pos)
    {
        MACharacterBase cb = _focusCharacter as MACharacterBase;
        if(cb == null)
        {
            ParserError($"StartCameraTransition - could not find focus character ");
            return;
        }
        
        Vector3 focusPos = _focusCharacter.transform.position;
        
        Transform head = cb.transform.FindChildRecursiveByName("Head_Attach");
        if(head != null)
        {
            focusPos = Vector3.Lerp(cb.transform.position, head.position, 0.7f);
        }
        
        GameManager.Me.CameraTransition(focusPos, _pos, 1.0f, CameraTransitionComplete);
        m_triggers |= Triggers.MoveCamera;
    }

    static public void StartCameraFade(Vector3 _pos, Quaternion _rot)
    {
        GameManager.Me.CameraFade(_pos, _rot, CameraTransitionComplete);
        m_triggers |= Triggers.MoveCamera;
    }

    static void CameraTransitionComplete()
    {
        m_triggers &= ~Triggers.MoveCamera; 
    }
    
    [TutorialCommands] public static void EnableStudioMode()
    {
#if UNITY_EDITOR
        EditorCharacterPhotoStudioMode.EnableStudioMode();
#endif
    }
    
    [TutorialCommands] public static void DisableStudioMode()
    {
#if UNITY_EDITOR
        EditorCharacterPhotoStudioMode.DisableStudioMode();
#endif
    }
    #endregion Support

    #region Chapter_1
   
    [TutorialCommands] public static void ShowGenderChoice()
    {
        if (NGDemoManager.Me.m_genderChoice) 
        {
            CharacterSelectionSubscene.Trigger();
        }
    }

    [TutorialTrigger] static public bool WaitForGenderChoice()
    {
        return CharacterSelectionSubscene.IsOpen() == false;
    }

    [TutorialCommands] public static void ShowFresco(string _name)
    {
        CryptManager.Me.StartFresco(_name);
    }
    [TutorialTrigger] static public bool WaitForFresco()
    {
        return CryptManager.Me.IsFrescoActive() == false;
    }
    [TutorialCommands] public static void EnableIntro3rdPerson()
    {
        IntroControl.Me.StartIntro3rdPerson();
    } 
    [TutorialTrigger] static public bool WaitFor3rdPersonEnterCrypt()
    {
        var ret = IntroControl.Me.InCrypt || IntroControl.Me.HaveSkippedIntro; // if we skipped the intro (debug) don't wait to get into it
        return ret;
    }

    [TutorialTrigger] static public bool IsInCrypt()
    {
        return IntroControl.Me.InCrypt;
    }
    [TutorialTrigger] static public bool WaitFor3rdPersonEnd()
    {
        var ret = IntroControl.Me.InIntroPossess == false;
        return ret;
    }
    
    [TutorialCommands] public static void StartValmeyArrivesByWaggon()
    {
    }
    [TutorialTrigger] static public bool WaitForValmeyToArrive()
    {
        return false;
    }

    [TutorialTrigger] static public void SetCalenderTasks(string _advisor, int _number)
    {
        
    }
    [TutorialCommands] public static void ShowHelper(string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        var helper = MAHelper.Create(_type, null, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, 1);

    }
    [TutorialCommands] public static void ShowHelper(string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow, int _count)
    {
        var helper = MAHelper.Create(_type, null, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, _count);

    }

    [TutorialCommands] public static void ShowHelper(PathManager.Path _overObject, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        ShowHelper(_overObject, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, 1);
    }

    [TutorialCommands] public static void ShowHelper(PathManager.Path _overObject, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow, int _count)
    {
        if(_overObject == null)
        {
//            ParserError($"ShowHelper - could not find block '{_overObject}'");
            return;
        }
        var handle = _overObject.GetHandle(false);
        var helper = MAHelper.Create(_type, handle, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, _count);
    }

    [TutorialCommands] public static void ShowHelper(Block _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        ShowHelper(_overObjectName, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, 1);
    }
    [FunctionDescription("type can be: ClearVinesBlock, ClearVinesBuilding, TapDesign, DragOrder, Timed, WASD,  GodHandMove, ProduceBuilding,")]
    [TutorialCommands] public static void ShowHelper(Block _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow, int _count)
    {
        if(_overObjectName == null)
        {
//            ParserError($"ShowHelper - could not find block '{_overObjectName}'");
            return;
        }
        var helper = MAHelper.Create(_type, _overObjectName.gameObject, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, _count);
    }
    [FunctionDescription("type can be: ClearVinesBlock, ClearVinesBuilding, TapDesign, DragOrder, Timed, WASD,  GodHandMove, ProduceBuilding,")]
    
    [TutorialCommands] public static void ShowHelperComponent(BCBase _component, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow, int _count = 1)
    {
        if(_component == null)
        {
            ParserError($"ShowHelper - could not find building component '{_message}'");
            return;
        }

        ShowHelper(_component.Building, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, _count);
    }

    [TutorialCommands] public static void ShowHelper(MABuilding _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        ShowHelper(_overObjectName, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, 1);
    }
    
    [TutorialCommands] public static void ShowHelper(MABuilding _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow, int _count )
    {
        if(_overObjectName == null)
        {
            ParserError($"ShowHelper - could not find building '{_message}'");
            return;
        }
        var helper = MAHelper.CreateBuildingHelper(_type, _overObjectName, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, _count);
    }
    [TutorialCommands] public static void ShowHelper(BuildingCardHolder _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        if(_overObjectName == null)
        {
            ParserError($"ShowHelper - could not find building or cardholder '{_message}'");
            return;
        }
        var helper = MAHelper.CreateCardHolder(_type, _overObjectName, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow);
    }

    [TutorialCommands] public static void ShowHelper(GameObject _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        ShowHelper(_overObjectName, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow,1);
        
    }
    [FunctionDescription("type can be: ClearVinesBlock, ClearVinesBuilding, TapDesign, DragOrder, Timed, WASD,  GodHandMove, ProduceBuilding,")]
    [TutorialCommands] public static void ShowHelper(GameObject _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow, int _count)
    {
        if(_overObjectName == null)
        {
            ParserError($"ShowHelper - could not find object '{_message}'");
            return;
        }
        var helper = MAHelper.Create(_type, _overObjectName, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, _count);
    }

    [TutorialCommands] public static void ShowHelper(MAWorker _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        ShowHelper(_overObjectName, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, 1);
    }
    [FunctionDescription("type can be: ClearVinesBlock, ClearVinesBuilding, TapDesign, DragOrder, Timed, WASD,  GodHandMove, ProduceBuilding,")]
    [TutorialCommands] public static void ShowHelper(MAWorker _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow, int _count)
    {
        var helper = MAHelper.Create(_type, _overObjectName.gameObject, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, _count);
    }

    [TutorialCommands] public static void ShowHelper(NGMovingObject _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        ShowHelper(_overObjectName, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, 1);
    }
    
    [FunctionDescription("type can be: ClearVinesBlock, ClearVinesBuilding, TapDesign, DragOrder, Timed, WASD,  GodHandMove, ProduceBuilding,")]
    [TutorialCommands] public static void ShowHelper(NGMovingObject _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow, int _count)
    {
        var helper = MAHelper.Create(_type, _overObjectName.gameObject, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow, _count);
    }
    [FunctionDescription("type can be: ClearVinesBlock, ClearVinesBuilding, TapDesign, DragOrder, Timed, WASD,  GodHandMove, ProduceBuilding,")]
    [TutorialCommands] public static void ShowHelper(MAAnimal _overObjectName, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        var helper = MAHelper.Create(_type, _overObjectName.gameObject, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow);
    }
    [TutorialCommands] public static void ShowWallHelper(Vector3 _pos, string _type, string _message, float _startAfterTimer, float _lastForTimer, float _heightAdjust, string _arrow)
    {
        var path = RoadManager.Me.m_pathSet.GetPlayerPathNearPoint(_pos);
        var helper = MAHelper.Create(_type, null, _type, _message, _startAfterTimer, _lastForTimer, _heightAdjust, _arrow);
    }
    [TutorialCommands] public static void ShowHelperGUI(string _GUIType, string _type, string _message, float _startAfterTimer, float _xOffset, float _yOffset, string _position, string _arrow)
    {
        var helper = MAHelper.CreateGUI(_type, _GUIType, _type, _message, _startAfterTimer, _xOffset, _yOffset, _position, _arrow);
    }

    [TutorialCommands] public static void KillHelper(string _name)
    {
        var helper = NGManager.Me.m_helpers.Find(o => o.m_name == _name);
        if(helper == null)
        {
            ParserError($"KillHelper - could not find helper '{_name}'");
            return;
        }
        helper.DestroyMe();
    }
    [TutorialCommands] public static void SetOneOffTrigger(string _flowName)
    {
        if(NGBusinessFlow.s_flowsDict.ContainsKey(_flowName) == false)
        {
            ParserError($"SetOneOffTrigger - could not find flow '{_flowName}'");
            return;
        }
        var gf = MAGameFlow.StartFlow(NGBusinessFlow.s_flowsDict[_flowName]);
        MAGameFlow.m_updatingFlow.m_triggerFlows.Add(gf);
    }
    public static void ToggleTourists(bool _flag)
    {
        MATouristManager.Me.m_enableTourist = _flag;
    }

    [TutorialCommands] public static void ToggleAllGates(bool open)
    {
        GateOpener.ToggleAllGatesInUnlockedRegions(open ? GateOpener.GateCondition.Open : GateOpener.GateCondition.Closed);
    }
    
    [TutorialCommands] public static void ForceGateOpen(bool _open, Vector3 _gatePos)
    {
        var gate = GateOpener.NearestToPoint(_gatePos);
        if(gate == null)
        {
            Debug.LogError("Unable to find a gate");
            return;
        }
        gate.ForceOpenState(_open ? GateOpener.GateCondition.Open : GateOpener.GateCondition.Closed);
    }

    [TutorialCommands] public static void ToggleWorkersToTavern(bool _flag)
    {
        if (GlobalData.Me == null)
            return;
        
        GlobalData.Me.m_forceWorkersToTavern = _flag;
    }

    [TutorialCommands] public static void RegrowAllResources()
    {
        BCChopObject.RegrowAllResources(true);
    }

    [TutorialCommands] static public void GiveRing(string _ringType)
    {
        PlayerHandManager.RingType ringType = PlayerHandManager.RingType.Evil;

        if (Enum.TryParse(_ringType, out ringType))
        {
            PlayerHandManager.Me.UnlockRing(ringType);
            PlayerHandManager.Me.EquipRing(ringType, true);
        }
    }
    [TutorialTrigger] static public bool WaitForHeroInBuilding()
    {
        foreach (var b in NGManager.Me.m_maBuildings)
        {
            var bedroom = b.BuildingComponents<BCGuildBedroom>();
            foreach (var bc in bedroom)
            {
                var hero = bc.GetHeroesPresent();
                if (hero.Count > 0)
                    return true;
            }
        }
        return false;
    }

    [TutorialTrigger]
    static public bool WaitForHeroEjected()
    {
        foreach (var hero in NGManager.Me.m_MAHeroList)
        {
            if(hero.IsOrWillBeInState(CharacterStates.Spawn)||hero.IsOrWillBeInState(CharacterStates.Eject))
                return true;
        }

        return false;
    }
    [TutorialTrigger] static public bool WaitForStockOutInBuilding(MABuilding _building, NGCarriableResource _resource, int _count)
    {
        if (_building == null)
        {
            ParserError($"WaitForStockInBuilding - could not find building");
        }

        if (_resource == null)
        {
            ParserError($"WaitForStockInBuilding -  No Valid Resource ");
        }
        foreach (var so in _building.StockOuts)
        {
            var si = so.GetStock().Find(_resource);
            if (si != null && si.m_stock >= _count)
                return true;
        }
        return false;
    }
    
    [TutorialCommands] static public void ReincarnateHeroes()
    {
        foreach (var hero in NGManager.Me.m_MAHeroList)
        {
            if (hero.IsIncapacitated)
            {
                hero.Health = hero.ReincarnationHealth;
            }
        }
        
    }

    [TutorialCommands] public static void SetEnterInteraction(string _id, bool _flag)
    {
        GenericSubSceneEnterInteraction.LockByID(_id.Trim(), _flag);
    }
    [TutorialCommands] public static void CanBeTargeted(MAFlowCharacter _character, bool _flag)
    {
       if(_character == null)
        {
            ParserError($"CanBeTargeted - could not find character ");
            return;
        }
        _character.CanBeTargetted = _flag;
    }
    
    [TutorialCommands] public static void SetCanPickup(MAFlowCharacter _character, bool _canPickup)
    {
        if (_character == null)
        {
            ParserError($"SetCanPickup - could not find character ");
            return;
        }
        _character.SetCanPickup(_canPickup);
    }
    [TutorialCommands] static public void GiveKey(string _keyType)
    {
        switch (_keyType)
        {
            case "MineWorkersKey":
                if (Memory("DougDead").Equals("true") == false)
                {
                    SpendMoney(10000);
                }     
                //GameManager.Me.m_state.m_gameInfo.m_keyRing.AddKey(0);
                break;
            case "CliveMineWorkersKey": 
                if (Memory("CliveDead").Equals("true") == false)
                {
                    SpendMoney(20000);
                }            
                //GameManager.Me.m_state.m_gameInfo.m_keyRing.AddKey(0);
                break;
            default:
                ParserError($"GiveKey - could not find key '{_keyType}'");
                break;
        }
    }

    [TutorialCommands]
    static public void LookAt(NGMovingObject _movingObject, Vector3 _target, float _turnSpeed)
    {
        _movingObject.LookAt(_target, _turnSpeed);
    }

    [TutorialTrigger]
    static public bool WaitForLookAt(NGMovingObject _movingObject, Vector3 _target, float _turnSpeed)
    {
        if ((m_triggers & Triggers.LookAt) == 0)
        {
            m_triggers |= Triggers.LookAt;
            _movingObject.LookAt(_target, _turnSpeed, () => m_triggers &= ~Triggers.LookAt);
        }
        return (m_triggers & Triggers.LookAt) == 0;
    }
    
    [TutorialCommands] static public void LookAt(NGMovingObject _movingObject, NGMovingObject _targetCharacter, float _turnSpeed)
    {
        MACharacterBase cb = _targetCharacter as MACharacterBase;
        if(cb == null) return;
        _movingObject.LookAt(_targetCharacter.transform.position, _turnSpeed);
    }
    
    [TutorialCommands] static public void LookAtPossessed(NGMovingObject _movingObject, float _turnSpeed)
    {
        if (GameManager.Me.PossessedCharacter != null)
        {
            _movingObject.LookAt(GameManager.Me.PossessedCharacter.transform.position, _turnSpeed);
        }
    }
    
    [TutorialCommands] static public void LookAtCamera(NGMovingObject _movingObject, float _turnSpeed)
    {
        if (GameManager.Me.m_camera.transform != null)
        {
            _movingObject.LookAt(GameManager.Me.m_camera.transform.position, _turnSpeed);
        }
    }
    
    [TutorialCommands] static public void SetHeadTrackerTarget(MAFlowCharacter _character, MAFlowCharacter _targetCharacter)
    {
        if(_character == null)
        {
            ParserError($"SetHeadTrackerTarget - could not find character ");
            return;
        }
        if(_targetCharacter == null)
        {
            ParserError($"SetHeadTrackerTarget - could not find target character ");
            return;
        }
        
        HeadTracker ht = _character.GetComponent<HeadTracker>();
        if(ht == null)
        {
            ParserError($"SetHeadTrackerTarget - HeadTracker component is null ");
            return;
        }
        
        Transform head = _targetCharacter.transform.FindChildRecursiveByName("Head_Attach");
        if(head == null)
        {
            ParserError($"SetHeadTrackerTarget - Head_Attach transform is null for " + _targetCharacter);
            return;
        }
        
        ht.Override(head);
    }

    [TutorialCommands] static public void SetHeadTrackerTarget(MAFlowCharacter _character, NGMovingObject _targetCharacter)
    {
        MACharacterBase cb = _targetCharacter as MACharacterBase;
        if(_character == null)
        {
            ParserError($"SetHeadTrackerTarget - could not find character ");
            return;
        }
        if(cb == null)
        {
            ParserError($"SetHeadTrackerTarget - could not find target character ");
            return;
        }
        
        HeadTracker ht = _character.GetComponent<HeadTracker>();
        if(ht == null)
        {
            ParserError($"SetHeadTrackerTarget - HeadTracker component is null ");
            return;
        }
        
        Transform head = _targetCharacter.transform.FindChildRecursiveByName("head");
        if(head == null)
        {
            ht.Override(head);
        }
        else
        {
            ht.Override(cb.transform);
        }
    }
    
    
    [TutorialCommands] static public void SetHeadPossessedTrackerTarget(MAFlowCharacter _targetCharacter)
    {
        if (GameManager.Me.PossessedCharacter == null)
        {
            // Do nothing if there is no possessed character
            return;
        }
        
        if(_targetCharacter == null)
        {
            ParserError($"SetHeadPossessedTrackerTarget - could not find target character ");
            return;
        }
        
        HeadTracker ht = GameManager.Me.PossessedCharacter.GetComponent<HeadTracker>();
        if(ht == null)
        {
            ParserError($"SetHeadPossessedTrackerTarget - HeadTracker component is null ");
            return;
        }
        
        Transform head = _targetCharacter.transform.FindChildRecursiveByName("Head_Attach");
        if(head == null)
        {
            ParserError($"SetHeadPossessedTrackerTarget - Head_Attach transform is null for " + _targetCharacter);
            return;
        }
        
        ht.Override(head);
    }
    
    [TutorialCommands] static public void SetHeadTrackerTargetCamera(MAFlowCharacter _character)
    {
        if(_character == null)
        {
            ParserError($"SetHeadTrackerTargetCamera - could not find character ");
            return;
        }
        
        Transform cameraTransform = GameManager.Me.m_camera.transform;
        HeadTracker ht = _character.GetComponent<HeadTracker>();

        if (ht != null && cameraTransform != null)
        {
            ht.Override(cameraTransform);
        }
    }
    [TutorialCommands] static public void SetHeadTrackerTargetPortraitCamera(MAFlowCharacter _character)
    {
        if(_character == null)
        {
            ParserError($"SetHeadTrackerTargetCamera - could not find character ");
            return;
        }
        
        Transform cameraTransform = GameObject.Find("PortraitCamera").transform;
        HeadTracker ht = _character.GetComponent<HeadTracker>();

        if (ht != null && cameraTransform != null)
        {
            ht.Override(cameraTransform);
        }
    }
    
    [TutorialCommands] static public void SetHeadTrackerTargetCamera(NGMovingObject _character)
    {
        MACharacterBase cb = _character as MACharacterBase;
        
        if(cb == null)
        {
            ParserError($"SetHeadTrackerTargetCamera - could not find character ");
            return;
        }
        
        Transform cameraTransform = GameManager.Me.m_camera.transform;
        HeadTracker ht = cb.GetComponent<HeadTracker>();

        if (ht != null && cameraTransform != null)
        {
            ht.Override(cameraTransform);
        }
    }
    
    [TutorialCommands] static public void SetHeadTrackerTargetPossessed(MAFlowCharacter _character)
    {
        if(_character == null)
        {
            ParserError($"SetHeadTrackerTargetPossessed - could not find character ");
            return;
        }
        
        if (GameManager.Me.PossessedCharacter == null)
        {
            ParserError($"SetHeadTrackerTargetPossessed - could not find possessed ");
        }
        
        HeadTracker ht = _character.GetComponent<HeadTracker>();
        if(ht == null)
        {
            ParserError($"SetHeadTrackerTargetPossessed - HeadTracker component is null ");
            return;
        }
        
        Transform head = GameManager.Me.PossessedCharacter.transform.FindChildRecursiveByName("Head_Attach");
        if(head == null)
        {
            // Possessed character has no head, target position
            ht.Override(GameManager.Me.PossessedCharacter.transform);
        }
        else
        {
            ht.Override(head);
        }
    }
    
    [TutorialCommands] static public void SetHeadTrackerTargetPossessed(NGMovingObject _character)
    {
        MACharacterBase cb = _character as MACharacterBase;
        
        if(cb == null)
        {
            ParserError($"SetHeadTrackerTargetPossessed - could not find character ");
            return;
        }
        
        if (GameManager.Me.PossessedCharacter == null)
        {
            return;
        }
        
        HeadTracker ht = cb.GetComponent<HeadTracker>();
        if(ht == null)
        {
            ParserError($"SetHeadTrackerTargetPossessed - HeadTracker component is null ");
            return;
        }
        
        Transform head = GameManager.Me.PossessedCharacter.transform.FindChildRecursiveByName("Head");
        if(head == null)
        {
            ParserError($"SetHeadTrackerTargetPossessed - possessed head transform is null ");
            return;
        }

        ht.Override(head);
    }

    [TutorialCommands] static public void EnableHeadTracking(MAFlowCharacter _character, bool _enable)
    {
        if(_character == null)
        {
            ParserError($"DisableHeadTracking - could not find character ");
            return;
        }
        
        HeadTracker ht = _character.GetComponent<HeadTracker>();
        if(ht == null)
        {
            ParserError($"DisableHeadTracking - HeadTracker component is null ");
            return;
        }
        
        if (_enable)
        {
            ht.ForceUseAnimation(false);
        }
        else
        {
            ht.ForceUseAnimation(true);
        }
    }
    

    [TutorialCommands] static public void SetNightFailSpecial(string _crypt, string _tasks)
    {
        MAParserManager.Me.m_failWhatToDo = new MAParserManager.FailWhatToDo(_crypt, _tasks);
   //     MAParserManager.Me.m_failWhatToDo.m_failCrypt = _crypt.Trim();
   //     MAParserManager.Me.m_failWhatToDo.m_failTasks = _tasks.Trim();
   //     MAParserManager.Me.m_failWhatToDo.m_failFileName = MAParserManager.m_updatingMaParserSection.m_fileName;
        
//        MAParserManager.Me.m_failCryptWhatToDo = _crypt.Trim();
//        MAParserManager.Me.m_failTasksWhatToDo = _tasks.Trim();
//        MAParserManager.Me.m_failSetSectionFileName = MAParserManager.m_updatingMaParserSection.m_fileName;
    }

    [TutorialCommands] static public void ExitFailSection()
    {
        FailSequenceController.Me.HasExternalHandlingFinished = true;
    }

    [TutorialCommands] static public bool DoesCharacterExist(string _characterName)
    {
        return MAFlowCharacter.FindCharacter(_characterName.Trim()) != null;
    } 
    
    [TutorialCommands] static public void UnsetHeadTrackerTarget(MAFlowCharacter _character)
    {
        if(_character == null)
        {
            ParserError($"UnsetHeadTrackerTarget - could not find character ");
            return;
        }
        
        HeadTracker ht = _character.GetComponent<HeadTracker>();

        if (ht != null)
        {
            ht.Override(null);
        }
    }
    [TutorialCommands] static public void UnsetHeadTrackerTarget(NGMovingObject _character)
    {
        MACharacterBase cb = _character as MACharacterBase;
        
        if(cb == null)
        {
            ParserError($"UnsetHeadTrackerTarget - could not find character ");
            return;
        }
        
        HeadTracker ht = cb.GetComponent<HeadTracker>();

        if (ht != null)
        {
            ht.Override(null);
        }
    }

    [TutorialCommands] static public void UnSetHeadPossessedTrackerTarget()
    {
        if (GameManager.Me.PossessedCharacter == null)
        {
            ParserError($"UnsetHeadTrackerTarget - could not find character ");
            return;
        }
        
        HeadTracker ht = GameManager.Me.PossessedCharacter.GetComponent<HeadTracker>();

        if (ht != null)
        {
            ht.Override(null);
        }
    }

    #endregion Chapter_1
}
