using UnityEngine;

public class MAUpdateNightCharacters : MonoBehaviour
{
    private void OnDisable()
    {
        if(NGManager.Me != null)
        {
            foreach(MACharacterBase character in NGManager.Me.m_MACharacterList)
            {
                if(character.CharacterUpdateState.State != CharacterStates.GoingHome)
                    character.CharacterUpdateState.ApplyState(CharacterStates.GoingHome);
            }
        }
    }
}
