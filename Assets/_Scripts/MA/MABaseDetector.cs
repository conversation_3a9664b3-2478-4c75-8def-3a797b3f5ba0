using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MABaseDetector : MonoBehaviour
{
    public MeshCollider m_collider;
    public string m_collidedObject;
    public string m_collidedStayObject;
    public string m_triggedObject;
    public int m_count;
    public NGBusinessDecision m_decision;
    void Start()
    {
        m_collider = GetComponentInChildren<MeshCollider>();
    }

    public void Activate(MABuilding _building, NGBusinessDecision _decision)
    {
        m_decision = _decision;
    }

    private void LateUpdate()
    {
        m_collidedObject = "";
        m_collidedStayObject = "";
        m_triggedObject = "";
    }

    void OnTriggerEnter(Collider other)
    {
        m_triggedObject+= $"{other.name}\n";
    }

    void OnTriggerStay(Collider other)
    {
        m_triggedObject+= $"{other.name}\n";
        m_count++;
    }

    private void OnCollisionEnter(Collision collision)
    {
        m_collidedObject = $"{collision.collider.name}\n";
    }

    private void OnCollisionStay(Collision collisionInfo)
    {
        m_collidedStayObject = $"{collisionInfo.collider.name}\n";
    }
}
