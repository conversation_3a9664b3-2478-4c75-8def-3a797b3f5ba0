using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MARigidbodyRestChecker : MonoBehaviour
{
    public Rigidbody rb;
    public float restThreshold = 0.1f; // Adjust this value based on your needs

    void Awake()
    {
        rb = GetComponent<Rigidbody>();
    }
    void FixedUpdate()
    {
        // Check if the Rigidbody is sleeping (not moving)
        if (rb && rb.IsSleeping())
        {
            // Additional check for residual velocity (optional)
            if (Mathf.Abs(rb.linearVelocity.magnitude) <= restThreshold)
            {
                Destroy(rb);
                Destroy(this);
            }
        }
    }
}
