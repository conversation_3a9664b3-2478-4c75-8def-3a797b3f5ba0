using System;
using UnityEngine;
using UnityEngine.UI;

public class MAQuickTipResearchButton : MAQuickTipButton
{
    [SerializeField] 
    private MAIconAnimated m_affordableIconAnimated = null;
    public Button m_button;
    
    private int m_count = Int32.MinValue; //don't remove this, it's used to check and debug if the count
    private float m_nextUpdateTime;
    
    private void Awake()
    {
        if (m_affordableIconAnimated == null)
        {
            Debug.LogError($"{GetType()} - m_affordableIconAnimated is missing at {transform.Path()}", gameObject);
            enabled = false;
            return;
        }
        m_affordableIconAnimated.gameObject.SetActive(true); //ensure it's on for Awake. just in case it isn't in the prefab.
    }
    
    private void Start()
    {
        m_affordableIconAnimated.gameObject.SetActive(false); //turn off for Start, we'll turn it on in Update if we can afford things
        Activate(Name);
    }
    
    private void Update()
    {
        var showButton = MAUnlocks.Me.m_researchTabRoyal || MAUnlocks.Me.m_researchTabCommoners || 
                        MAUnlocks.Me.m_researchTabLords || MAUnlocks.Me.m_researchTabMystic;
                        
        if(m_button.interactable != showButton)
        {
            transform.localScale = showButton ? Vector3.one : Vector3.zero;
            m_button.interactable = showButton;
        }
        
        if(m_nextUpdateTime < Time.time)
        {
            int newCount = MAResearchInfo.GetPlayerCanAffordCount();
            if (newCount != m_count)
            {
                m_affordableIconAnimated.Set(newCount, 0, 4);
                m_affordableIconAnimated.SetText(newCount.ToString());
                m_count = newCount;
            }
            m_nextUpdateTime = Time.time + 3f;
        }
    }
}
