using System.Collections.Generic;
using NUnit.Framework;
using UnityEngine;

public class BCQuestStockIn : BCStockIn
{
    public override bool AddResource(NGCarriableResource _resource)
    {
        if(base.AddResource(_resource))
        {
            var product = _resource.GetProduct();
            if(product != null && product.GetLinkedOrder(out MAOrder order))
            {
                order.ItemDispatched();
                order.ItemDelivered();
            }
            
            return true;
        }
        return false;
    }
    
    public NGCarriableResource GetResource()
    {
        foreach (var item in m_stock.Items)
        {
            if(item.Stock > 0)
            {
                return item.Resource;
            }
        }
        return null;
    }
}
