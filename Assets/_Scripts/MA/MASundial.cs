using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MASundial : MonoBehaviour
{
    public Transform m_sun;
    public float m_angle = 360f;
    public float m_offset = 0f;
    // Update is called once per frame
    void Update()
    {
        var tod = DayNight.Me.currentDayNightInfo.m_dayFraction;
        m_sun.localRotation = Quaternion.Euler(0, 0, tod * m_angle+m_offset);
    }
}
