using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MALandDeedDialog : MAGUIBase
{
    public Image m_landImage;
    public TMP_Text m_landText;
    
    ReactDistrictTable m_district;
    public void Activate(ReactDistrictTable _district)
     {
        Activate();
        m_district = _district;
        m_landImage.sprite = _district.ImageSprite;
        m_landText.text = _district.m_districtFlavourText;
    } 
    
    public void ClickedClose()
    {
        DistrictManager.Me.UnlockDistrictByID(m_district.m_districtID);
        Destroy(gameObject);
    }
    public static MALandDeedDialog Create(ReactDistrictTable _district)
    {
        var prefab = Resources.Load<MALandDeedDialog>("_Prefabs/Dialogs/MALandDeedDialog");
        var instance = Instantiate(prefab, NGManager.Me.m_centreScreenHolder);
        instance.Activate(_district);
        return instance;
    }
}
