using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

//public class MAResearchManager : MonoSingleton<MAResearchManager>, IPointer<PERSON>nter<PERSON><PERSON><PERSON>, IPointerExit<PERSON>and<PERSON>, IPointerClickHandler 
//public class MAResearchManager : MonoBeh<PERSON>our, IPointerEnterHandler 
//{
    /*public float m_spokeAngle = 30f;
    public float m_spokeAngleIncrement = 30f;
    public float m_spokeLength = 4f;
    public Transform m_researchItemHolder;
    public Transform m_cameraPosition;
    public Transform m_cameraFocus;
    public bool m_refresh = false;
    public MAFactionInfo.FactionType m_faction;
    public Image m_background;
    public TMP_Text m_title;
    private Vector2? lastLocalMousePosition;
    public Camera uiCamera;
    public GameObject prefabToSpawn;

    private void Start()
    {
        if(uiCamera == null)
        {
            uiCamera = Camera.main;
        }
    }

    void UpdateOld()
    {
        if(m_refresh)
        {
            m_refresh = false;
            //Activate(m_faction);
        }
    }
    /*public void OnPointerEnter(PointerEventData eventData)
    {
        if (m_background != null)
        {
            RectTransform rectTransform = m_background.rectTransform;
            Vector2 mousePosition = Input.mousePosition;
            Camera camera = eventData.pressEventCamera; // Get the camera used for rendering
            camera = Camera.main;
            var camera2 = Camera.current;
            // Check if mouse is hovering over the UI Image
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(rectTransform, mousePosition, camera, out Vector2 localPos))
            {
                m_title.text = $" Mouse2 {localPos}";
            }
            else if (RectTransformUtility.ScreenPointToLocalPointInRectangle(rectTransform, mousePosition, camera2, out Vector2 localPos2))
            {
                m_title.text = $" Mouse2 {localPos2}";
            }
            else
            {
                m_title.text = $" NO MOUSE";
            }
        }
    }#1#
    bool isEntered = false;
    public void OnPointerEnter(PointerEventData eventData)
    {
        if (m_background != null)
        {
            isEntered = true;
        }
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        isEntered = false; // Clear when mouse leaves
    }
    public void OnPointerDown(PointerEventData eventData)
    {
    }
    public void OnPointerClick(PointerEventData eventData)
    {
        // Get click position in screen space
        Vector2 screenPosition = eventData.position;

        // Convert screen position to world position using the UI camera
        Vector3 worldPosition = uiCamera.ScreenToWorldPoint(new Vector3(screenPosition.x, screenPosition.y, uiCamera.nearClipPlane));

        // Adjust z-position if needed (e.g., set to 0 to spawn on a specific plane)
        worldPosition.z = 0f; 

        // Instantiate the prefab at the calculated world position
        Instantiate(prefabToSpawn, worldPosition, Quaternion.identity);
    }

    void Update()
    {
        bool gotPosition = false;
        if (isEntered &&  m_background != null)
        {
            if (Input.mousePosition == null)
            {
                m_title.text = $" No MousePosition";
                return;
            }
            RectTransform rectTransform = m_background.rectTransform;
            Vector2 mousePosition = Input.mousePosition;
            //Camera camera = eventData.pressEventCamera; // Assuming you have an eventData reference (modify if needed)
            Camera camera = null;
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(rectTransform, mousePosition, camera, out Vector2 localPos))
            {
                if (lastLocalMousePosition.HasValue && lastLocalMousePosition.Value != localPos) // Check for change
                {
                    m_title.text = $" Mouse {localPos}";
                    gotPosition = true;
                }
                lastLocalMousePosition = localPos;
            }
        }
        
        if(gotPosition == false)
        {
            m_title.text = $" NO MOUSE";
        }
    }
    void Activate(MAFactionInfo.FactionType _faction)
    {
        m_faction = _faction;

    }

    void ActivateOld(MAFactionInfo.FactionType _faction)
    {
        m_faction = _faction;
        GameManager.Me.m_camera.transform.position = m_cameraPosition.transform.position;
        GameManager.Me.m_camera.transform.LookAt(m_cameraFocus.transform, Vector3.up);
        
        var factionList = MAResearchInfo.s_factionResearchDict[_faction];
        foreach(var item in factionList)
            item.m_displayedItem = null;
        m_researchItemHolder.DestroyChildren();
        var position = new Vector3(0, 0, 0);
        MAResearchItem lastItem = null;
        foreach(var item in factionList)
        {
            if (item.m_displayedItem == null)
            {
                var ri = MAResearchItem.Create(m_researchItemHolder, null, item, 0, position);
                var tPosition = SetPosition(ri, 0);
    //            if(lastItem != null)
    //                DrawConnection(ri, lastItem);
                position = tPosition;
            }
            lastItem = item.m_displayedItem;
        }
    }
    
    public Vector3 SetPosition(MAResearchItem _item, int _index)
    {
        var angle = m_spokeAngle + m_spokeAngleIncrement * _index;
        var pos = _item.transform.TransformPoint(_item.transform.localPosition);
        float newX = pos.x + m_spokeLength * (float)Mathf.Cos(angle);
        float newY = pos.y + m_spokeLength * (float)Mathf.Sin(angle);
        float newZ = pos.z; 
        return new Vector3(newX, newY, newZ);
    }
    public void DrawConnection(MAResearchItem _from, Vector3 _to)
    {
        var line = new GameObject("Line", typeof(LineRenderer));
        line.transform.SetParent(_from.m_lineHolder);
        var lr = line.GetComponent<LineRenderer>();
        lr.startWidth= .1f;
        lr.endWidth= .1f;
        lr.positionCount = 2;
        lr.SetPosition(0, _from.transform.position);
        lr.SetPosition(1, _to);
    }
    public void DrawConnection(MAResearchItem _from, MAResearchItem _to)
    {
        var line = new GameObject("Line", typeof(LineRenderer));
        line.transform.SetParent(_from.m_lineHolder);
        var lr = line.GetComponent<LineRenderer>();
        lr.startWidth= .1f;
        lr.endWidth= .1f;
        lr.positionCount = 2;
        var from =_from.transform.TransformPoint(_from.transform.localPosition);
        var to = _to.transform.TransformPoint(_to.transform.localPosition);
        lr.SetPosition(0, from);
        lr.SetPosition(1, to);
    }
    public static MAResearchManager Create(Transform _holder, MAFactionInfo.FactionType _faction)
    {
        var prefab = Resources.Load<MAResearchManager>("_Prefabs/Research/MAResearchManager");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_faction);
        return instance;
    }*/
//}
