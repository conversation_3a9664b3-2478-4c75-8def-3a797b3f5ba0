using System;
using System.Collections.Generic;
using UnityEngine;

public class MATriggerOverlapCheck : MonoBehaviour
{
    public HashSet<Collider> m_triggersOverlapping = new();
    [TextArea(1, 5)] public string m_debugTriggerOverlap = "";
    public SphereCollider m_trigger = null;

    private void Awake()
    {
        m_trigger = GetComponent<SphereCollider>();
    }

    private void Update()
    {
        #if UNITY_EDITOR
        m_debugTriggerOverlap = "Overlapping Triggers:\n";
        foreach (Collider collider1 in m_triggersOverlapping)
        {
            m_debugTriggerOverlap += $"{(collider1 != null ? collider1.transform.Path() : "null")},\n";
        }
        #endif
        foreach (Collider collider1 in m_triggersOverlapping)
        {
            if (collider1 == null)
            {
                m_triggersOverlapping.Remove(collider1);
                break;
            }
        }

        foreach (Collider collider1 in m_triggersOverlapping)
        {
            if (collider1 == null)
            {
                m_triggersOverlapping.Remove(collider1);
                break;
            }
            if (collider1.enabled == false)
            {
                m_triggersOverlapping.Remove(collider1);
                break;
            }
            else
            {
                GameObject go = collider1.gameObject;
                if ((go.activeSelf && go.activeInHierarchy) == false)
                {
                    m_triggersOverlapping.Remove(collider1);
                    break;
                }
            }
        }
    }

    protected void OnTriggerEnter(Collider _other)
    {
        OnTrigger(_other);
    }
	
    protected void OnTriggerStay(Collider _other)
    {
        OnTrigger(_other);
    }
	
    protected void OnTriggerExit(Collider other)
    {
        m_triggersOverlapping.Remove(null);
        m_triggersOverlapping.Remove(other);
    }
	
    protected bool OnTrigger(Collider _other)
    {
        m_triggersOverlapping.Remove(null);
        bool newOverlap = m_triggersOverlapping.Add(_other);
        return newOverlap;
    }
}
