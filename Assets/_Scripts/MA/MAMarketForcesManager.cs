using UnityEngine;
using System;
using System.Collections.Generic;
using Random = System.Random;

public class MAMarketForcesManager : MonoSingleton<MAMarketForcesManager>
{
    public bool IsMarketForceInPlace => State.m_currentMarketForce.IsNullOrWhiteSpace() == false;
    private GameState_MarketForces State => GameManager.Me.m_state.m_marketForces;
    private Dictionary<string, List<NGBlockInfo>> m_blockCategoryMap = null;
    
    public List<NGBlockInfo> EffectedBlocks => m_effectedBlocks;
    private List<NGBlockInfo> m_effectedBlocks = new();
    public int EffectedBlocksIteration => m_effectedBlocksIteration;
    
    private int m_effectedBlocksIteration = 1;
    
    private bool m_isTabUnlocked = false;
    
    public void PostLoad()
    {
        UpdateEffectedBlocks();
        
        UpdateMarketTabLocked();
    }
    
    private void UpdateMarketTabLocked()
    {
        if(DesignUIManager.Me)
        {
            bool unlocked = MAUnlocks.Me.m_marketForces;
            if(unlocked)
                DesignUIManager.Me.m_marketPanelTitle.text = "Market";
            else
                DesignUIManager.Me.m_marketPanelTitle.text = MAMessageManager.GetLockIcon(10, " Market"); 
                
            DesignUIManager.Me.m_marketPanel.Lock(unlocked == false, true);
            m_isTabUnlocked = MAUnlocks.Me.m_marketForces;
        }
    }
    
    public void Update()
    {
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
        if(MAUnlocks.Me.m_marketForces != m_isTabUnlocked)
        {
            UpdateMarketTabLocked();
        }
#endif //!(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
    }
    
    public void UpdateEffectedBlocks()
    {
        m_effectedBlocksIteration++;
        m_effectedBlocks.Clear();
        
        // This will sort by product line
        var blockMap = GetBlockMap();
        foreach (var blockCategory in blockMap.Values)
        {
            foreach(var blockInfo in blockCategory)
            {
                if(blockInfo.MarketInfo.Impacted)
                {
                    m_effectedBlocks.Add(blockInfo); 
                }
            }
        }
        
    }
    
    public void OnProductSold(NGDesignInterface.DesignScoreInterface _dsi)
    {
        if(_dsi == null) return;
        foreach(var part in _dsi.Parts)
        {
            part.m_block.MarketInfo.m_totalUsages++;
        }
    }
    
    private Dictionary<string, List<NGBlockInfo>> GetBlockMap()
    {
        if (m_blockCategoryMap != null) return m_blockCategoryMap;
        
        m_blockCategoryMap = new();
        foreach(var blockInfo in NGBlockInfo.s_blockInfos)
        {
            if(blockInfo.m_nGProductInfo.IsNullOrWhiteSpace()) continue;
            
            
            
            if(blockInfo.m_productInfos.Count == 0)
                continue;
                
            var category = blockInfo.m_productInfos[0].m_prefabName;
            
            if(m_blockCategoryMap.TryGetValue(category, out var blockInfos) == false)
                m_blockCategoryMap[category] = new() { blockInfo };
            else
                blockInfos.Add(blockInfo);
        }
        return m_blockCategoryMap;
    }
    
    public BlockMarketInfo GetOrCreateBlockInfo(NGBlockInfo _blockInfo)
    {
        if(_blockInfo == null) return null;
        
        if(State.m_blockMarketInfo.TryGetValue(_blockInfo.m_prefabName, out var marketForce) == false)
        {
            marketForce = new BlockMarketInfo();
            State.m_blockMarketInfo[_blockInfo.m_prefabName] = marketForce;
        }
        return marketForce;
    }
    
    public void ClearMarketForces()
    {
        m_effectedBlocks.Clear();
        State.m_currentMarketForce = null;
        foreach(var marketInfo in State.m_blockMarketInfo.m_values)
        {
            marketInfo.RemoveForce();
        }
        m_effectedBlocksIteration++;
    }
    
    public void ApplyMarketForces(string _groupName)
    {
        State.m_currentMarketForce = _groupName;

        if (_groupName.IsNullOrWhiteSpace())
            return;

        foreach(var blockId in State.m_blockMarketInfo.Keys)
        {
            var info = NGBlockInfo.GetInfo(blockId);
            bool runMarketForcesOnBlock = info != null && info.IsUnlocked();
            
            // Max value will prevent the block from being considered
            State.m_blockMarketInfo[blockId].m_processStep = runMarketForcesOnBlock ? 0 : int.MaxValue;
        }
        
        var blockMap = GetBlockMap();
        foreach (var blockCategory in blockMap.Values)
        {
            int highestUsageCount = GetHighestUsageCount(blockCategory);
            int lowestUsageCount = GetLowestUsageCount(blockCategory);
            
            ApplyMarketForces(1, highestUsageCount, lowestUsageCount, _groupName, MAMarketForce.EffectType.BlockCost, blockCategory);
            ApplyMarketForces(2, highestUsageCount, lowestUsageCount, _groupName, MAMarketForce.EffectType.SalesPrice, blockCategory);
        }
    }
    
    private void ApplyMarketForces(int _step, int _highestUsageCount, int _lowestUsageCount, string _groupName, MAMarketForce.EffectType _effect, List<NGBlockInfo> _blocks)
    {
        var marketForcesToApply = MAMarketForce.Get(_groupName, _effect);
        
        if(marketForcesToApply == null)
            return;
        
        m_effectedBlocksIteration++;
        
        foreach(var force in marketForcesToApply)
        {
            if(force.Condition == MAMarketForce.ConditionType.MostUsedPart && _highestUsageCount == 0)
                continue;
            if(force.Condition == MAMarketForce.ConditionType.LeastUsedPart && _lowestUsageCount == 0)
                continue;
                
            foreach(var block in _blocks)
            {
                var marketInfo = block.MarketInfo;
                
                // Check that we've not already processed this item
                if(marketInfo.m_processStep >= _step) continue;
                
                bool apply = false;
                switch(force.Condition)
                {
                    case MAMarketForce.ConditionType.MostUsedPart:
                        if(marketInfo.m_totalUsages == _highestUsageCount) apply = true;
                        break;
                        
                    case MAMarketForce.ConditionType.LeastUsedPart:
                        if(marketInfo.m_totalUsages == _lowestUsageCount) apply = true;
                        break;
                    case MAMarketForce.ConditionType.NeverUsedPart:
                        if(marketInfo.m_totalUsages == 0) apply = true;
                        break;
                    case MAMarketForce.ConditionType.OtherPart:
                        apply = true;
                        break;
                }
                
                if(apply)
                {
                    marketInfo.ApplyMarketForce(_step, force);
                    
                    if(marketInfo.Impacted && m_effectedBlocks.Contains(block) == false)
                    {
                        m_effectedBlocks.Add(block);
                    }
                }
            }
        }
    }
    
    private int GetHighestUsageCount(List<NGBlockInfo> _blocks)
    {
        int count = 0;
        foreach(var block in _blocks)
        {
            var marketInfo = block.MarketInfo;
            if(marketInfo.m_processStep == 0 && marketInfo.m_totalUsages > count)
                count = block.MarketInfo.m_totalUsages;
        }
        return count;
    }
    
    private int GetLowestUsageCount(List<NGBlockInfo> _blocks)
    {
        int count = int.MaxValue;
        foreach(var block in _blocks)
        {
            var marketInfo = block.MarketInfo;
            if(marketInfo.m_processStep == 0 && marketInfo.m_totalUsages < count)
                count = block.MarketInfo.m_totalUsages;
        }
        return count;
    }
}

[Serializable]
public class BlockMarketInfo
{
    public int m_totalUsages;
    public float m_currentSalesPriceMultiplier = 1f;
    public float m_currentBlockCostMultiplier = 1f; 
	
    public string BlockCostModifierText => GetModifierString(m_currentBlockCostMultiplier, false);	
    public string SalesPriceModifierText => GetModifierString(m_currentSalesPriceMultiplier);
    public bool Impacted => SalesPriceImpacted || BlockCostImpacted;
    public bool SalesPriceImpacted => m_currentSalesPriceMultiplier != 1;
    public bool BlockCostImpacted => m_currentBlockCostMultiplier != 1;
    
    public int m_processStep = 0;
    
    public void RemoveForce()
    {
        m_currentSalesPriceMultiplier = 1f;
        m_currentBlockCostMultiplier = 1f;
        
        m_processStep = 0;
    }
    
    public string GetModifierString(float _multiplier, bool _negativeIsBad = true)
    {
        if(Mathf.Approximately(_multiplier, 1))
            return null;
            
        var positiveColor = _negativeIsBad ? MAGUIBase.GreenColor : MAGUIBase.RedColor;
        var negativeColor = _negativeIsBad ? MAGUIBase.RedColor : MAGUIBase.GreenColor;
        
        _multiplier -= 1f;
        
        return $" <link=\"MarketForce\"><color={(_multiplier > 0 ? positiveColor : negativeColor)}>({(_multiplier > 0 ? "+" : "") }{_multiplier:P0})</color></link>";
    }
    
    private void ApplyMultiplier(ref float _valueToMod, float _multiplier)
    {
        float previous = _valueToMod - 1f;
        _valueToMod = 1 + (previous + _multiplier + (previous * _multiplier));
    }
    
    public void ApplyMarketForce(int _currentStep, MAMarketForce _force)
    {
        m_processStep = _currentStep;
        
        var rand = UnityEngine.Random.Range(0f, 1f);
        float multiplier = 1f;
        
        if(rand < _force.m_decreaseChance)
        {
            multiplier = -UnityEngine.Random.Range(_force.m_decreaseMin, _force.m_decreaseMax);
        }
        else if(rand < _force.m_increaseChance)
        {
            multiplier = UnityEngine.Random.Range(_force.m_increaseMin, _force.m_increaseMax);
        }
        
        if(multiplier != 1)
        {
            switch(_force.Effect)
            {
                case MAMarketForce.EffectType.BlockCost: ApplyMultiplier(ref m_currentBlockCostMultiplier, multiplier); break;
                case MAMarketForce.EffectType.SalesPrice: ApplyMultiplier(ref m_currentSalesPriceMultiplier, multiplier); break;
            }
        }
    }
}