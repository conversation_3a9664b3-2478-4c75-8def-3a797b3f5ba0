using System;
using UnityEngine;

public class MACollectableResource : MonoBehaviour
{
    [SerializeField] private string idleAnim = "";
    [SerializeField] private string hitAnim = "";
    [SerializeField] private AkEventHolder m_hitSound = null;
    [SerializeField] private string lastHitAnim = "";
    [SerializeField] private AkEventHolder m_lastHitSound = null;

    [SerializeField] private string m_tryUprootAnim = "";
    [SerializeField] private string m_uprootedAnim = "";
    [SerializeField] private AkEventHolder m_tryUprootSound = null;
    [SerializeField] private AkEventHolder m_uprootedSound = null;
    
    public bool WaitForChopDownFallingAnimation => lastHitAnim.IsNullOrWhiteSpace() == false;
    
    private Animator animator = null;
    public Animator Animator
    {
        get
        {
            if (animator == null)
            {
                animator = GetComponent<Animator>();
            }

            return animator;
        }
    }

    private ParticleSystem smoke = null;
    private ParticleSystem Smoke
    {
        get
        {
            if (smoke == null)
            {
                smoke = GetComponentInChildren<ParticleSystem>();
            }

            return smoke;
        }
    }

    public void Regrow(float regrowHeight, float regrowWidth)
    {
        if (!gameObject.activeSelf)
        {
            gameObject.SetActive(true);
            Animator.Play(idleAnim);
        }

        float height = Mathf.Clamp(regrowHeight, 0.0001f, 10.0f);
        float width = Mathf.Clamp(regrowWidth, 0.0001f, 10.0f);
        transform.localScale = new Vector3(width, height, width);
    }
    
    public void ApplyHit(float health)
    {
        if (health <= 0.0f)
        {
            m_lastHitSound.Play(gameObject);
            if (Animator != null)
            {
                Animator.Play(lastHitAnim);
            }

            if (Smoke != null)
            {
                Smoke.Play();
            }
        }
        else
        {
            m_hitSound.Play(gameObject);
            
            if (Animator != null)
            {
                Animator.Play(hitAnim);
            }
        }
    }

    public void StartUprootAnimation()
    {
        m_tryUprootSound.Play(gameObject);
        if (string.IsNullOrEmpty(m_tryUprootAnim) == false)
            Animator.Play(m_tryUprootAnim);
    }

    public void StopUprootAnimation()
    {
        m_tryUprootSound.Stop(gameObject);
        Animator.StopPlayback();
    }

    public void PlayUprootedAnimation()
    {
        StopUprootAnimation();
        m_uprootedSound.Play(gameObject);
        if (string.IsNullOrEmpty(m_uprootedAnim) == false)
            Animator.Play(m_uprootedAnim);
    }

    public Action m_onTreeFallAnimationFinished;
    public void OnTreeFallAnimationFinished()
    {
        if (Smoke != null)
            Smoke.Stop();
        Animator.Play(idleAnim);

        gameObject.SetActive(false);
        
        m_onTreeFallAnimationFinished?.Invoke();
    }
}
