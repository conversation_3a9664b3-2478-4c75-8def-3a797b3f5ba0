using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCResourceNeededBase  : BCActionBase
{
    public virtual bool OnWorkerDelivers(MAWorker _worker) => false;

    override public float GetResourceRequirement(NGCarriableResource _resource, bool _isTest = false)
    {
       if(m_stock.IsCompatible(this, _resource, m_building, _isTest))
            return 1f;
       return 0f;
    }

    public void SetupInputStock(Transform _stockHolder, NGCarriableResource _resource)
    {
        m_stock.RemoveEmptyStockAndClearNeededToProduce();
        m_stock.AddOrCreateStock(_resource, 0, 1f);
        
        /*if(m_stockPositions)
        {
            m_stockPositions.ClearStock();
            m_stockPositions.m_pads.Clear();
        
            m_stockPositions.m_pads.Add(new MAStockPositions.StockPositionPads()
            {
                m_resourceHolder = _stockHolder,
            });
        }*/
    }

}
