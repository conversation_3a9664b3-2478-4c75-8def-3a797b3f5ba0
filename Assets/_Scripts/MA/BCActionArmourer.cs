using UnityEngine;

public class BCActionArmourer : BCActionShop
{
    public override bool ClearOrderDesignOnReturn => true;
    public override bool ResetOrderQuantityOnReturn => true;
    public override int MaxCardSlots { get { return 2; } }
    protected override string OrderInfoBlockName => "ArmourOrder";

    public override SpecialHandlingAction GetSpecialHandlingAction(NGMovingObject _obj, SpecialHandlingAction _restrictedAction)
    {
        MAHeroBase hero = _obj as MAHeroBase;
        if(hero != null)
        {
            if(_restrictedAction == null || _restrictedAction == SpecialHandlingAction.CollectArmour)
                if(<PERSON><PERSON><PERSON><PERSON><PERSON>(hero))
                    return SpecialHandlingAction.CollectArmour;
        }
        return null;
    }
    
    virtual public void SetDesign(MACharacterBase _character, GameState_Design _design)
    {
        _character.SetArmourDesign(_design);
    }
}
