using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAParticule : MonoBeh<PERSON>our
{
   public List<ParticleSystem> m_particleSystem = new();

   public void Start()
   {
      if (m_particleSystem.Count == 0)
      {
         var particules = GetComponentsInChildren<ParticleSystem>();
         if(particules != null && particules.Length > 0)
            m_particleSystem.AddRange(particules);
      }
   }
   public void Toggle(bool _flag)
   {
      foreach (var p in m_particleSystem)
      {
         if (p == null) continue;
         if (p.isPlaying != _flag)
         {
            if(_flag) p.Play();
            else p.Stop();
         }
      }
   }
}
