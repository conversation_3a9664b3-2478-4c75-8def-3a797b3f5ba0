using System;
using System.Collections;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Xml;
using TMPro;
using UnityEngine;
using UnityEngine.Purchasing;
using UnityEngine.Rendering.Universal;
using Debug = UnityEngine.Debug;
#if OldBusinessFlow

public class MAKnackSupportd
{
    
    #region Fields Definitions
    public const string CNComponent = "component";
    public const string CNBuilding = "building";
    public const string CNRegion = "region";
    public const string CNBuildingComponent = "buildingcomponent";
    public const string CNBuildingTile = "buildingtile";
    public const string CNWorker = "worker";
    public const string CNBlock = "block";
    public const string CNCarryable = "carryable";
    public const string CNPos = "pos";
    public const string CNAll = "all";
    public const string CNGUI = "gui";
    public const string CNPartOnDesignTable = "part";
    public const string CNCount = "count";
    public const string CNMessage = "message";
    public const string CNOrderInfo = "orderinfo";
    public class TutorialTrigger : Attribute { }
    public class TutorialCommands : Attribute { }
    public class BusinessCommands : Attribute { }


    [Flags]
    public enum Triggers
    {
        None = 0,
        MoveCamera = 1 << 0,
        FollowCamera = 1 << 1,
        Gesturing = 1 << 2,
        Screenshot = 1 << 3,
    }
    public class KnackParser
    {
        public KnackParser()
        {
        }

        public KnackParser(List<string> m_controls, string _callingStep = "")
        {
            foreach (var c in m_controls)
                m_parameters.Add(new Parameter() {m_control = c});
        }

        public class Parameter
        {
            public string m_control = "";
            public string m_id = "";
            public string m_modifier = "";
        }
        public string m_function;
        public string m_callingStep;
        public string m_originalString;
        public List<Parameter> m_parameters = new List<Parameter>();
    }
    public static Triggers m_triggers;
    #endregion Fields Definitions
    #region Parser
   public static KnackParser DecodeLines(string _source, string _callingStep = "")
    {
        m_debugError = false;
        foreach (var l in _source.Split('\n', '|'))
        {

            if (l.Contains('('))
            {
                var parser = DecodeLinesFunction(_source, _callingStep);
                return parser;
            }
            else if (l.Contains('='))
            {

            }
        }

        return null;
    }
    static KnackParser DecodeLinesFunction(string _source, string _callingStep = "")
    {
        var parser = new KnackParser();
        var bsplit = _source.Split('(',')');
        parser.m_function = bsplit[0].Trim();
        parser.m_callingStep = _callingStep;
        parser.m_originalString = _source;
        var csplit = bsplit[1].Split(',');
        foreach (var c in csplit)
        {
            var bracketSplit = c.Split('[', ']');
            if (bracketSplit.Length > 1)
            {
                var special = new KnackParser.Parameter() {m_control = bracketSplit[0].ToLower().Trim(), m_id = bracketSplit[1].Trim()};
                if (bracketSplit.Length > 2)
                    special.m_modifier = bracketSplit[2].TrimStart(':');
                parser.m_parameters.Add(special);
            }
            else
            {
                parser.m_parameters.Add(new KnackParser.Parameter(){m_control = c});
            }
        }

        return parser;
    }

    static public bool TryParse(string _what, out bool result, string _callingStep = "")
    {
        result = true;
        if(_what.IsNullOrWhiteSpace())
        {
            return false;
        }
        foreach (var line in _what.Split('\n', '|'))
        {
            if (line.IsNullOrWhiteSpace()) continue;
            var decodeedLine = DecodeLines(line, _callingStep);
            if (decodeedLine == null)
            {
                return false;
            } 
            var method = typeof(MAParserSupport).GetMethod(decodeedLine.m_function);
            if (method != null)
            {
                if(method.IsStatic == false)
                {
                    Debug.LogError(DebugColor($"[{_callingStep}]{decodeedLine.m_function} is not static"));
                    return false;
                }
                var iResult = method.Invoke(null, new object[]{decodeedLine});
                if (iResult != null)
                {
                    var boolResult = (bool)Convert.ChangeType(iResult, TypeCode.Boolean);
                    result &= boolResult;
                    if (result == false)
                    {
                        return true;
                    }
                }
                else
                {
                    continue;
                }
            } 
            else
            {
                return false;
            }
        }
        result = true;
        return true;
    }
 
    #endregion Parser
    #region Commands
    
    [TutorialCommands] static public void SetTimeOfDay(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return;
        }
        if (floatinv.TryParse(_pass.m_parameters[0].m_control, out var tod) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} invalid float {_pass.m_parameters[0].m_control}"));
            return;
        }
        GlobalData.Me.m_overrideDayFraction = tod;
    }
    [TutorialCommands] static public void MoveCamera(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 2)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes two parameters not {_pass.m_parameters.Count}"));
            return;
        }
        if(_pass.m_parameters[1].m_control.TryFloatInv(out var distance) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} float Parse failed on '{_pass.m_parameters[0].m_id}'"));
            return;
        }             
        switch (_pass.m_parameters[0].m_control)
        {
            case CNBuilding:
                ProcessBuilding(_pass, (building) =>
                {
                    StartCameraTransition(building.DoorPosOuter, distance);               
                });
                break;
            case CNBuildingComponent:
                ProcessBuildingComponent(_pass, (building, component) =>
                {
                    StartCameraTransition(component.transform.position, distance);               
                    
                });
                break;
            case CNBlock:
                ProcessBlock(_pass, (block) =>
                {
                    StartCameraTransition(block.transform.position, distance);               

                });
                break;
            case CNWorker:
                ProcessWorker(_pass, (worker) =>
                {
                    StartCameraTransition(worker.transform.position, distance);   
                });
                break;
            case CNCarryable:
                ProcessCarryable(_pass, (found) =>
                {
                    StartCameraTransition(found.transform.position, distance);
                });
                break;
            case CNPos:
                ProcessPos(_pass, (pos) =>
                {
                    StartCameraTransition(pos, distance);
                });
                break;
            case CNComponent:
                ProcessComponent(_pass, (comp) => { StartCameraTransition(comp.transform.position, distance); });
                break;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such control"));
                break;
        }
    }
    
    [TutorialCommands] static public void FollowCamera(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 2)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes two parameters not {_pass.m_parameters.Count}"));
            return;
        }
        if(_pass.m_parameters[1].m_control.TryFloatInv(out var distance) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} float Parse failed on '{_pass.m_parameters[0].m_id}'"));
            return;
        }             
        switch (_pass.m_parameters[0].m_control)
        {
            case CNBuilding:
                ProcessBuilding(_pass, (building) =>
                {
                    StartFollowCamera(building.gameObject, distance);               
                });
                break;
            case CNBuildingComponent:
                ProcessBuildingComponent(_pass, (building, component) =>
                {
                    StartFollowCamera(component.gameObject, distance);               
                });
                break;
            case CNWorker:
                ProcessWorker(_pass, (worker) =>
                {
                    StartFollowCamera(worker.gameObject, distance);   
                });
                break;
            case CNCarryable:
                ProcessCarryable(_pass, (found) =>
                {
                    StartFollowCamera(found.gameObject, distance);
                });
                break;
            case CNComponent:
                ProcessComponent(_pass, (comp) =>
                {
                    StartFollowCamera(comp.gameObject, distance);
                });
                break;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such control"));
                break;
        }
    }

    [TutorialCommands] static public void LockInteractions(KnackParser _pass)
    {
        LockUnlockInteractions(_pass, false);
    }

    [TutorialCommands] static public void UnlockInteractions(KnackParser _pass)
    {
        LockUnlockInteractions(_pass, true);
    }

    [TutorialCommands] static private void LockUnlockInteractions(KnackParser _pass, bool _lockFlag)
    {
        switch (_pass.m_parameters[0].m_control)
        {
            case "ALL":
            case "All":
            case CNAll:
                foreach (var b in NGManager.Me.m_maBuildings)
                {
                    var cl = b.ComponentLists<MABuildingComponentDrag, MABuildingComponentTap>();
                    foreach (var l in cl)
                        l.enabled = _lockFlag;
                }
                break;
            case CNBuilding:
                ProcessBuilding(_pass, (building) =>
                {
                    var bcl = building.ComponentLists<MABuildingComponentDrag, MABuildingComponentTap>();
                    foreach (var l in bcl)
                        l.enabled = _lockFlag;
                });
              
                break;
            case CNWorker:
                ProcessWorker(_pass, (worker) =>
                {
                    var wc = worker.GetComponent<Pickup>();
                    if (wc) wc.enabled = _lockFlag;
                });
                break;
            case CNCarryable:
                ProcessCarryable(_pass, (foundCarryable) =>
                {
                    var fc = foundCarryable.GetComponent<Pickup>(); 
                    if (fc) fc.enabled = _lockFlag;
                });
                break;
            case CNComponent:
                ProcessComponent(_pass, (comp) =>
                {
                    var building = comp.GetComponent<MABuildingComponentBase>();
                    if(building)
                        building.enabled = _lockFlag;
                });   
                break;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such control"));
                break;
        }
    }
    
    [TutorialCommands] static public void Highlight(KnackParser _pass)
    {
        bool toggle = true;
        if (_pass.m_parameters.Count > 1)
        {
            bool.TryParse(_pass.m_parameters[1].m_control, out toggle);
        }
        switch (_pass.m_parameters[0].m_control)
        {
            case CNBuildingTile:
                ProcessBuildingTile(_pass, (building, thing) =>
                {
                    if (thing == null)
                    {
                        Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} Cannot highlight 'Any'"));
                        return;
                    }
                    var mesh = thing.GetComponent<MeshRenderer>();
                    mesh.material.color = (toggle) ? Color.red : Color.white;
                });
                break;
            case CNBuilding:
                ProcessBuilding(_pass, (building) =>
                {
                if(toggle)
                    CameraRenderSettings.Me.SetHighlight(building);
                else
                    CameraRenderSettings.Me.ClearHighlight(building);
                });
                break;
            case CNBlock:
                ProcessBlock(_pass, (block) =>
                {
                    if(toggle)
                        CameraRenderSettings.Me.SetHighlight(block.transform.position, 4F);
                    else
                        CameraRenderSettings.Me.ClearHighlight();
                });
                break;
            case CNWorker:
                ProcessWorker(_pass, (worker) =>
                {
                    if(toggle)
                        CameraRenderSettings.Me.SetHighlight(worker.transform.position, 4f);
                    else
                        CameraRenderSettings.Me.ClearHighlight();   
                });
                break;
            case CNCarryable:
                ProcessCarryable(_pass, (carryable) =>
                {
                if(toggle)
                    CameraRenderSettings.Me.SetHighlight(carryable.transform.position, 4f);
                else
                    CameraRenderSettings.Me.ClearHighlight();   
                });
                break;
            case CNGUI:
                if (MAGUIBase.HighlightGUI(_pass.m_parameters[0].m_id, toggle) == false)
                {
                    Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} GUI element '{_pass.m_parameters[0].m_id}' not found"));
                }
                break;
            case CNComponent:
                 ProcessComponent(_pass, (comp) =>
                {
                    if(toggle)
                        FlashObject.Flash(comp.gameObject, new Color(1f,0f,0f,.1f), new Color(.5f,0f,0f,.4f), 0.5f);
                    else
                        FlashObject.Stop(comp.gameObject);
                });
                break;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such control"));
                break;
        }
//        LockUnlockInteractions(_pass, true);
    }

    [TutorialCommands] static public void DeactivateGUI(KnackParser _pass)
    {
        switch (_pass.m_parameters[0].m_control)
        {
            case CNGUI:
                if (MAGUIBase.ActivateGUI(_pass.m_parameters[0].m_id, false) == false)
                {
                    Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} GUI element '{_pass.m_parameters[0].m_id}' not found"));
                }
                break;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such control"));
                break;
        }
    }
    
    [TutorialCommands] static public void ActivateGUI(KnackParser _pass)
    {
        switch (_pass.m_parameters[0].m_control)
        {
            case CNGUI:
                if (MAGUIBase.ActivateGUI(_pass.m_parameters[0].m_id, true) == false)
                {
                    Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} GUI element '{_pass.m_parameters[0].m_id}' not found"));
                }
                break;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such control"));
                break;
        }
    }
    [TutorialCommands] static public void ClearBanners(KnackParser _pass)
    {
        if (NGTutorial.m_parseTutorialLine != null)
        {
            if(NGTutorial.m_parseTutorialLine.ShowingMessage)
                NGTutorial.m_parseTutorialLine.DestroyMessage();
        } 
        NGTutorialManager.Me.ShowMentorPose("", false);
        NGTutorialManager.Me.ShowVCPose("", false);
    }
    
    [TutorialCommands] static public void ShowMessageDialog(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 2)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 2 parameter not {_pass.m_parameters.Count}"));
            return;
        }
        var message = _pass.m_parameters[0].m_control;
        var pose = _pass.m_parameters[1].m_control;
        if (NGTutorial.m_parseTutorialLine != null)
        {
            if(NGTutorial.m_parseTutorialLine.ShowingMessage)
                NGTutorial.m_parseTutorialLine.DestroyMessage();
        } 
        NGTutorialManager.Me.ShowMentorPose("", false);
        NGTutorialManager.Me.ShowVCPose("", false);
    }

    [TutorialCommands] static public void SpawnSpecialOrder(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameter not {_pass.m_parameters.Count}"));
            return;
        }
        var gift = NGBusinessGift.GetInfo(_pass.m_parameters[0].m_control);
        var order = MAOrderInfo.GetInfo(_pass.m_parameters[0].m_control);
        if (order == null && gift == null)
        {
            Debug.LogError(DebugColor($"MAKnackSupport - SpawnOrder - could not find knack NGBusinessGift '{_pass.m_parameters[0].m_control}'"));
            return;
        }
        
        MAOrderDataManager.Me.PushNewOrderToBoard(order);
    }
    [TutorialCommands] static public void TriggerOrderSequence(KnackParser _pass)
    {
        //params: name, count, keep/clear 
        if (_pass.m_parameters.Count != 3)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 3 parameter not {_pass.m_parameters.Count}"));
            return;
        }
        var blockName = _pass.m_parameters[0].m_control;
        if(int.TryParse(_pass.m_parameters[1].m_control, out var count) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} Unrecognised Int {_pass.m_parameters[1].m_control}"));
        }

        var keepClear = _pass.m_parameters[2].m_control.ToLower().Trim();

        MAOrderDataManager.Me.TriggerOrderSequence(blockName, count, keepClear);
    }

    [TutorialCommands] static public void LockOrder(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 2)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 2 parameter not {_pass.m_parameters.Count}"));
            return;
        }
        var info = MAOrderInfo.GetInfo(_pass.m_parameters[0].m_control);
        if (info == null)
        {
            Debug.LogError(DebugColor($"MAKnackSupport - LockOrder - could not find knack order '{_pass.m_parameters[0].m_control}'"));
            return;
        }
        if(Boolean.TryParse(_pass.m_parameters[0].m_control, out var lockOrder) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} Unreconised Bool {_pass.m_parameters[1].m_control}"));
        }

        HashSet<MAOrder> inGameOrder = MAOrderDataManager.Me.m_sortedOrderData.GetOrdersByIndexer(_pass.m_parameters[0].m_control);
        foreach(var order in inGameOrder) { order.m_locked = lockOrder; }
    }
    
    [TutorialCommands] static public void SpawnFlow(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameter not {_pass.m_parameters.Count}"));
            return;
        }
        if(NGBusinessFlow.s_flowsDict.TryGetValue(_pass.m_parameters[0].m_control, out var flow) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} could not find flow '{_pass.m_parameters[0].m_control}'"));
            return;
        }
        MAGameFlow.StartFlow(flow);
    }
  

    [TutorialCommands] static public void SpawnWaggonTrain(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 3)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 3 parameters not {_pass.m_parameters.Count}"));
            return;
        }

        var blockNames = _pass.m_parameters[2].m_control.Split('|', ';').ToList();
        switch (_pass.m_parameters[1].m_control)
        {
            case CNBuilding:
                ProcessBuilding(_pass, (building) =>
                {
                    var t = building.transform;
                    MAWaggonTrainManager.Me.AddWaggonTrainSequence(_pass.m_parameters[0].m_control, t, blockNames);                    
                }, 1);
                break;
            case CNPos:
                ProcessPos(_pass, (pos) =>
                {
                    MAWaggonTrainManager.Me.AddWaggonTrainSequence( _pass.m_parameters[0].m_control, pos, blockNames);                    

                },1);
                break;
        }
    }
    [TutorialCommands] static public void SpawnCreature(KnackParser _pass)
    {
        var creature = _pass.m_parameters[0].m_control;
        if(creature != "Creature") return;

        string[] availableCreatureTypes = MACreatureControl.Me.CreaturePrefabNames;//TODO: type names or prefab names (depends on complexity of zombie types, sample zombie pack makes it hard to avoid inherently different prefabs per zombie)
        string creatureType = _pass.m_parameters[0].m_id;//quantity of each?
        if(availableCreatureTypes.Contains(creatureType) == false)
        {
            Debug.LogError(DebugColor($"MAKnackSupport - SpawnCreature - could not find knack creature type '{creatureType}' in CreatureTypeNames"));
            return;
        }
        
        switch (_pass.m_parameters[1].m_control)
        {
            case CNBuilding:
                ProcessBuilding(_pass, (building =>
                {//TODO: currently we dont distinguish between graveyard and crypt -> we spawn creature on the base of both
                    MACreatureControl.Me.SpawnNewCreatureAtBuilding(creatureType, building);
                }), 1);
                break;
            case CNPos:
                ProcessPos(_pass, (spawnPos) =>
                {
                    MACreatureControl.Me.SpawnNewCreatureAtPosition(creatureType, spawnPos);
                });
                break;
        }
    }

    [TutorialCommands] static public void EndTutorialPhase(KnackParser _pass)
    {
        NGTutorialManager.Me.ReadyToMake = true;
        NGTutorialManager.Me.TutorialHasEnded();
    }

    [TutorialCommands] static public void CancelFollowCamera(KnackParser _pass)
    {
        m_triggers &= ~Triggers.FollowCamera;
    }

    [TutorialCommands] static public void KillGestures(KnackParser _pass)
    {
        m_triggers &= ~Triggers.Gesturing;
    }

    [TutorialCommands] static public void GestureDrag(KnackParser _pass) // (string _from, string _to, string _type, bool _playImmediate)
    {
        if (_pass.m_parameters.Count < 2)
        {
            Debug.LogError(DebugColor(
                $"[{_pass.m_callingStep}]{_pass.m_function} takes 2 parameters not {_pass.m_parameters.Count}"));
            return;
        }

        var drags = new Transform[2];
        for (var i = 0; i < 2; i++)
        {
            var para = _pass.m_parameters[i];
            switch (_pass.m_parameters[i].m_control)
            {
                case CNBuilding:
                    ProcessBuilding(_pass, (building) => { drags[i] = building.m_balloonHolder; }, i);
                    break;
                case CNBuildingComponent:
                    ProcessBuildingComponent(_pass, (building, component) => { drags[i] = component.transform; }, i);
                    break;
                case CNWorker:
                    ProcessWorker(_pass, (worker) => { drags[i] = worker.transform; }, i);
                    break;
                case CNCarryable:
                    ProcessCarryable(_pass, (found) => { drags[i] = found.transform; }, i);
                    break;
                case CNComponent:
                    ProcessComponent(_pass, (comp) => { drags[i] = comp.transform; }, i);
                    break;
                default:
                    Debug.LogError(DebugColor(
                        $"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such control"));
                    break;
            }
        }

        var gesture = NGTutorialGesture.Create(drags[0], drags[1], "Dragging", true,
            () => m_triggers.HasFlag(Triggers.Gesturing));
        if (gesture)
        {
            //NGTutorialManager.Me.AddGesture(gesture);
            m_triggers |= Triggers.Gesturing;
        }
    }

    [TutorialCommands] static public void GestureTap(KnackParser _pass)// (string _from, string _to, string _type, bool _playImmediate)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return;
        }
        Transform what = null;
        switch (_pass.m_parameters[0].m_control)
        {
            case CNBuilding:
                ProcessBuilding(_pass, (building) => { what =building.transform; });
                break;
            case CNBuildingComponent:
                ProcessBuildingComponent(_pass, (building, component) => { what = component.transform; });
                break;
            case CNWorker:
                ProcessWorker(_pass, (worker) => { what = worker.transform; });
                break;
            case CNCarryable:
                ProcessCarryable(_pass, (found) => { what = found.transform; });
                break;
            case CNComponent:
                ProcessComponent(_pass, (comp) => { what = comp.transform; });
                break;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such control"));
                break;
        }

        var gesture = NGTutorialGesture.Create(what, what, "Tap", true, () => m_triggers.HasFlag(Triggers.Gesturing));
        if (gesture)
        {
            NGTutorialManager.Me.AddGesture(gesture);
            m_triggers |= Triggers.Gesturing;
        }
    }
    [BusinessCommands] static public void TakeScreenshot(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return;
        }

        if (int.TryParse(_pass.m_parameters[0].m_control, out var index) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} Unreconised Int {_pass.m_parameters[0].m_control}"));
        }

        m_triggers |= Triggers.Screenshot;
        NGManager.Me.StartCoroutine(Screenshot(index));
    }
    [BusinessCommands] static public void LoopFlow(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 3)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 3 parameters not {_pass.m_parameters.Count}"));
            return;
        }

        if (int.TryParse(_pass.m_parameters[0].m_control, out var index) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} Unrecognised Int {_pass.m_parameters[0].m_control}"));
        }
        if (_pass.m_parameters[1].m_control.TryFloatInv(out var multiplier) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} Unrecognised float {_pass.m_parameters[0].m_control}"));
        }
        if (int.TryParse(_pass.m_parameters[2].m_control, out var loopFlowCount) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} Unrecognised Int {_pass.m_parameters[0].m_control}"));
        }
#if OldBusinessFlow
      
        if (NGBusinessFlow.m_inLoopFlow)
        {
            if (NGBusinessFlow.m_loopFlowCount > 0)
            {
                NGBusinessFlow.m_loopFlowCount--;
                if (NGBusinessFlow.m_loopFlowCount == 0)
                {
                    NGBusinessFlow.m_inLoopFlow = false;
                    NGBusinessFlow.m_loopFlowMultiplier = 1;
                    NGBusinessFlow.m_loopFlowCount = -1;
                    return;
                }
            }
        }
        else
        {
            NGBusinessFlow.m_loopFlowCount = loopFlowCount;
        }

        NGBusinessFlow.m_inLoopFlow = true;
        NGBusinessFlow.m_loopFlowToIndex = index;
        NGBusinessFlow.m_loopFlowMultiplier *= multiplier;
        #endif
    }
    
    [BusinessCommands] static public bool Dialog(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return false;
        }
        var name = _pass.m_parameters[0].m_control;
        if (NGTutorial.s_dialogDict.ContainsKey(name) == false)
        {
            Debug.LogError(DebugColor($"A Tutorial Phase was called <b>Dialog({name})</b> does not exist."));
            return false;
        }

        NGTutorial.s_dialogDict[name].Restart(NGTutorialManager.m_parseFlowLine, MAGameFlow.m_updatingFlow);
        return false;
    }
    
    [BusinessCommands] static public bool Flow(KnackParser _pass)
    {
        var flow = BranchFlow(_pass);
        return flow != null;
    }

    [BusinessCommands] static MAGameFlow BranchFlow(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return null;
        }
        var name = _pass.m_parameters[0].m_control;
        if (NGBusinessFlow.s_flowsDict.ContainsKey(name) == false)
        {
            Debug.LogError(DebugColor($"A Tutorial Phase was called <b>Dialog({name})</b> does not exist."));
            return null;
        }
        var newFlow = MAGameFlow.StartFlow(NGBusinessFlow.s_flowsDict[name]);
        return newFlow;
    }
    [BusinessCommands] static public bool FlowWait(KnackParser _pass)
    {
        var newFlow = BranchFlow(_pass);
        if (newFlow != null)
        {
            MAGameFlow.m_updatingFlow.m_waitingForOtherFlow = newFlow.m_blockName;
            MAGameFlow.m_updatingFlow.State = MAGameFlow.GameFlowState.WaitingForOtherFlow;
            return true;
        }
        return false;
    }
    [BusinessCommands] static public bool Dialogait(KnackParser _pass)
    {
        Dialog(_pass);
        return true;
    }
    [TutorialCommands] static public void StartBusinessFlow(KnackParser _pass)
    {
        NGTutorialManager.Me.ReadyToMake = true;
        NGTutorialManager.Me.TutorialHasEnded();
 //       HideBuildingName();
   //     UnlockTownInteractions();
        NGBusinessDecisionManager.Me?.StartFlow();
    }

    [TutorialCommands] static public void Unlock(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return;
        }

        NGUnlocks.Change(_pass.m_parameters[0].m_control);
    }

    [TutorialCommands] static public void FlowMessage(KnackParser _pass)
    {
        var message = "";
        switch (_pass.m_parameters.Count)
        {
            case 1:
                message = _pass.m_parameters[0].m_control.Trim();
                if (message.Equals("message", StringComparison.OrdinalIgnoreCase))
                    message = MAGameFlow.m_updatingFlow.m_message;
                MAGameFlowMessage.Create(message);      //FLowMessage("message")
                break;
            case 2:
                message = _pass.m_parameters[1].m_control.Trim();
                if (message.Equals("message", StringComparison.OrdinalIgnoreCase))
                    message = MAGameFlow.m_updatingFlow.m_message;
                MAGameFlowMessage.Create(_pass.m_parameters[0].m_control, message); //FlowMessage(Title, "message")
                break;
            case 3:  
                message = _pass.m_parameters[2].m_control.Trim();
                if (message.Equals("message", StringComparison.OrdinalIgnoreCase))
                    message = MAGameFlow.m_updatingFlow.m_message;
                MAGameFlowMessage.Create(_pass.m_parameters[0].m_control, _pass.m_parameters[1].m_control.Trim(), message); //FlowMessage(Title, "message", advisor)
                break;
            case 4:  
                message = _pass.m_parameters[2].m_control.Trim();
                if (message.Equals("message", StringComparison.OrdinalIgnoreCase))
                    message = MAGameFlow.m_updatingFlow.m_message;
                MAGameFlowMessage.Create(_pass.m_parameters[0].m_control, _pass.m_parameters[1].m_control.Trim(), message, _pass.m_parameters[3].m_control); //FlowMessage(Title, "message", advisor)
                break;
                           
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes no more than 3 parameters not {_pass.m_parameters.Count}"));
                break;
   
        }
    }
    // FlowAudioMessage(MessageType, "message", Audio, pose)
    [TutorialCommands] static public void FlowAudioMessage(KnackParser _pass)
    {
        var message = "";
        switch (_pass.m_parameters.Count)
        {
            case 4:
                var type = _pass.m_parameters[0].m_control.Trim();
                message = _pass.m_parameters[1].m_control.Trim();
                var audio = _pass.m_parameters[2].m_control.Trim();
                var pose = _pass.m_parameters[3].m_control.Trim();
                var maMessage = MAMessage.Create(type, message, audio, pose);
                maMessage.Display();
                break;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes no more than 3 parameters not {_pass.m_parameters.Count}"));
                break;

        }
    }

    [TutorialCommands] static public void FlowBannerMessage(KnackParser _pass)
    {
        switch (_pass.m_parameters.Count)
        {
            case 1:
               // MAGameFlow.m_updatingFlow.TriggerBannerMessage(); 
                NGBusinessDirectionBanner.Create(MAGameFlow.m_updatingFlow, _pass.m_parameters[0].m_control, MAGameFlow.m_updatingFlow.TappedBanner);
                break;
   
        }
    }

    [TutorialCommands] static public bool StartCotterPlotPlacement(KnackParser _pass)
    {
        MAGameInterface.StartCotterPlotPlacement();
        return true;
    } 
    [TutorialCommands] static public void EndCotterPlotPlacement(KnackParser _pass)
    {
        MAGameInterface.EndCotterPlotPlacement();
    }
    [TutorialCommands] static public bool StartCotterHouseDesign(KnackParser _pass)
    {
        MAGameInterface.StartCotterPlotPlacement();
        return true;
    } 
    [TutorialCommands] static public void EndCotterHouseDesign(KnackParser _pass)
    {
        MAGameInterface.EndCotterPlotPlacement();
    }
    
    [TutorialCommands] static public void Change(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return;
        }
        NGUnlocks.Change(_pass.m_parameters[0].m_control.Trim());
    }
    [TutorialCommands] static public void UnlockProductPack(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return;
        }
        var split = _pass.m_parameters[0].m_control.Trim().Split('\n', '|', ';', ',');

        foreach (var s in split)
        {
            var packName = s.Trim();
            if (packName.IsNullOrWhiteSpace()) continue;

            List<string> contents = ProductPacks.GetDecorationsFromPack(packName);
            foreach(string item in contents)
                GameManager.AddUnlock(item);
        }
    }
    [TutorialCommands] static public void UnlockProductLine(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return;
        }
        var split = _pass.m_parameters[0].m_control.Trim().Split('\n', '|', ';', ',');
        var gotOne = false;
        foreach (var s in split)
        {
            var productName = s.Trim();
            if (productName.IsNullOrWhiteSpace()) continue;
            var product = NGProductInfo.GetInfo(s);
            if (product != null)
            {
                product.m_isUnlocked = true;
                gotOne = true;
                NGProductInfo.ShowUnlockedChoiceUI((t) => { }, s);
                ResearchLabRewardsController.UnlockProductLine(s);
            }
        }
        if (gotOne == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} Trying to UnlockProductLine but '{_pass.m_parameters[0].m_control}' not found"));
            return;
        }
    }
    [TutorialCommands] static public void ShowLandDeed(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1 && _pass.m_parameters[0].m_control.IsNullOrWhiteSpace() == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameter not {_pass.m_parameters.Count}"));
            return;
        }

        switch (_pass.m_parameters[0].m_control.ToLower())
        {
            case CNRegion:
                ProcessRegion(_pass, (region) =>
                {
                    MALandDeedDialog.Create(region);
                });
                break;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} No such control {_pass.m_parameters[0].m_control}"));
                break;
        }
    }

    #endregion Controls

    #region Triggers
    [TutorialTrigger] static public bool WaitForLastAudioComplete(KnackParser _pass)
    {
        return true;
        if (_pass.m_parameters.Count != 0 && _pass.m_parameters[0].m_control.IsNullOrWhiteSpace() == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 0 parameter not {_pass.m_parameters.Count}"));
            return false;
        }

        //return MAGameFlow.m_audioPlaying == false;
       if(NGTutorial.m_lastAudioPlayed.IsNullOrWhiteSpace())
       {
           return true;
       }
       if (GameManager.Me.m_state.m_tutorialTimer < 0f)
           GameManager.Me.m_state.m_tutorialTimer = Time.time + 25f;
       var result = Time.time > GameManager.Me.m_state.m_tutorialTimer;
       if (result)
           GameManager.Me.m_state.m_tutorialTimer = -1;

       string clipName = NGTutorialManager.Me.DecodeAudio(NGTutorial.m_lastAudioPlayed);
       string clip = "PlaySound_" + clipName;
       var isPlaying = AudioClipManager.Me.IsPlayingVO(clip);
       return !isPlaying;
    }
    [TutorialTrigger] static public bool WaitForEndCotterPlotPlacement(KnackParser _pass)
    {
        return MAGameInterface.InCottersPlotPlacement == false;
    }
    [TutorialTrigger] static public bool WaitForForever(KnackParser _pass)
    {
        return false;
    }
    [TutorialTrigger] static public bool WaitForMoney(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return false;
        }

        if (int.TryParse(_pass.m_parameters[0].m_control, out int money) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} syntax of money {_pass.m_parameters[0].m_control}"));
            return false;
        }
        
        return NGPlayer.Me.m_cash.Balance >= money;
    }
    [TutorialTrigger] static public bool WaitForPartAdded(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes one parameters not {_pass.m_parameters.Count}"));
            return false;
        }     
        var result = false;
        switch (_pass.m_parameters[0].m_control)
        {
            case CNPartOnDesignTable:
                ProcessPart(_pass, (flag) =>
                {
                    result = flag;
                });
                break;
            case CNCount:
                if (int.TryParse(_pass.m_parameters[0].m_id, out var count) == false)
                {
                    Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} bad syntax on Count {_pass.m_parameters[0].m_id}"));
                    return false;
                }
                if (DesignTableManager.IsDesignInPlace == false || DesignTableManager.Me.IsInProductMode == false)
                    return false;
                var design = DesignTableManager.Me.GetDesignOnTable();
                result = design.HasDesign && design.GetNumBlocks() >= count;
                break;
        }

        return result;
    }

    [TutorialTrigger] static public bool WaitForTime(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes one parameters not {_pass.m_parameters.Count}"));
            return false;
        }     
        if (_pass.m_parameters[0].m_control.TryFloatInv(out var time) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} invalid Time of {_pass.m_parameters[0].m_control}"));
            return false;
        }
        if (GameManager.Me.m_state.m_tutorialTimer < 0f)
            GameManager.Me.m_state.m_tutorialTimer = Time.time + time;
        var result = Time.time > GameManager.Me.m_state.m_tutorialTimer;
        if (result)
            GameManager.Me.m_state.m_tutorialTimer = -1;

        return result;
    }

    [TutorialTrigger] static public bool WaitForOrderTo(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes one parameters not {_pass.m_parameters.Count}"));
            return false;
        }

        var result = false;
        switch (_pass.m_parameters[0].m_control)
        {
            case CNBuilding:
                ProcessBuilding(_pass, (building) =>
                {
                    var cl = building.ComponentLists<MABuildingComponentFactory>();
                    if (cl.Count == 0)
                    {
                       Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} building '{building.name}' has no component that accepts orders"));
                       result = false;
                    }

                    foreach (var c in cl)
                    {
                        var order = c.GetOrder();
                        result |= (order != null);
                    }
                });
                break;
        }

        return result;
    }

    [TutorialTrigger]
    static public bool WaitForElement(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes one parameters not {_pass.m_parameters.Count}"));
            return false;
        }

        switch (_pass.m_parameters[0].m_control)
        {
            case CNGUI:
                var result = false;
                ProcessGUI(_pass, (gui) =>
                {
                    result = true;
                },_showError:false);
                return result;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such GUI Control"));
                return false;
        }

        return false;
    }

    [TutorialTrigger] static public bool WaitForClick(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes one parameters not {_pass.m_parameters.Count}"));
            return false;
        }

        switch (_pass.m_parameters[0].m_control)
        {
            case "left":
            case "0":
                return Input.GetMouseButtonDown(0);
            case "right":
            case "1":
                return Input.GetMouseButtonDown(1);
            case "middle":
            case "2":
                return Input.GetMouseButtonDown(2);
            case "any":
                return Input.GetMouseButtonDown(0) | Input.GetMouseButtonDown(1) | Input.GetMouseButtonDown(2);
            case CNGUI:
                var result = false;
                ProcessGUI(_pass, (gui) =>
                {
                    result = gui.m_mouseDown;
                });
                return result;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such GUI Control"));
                return false;
        }

        return false;
    }

    [TutorialTrigger] static public bool WaitForBlockPlaced(KnackParser _pass)
    {
        if (_pass.m_parameters.Count > 2 || _pass.m_parameters.Count == 0)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} incorrect number of parameters {_pass.m_parameters.Count}"));
            return false;
        }
        MAComponentInfo toComponent; 

        switch (_pass.m_parameters[0].m_control)
        {
            case CNComponent:
                toComponent = MAComponentInfo.GetInfo(_pass.m_parameters[0].m_id);
                if (toComponent == null)
                {
                    Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} {_pass.m_parameters[0].m_control} {_pass.m_parameters[0].m_id} not valid component"));
                    return false;
                }
                break;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} {_pass.m_parameters[0].m_control} not implemented"));
                return false;
                break;
        }

        if (_pass.m_parameters.Count == 1)
        {
            foreach (var b in NGManager.Me.m_maBuildings)
            {
                var comps = b.ComponentList(toComponent.m_classType);
                if (comps.Count > 0)
                    return true;
            }
            return false;
        }
        MABuilding building = null;
        switch (_pass.m_parameters[1].m_control)
        {
            case CNBuildingComponent:
                GameObject pad = null;
                ProcessBuildingTile(_pass, (maBuilding, thing) =>
                {
                    building = maBuilding;
                    pad = thing;
                }, 1);
                if (pad == null)
                {
                    Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} {_pass.m_parameters[1].m_modifier} PAD not valid"));
                    return false;
                }

                if (building == null)
                {
                    Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} {_pass.m_parameters[1].m_modifier} not valid building"));
                    return false;
                }

                foreach (var cl in building.ComponentList(toComponent.m_classType))
                {
                    float distance = Vector3.Distance(cl.transform.localPosition, pad.transform.localPosition);
                    var inHand = DesignTableManager.Me.GrabList.Contains(cl.gameObject);
                    if (distance < 4 && inHand == false)
                        return true;
                }
                break;
            case CNBuildingTile:
                var result = false;
                ProcessBuildingTile(_pass, (building, thing) =>
                {
                    var cList = building.ComponentList(toComponent.m_classType);
                    if (thing == null)
                    {
                        if (cList != null && cList.Count > 0)
                            result = true;
                        return;
                    } 
                    foreach (var cl in cList)
                    {
                        var distance = Vector3.Distance(cl.transform.localPosition, thing.transform.localPosition);
                        var inHand = DesignTableManager.Me.GrabList?.Contains(cl.gameObject) ?? false;
                        if (distance < 4 && inHand == false)
                            result = true;
                    }
                }, 1);
                return result;
        }
 
        return false;
    }

    [TutorialTrigger]
    static public bool WaitForWaggonTrain(KnackParser _pass)
    {
        if(_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return false;
        }
        var result =MAWaggonTrainManager.Me.HasWaggonTrainSequenceArrived(_pass.m_parameters[0].m_control);
        return result;
    }

    [TutorialTrigger] static public bool WaitForWorkerCount(KnackParser _pass)
    {
        if (_pass.m_parameters[0].m_control.TryFloatInv(out var count) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} invalid Count of {_pass.m_parameters[0].m_control}"));
            return false;
        }

        int workerCount = 0;
        foreach (var w in NGManager.Me.m_MAWorkerList)
        {
            if (w.Home != null)
                workerCount++;
        }
        var result = workerCount >= count;
        return result;
    }

    [TutorialTrigger] static public bool WaitForBuildingFull(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameters not {_pass.m_parameters.Count}"));
            return false;
        }

        var result = false;
        switch (_pass.m_parameters[0].m_control)
        {
            case CNBuilding:
                ProcessBuildingComponent(_pass, (building, component) =>
                {
                    result = building.GetStockSpace(component.GetType()) == 0;
                });
                break;
            default:
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such control"));
                break;
        }
        return result;
    }

    [TutorialTrigger] static public bool WaitForStockCount(KnackParser _pass)
    {
        var result = false;
        if (_pass.m_parameters.Count == 2)
        {
            if (_pass.m_parameters[1].m_control.TryFloatInv(out var count) == false)
            {
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} invalid Count of {_pass.m_parameters[1].m_control}"));
                return false;
            }

            int stockCount = 0;
            var resource = NGCarriableResource.GetInfo(_pass.m_parameters[0].m_control);
            if (resource == null)
            {
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} invalid Stock of {_pass.m_parameters[0].m_control}"));
                return false;
            }
            foreach (var b in NGManager.Me.m_maBuildings)
            {
     
                foreach (var so in b.ComponentLists<MABuildingComponentStockOut>())
                {
                    var cso = so as MABuildingComponentStockOut;
                    var si = cso.m_stock.Find(resource);
                    if (si != null)
                        stockCount += si.m_stock;
                }
            }
            result = stockCount >= count;
            return result;           
        }
        else if (_pass.m_parameters.Count == 3)
        {
            switch (_pass.m_parameters[0].m_control)
            {
                case CNBuilding:
                    ProcessBuilding(_pass, (building) =>
                    {
                        if (_pass.m_parameters[2].m_control.TryFloatInv(out var count) == false)
                        {
                            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} invalid Count of {_pass.m_parameters[2].m_control}"));
                            return;
                        }
                        var resource = NGCarriableResource.GetInfo(_pass.m_parameters[1].m_control);
                        if (resource == null)
                        {
                            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} invalid Stock of {_pass.m_parameters[1].m_control}"));
                            return;
                        }

                        var stockCount = 0;
                        switch (_pass.m_parameters[0].m_modifier.ToLower())
                        {
                            case "stockin":
                                var si = building.GetStock<MABuildingComponentStockIn>();
                                stockCount+=si.GetStock(resource);
                                si = building.GetStock<MABuildingComponentStockHotspot>();
                                stockCount+=si.GetStock(resource);
                                break;
                            case "stockout":
                                var so = building.GetStock<MABuildingComponentStockOut>();
                                stockCount+=so.GetStock(resource);
                                break;
                            case "stockany":
                                var sa = building.GetStock<MABuildingComponentStockIn>();
                                stockCount+=sa.GetStock(resource);
                                sa = building.GetStock<MABuildingComponentStockHotspot>();
                                stockCount+=sa.GetStock(resource);
                                sa = building.GetStock<MABuildingComponentStockOut>();
                                stockCount+=sa.GetStock(resource);
                                sa = building.GetStock<MABuildingComponentStables>();
                                stockCount+=sa.GetStock(resource);
                                break;
                            default:
                                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} invalid Stock ID of {_pass.m_parameters[1].m_id}"));
                                return;
                        }
                        result = stockCount >= count;
                    });
                    break;
            }
            
        }
        else
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 2 or 3 parameters not {_pass.m_parameters.Count}"));
            return false;         
        }

        return result;
    }
    [TutorialTrigger] static public bool WaitForDesignTablePart(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameter not {_pass.m_parameters.Count}"));
            return false;
        }

        if (int.TryParse(_pass.m_parameters[0].m_control, out var numPartsNeeded))
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameter not {_pass.m_parameters.Count}"));
        }
        string design = DesignTableManager.Me.GetDesign();
        if (string.IsNullOrEmpty(design) || design == "0|0|")
        {
            return false;
        }
        var bits = design.Split('|');
        int numParts = int.Parse(bits[0]);

        for (int i = 0; i < numParts; ++i)
        {
            var bits2 = bits[1 + i].Split('@');
            var entry = bits2[0];
            var info = NGBlockInfo.GetInfo(entry);
            if (info.m_isBasePart && numPartsNeeded == 0)
                return true;
        }

        var result = numParts == numPartsNeeded;
        return result;
    }
    [TutorialTrigger] static public bool WaitForAudioComplete(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameter not {_pass.m_parameters.Count}"));
            return false;
        }
        if (GameManager.Me.m_state.m_tutorialTimer < 0f)
            GameManager.Me.m_state.m_tutorialTimer = Time.time + 25f;
        var result = Time.time > GameManager.Me.m_state.m_tutorialTimer;
        if (result)
            GameManager.Me.m_state.m_tutorialTimer = -1;

        string clipName = NGTutorialManager.Me.DecodeAudio(_pass.m_parameters[0].m_control);
        string clip = "PlaySound_" + clipName;
        return !AudioClipManager.Me.IsPlayingVO(clip);
    }
    [TutorialTrigger] static public bool WaitForAudioCompleteOrClick(KnackParser _pass)
    {
        if (_pass.m_parameters.Count != 1)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function} takes 1 parameter not {_pass.m_parameters.Count}"));
            return false;
        }
        if (GameManager.Me.m_state.m_tutorialTimer < 0f)
            GameManager.Me.m_state.m_tutorialTimer = Time.time + 2f;
        var result = Time.time > GameManager.Me.m_state.m_tutorialTimer;
        if (result)
            GameManager.Me.m_state.m_tutorialTimer = -1;

        string clipName = NGTutorialManager.Me.DecodeAudio(_pass.m_parameters[0].m_control);
        string clip = "PlaySound_" + clipName;
        var clipAudio = AudioClipManager.Me.GetClip(clip);
        if (clipAudio != null)
        {
            result |= !AudioClipManager.Me.IsPlayingVO(clip);
        }
        bool hasUserClicked = Input.GetMouseButtonDown(0) | Input.GetMouseButtonDown(1) | Input.GetMouseButtonDown(2);
        return result || hasUserClicked;
    }
    [TutorialTrigger] static public bool WaitForAudioTriggered(KnackParser _pass)
    {
        if (GameManager.Me.m_state.m_tutorialTimer < 0f)
            GameManager.Me.m_state.m_tutorialTimer = Time.time + 10.0f;
        var result = Time.time > GameManager.Me.m_state.m_tutorialTimer;
        if (result)
            GameManager.Me.m_state.m_tutorialTimer = -1;

        string clipName = NGTutorialManager.Me.DecodeAudio(_pass.m_parameters[0].m_control);
        string clip = "PlaySound_" + clipName;
        return AudioClipManager.Me.IsPlayingVO(clip) || result;
    }

    #endregion Triggers
    #region Processes

    static void ProcessPart(KnackParser _pass, Action<bool> _action, int _index = 0)
    {
        var blockID = _pass.m_parameters[_index].m_id;
        var info = NGBlockInfo.GetInfo(blockID);
        if (info == null)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}> no such block as '{_pass.m_parameters[_index].m_id}'"));
            return;
        }

        if (DesignTableManager.IsDesignInPlace == false || DesignTableManager.Me.IsInProductMode == false)
            return;
        var design = DesignTableManager.Me.GetDesignOnTable();
        var parts = DesignUtilities.GetDesignData(design.m_design);
        var part = parts.Find(o => o.m_blockID.Equals(blockID));
        _action(part != null);
    }
    static void ProcessBlock(KnackParser _pass, Action<Block> _action, int _index = 0)
    {
        var info = NGBlockInfo.GetInfo(_pass.m_parameters[_index].m_id);
        if (info == null)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}> no such block as '{_pass.m_parameters[_index].m_id}'"));
            return;
        }

        foreach (var b in NGManager.Me.m_maBuildings)
        {
            var building = b as MABuilding;
            var blocks = b.m_blockHolder.GetComponentsInChildren<Block>();
            var blockFound = blocks.Find(o => o.m_blockInfoID.Equals(_pass.m_parameters[_index].m_id));
            if (blockFound)
            {
                _action(blockFound);
            }
        }
    }

    static void ProcessComponent(KnackParser _pass, Action<GameObject> _action, int _index = 0)
    {
        var info = MAComponentInfo.GetInfo(_pass.m_parameters[_index].m_id);
        if (info == null)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_id} no such component as '{_pass.m_parameters[_index].m_id}'"));
            return;
        }
        foreach (var bc in NGManager.Me.m_maBuildings)
        {
            var dbcl = bc.ComponentList(info.m_classType);
            foreach (var dd in dbcl)
                _action(dd.gameObject);
        }

        var blocks = DesignTableManager.Me.m_wildBlockHolder.GetComponentsInChildren<Block>();
        foreach (var b in blocks)
        {
            var bInfo = b.BlockInfo;
            if (bInfo == null) continue;
            var components = bInfo.GetComponentInfos();
            if (components.Contains(info))
                _action(b.gameObject);
        }
    }
    static public void ProcessBuilding(KnackParser _pass, Action<MABuilding> _action, int _index = 0)
    {
        ProcessBuilding(_pass.m_parameters[_index].m_id, _action);
    }
    
    static public bool ProcessBuilding(string _id, Action<MABuilding> _action)
    {
        var building = FindBuilding(_id);
        if(building == null)
        {
            Debug.LogError(DebugColor($"No building with ID '{_id}'"));
            return false;
        }

        if (building)
            _action(building);
        return true;
    }
    static public void ProcessRegion(KnackParser _pass, Action<ReactDistrictTable> _action, int _index = 0)
    {
        var region = ReactDistrictTable.GetInfo(_pass.m_parameters[_index].m_id);
        if(region == null)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} No region with ID '{_pass.m_parameters[_index].m_id}'"));
            return;
        }
        _action(region);
    }


    static void ProcessBuildingComponent(KnackParser _pass, Action<MABuilding, MABuildingComponentBase> _action, int _index = 0)
    {
        ProcessBuilding(_pass, (building) =>
        {
            var info = MAComponentInfo.GetInfo(_pass.m_parameters[_index].m_modifier);
            if (info == null)
            {
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} No such component as '{_pass.m_parameters[_index].m_modifier}'"));
                return;
            }     
            if (info.m_classType == null)
            {
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} is and unreconised control'"));
                return;
            }

            var comps = building.GetComponentsInChildren(info.m_classType);
            if (comps == null || comps.Length == 0)
            {
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} { _pass.m_parameters[_index].m_modifier} is and Not found"));
                return;
            }
            _action(building, comps[0] as MABuildingComponentBase);
        });
    }
    
    
    static void ProcessBuildingTile(KnackParser _pass, Action<MABuilding, GameObject> _action, int _index = 0)
    {
        var building = FindBuilding(_pass.m_parameters[_index].m_id);
        if(building == null)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} No building with ID '{_pass.m_parameters[_index].m_id}'"));
            return;
        }
        if (int.TryParse(_pass.m_parameters[_index].m_modifier, out var padNum) == false)
        {
            if (_pass.m_parameters[_index].m_modifier.Equals("any", StringComparison.OrdinalIgnoreCase))
            {
                _action(building, null);
                return;
            }
            var mInfo = MAComponentInfo.GetInfo(_pass.m_parameters[_index].m_modifier);
            if (mInfo == null)
            {
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} Parse failed Pad Num/Component invalid '{_pass.m_parameters[_index].m_modifier}'"));
                return;
            }

            var bc = building.ComponentList(mInfo.m_classType);
            if (bc.Count == 0)
            {
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} Component not in building '{_pass.m_parameters[_index].m_modifier}'"));
                return;
            }

            _action(building, bc[0].gameObject);
            return;
        }
              
     
        var baseBlock = building.Visuals.GetComponentInChildren<BaseBlock>();
        if (baseBlock == null )
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} No base blocks with ID '{_pass.m_parameters[_index].m_id}'"));
            return;
        }

        var pads = baseBlock.GetComponentsInChildren<MeshRenderer>(true);
        if (padNum < 0 || padNum >= pads.Length)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} Pad number {padNum} out of range there are '{pads.Length}'"));
            return;
        }

        _action(building, pads[padNum].gameObject);
    }
 
    static void ProcessWorker(KnackParser _pass, Action<MAWorker> _action, int _index = 0)
    {
        if (int.TryParse(_pass.m_parameters[_index].m_id, out var id) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} Parse failed on '{_pass.m_parameters[_index].m_id}'"));
            return;
        }
        var worker = NGManager.Me.m_MAWorkerList.Find(o => o.m_ID.Equals(id));
        if (worker == null)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} No worker with ID '{id}'"));
            return;
        }
        _action(worker);
    }
    static void ProcessCarryable(KnackParser _pass, Action<ReactPickup> _action, int _index = 0)
    {
        var pickups = GlobalData.Me.m_pickupsHolder.GetComponentsInChildren<ReactPickup>();
        var count = 0;
        ReactPickup found = null;
        int.TryParse(_pass.m_parameters[_index].m_modifier, out var lookForCount);
        foreach (var p in pickups)
        {
            if (p.Name.Equals(_pass.m_parameters[_index].m_id))
            {
                if (count++ >= lookForCount)
                {
                    found = p;
                    break;
                }
            }
        }
        if (found == null)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} No carryable with ID '{_pass.m_parameters[_index].m_id}' modifier '{_pass.m_parameters[_index].m_modifier}'"));
            return;
        }
        _action(found);
    }
    static void ProcessPos(KnackParser _pass, Action<Vector3> _action, int _index = 0)
    {

        var coordSplit = _pass.m_parameters[_index].m_id.Split(';');
        if (coordSplit.Length != 2 && coordSplit.Length != 3)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} has bad coords of {_pass.m_parameters[_index].m_id}'"));
            return;
        }

        float x = 0f;
        float y = 0f;       
        float z = 0f;
        if (coordSplit[0].TryFloatInv(out x) == false)
        {
            Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} has bad x coord of {coordSplit[_index]}'"));
            return;
        }
        if (coordSplit.Length == 2)
        {
            if (coordSplit[1].TryFloatInv(out z) == false)
            {
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} has bad z coord of {coordSplit[1]}'"));
                return;
            }
        }
        else
        {
            if (coordSplit[1].TryFloatInv(out y) == false)
            {
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} has bad y coord of {coordSplit[1]}'"));
                return;
            }
            if (coordSplit[2].TryFloatInv(out z) == false)
            {
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[_index].m_control} has bad y coord of {coordSplit[1]}'"));
                return;
            }
        }

        _action(new Vector3(x, y, z).GroundPosition());
    }

    static void ProcessGUI(KnackParser _pass, Action<MAGUIBase> _action, int _index = 0, bool _showError = true)
    {
        var gui = MAGUIBase.Find(_pass.m_parameters[0].m_id);
        if (gui == null)
        {
            if(_showError)
                Debug.LogError(DebugColor($"[{_pass.m_callingStep}]{_pass.m_function}>{_pass.m_parameters[0].m_control} No such GUI"));
            return;
        }

        _action(gui);
    }
    #endregion Processes
    #region Support

    static public bool m_debugError = false;
    static public string DebugColor(string _what)
    {
        m_debugError = true;
        return $"<color=#fefc78>{_what}</color>";
    }
    static void StartFollowCamera(GameObject _followWhat, float _distance)
    {
        m_triggers |= Triggers.FollowCamera;
        NGManager.Me.StartCoroutine(FollowCameraCorroutine(_followWhat, _distance));
    }

    static IEnumerator FollowCameraCorroutine(GameObject _object, float _distance)
    {
        while (m_triggers.HasFlag(Triggers.FollowCamera))
        {
            yield return null;
            if(m_triggers.HasFlag(Triggers.MoveCamera) == false)
                StartCameraTransition(_object.transform.position, _distance);
        }
    }
    static public void StartCameraTransition(Vector3 _pos, float _distance)
    {
        GameManager.Me.CameraTransition(_pos, 1.0f, _distance, CameraTransitionComplete);
        m_triggers |= Triggers.MoveCamera;
    }
    static void CameraTransitionComplete()
    {
        m_triggers &= ~Triggers.MoveCamera; 
    }
    static public IEnumerator Screenshot(int _index)
    {
        yield return null;
        Utility.ScreenGrab(1, _index);
        yield return null;
        m_triggers &= ~Triggers.Screenshot;
    }

    static public MABuilding FindBuilding(string _name)
    {
        MABuilding building = null;
        if (int.TryParse(_name, out var bid))
        {
            building = NGManager.Me.m_maBuildings.Find(o => o.m_linkUID.Equals(bid));
        }
        else
        {
            building = NGManager.Me.m_maBuildings.Find(o => o.Name.Equals(_name, StringComparison.OrdinalIgnoreCase));
            if(building == null)
                building = NGManager.Me.m_maBuildings.Find(o => o.name.Contains(_name));
        }

        return building;
    }
    static public Vector3 GetPos(string _pos)
    {
        var coordSplit = _pos.Split(';');
        if (coordSplit.Length != 2)
        {
            Debug.LogError(DebugColor($"Bad coords of {_pos}'"));
            return Vector3.zero;
        }

        if (coordSplit[0].TryFloatInv(out var x) == false)
        {
            Debug.LogError(DebugColor($"Bad x coord of {coordSplit[0]}'"));
            return Vector3.zero;
        }
        if (coordSplit[1].TryFloatInv(out var z) == false)
        {
            Debug.LogError(DebugColor($"Bad y coord of {coordSplit[1]}'"));
            return Vector3.zero;
        }
        return new Vector3(x, 0f, z).GroundPosition();
    }
    #endregion Support
   
}
#endif
