using System;
using System.Collections.Generic;
using MACharacterStates;


#if UNITY_EDITOR
using UnityEditor;
#endif
public class MAWerewolf : MACreatureBase
{
	//[<PERSON><PERSON>("MAWerewolf")]
	public override Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary() { return MAWerewolfStateLibrary.StateLibrary; }
	override public string HumanoidType => "Werewolf";
	public override EDeathType DeathCountType => EDeathType.Enemy;
	
	
	protected override void Awake()
	{
		base.Awake();
	}

	public override void DestroyedTarget()
	{
		base.DestroyedTarget();
	}

	public override void OnJumpEnded()
	{
		var jumpState = CharacterUpdateState as JumpWall;
		if (jumpState != null)
			jumpState.OnJumpEnded();
	}
}


#if UNITY_EDITOR
[CustomEditor(typeof(MAWerewolf))]
public class MAWerewolfEditor : MACreatureBaseEditor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
	}
}
#endif