using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Serialization;

public class MAMeshClickDetector : MonoBehaviour
{
    public List<Collider> m_colliders;
    public bool m_raycastAll = false;
    public UnityEvent m_onHit;   // Create a UnityEvent to hold the function you want to call

    void Update()
    {
        if (m_colliders.Count == 0 || m_onHit == null) return;

        if (Input.GetMouseButtonDown(0)) 
        {
            Ray ray = Camera.main.ScreenPointToRay(Input.mousePosition);

            if (m_raycastAll)
            {
                RaycastHit[] hits = Physics.RaycastAll(ray);

                foreach (RaycastHit hit in hits)
                {             
                    if (m_colliders.Contains(hit.collider))
                    {
                        m_onHit.Invoke();
                    }
                }
            }
            else
            {
                RaycastHit hit;

                if (Physics.Raycast(ray, out hit))
                {
                    if (m_colliders.Contains(hit.collider))
                    {
                        m_onHit.Invoke();
                    }
                }
            }
        }
    }

    public void AddCollider(Collider _collider)
    {
        if(!m_colliders.Contains(_collider))
        {
            m_colliders.Add(_collider);
        }
    }
}
