using System;
using System.Collections.Generic;
using UnityEngine;

public class IgnoreAllCollisionsExcept : MonoBehaviour
{
    [ReadOnlyInspector][SerializeField]
    private Collider[] m_localColliders = null;
    [ReadOnlyInspector][SerializeField]
    private HashSet<Collider> m_collidersIgnored = new HashSet<Collider>();
    [ReadOnlyInspector][SerializeField]
    private HashSet<Collider> m_exceptObjects = null;
   
    private Action<Collider> m_collidedWithExpectedObject = null;
    public Action m_onTerrainCollisionStart = null;
    public Action m_onTerrainCollisionEnd = null;

    private void Awake()
    {
        m_localColliders = GetComponentsInChildren<Collider>(true);
    }

    private void OnEnable()
    {
        /*keep here so component can be enabled/disabled */
    }

    private void OnDisable()
    {
        Reset();/*keep here so component can be enabled/disabled */
    }
    
    private void Update() { /*keep here so component can be enabled/disabled */}

    public void ExceptObjectsWithComponents(Collider[] _objectsToExpect, System.Action<Collider> _onExpectedCollision)
    {
        m_exceptObjects = new HashSet<Collider>(_objectsToExpect);
        m_collidedWithExpectedObject = _onExpectedCollision;

        foreach(var localCollider in m_localColliders)
        {
            foreach(var otherCollider in m_exceptObjects)
            {
                Physics.IgnoreCollision(localCollider, otherCollider, false);
            }
        }
    }

    public void Reset()
    {
        foreach(var localCollider in m_localColliders)
        {
            foreach(var otherCollider in m_collidersIgnored)
            {
                Physics.IgnoreCollision(localCollider, otherCollider, false);
            }
        }
        m_collidersIgnored.Clear();
    }
    
    private void HandleCollision(Collision _other)
    {
        if(enabled == false)
            return;

        if(m_exceptObjects != null && m_exceptObjects.Contains(_other.collider))
        {
            m_collidedWithExpectedObject?.Invoke(_other.collider);
            m_exceptObjects = null;
            m_collidedWithExpectedObject = null;
            return;
        }
        
        foreach(var localCollider in m_localColliders)
        {
            Debug.Log($"{GetType().Name} - IGNORING collision with: {_other.gameObject.name}");
            Physics.IgnoreCollision(localCollider, _other.collider, true);
            m_collidersIgnored.Add(_other.collider);
        }
    }
    
    private void OnCollisionEnter(Collision _other)
    {
        if(_other.collider is TerrainCollider)
        {
            m_onTerrainCollisionStart?.Invoke();
            return;
        }
        HandleCollision(_other);
    }

    private void OnCollisionStay(Collision other)
    {
        HandleCollision(other);
    }

    private void OnCollisionExit(Collision _other)
    {
        if(_other.collider is TerrainCollider)
        {
            m_onTerrainCollisionEnd?.Invoke();
            return;
        }
        HandleCollision(_other);
    }
}
