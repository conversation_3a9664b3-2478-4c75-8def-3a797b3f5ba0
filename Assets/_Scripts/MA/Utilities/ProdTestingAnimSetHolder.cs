using UnityEngine;

public class ProdTestingAnimSetHolder : MonoBeh<PERSON>our
{
	// RW-12-SEP-25: This object holds an MAAnimationSet so Product Testing anims can be diverged for different rigs.
	// This is necessary because the Product Testing lab doesn't use proper characters so AnimSet can't be specified as it 
	// usually would. Possible alternative is to make the testing lab use MADisplayCharacters.
	public MAAnimationSet m_animSet;
}
