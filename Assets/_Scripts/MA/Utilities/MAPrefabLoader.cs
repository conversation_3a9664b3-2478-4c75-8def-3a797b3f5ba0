using System.Collections.Generic;
using UnityEngine;

public class MAPrefabLoader : MonoSingleton<MAPrefabLoader>
{
    public bool m_isEnabled = true;
    public List<string> m_preloadedPrefabPaths = new List<string>();
    private Dictionary<string, GameObject> m_prefabs = new Dictionary<string, GameObject>();

    override protected void Awake()
    {
        base.Awake();

        if (m_isEnabled)
        {
            LoadPrefabs();
        }
    }

    private void LoadPrefabs()
    {
        foreach (string path in m_preloadedPrefabPaths)
        {
            LoadPrefab(path);
        }
    }

    public GameObject LoadPrefab(string _path)
    {
        if (!m_isEnabled || !m_prefabs.ContainsKey(_path))
        {
            var prefab = Resources.Load(_path) as GameObject;

            if (prefab == null)
            {
                MAParser.ParserError($"No such prefab: '{_path}'");
                return null;
            }

            if(!m_isEnabled)
            {
                return prefab;
            }

            m_prefabs[_path] = prefab;
        }

        return m_prefabs[_path];
    }

    public T LoadPrefab<T>(string _path)
    {
        GameObject prefab = LoadPrefab(_path);

        if(prefab != null)
        {
            return prefab.GetComponent<T>();
        }

        return default(T);
    }

    public void ClearPrefabs()
    {
        m_prefabs.Clear();
    }
}
