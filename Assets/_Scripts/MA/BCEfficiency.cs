#if false
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCEfficiency : BCBase
{
    [KnackField] public float m_workerContributionMultiplier = 1f;
    [KnackField] public float m_tapContributionMultiplier = 1f;

    public float GetWorkerContributionMultiplier() => m_workerContributionMultiplier;
    public float GetTapContributionMultiplier() => m_tapContributionMultiplier;
}
#endif