using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MABedroomInfo : MonoBehaviour
{
    public TMP_Text m_title;
    public Transform m_bedroomHolder;
    public GameObject m_bedroomPrefab;

    void Activate(MABuilding _building)
    {
        
    }

    public static MABedroomInfo Create(MABuilding _building, MABedroomInfo _prefab, Transform _holder)
    {
        var go = Instantiate(_prefab.gameObject, _holder);
        var bi = go.GetComponent<MABedroomInfo>();
        bi.Activate(_building);
        return bi;
    }
}
