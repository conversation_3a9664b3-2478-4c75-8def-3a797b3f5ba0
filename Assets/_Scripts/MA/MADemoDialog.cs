using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MADemoDialog : MonoSingleton<MADemoDialog>
{
    public TMP_Text m_title;
    public TMP_Text m_pauseText;
    public Image m_frescoImage;
    
    public Slider m_slider;
    public Toggle m_todFreezeToggle;
    public TMPro.TextMeshProUGUI m_todText;
    public string m_2x2Plot = "MA 2x2 Plot";
    public string m_hero = "Giant";
    public string m_femaleHero = "GiantFemale";
    public string m_maleHero = "Giant";
    public string m_zombie = "Zombie";
    public int m_zombieCount = 100;
    public int m_cash = 1000;
    public int m_maxNumTourists = 100;
    public float m_touristSpawnInterval = 1;
    public TMP_Text m_healthBarButtonText = null;

    private void Update()
    {
        m_title.text = $"Demo: {Time.time.ToTimeString()}";
        if (NGManager.Me.m_pauseCharacters)
            m_pauseText.text = $"UnPause Characters";
        else
            m_pauseText.text = $"Pause Characters";
        SetTODControls();
    }

    public void ClickedDesignWeapon()
    {
        MABuilding.Debug_AssignOrder("weaponorder:0010");
    }
    
    public void ClickedToggleBlight()
    {
        CameraRenderSettings.Me._BlightEnabled = !CameraRenderSettings.Me._BlightEnabled;
        if (CameraRenderSettings.Me._BlightEnabled)
            BlightManager.Me.RestartAll(true);
    }
 
    public void Clicked2x2Plot()
    {
        var gift = NGBusinessGift.s_gifts.Find(o => o.m_name.Equals(m_2x2Plot));
        if (gift != null)
        {
            NGBusinessGiftsPanel.CreateOrCall(new List<NGBusinessGift>() { gift });
        }
    }
    public void ClickedSpawnHorde()
    {
        MAParser.SpawnHorde(MACreatureInfo.GetInfo(m_zombie),Utility.GetRaycastCameraPosition(), 10, m_zombieCount);
    }
    public void ClickedSpawnHero()
    {
        var info = MACreatureInfo.GetInfo(m_hero);
        MACreatureBase.Create(info, Utility.GetRaycastCameraPosition());
    }
    public void ClickedUnlockWeapons()
    {
        var gift = NGBusinessGift.s_gifts.Find(o => o.m_name.Equals("UnlockProductLine (Weapons)"));
        if (gift != null)
        {
            NGBusinessGiftsPanel.CreateOrCall(new List<NGBusinessGift>() { gift });
        }   
    }
    public void ClickedTourists()
    {
        if(MATouristManager.Me == null) return;
        MATouristManager.Me.m_maxNumTourists = m_maxNumTourists;
        MATouristManager.Me.m_touristSpawnInterval = m_touristSpawnInterval;
    }

    public void ClickedCash()
    {
        NGPlayer.Me.m_cash.Add(CurrencyContainer.TransactionType.Earned, m_cash, "Cheat");
    }

    public void ClickedPaths()
    {
        if (GameManager.Me.IsInTitleScreen())
        {
            GameManager.IsPathSeed = true;
        }
        else
        {
            RoadManager.Me.m_inPathEdit = true;
            //Me.m_isDebugEditMode = string.IsNullOrEmpty(_s) == false;
        }
    }
    public void ClickedPauseCharacters()
    {
        NGManager.Me.m_pauseCharacters = !NGManager.Me.m_pauseCharacters;
    }

    public void ClickedShowFresco()
    {
        var fresco = NGManager.Me.m_centreScreenHolder.Find("Fresco");
        if(fresco)
            fresco.gameObject.SetActive(true);
    }

    public void ClickZombie()
    {
        MAParser.SpawnHorde(MACreatureInfo.GetInfo(m_zombie),Utility.GetRaycastCameraPosition(), 1, 1);

    }
    public void ClickedSlider()
    {
        UpdateTODOverride();
    }

    public void ClickedTODFreeze()
    {
        UpdateTODOverride();
    }

    public void ClickedRefreshWheatFields()
    {
        MAParser.RegrowAllResources();
    }
    private void UpdateTODOverride()
    {
        bool isFrozen = m_todFreezeToggle.isOn;
        float tod = m_slider.value;
        if (DayNight.Me != null)
            DayNight.Me.SetTimeOfDayFraction(tod, isFrozen);
    }

    private void SetTODControls()
    {
        if (DayNight.Me)
        {
            var (time, freeze) = DayNight.Me.GetTimeOfDay();
            m_slider.SetValueWithoutNotify(time);
            m_todFreezeToggle.SetIsOnWithoutNotify(freeze);
            m_todText.text = DayNight.Me.CurrentTimeString;
        }
    }

    public void ClickedResearch()
    {
        MAResearchManagerUI.Create(NGManager.Me.m_centreScreenHolder);
    }

    public void ClickedPosess()
    {
        var info = MACreatureInfo.GetInfo(m_maleHero);
        var creature = MACharacterBase.Create(info, Utility.GetRaycastCameraPosition());
        GameManager.Me.PossessObject(creature, true);
    }
    public void ClickedHandPowers()
    {
    if(PlayerHandManager.Me.AnyPowerActive)
        PlayerHandManager.DemoUnlockHandPowers("*:0");
    else
        PlayerHandManager.DemoUnlockHandPowers("*");
    }

    public void ClickedToDusk()
    {
        DayNight.Me.StartBlend(.95f, 3f);
    }

    [NonSerialized]
    public bool alwaysShowingHealthBars = false;
    public void ClickedHealthBarToggle()
    {
        alwaysShowingHealthBars = !alwaysShowingHealthBars;
        if(m_healthBarButtonText != null)
            m_healthBarButtonText.text = alwaysShowingHealthBars ? "Hide Health" : "Show Health";
    }
    
    public void ClickedGiveBigOrder()
    {
        var orderInfo = new string[] {"","Special:0020"};
        MAParser.LoadDispatchOrder("food", MAParserSupport.ConvertOrderInfo(orderInfo) as MAOrderInfo);
    }

    public void ClickedClose()
    {
        DestroyMe();
    }

    public override void DestroyMe()
    {
        MAParserFlowGUI.Me.DestroyMe();
        base.DestroyMe();
    }
    protected override void _OnDestroy()
    {
        base._OnDestroy();
    }

    public void Activate()
    {
        SetTODControls();
        MAParserFlowGUI.Create();
    }
    public static MADemoDialog Create()
    {
        var prefab = Resources.Load<MADemoDialog>("_Prefabs/Dialogs/MADemoDialog");
        var instance = Instantiate(prefab, NGManager.Me.m_middleLeftGUIHolder);
        //instance.transform.SetAsFirstSibling();
        instance.Activate();
        return instance;
    }
}
