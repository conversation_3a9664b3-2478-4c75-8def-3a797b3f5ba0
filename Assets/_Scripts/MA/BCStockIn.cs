using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(BCStockIn))]
public class NGBCStockInInspector : MonoEditorDebug.MonoBehaviourEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        var myScript = (BCStockIn) target;
        if (GUILayout.Button($"Clear Stock"))
        {
            myScript.ClearAllStock();
        }
    }
}
#endif

public class BCStockIn  : BCStockBase
{
    public bool m_ignoreWorkerDelivery = false;

    public override void UpdateInternal(BuildingComponentsState _state)
    {
        MoveStockFromBuilding();
    }
    
    private void MoveStockFromBuilding()
    {
        if (GetStockSpace() <= 0) return;

        var stock = m_building.ConsumeStockForStockIn();
        if (stock != null)
        {
            CreateStock(stock);
        }
    }
    
    override public float GetResourceRequirement(NGCarriableResource _resource, bool _isTest = false)
    {
        if(m_ignoreWorkerDelivery && _isTest)
        {
            return 0f;
        }

        if(CanAcceptResource(_resource, _isTest) == false)
            return 0f;
            
        //return Mathf.Min(1f, m_stock.Find(_resource).m_neededToProduce);
        return 0.1f;
    }

    override protected CraneHandler Crane => UseCrane ? m_building?.InputCrane : null;
}
