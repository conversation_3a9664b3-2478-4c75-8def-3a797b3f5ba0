using System.Collections;
using UnityEngine;

public class MAGallows : MADecorationActionBase
{
    public enum DropState
    {
        None = 0,
        Triggered,
        Dropping,
        Hanging,
        Released
    }

    public Animator m_animator;
    public Collider m_trapdoorCollider;
    public AkEventHolder m_trapdoorOpenAudioEvent;
    public AkEventHolder m_trapdoorCloseAudioEvent;
    public AkEventHolder m_neckSnapAudioEvent;
    public GameObject m_detachedNoose;
    public MANoose m_attachedNoose;

    private MAWorker m_condemned;
    private int m_openID;
    private bool m_isOpen = false;
    private Coroutine m_dropSequenceCoroutine;
    private GameState_Gallows m_derrivedState;
    private DropState m_dropState = DropState.None;

    protected override void Awake()
    {
        base.Awake();
        m_openID = Animator.StringToHash("Open");
    }

    public void SetCondemned(MAWorker _condemned)
    {
        OpenTrapdoor(false);
        m_condemned = _condemned;
        m_condemned.SetState(NGMovingObject.STATE.MA_ANIMATOR_CONTROLLED);
        m_condemned.SetCanPickup(false);
        UpdateState();
    }

    public void ReleaseCondemned()
    {
        if (m_condemned != null)
        {
            m_condemned.m_nav.Unpause();
            m_condemned = null;
            SetInNoose(null);
            UpdateState();
        }
    }

    public bool IsOccupied(MAWorker _worker = null)
    {
        if (_worker != null)
        {
            return m_condemned == _worker;
        }
        else
        {
            return m_condemned != null;
        }
    }

    public DropState GetDropState()
    {
        return m_dropState;
    }

    public void Interact()
    {
        if (!IsOccupied())
        {
            OpenTrapdoor(!m_isOpen);
        }
        else
        {
            StartDropSequence();
        }
    }

    public void OpenTrapdoor(bool _open)
    {
        if (_open != m_isOpen)
        {
            m_isOpen = _open;

            if (m_animator != null)
            {
                m_animator.SetBool(m_openID, m_isOpen);
            }

            AkEventHolder audioEvent = m_isOpen ? m_trapdoorOpenAudioEvent : m_trapdoorCloseAudioEvent;

            if (audioEvent != null)
            {
                audioEvent.Play(gameObject);
            }

            m_trapdoorCollider.enabled = !m_isOpen;    
        }
    }

    public override float GetDropScore(NGMovingObject _object, out SpecialHandlingAction _action, SpecialHandlingAction _restrictedAction)
    {
        _action = HasSpecialHandling(_object, _restrictedAction);

        if (_action != null)
        {
            return 1f;
        }

        return 0f;
    }

    override public SpecialHandlingAction HasSpecialHandling(NGMovingObject _o, SpecialHandlingAction _restrictedAction)
    {
        MAWorker worker = null;

        if (AllowWorkers())
        {
            worker = _o as MAWorker;
        }
        else
        {
            worker = _o as MAFlowCharacter;
        }

        if (worker != null)
        {
            return SpecialHandlingAction.AssignToGallows;
        }

        return null;
    }

    override public bool ApplySpecialDropHandling(NGMovingObject _o, SpecialHandlingAction _restrictedAction)
    {
        MAWorker worker = null;

        if (AllowWorkers())
        {
            worker = _o as MAWorker;
        }
        else
        {
            worker = _o as MAFlowCharacter;
        }

        if (worker != null)
        {
            if (_restrictedAction == SpecialHandlingAction.AssignToGallows)
            {
                SetCondemned(worker);
                return true;
            }
        }
        return false;
    }

    override public void OnDropTargetComplete()
    {
        m_condemned.SetState(NGMovingObject.STATE.MA_IN_GALLOWS);
        SetInNoose(m_condemned); 
    }

    private void StartDropSequence()
    {
        if (m_dropSequenceCoroutine == null)
        {
            m_dropSequenceCoroutine = StartCoroutine(Co_DropSequence());
        }
    }

    private IEnumerator Co_DropSequence()
    {
        m_dropState = DropState.Triggered;

        yield return new WaitForSeconds(0.6f);
        OpenTrapdoor(true);

        m_condemned.RigidBody.AddForce(0.0f, -10.0f, 0.0f, ForceMode.VelocityChange);
        m_dropState = DropState.Dropping;

        Vector3 prevPos = m_condemned.transform.position;
        float yThreshold = m_condemned.transform.position.y - 2.5f;

        while (m_condemned.transform.position.y > yThreshold)
        {
            prevPos = m_condemned.transform.position;
            yield return null;
        }

        float t = (yThreshold - prevPos.y) / (m_condemned.transform.position.y - prevPos.y);
        m_condemned.transform.position = Vector3.Lerp(prevPos, m_condemned.transform.position, t);

        m_condemned.RigidBody.linearVelocity = Vector3.zero;
        m_condemned.RigidBody.angularVelocity = Vector3.zero;

        KillCondemned();

        m_dropState = DropState.Hanging;

        yield return new WaitForSeconds(60.0f);

        ReleaseCondemned();

        m_dropState = DropState.Released;

        yield return new WaitForSeconds(2.0f);

        OpenTrapdoor(false);

        m_dropState = DropState.None;

        m_dropSequenceCoroutine = null;
        yield return null;
    }


    private void SetInNoose(MAWorker _condemned)
    {
        if(_condemned != null)
        {
            m_detachedNoose.SetActive(false);
            m_attachedNoose.gameObject.SetActive(true);
            m_attachedNoose.Attach(_condemned);     
        }
        else
        {
            m_attachedNoose.Detach();
            m_attachedNoose.gameObject.SetActive(false);
            m_detachedNoose.SetActive(true);        
        }
    }

    private void KillCondemned()
    {
        if (m_condemned != null)
        {
            if (m_neckSnapAudioEvent != null)
            {
                m_neckSnapAudioEvent.Play(m_condemned.gameObject);
            }

            m_condemned.SetState(NGMovingObject.STATE.MA_DEAD);
            m_condemned.Health = 0.0f;
            m_condemned.m_nav.Pause(true, true);
            m_condemned.DeallocateJobAndHome();

            //RagdollHelper.StartRagdoll(m_condemned.gameObject, Vector3.up * -3.0f);
        }
    }

    override public MADropTargetInfo GetDropTargetInfo(GameObject _source)
    {
        MADropTargetInfo dropTargetInfo = new MADropTargetInfo();

        var ba = GetComponentInChildren<BezierAnchor>();

        if (ba != null)
        {
            dropTargetInfo.m_position = ba.transform.position;
            dropTargetInfo.m_rotation = ba.transform.rotation;
        }
        else
        {
            dropTargetInfo.m_position = transform.position;
            dropTargetInfo.m_rotation = transform.rotation;
        }

        dropTargetInfo.m_landAnim = "WorkerLanding";

        return dropTargetInfo;
    }

    public override NGDecoration SetDecorationData(GameState_Deccoration _data)
    {
        base.SetDecorationData(_data);

        if (_data.m_derrivedState == null)
        {
            _data.m_derrivedState = new GameState_Gallows();
        }

        m_derrivedState = _data.m_derrivedState as GameState_Gallows;

        if (m_derrivedState != null)
        {
            m_condemned = NGManager.Me.m_MACharacterList.Find(o => o.m_ID.Equals(m_derrivedState.m_condemnedID)) as MAWorker;
        }

        return this;
    }

    protected override void UpdateState()
    {
        base.UpdateState();

        if (m_derrivedState != null)
        {
            m_derrivedState.m_condemnedID = m_condemned != null ? m_condemned.m_ID : -1;
        }
    }
}
