using System;
using System.Collections.Generic;
using UnityEngine;

public class MACameraOverlayHolder : MonoSingleton<MACameraOverlayHolder>
{
    private class CheckShowOverlay
    {
        public MACameraOverlay m_overlay;
        public Func<bool> m_shouldShowFunc;

        public CheckShowOverlay(MACameraOverlay _overlay, Func<bool> _shouldShowFunc)
        {
            m_overlay = _overlay;
            m_shouldShowFunc = _shouldShowFunc;
        }
    }

    private Dictionary<MACameraOverlay.OverlayType, MACameraOverlay> m_overlays = new Dictionary<MACameraOverlay.OverlayType, MACameraOverlay>();
    private Dictionary<MACameraOverlay.OverlayType, CheckShowOverlay> m_checkShowOverlays = new Dictionary<MACameraOverlay.OverlayType, CheckShowOverlay>();

    override protected void Awake()
    {
        base.Awake();

        var overlays = GetComponentsInChildren<MACameraOverlay>(true);

        foreach(var overlay in overlays)
        {
            m_overlays[overlay.m_type] = overlay;
        }

    }

    private void Update()
    {
        foreach(var check in m_checkShowOverlays)
        {
            bool shouldShow = check.Value.m_shouldShowFunc();
            var overlay = check.Value.m_overlay;

            if (shouldShow != overlay.IsShowing)
            {
                if(shouldShow)
                {
                    overlay.Show();
                }
                else
                {
                    overlay.Hide();
                }
            }
        }
    }

    public bool Show(MACameraOverlay.OverlayType _type, Func<bool> _shouldShowFunc = null)
    {
        if(m_overlays.ContainsKey(_type))
        {
            if(_shouldShowFunc != null)
            {
                m_checkShowOverlays[_type] = new CheckShowOverlay(m_overlays[_type], _shouldShowFunc);
            }
            else
            {
                m_overlays[_type].Show();
            }

            return true;
        }

        return false;
    }

    public bool Hide(MACameraOverlay.OverlayType _type)
    {
        if (m_overlays.ContainsKey(_type))
        {
            if(m_checkShowOverlays.ContainsKey(_type))
            {
                m_checkShowOverlays.Remove(_type);
            }

            m_overlays[_type].Hide();
            return true;
        }

        return false;
    }

    public bool IsShowing(MACameraOverlay.OverlayType _type)
    {
        if (m_overlays.ContainsKey(_type))
        {
            return m_overlays[_type].IsShowing;
        }

        return false;
    }
}
