using UnityEngine;

public class MAQuestCreateScroll : MonoBehaviour
{
    public bool m_beenClicked = false;
    void Activate(MAQuestBase.QuestInteractType _questInteractType)
    {
        m_beenClicked = false;
        GetComponent<MAQuestScroll>().m_onClick = () =>
        {
            m_beenClicked = true;
        };
        GetComponent<MAQuestScroll>().m_questInteractType = _questInteractType;
    }
    public void DestroyMe()
    {
        Destroy(gameObject);
    }

    public static MAQuestCreateScroll Create(Transform _parent, float _offset, MAQuestBase.QuestInteractType _questInteractType = MAQuestBase.QuestInteractType.GodMode)
    {
        var prefab = Resources.Load<MAQuestCreateScroll>("_Prefabs/Dialogs/MAQuestCreateScroll"); 
        var instance = Instantiate(prefab, _parent);
        instance.transform.localPosition = new Vector3(0, _offset, 0);
        instance.Activate(_questInteractType);
        return instance;
    }
}
