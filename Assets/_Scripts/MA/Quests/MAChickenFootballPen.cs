using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class MAChickenFootballPen : MonoBehaviour
{
    private List<MAChicken> m_chickenList = new List<MAChicken>();
    public int ChickenCount => m_chickenList.Count;
    
    public AkEventHolder m_chickenReturnedSFX = new();
    
    private void OnTriggerEnter(Collider _collider)
    {
        MAChicken chicken = _collider.GetComponent<MAChicken>();

        if (chicken != null)
        {
            if(ChickenCount < MAQuestChickenFootball.m_chickenToReturn)
                m_chickenReturnedSFX.Play(gameObject);
            AddChickenToPen(chicken);
        }
    }

    private void AddChickenToPen(MAChicken _chicken)
    {
        if (!m_chickenList.Contains(_chicken))
        {
            m_chickenList.Add(_chicken);
        }
    }

    public bool IsChickenPenned(MAChicken _chicken)
    {
        return m_chickenList.Contains(_chicken);
    }
   
}
