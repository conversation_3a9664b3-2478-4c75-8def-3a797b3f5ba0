using UnityEngine;

public class MAQuestStoneVFX : MonoBehaviour
{
    public Animator m_animator;
    public GameObject m_ring;
    public GameObject m_ringTop;
    public GameObject m_ringBottom;
    public GameObject m_ringNoise;
    public GameObject m_ringNoiseTop;
    public GameObject m_ringNoiseBottom;
    
    private int m_VFXID;
    private int m_NoiseVFXID;
    
    protected void Awake() 
    {
        m_VFXID = Animator.StringToHash("VFX");
        m_NoiseVFXID = Animator.StringToHash("Noise_VFX");
    }

    public void Play()
    {
        m_ring.SetActive(true);
        m_ringTop.SetActive(true);
        m_ringBottom.SetActive(true);
        m_animator.SetTrigger(m_VFXID);
    }

    public void PlayNoise()
    {
        m_ringNoise.SetActive(true);
        m_ringNoiseTop.SetActive(true);
        m_ringNoiseBottom.SetActive(true);
        m_animator.SetTrigger(m_NoiseVFXID);
    }

    public void End()
    {
        m_ring.SetActive(false);
        m_ringTop.SetActive(false);
        m_ringBottom.SetActive(false);
        m_ringNoise.SetActive(false);
        m_ringNoiseTop.SetActive(false);
        m_ringNoiseBottom.SetActive(false);
    }
}
