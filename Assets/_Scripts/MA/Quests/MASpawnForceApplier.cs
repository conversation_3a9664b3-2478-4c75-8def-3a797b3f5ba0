using UnityEngine;

public class MASpawnForceApplier : MonoBehaviour
{
    public float m_forcePerDamage = 25.0f;
    public float m_explosionRadius = 5.0f;
    public float m_upwardModifier = 0.5f;
    public ForceMode m_forceMode = ForceMode.Impulse;

    public void ApplySpawnForce(float _damageDone, Vector3 _damageOrigin)
    {
        float force = _damageDone * m_forcePerDamage;

        var rbs = GetComponentsInChildren<Rigidbody>();

        foreach(var rb in rbs)
        {
            rb.AddExplosionForce(force, _damageOrigin, m_explosionRadius, m_upwardModifier, m_forceMode);
        }
    }
}
