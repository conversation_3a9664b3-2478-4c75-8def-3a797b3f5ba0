using System;
using UnityEngine;

public class MAQuestScroll : DragBase, ICharacterObjectInteract
{
    public Action m_onClick;
    public GameObject[] m_questScrolls = new GameObject[(int) MAQuestBase.QuestStatus.Last];

    public MAQuestBase.QuestStatus m_status = MAQuestBase.QuestStatus.Active;
    public MAQuestBase.QuestInteractType m_questInteractType = MAQuestBase.QuestInteractType.GodMode;
    private bool m_possesMode = false;

    public float AutoInteractTime => 0;
    
    void Start()
    {
        SetQuestStatus(m_status);
        
        Vector3 parentScale = transform.parent.localScale;

        // Calculate the inverse of the parent's scale
        Vector3 inverseScale = new Vector3(
            1 / parentScale.x,
            1 / parentScale.y,
            1 / parentScale.z
        );

        // Apply the inverse scale to the child object
        transform.localScale = Vector3.Scale(transform.localScale, inverseScale);
    }
    
    public void SetQuestStatus(MAQuestBase.QuestStatus _status)
    {
        int scrollIndex = (int)_status;

        if (!CanInteract())
        {
            scrollIndex = (int)MAQuestBase.QuestStatus.Invalid;
        }

        for (int i = 0; i < m_questScrolls.Length; i++)
        {
            bool allowedVisible = _status == MAQuestBase.QuestStatus.Active; // Hide completed scrolls for now
            bool activate = i == scrollIndex && allowedVisible;
            m_questScrolls[i].SetActive(activate);
        }
        m_status = _status;
    }
    
    public void DestroyMe()
    {
        Destroy(gameObject);
    }

    void Update()
    {
        if(GameManager.Me.IsPossessing != m_possesMode)
        {
            SetQuestStatus(m_status);
            m_possesMode = GameManager.Me.IsPossessing;
        }
    }   
    
    public override void OnClick()
    {        
        if (DistrictManager.Me.IsWithinDistrictBounds(transform.position) == false) return;
        if (!CanInteract()) return;
        if (GameManager.Me.CameraExceededMaxInteractionDistance() || m_onClick == null) return;
        if (IsLeftButton)
        {
            DoAction();
        }
    }
    
    public void Activate(Action _onClick, MAQuestBase.QuestStatus _status, MAQuestBase.QuestInteractType _questInteractType)
    { 
        m_onClick = _onClick;
        m_questInteractType = _questInteractType;
        SetQuestStatus(_status);
    }

    public string InteractType => null;

    public bool CanInteract(NGMovingObject _chr)
    {
        return CanInteract() && m_status == MAQuestBase.QuestStatus.Active && _chr is not MAAnimal && _chr is not MACreatureBase;
    }

    public const string InteractLabel = "Accept";
    
    public string GetInteractLabel(NGMovingObject _chr)
    {
        return InteractLabel;
    }

    public void DoInteract(NGMovingObject _chr)
    {
        DoAction();
    }

    protected virtual void DoAction()
    {   
        if (m_onClick != null)
        {
            NGManager.Me.m_clickedQuestScrollAudio?.Play(gameObject);
            m_onClick();
        }
    }
    
    public bool RunInteractSequence(System.Collections.Generic.List<FollowChild> _chr) => false;
    public void EnableInteractionTriggers(bool _b) {}
    private bool CanInteract()
    {
        bool canInteract = false;

        switch (m_questInteractType)
        {
            case MAQuestBase.QuestInteractType.GodMode:
                canInteract = GameManager.Me.IsPossessing == false;
                break;
            case MAQuestBase.QuestInteractType.Possessed:
                canInteract = GameManager.Me.IsPossessing == true;
                break;
            case MAQuestBase.QuestInteractType.Both:
                canInteract = true;
                break;
            default:
                break;
        }

        return canInteract;
    }
    
    public static MAQuestScroll Create(Transform _parent, MAQuestBase.QuestStatus _status, Action _onClick, MAQuestBase.QuestInteractType _questInteractType)
    {
        var prefab = Resources.Load<MAQuestScroll>("_Prefabs/Dialogs/MAQuestScroll"); 
        var instance = Instantiate(prefab, _parent);
        instance.Activate(_onClick, _status, _questInteractType);
        return instance;
    }
    public static MAQuestScroll Create(string _name, Transform _parent, MAQuestBase.QuestStatus _status, Action _onClick, MAQuestBase.QuestInteractType _questInteractType)
    {
        var prefab = Resources.Load<MAQuestScroll>($"_Prefabs/Dialogs/{_name}"); 
        var instance = Instantiate(prefab, _parent);
        instance.Activate(_onClick, _status, _questInteractType);
        return instance;
    }
}
