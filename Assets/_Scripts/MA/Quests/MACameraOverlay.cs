using UnityEngine;
using UnityEngine.Events;

public class MACameraOverlay : MonoBehaviour
{
    public enum OverlayType
    {
        PirateTreasureMap
    }

    public OverlayType m_type;
    [Range(0f, 1f)] public float m_xScreenPos = 0.5f;
    [Range(0f, 1f)] public float m_yScreenPos = 0.5f;
    public float m_depth = 2.0f;
    public float m_leftMargin = 100.0f;
    public float m_rightMargin = 100.0f;
    public float m_bottomMargin = 100.0f;
    public float m_topMargin = 100.0f;

    public UnityEvent m_onShow;
    public UnityEvent m_onHide;

    public bool IsShowing { get => m_isShowing; }
    private bool m_isShowing = false;

    private float m_lastScreenWidth = 0f;
    private float m_lastScreenHeight = 0f;

    public void Show()
    {
        m_onShow?.Invoke();
        m_isShowing = true;
    }

    public void Hide()
    {
        m_onHide?.Invoke();
        m_isShowing = false;
    }

    private void Awake()
    {
        UpdatePosition();
    }

    private void Update()
    {
        if(Screen.width != m_lastScreenWidth || Screen.height != m_lastScreenHeight)
        {
            UpdatePosition();          
        }
    }

    private void UpdatePosition()
    {
        float screenX = Screen.width * m_xScreenPos;
        float screenY = Screen.height * m_yScreenPos;

        screenX = Mathf.Clamp(screenX, m_leftMargin, Screen.width - m_rightMargin);
        screenY = Mathf.Clamp(screenY, m_bottomMargin, Screen.height - m_topMargin);

        Vector3 screenPos = new Vector3(screenX, screenY, m_depth);

        Vector3 worldPos = Camera.main.ScreenToWorldPoint(screenPos);

        transform.localPosition = Camera.main.transform.InverseTransformPoint(worldPos);

        m_lastScreenWidth = Screen.width;
        m_lastScreenHeight = Screen.height;
    }
}
