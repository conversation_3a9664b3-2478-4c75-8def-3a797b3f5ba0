using UnityEngine;

public class MATableSaw : MADecorationActionBase
{
    public Animator m_animator;
    public Transform m_followTransform;

    private MAWorker m_prisoner;
    private int m_clickID;
    private GameState_TableSaw m_derrivedState;

    public bool m_shacklesReleased = false;
    public bool m_hasBeenReleased = false;

    protected override void Awake()
    {
        base.Awake();
        m_clickID = Animator.StringToHash("Click");
    }

    public bool IsOccupied(MAWorker _worker = null)
    {
        if (_worker != null)
        {
            return m_prisoner == _worker;
        }
        else
        {
            return m_prisoner != null;
        }
    }

    public void SetPrisoner(MAWorker _prisoner)
    {
        m_prisoner = _prisoner;
        m_prisoner.SetState(NGMovingObject.STATE.MA_ANIMATOR_CONTROLLED);
        m_prisoner.SetCanPickup(false);
        UpdateState();
    }

    public void ReleasePrisoner()
    {
        if (m_prisoner != null)
        {
            //m_prisoner.SetDefaultAction(); // KW: this causes janky ground level bug when transitioning to MA_DECIDE_WHAT_TO_DO state
            m_prisoner.m_nav.Unpause();
            m_prisoner.m_nav.PopPause("HeldByPlayer");
            m_prisoner = null;
            UpdateState();
        }
    }

    public void Interact()
    {
        if (!IsOccupied())
        {
            TriggerAnimation();
        }     
    }

    public void OnStartSlidingAnimEvent()
    {
    }

    public void OnBreakShacklesEvent()
    {
        if (IsOccupied())
        {
            MAQuestJudgement quest = MAQuestManager.Me.GetQuestBase<MAQuestJudgement>() as MAQuestJudgement;

            if (quest != null)
            {
                quest.BreakShackles();
            }

            // Used to trigger SFX
            m_shacklesReleased = true;
        }
    }

    public void OnStopFollowingTransformEvent()
    {
        if (IsOccupied())
        {
            m_prisoner.SetState(NGMovingObject.STATE.MA_ANIMATOR_CONTROLLED);
        }
    }

    private void TriggerAnimation()
    {
        if (m_animator != null)
        {
            m_animator.SetTrigger(m_clickID);
        }
    }

    public override float GetDropScore(NGMovingObject _object, out SpecialHandlingAction _action, SpecialHandlingAction _restrictedAction)
    {
        _action = HasSpecialHandling(_object, _restrictedAction);

        if (_action != null && !IsOccupied())
        {
            return 1f;
        }

        return 0f;
    }

    override public SpecialHandlingAction HasSpecialHandling(NGMovingObject _o, SpecialHandlingAction _restrictedAction)
    {
        MAWorker worker = null;

        if (AllowWorkers())
        {
            worker = _o as MAWorker;
        }
        else
        {
            worker = _o as MAFlowCharacter;
        }

        if (worker != null)
        {
            return SpecialHandlingAction.AssignToTableSaw;
        }

        return null;
    }

    override public bool ApplySpecialDropHandling(NGMovingObject _o, SpecialHandlingAction _restrictedAction)
    {
        MAWorker worker = null;

        if (AllowWorkers())
        {
            worker = _o as MAWorker;
        }
        else
        {
            worker = _o as MAFlowCharacter;
        }

        if (worker != null)
        {
            if (_restrictedAction == SpecialHandlingAction.AssignToTableSaw)
            {
                SetPrisoner(worker);
                return true;
            }
        }
        return false;
    }

    override public void OnDropTargetComplete()
    {
        m_prisoner.SetStateFollowTransform(m_followTransform);
        TriggerAnimation();

        HeadTracker ht = m_prisoner.GetComponent<HeadTracker>();

        if (ht != null)
        {
            ht.Stop();
        }

        m_prisoner.PlaySingleAnimation("Worker_Tablesaw_ShacklesCut", cb1);

        void cb1(bool _interrupted)
        {
            m_hasBeenReleased = true;

            if (ht != null)
            {
                ht.Resume();
            }

            ReleasePrisoner();
        }
    }

    override public MADropTargetInfo GetDropTargetInfo(GameObject _source)
    {
        MADropTargetInfo dropTargetInfo = new MADropTargetInfo();

        var ba = GetComponentInChildren<BezierAnchor>();

        if (ba != null)
        {
            dropTargetInfo.m_position = ba.transform.position;
            dropTargetInfo.m_rotation = ba.transform.rotation;
        }
        else
        {
            dropTargetInfo.m_position = transform.position;
            dropTargetInfo.m_rotation = transform.rotation;
        }

        dropTargetInfo.m_landAnim = "";

        return dropTargetInfo;
    }

    public override NGDecoration SetDecorationData(GameState_Deccoration _data)
    {
        base.SetDecorationData(_data);

        if (_data.m_derrivedState == null)
        {
            _data.m_derrivedState = new GameState_PilloryStocks();
        }

        m_derrivedState = _data.m_derrivedState as GameState_TableSaw;

        if (m_derrivedState != null)
        {
            m_prisoner = NGManager.Me.m_MACharacterList.Find(o => o.m_ID.Equals(m_derrivedState.m_prisonerID)) as MAWorker;
        }

        return this;
    }

    protected override void UpdateState()
    {
        base.UpdateState();

        if (m_derrivedState != null)
        {
            m_derrivedState.m_prisonerID = m_prisoner != null ? m_prisoner.m_ID : -1;
        }
    }
}

