using UnityEngine;

public class MAShackles : MonoBehaviour
{
    public GameObject m_leftCuff, m_rightCuff, m_chain;
    public ConfigurableJoint m_middleLink;
    private MACharacterBase m_shackled;

    public void Attach(MACharacterBase _shackled)
    {
        m_shackled = _shackled;

        if (m_shackled != null)
        {
            Transform leftFoot = m_shackled.transform.FindChildRecursiveByName("mixamorig:LeftFoot");
            Transform rightFoot = m_shackled.transform.FindChildRecursiveByName("mixamorig:RightFoot");

            if (leftFoot != null && rightFoot != null)
            {
                m_shackled.CharacterDestroyed += OnCharacterDestroyed;
                m_leftCuff.transform.SetParent(leftFoot, false);
                m_rightCuff.transform.SetParent(rightFoot, false);
                m_chain.transform.position = (m_leftCuff.transform.position + m_rightCuff.transform.position) * 0.5f;

                m_leftCuff.SetActive(true);
                m_rightCuff.SetActive(true);
                m_chain.SetActive(true);

                // Fix up connection of left cuff to first chain link
                ConfigurableJoint lccj = m_leftCuff.GetComponent<ConfigurableJoint>();
                Rigidbody cb = lccj.connectedBody;
                lccj.connectedBody = null;
                lccj.connectedBody = cb;
                lccj.connectedAnchor = new Vector3(0.04f, 0.0f, 0.0f);

                Repair();
            }
        }
    }

    public void Detach()
    {
        if(m_shackled != null)
        {
            m_shackled.CharacterDestroyed -= OnCharacterDestroyed;
        }

        m_leftCuff.transform.SetParent(transform, false);
        m_rightCuff.transform.SetParent(transform, false);     

        m_leftCuff.SetActive(false);
        m_rightCuff.SetActive(false);
        m_chain.SetActive(false);
    }

    public void Break()
    {
        m_middleLink.xMotion = ConfigurableJointMotion.Free;
        m_middleLink.yMotion = ConfigurableJointMotion.Free;
        m_middleLink.zMotion = ConfigurableJointMotion.Free;
    }

    private void Repair()
    {
        m_middleLink.xMotion = ConfigurableJointMotion.Locked;
        m_middleLink.yMotion = ConfigurableJointMotion.Locked;
        m_middleLink.zMotion = ConfigurableJointMotion.Locked;
    }

    public void OnCharacterDestroyed(MACharacterBase sender)
    {
        if (m_shackled == sender)
        {
            Detach();
        }
    }
}
