using UnityEditor.Rendering;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;

public class MAQuestStone : ReactPickupPersistent, IPointerDownHandler, IImpactDataSupplier
{
    public AkEventHolder m_stoneAudioEvent = new ();
    public AkEventHolder m_lockedAudioEvent = new ();
    public AkEventHolder m_stonePlacedEvent = new();
    public int m_stoneNum;
    public MAQuestStonePad m_targetPad;
    public float m_flySpeed = 3f;
    public MAQuestStonePad m_onPad;
    public bool m_isLocked = false;
    public bool m_shouldReset = true;
    private const int MAX_VALID_STONES = 8;

    public float m_maxDistanceToShowLine = 25f;

    public Animator m_animator;
    private int m_wobbleID;
    private int m_shakeID;
    private int m_lockedID;
    private int m_idleID;
    private bool m_playAnimSounds = true;
    private bool m_wasEverInDistrictBounds = false;
    public bool WasEverInDistrictBounds { get => m_wasEverInDistrictBounds; set => m_wasEverInDistrictBounds = value; }

    public MAQuestStoneVFX m_stoneVFX;
    public ParticleSystem m_droppedVFX;

    private Vector3 m_resetPosition;
    private Quaternion m_resetRotation;
    private float m_checkResetPositionTime = 0.0f;
    private const float CHECK_RESET_POSITION_INTERVAL = 5.0f;
    private const float MAX_RESET_DISTANCE = 100.0f;
    private const float MAX_GROUND_PENETRATION = 1.0f;
    private const float GROUND_RESET_HEIGHT = 1.0f;

    protected override void Awake() 
    {
        base.Awake();
        m_wobbleID = Animator.StringToHash("Wobble");
        m_shakeID = Animator.StringToHash("Shake");
        m_lockedID = Animator.StringToHash("Locked");
        m_idleID = Animator.StringToHash("Idle");
        m_resetPosition = transform.position;
        m_resetRotation = transform.rotation;
    }

    protected override void Start()
    {
        base.Start();

        if(m_isLocked)
        {
            if (!m_onPad)
            {
                var quest = MAQuestManager.Me.GetQuestBase<MaQuest6Stones>() as MaQuest6Stones;

                if (quest)
                {
                    m_onPad = quest.m_stonePads.Find(x => x.m_padNum == m_stoneNum);
                }
            }           
        }

        if (m_onPad)
        {
            SetOnPad(m_onPad, false);
        }
    }
    override protected void Update()
    {
        base.Update();
        if (m_targetPad)
        {
            transform.position = Vector3.MoveTowards(transform.position, m_targetPad.transform.position, m_flySpeed * Time.deltaTime);

       //     transform.position = Vector3.Lerp(transform.position, m_targetPad.transform.position, m_flySpeed*Time.deltaTime);
            transform.localRotation = Quaternion.Lerp(transform.localRotation, m_targetPad.transform.localRotation, m_flySpeed*Time.deltaTime);
            if((transform.position-m_targetPad.transform.position).magnitude < 0.1f)
            {
                SetOnPad(m_targetPad, true);
                m_targetPad = null;
                // PlaySound();
            }
        }
        else if(m_shouldReset)
        {
            CheckResetPosition();
        }
    }

    private void CheckResetPosition()
    {
        if (m_onPad == null && !IsBeingDragged)
        {
            m_checkResetPositionTime += Time.deltaTime;

            if (m_checkResetPositionTime > CHECK_RESET_POSITION_INTERVAL)
            {
                if((transform.position - m_resetPosition).sqrMagnitude > MAX_RESET_DISTANCE * MAX_RESET_DISTANCE)
                {
                    transform.position = m_resetPosition;
                    transform.rotation = m_resetRotation;
                }
                else 
                {
                    float groundHeight = GameManager.Me.HeightAtPoint(transform.position);

                    if (transform.position.y < groundHeight - MAX_GROUND_PENETRATION)
                    {
                        Vector3 resetPosition = transform.position;
                        resetPosition.y = groundHeight + GROUND_RESET_HEIGHT;
                        transform.position = resetPosition;
                    }
                }

                m_checkResetPositionTime = 0.0f;
            }
        }
        else
        {
            m_checkResetPositionTime = 0.0f;
        }
    }
    
    public void OnClick()
    {
        if (m_onPad == null) return;
    }

    public override void OnDrop(Vector3 _pos, GameObject _target, GameObject _source, Vector3 _smoothedDragMove, bool _undo, SpecialHandlingAction _action)
    {
        if (_target == null)
        {
            base.OnDrop(_pos, _target, _source, _smoothedDragMove, _undo, _action);
            return;            
        }
        var pad = _target.GetComponent<MAQuestStonePad>();
        if (pad == null) return;
        m_targetPad = pad;

    }

    public override void PrepareForHolding()
    {
        base.PrepareForHolding();
        if (m_onPad)
        {
            m_onPad.m_stone = null;
            m_onPad = null;
        }
        m_animator.SetTrigger(m_idleID);
        m_stoneVFX.End();
        canCreateExplosion = true;
    }
    override public GameObject GetBestTarget(int _inputId, GameObject _obj, Vector3 _pos, out SpecialHandlingAction _action)
    {   
        _action = null;
        var quest = MAQuestManager.Me.GetQuestBase<MaQuest6Stones>() as MaQuest6Stones;
        if (quest == null) return null;
        var bestDistance = float.MaxValue;
        GameObject bestObject = null;
        // var currentStonePos = InputUtilities.GetCursorToTerrainPos(_inputId);
        foreach (var s in quest.m_stonePads)
        {
            if(s.m_stone != null) continue;
            // var distance = (currentStonePos - s.transform.position).xzMagnitude();
            var distance = (transform.position - s.transform.position).xzMagnitude();
            if(distance >= m_maxDistanceToShowLine) continue;
            if(distance < bestDistance)
            {
                bestDistance = distance;
                bestObject = s.gameObject;
            }
        }
        return bestObject;
    }

    public void PlayAnim(bool _playSound, bool _correctNote)
    {
        m_playAnimSounds = _playSound;

        if (!IsDecoy() && _correctNote)   
        {
            m_animator.SetTrigger(m_wobbleID);
            m_stoneVFX.Play();
        }
        else
        {
            m_animator.SetTrigger(m_shakeID);
            m_stoneVFX.PlayNoise();
        }
    }

    public void PlayLockedAnim()
    {
        m_animator.SetTrigger(m_lockedID);
        
    }
    public void PlayLockedSound()
    {
        m_lockedAudioEvent.Play(gameObject);
    }

    public void OnSoundAnimEvent()
    {
        if (m_playAnimSounds)
        {
            PlaySound();
        }
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        if(GameManager.Me.CameraExceededMaxInteractionDistance()) return;
        
        if (m_onPad != null)
        {
            PlayAnim(true, m_onPad.m_padNum == m_stoneNum);
        }
        else
        {
            PlaySound();
        }
    }

    void PlaySound()
    {
        m_stoneAudioEvent.Play(gameObject);
    }

    public void SetOnPad(MAQuestStonePad pad, bool _playSound)
    {
        if (pad)
        {
            m_onPad = pad;
            transform.position = m_onPad.transform.position;
            transform.eulerAngles = m_onPad.transform.eulerAngles;
            m_onPad.m_stone = this;

            var rb = GetComponentInChildren<Rigidbody>();
            if (rb)
            {
                rb.isKinematic = true;
            }

            if (_playSound)
            {
                m_stonePlacedEvent.Play(gameObject);
                m_droppedVFX.Play();
            }
        }
    }

    public bool IsDecoy()
    {
        return m_stoneNum >= MAX_VALID_STONES;
    }

    private bool canCreateExplosion = false;
    public float m_throwExplosionSpeedBase = 6.0f;
    public float m_throwExplosionRadiusBase = 4.0f;
    public float m_throwExplosionPowerBase = 40.0f;
    public float m_throwExplosionDamageBase = 50.0f;
    public AkEventHolder m_throwExplosionImpactSound;

    override protected void OnCollisionEnter(Collision _collision)
    {
        base.OnCollisionEnter(_collision);

        int layer = _collision.gameObject.layer;
        if (!canCreateExplosion) return;
        if (layer == LayerMask.NameToLayer("RagDollMovingObject")) return;

        if ((layer == LayerMask.NameToLayer("Terrain")) || (layer == LayerMask.NameToLayer("Roads")))
            canCreateExplosion = false;

        var maxSpeedSqrd = 0f;
        var maxSpeedPoint = Vector3.zero;
        foreach (var contact in _collision.contacts)
        {
            var thisBody = contact.thisCollider.attachedRigidbody;
            if (thisBody == null) continue;
            var thisSpeedSqrd = thisBody.linearVelocity.sqrMagnitude;
            if (thisSpeedSqrd > maxSpeedSqrd)
            {
                maxSpeedSqrd = thisSpeedSqrd;
                maxSpeedPoint = contact.point;
            }
        }
        const string c_debugLabel = null;//"DecorationExplosion";
        float explosionBaseSpeed = m_throwExplosionSpeedBase;
        float explosionMinSpeed = explosionBaseSpeed * .2f;
        float explosionRadius = m_throwExplosionRadiusBase;
        float explosionForceAtBase = m_throwExplosionPowerBase;
        if (maxSpeedSqrd > explosionMinSpeed * explosionMinSpeed)
        {
            var explosionMultiplier = Mathf.Sqrt(maxSpeedSqrd) / explosionBaseSpeed;
            var explosionForce = explosionForceAtBase * explosionMultiplier;
            explosionRadius *= Mathf.Sqrt(explosionMultiplier);//.5f + .5f * explosionMultiplier; 
            MAPowerEffectBase.CreateExplosionAtPoint(IDamageReceiver.DamageSource.ThrownObject, transform.position/*maxSpeedPoint*/, explosionRadius, explosionForce, this, Vector3.up, gameObject, c_debugLabel);
        }
    }

    public void PlayImpactAudioOnObject(NGMovingObject _obj)
    {
        _obj.m_hitByDecorationEvent?.Play(_obj.gameObject);
        m_throwExplosionImpactSound.Play(_obj.gameObject);
    }

    public void ApplyDamage(IDamageReceiver.DamageSource _source, NGMovingObject _obj, bool _isContinuous, float _multiplier = 1)
    {
        var damage = m_throwExplosionDamageBase * _multiplier;
        _obj.ApplyDamageEffect(_source, damage, _obj.transform.position + Vector3.up * 1.5f);
    }
}
