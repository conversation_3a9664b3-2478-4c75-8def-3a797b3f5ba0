using UnityEngine;

public class MANoose : MonoBehaviour
{
    public ConfigurableJoint m_attachment;
    public GameObject m_nooseLoop;
    private RagdollController m_attachedRagdollController;
    private Rigidbody m_attachedRigidBody;

    public void Attach(MAWorker _condemned)
    {
        m_attachedRagdollController = _condemned.gameObject.GetComponentInChildren<RagdollController>();

        Transform neckTransform = FindNeckTransform(_condemned.transform);

        if (neckTransform != null)
        {
            m_nooseLoop.transform.SetParent(neckTransform, false);
            m_nooseLoop.transform.localPosition = new Vector3(0.0f, 0.1f, 0.03f);
            m_nooseLoop.transform.localRotation = Quaternion.Euler(-58.53f, 0.0f, 0.0f);
            m_nooseLoop.SetActive(true);
            m_attachment.connectedBody = m_nooseLoop.GetComponent<Rigidbody>();
            m_attachment.connectedAnchor = new Vector3(-0.002f, 0.002f, 0.0f);
            m_attachment.gameObject.GetComponent<Rigidbody>().isKinematic = false;
        }  
    }

    public void Detach()
    {
        m_attachment.gameObject.GetComponent<Rigidbody>().isKinematic = true;
        m_attachment.connectedBody = null;
        m_attachedRagdollController = null;
        m_attachedRigidBody = null;
        m_nooseLoop.transform.SetParent(transform, false);
        m_nooseLoop.SetActive(false);
    }

    private void Update()
    {
        if(m_attachedRagdollController != null && m_attachedRigidBody == null)
        {
            // check if ragdoll has been turned on
            if(m_attachedRagdollController.BoneHipsRagdoll != null)
            {
                Transform neckTransform = FindNeckTransform(m_attachedRagdollController.BoneHipsRagdoll);

                if (neckTransform != null)
                {
                    Rigidbody rigidbody = neckTransform.GetComponent<Rigidbody>();

                    if (rigidbody != null)
                    {
                        m_attachment.connectedBody = neckTransform.GetComponent<Rigidbody>();
                        m_attachment.connectedAnchor = new Vector3(-0.01f, 0.0f, 0.0f);
                        m_attachedRigidBody = rigidbody;
                    }
                }
            }
        }
    }

    private Transform FindNeckTransform(Transform _transform)
    {
        return _transform.FindChildRecursiveByName("mixamorig:Neck");
    }
}
