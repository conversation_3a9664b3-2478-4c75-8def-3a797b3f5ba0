using System.Collections.Generic;
using UnityEngine;

public class MADropTargetInfo
{
	public Vector3 m_position;
	public Quaternion m_rotation;
    public string m_landAnim;
}

public class MADecorationActionBase : NGDecoration
{
	public bool m_lockedInPlace = true;

	protected override void Start()
	{
		base.Start();

		NGDecorationInputHandler inputHandler = GetComponent<NGDecorationInputHandler>();

		if(inputHandler != null)
        {
			inputHandler.m_lockedInPlace = m_lockedInPlace;
        }
	}

    protected override List<ContextMenuData.ButtonData> GetContextMenuButtonData()
    {
        List<ContextMenuData.ButtonData> buttonDatas = new List<ContextMenuData.ButtonData>
        {
                new ContextMenuData.ButtonData{
                    m_label = Localizer.Get(TERM.GUI_INFO),
                    m_onClick = ShowInfoPlaque
                }        
        };

		if(!m_lockedInPlace)
        {
			buttonDatas.Add(new ContextMenuData.ButtonData
			{
				m_label = "Move Decoration",
				m_onClick = PickupDecoration
			});
		}

		return buttonDatas;
    }

    public bool IsWithinOpenDistrict()
	{
		if (DistrictManager.Me.IsWithinDistrictBounds(transform.position, true))
			return true;

		return false;
	}

	virtual public float GetDropScore(NGMovingObject _object, out SpecialHandlingAction _action, SpecialHandlingAction _restrictedAction) { _action = null; return 0f; }
	virtual public SpecialHandlingAction HasSpecialHandling(NGMovingObject _obj, SpecialHandlingAction _restrictedAction) => null;
	virtual public bool ApplySpecialDropHandling(NGMovingObject _obj, SpecialHandlingAction _restrictedAction) => false;
	virtual public MADropTargetInfo GetDropTargetInfo(GameObject _source) => null;
	virtual public void OnDropTargetComplete() { }
	virtual public bool AllowWorkers() => false;
}
