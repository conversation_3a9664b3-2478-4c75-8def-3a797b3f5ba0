using System.Collections.Generic;
using UnityEngine;

public class MAQuestWaspSwarm : MonoBehaviour
{
    public GameObject m_waspModelPrefab;
    public int m_waspCount = 10;
    public List<MAQuestWasp> m_wasps = new List<MAQuestWasp>();
    
    private Transform m_attackTarget;
    
    // Constants
    private const float c_spawnHeightOffest = 1.0f;
    
    public void SpawnWasps()
    {
        if (GameManager.Me.PossessedCharacter != null && GameManager.Me.PossessedCharacter is MAHeroBase)
        {
            m_attackTarget = GameManager.Me.PossessedCharacter.transform;
            
            Vector3 spawnPoint = transform.position;
            spawnPoint.y += c_spawnHeightOffest;

            for (int i = 0; i < m_waspCount; i++)
            {
                GameObject waspGO = Instantiate(m_waspModelPrefab, spawnPoint, Quaternion.identity);
                MAQuestWasp wasp = waspGO.GetComponent<MAQuestWasp>();
                if (wasp == null)
                {
                    wasp = waspGO.AddComponent<MAQuestWasp>();
                }
                wasp.m_waspModel = waspGO;
                wasp.Initialize(spawnPoint, GameManager.Me.PossessedCharacter, this);
                m_wasps.Add(wasp);
                wasp.name = $"Wasp [{m_wasps.Count-1}]";
                wasp.transform.SetParent(transform, true);
            }
        }
    }

    public void RemoveWasp(MAQuestWasp wasp)
    {
        if (m_wasps.Contains(wasp))
        {
            m_wasps.Remove(wasp);
            Destroy(wasp.gameObject);
        }

        if (m_wasps.Count == 0)
        {
            Destroy(gameObject);
        }
    }

    private void Start()
    {
        SpawnWasps();
    }
}