using UnityEngine;

public class MAQuestJudgement : MAQuestBase
{
    public MAShackles m_shackles;
    private MAFlowCharacter m_accused;
    private MAPilloryStocks m_stocks;
    private MAGallows m_gallows;
    private MATableSaw m_tableSaw;
    private Vector3 m_accusedStartPosition;
    private Quaternion m_accusedStartRotation;
    private string m_accusedName = "";
    
    private MAFlow<PERSON>haracter m_joshua, m_executioner;

    override protected void OnPostLoad()
    {
        // do not call base OnPostLoad(), .MOA file will create quest giver
        InitReferences();
    }

    private void Update()
    {
        CheckResetAccusedTransform();
    }

    override public void SetQuestStatus(QuestStatus _status)
    {
        base.SetQuestStatus(_status);

        if (m_status == QuestStatus.InProgress)
        {
            if (m_stocks != null && m_stocks.IsOccupied())
            {
                m_stocks.ReleasePrisoner();
            }

            if (m_gallows != null && m_gallows.IsOccupied())
            {
                m_gallows.ReleaseCondemned();
            }

            if (m_tableSaw != null && m_tableSaw.IsOccupied())
            {
                m_tableSaw.ReleasePrisoner();
            }
        }
    }

    private void InitReferences()
    {
        if (m_executioner == null)
        {
            m_executioner =
                NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "Executioner") as MAFlowCharacter;
        }
        if (m_joshua == null)
        {
            m_joshua =
                NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == "AccusedMale") as MAFlowCharacter;
        }
        
        if (m_executioner != null)
        {
            LipSyncManager.Me.AddLipSyncer("Executioner", m_executioner, -22.0f);
            LipSyncManager.Me.SetGestureSet("Executioner", "Default");
        }
        
        if (m_joshua != null)
        {
            LipSyncManager.Me.AddLipSyncer("JoshuaRedgrave", m_joshua, -16.0f);
            LipSyncManager.Me.SetGestureSet("JoshuaRedgrave", "Agitated");
        }

        if (m_stocks == null)
        {
            m_stocks = NGManager.Me.m_decorationHolder.GetComponentInChildren<MAPilloryStocks>();
        }

        if (m_gallows == null)
        {
            m_gallows = NGManager.Me.m_decorationHolder.GetComponentInChildren<MAGallows>();
        }

        if (m_tableSaw == null)
        {
            m_tableSaw = NGManager.Me.m_decorationHolder.GetComponentInChildren<MATableSaw>();
        }

        if (m_accused == null && !m_accusedName.IsNullOrWhiteSpace())
        {
            m_accused = NGManager.Me.m_MACharacterList.Find(x => x.GetTypeInfo() == m_accusedName) as MAFlowCharacter;

            if (m_accused != null)
            {
                m_shackles.Attach(m_accused);
                m_accused.m_dragRaise = 1.0f;
                m_accusedStartPosition = m_accused.transform.position;
                m_accusedStartRotation = m_accused.transform.rotation;
            }
        }
    }

    public override float QuestObjectiveValue(string _objective)
    {
        if (m_accused != null)
        {
            if (_objective.Contains("InAny"))
            {
                return (m_stocks.IsOccupied(m_accused) || m_gallows.IsOccupied(m_accused) || m_tableSaw.IsOccupied(m_accused)) ? 0.0f : 1.0f;
            }

            if (_objective.Contains("InStocks"))
            {
                return m_stocks.IsOccupied(m_accused) ? 0.0f : 1.0f;
            }

            if (_objective.Contains("InGallows"))
            {
                return m_gallows.IsOccupied(m_accused) ? 0.0f : 1.0f;
            }

            if (_objective.Contains("InTableSaw"))
            {
                return m_tableSaw.IsOccupied(m_accused) ? 0.0f : 1.0f;
            }
        }

        if (_objective.Contains("GallowsTriggered"))
        {
            return m_gallows.GetDropState() > MAGallows.DropState.None ? 0.0f : 1.0f;
        }

        return 1.0f;
    }

    public override bool WaitForQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');
        if (m_accused != null)
        {

            if (split[0].Contains("ShacklesReleased"))
            {
                bool shacklesReleased = m_tableSaw.m_shacklesReleased;
                if (m_tableSaw.m_shacklesReleased)
                    m_tableSaw.m_shacklesReleased = false;
                return shacklesReleased;
            }
            if (split[0].Contains("TableSawReleased"))
            {
                return !m_tableSaw.IsOccupied(m_accused);
            }
        }

        return false;
    }

    public override void TriggerQuestEvent(string _event)
    {
        base.TriggerQuestEvent(_event);

        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("InitReferences"))
        {
            InitReferences();
        }

        if (split[0].Contains("EnablePickup"))
        {
            if (m_accused != null)
            {
                m_accused.SetCanPickup(true);
            }
        }

        if (split[0].Contains("SetAccused"))
        {
            m_accusedName = split[1];
        }
    }

    public void BreakShackles()
    {
        m_shackles.Break();
    }

    public void DetachShackles()
    {
        m_shackles.Detach();
    }

    private float m_maxAccusedDistanceSqr = 25.0f;

    private void CheckResetAccusedTransform()
    {
        if (m_status == QuestStatus.InProgress && m_accused != null && m_stocks != null && m_gallows != null && m_tableSaw != null)
        {
            if (!m_stocks.IsOccupied(m_accused) && !m_gallows.IsOccupied(m_accused) && !m_tableSaw.IsOccupied(m_accused))
            {
                CharacterPickupBehaviour cpb = m_accused.GetComponent<CharacterPickupBehaviour>();

                if (cpb != null && !cpb.Held && !cpb.Falling)
                {
                    if (m_shackles.m_chain.activeInHierarchy && !m_tableSaw.m_hasBeenReleased)
                    {
                        if ((m_accused.transform.position - m_accusedStartPosition).sqrMagnitude > m_maxAccusedDistanceSqr)
                        {
                            m_accused.transform.position = m_accusedStartPosition;
                            m_accused.transform.rotation = m_accusedStartRotation;
                        }
                    }
                }
            }
        }
    }

    override public void StopFlowMusic()
    {
        GameManager.Me.StopFlowMusicState("JoshuaTrial_LOOP");
    }

    public class SaveLoadQuestJudgementContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestJudgementContainer() : base() { }
        public SaveLoadQuestJudgementContainer(MAQuestBase _base) : base(_base) { }
        [Save] public string m_accusedName;
    }

    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestJudgementContainer(this);

        saveContainer.m_accusedName = m_accusedName;

        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestJudgementContainer;
        if (saveContainer != null)
        {
            m_accusedName = saveContainer.m_accusedName;
        }
    }
}
