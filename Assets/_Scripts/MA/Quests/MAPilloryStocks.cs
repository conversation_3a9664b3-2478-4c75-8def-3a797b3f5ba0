using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class MAPilloryStocks : MADecorationActionBase
{
    public Animator m_animator;
    public Transform m_boardsTransform;

    private MAWorker m_prisoner;
    private long m_releaseDay = -1;
    private int m_clickID;
    private GameState_PilloryStocks m_derrivedState;

    private Vector3 m_targetSize;
    private Vector3 m_initialSize;
    private float m_sizeLerpValue = 1.0f;
    private float m_sizeLerpRate;

    protected override void Awake()
    {
        base.Awake();
        m_clickID = Animator.StringToHash("Click");
    }

    public bool IsOccupied(MAWorker _worker = null)
    {
        if (_worker != null)
        {
            return m_prisoner == _worker;
        }
        else
        {
            return m_prisoner != null;
        }
    }

    public void SetPrisoner(MAWorker _prisoner, float _sentenceDays = 0.75f)
    {
        TriggerOpenCloseAnimation();
        m_prisoner = _prisoner;
        m_prisoner.SetState(NGMovingObject.STATE.MA_ANIMATOR_CONTROLLED);
        m_prisoner.SetCanPickup(false);
        Vector3 size = new Vector3(m_prisoner.transform.localScale.x, m_prisoner.transform.localScale.y, 1.0f);
        Resize(size, 0.5f);
        float dawnFraction = DayNight.StageToFraction(DayNight.c_timeStageDawnStart);
        m_releaseDay = DayNight.Me.m_day + Mathf.CeilToInt(DayNight.Me.m_timeOfDay + _sentenceDays - dawnFraction);
        UpdateState();
    }

    public void ReleasePrisoner()
    {
        if (m_prisoner != null)
        {
            TriggerOpenCloseAnimation();

            MAQuestJudgement quest = MAQuestManager.Me.GetQuestBase<MAQuestJudgement>() as MAQuestJudgement;

            if (quest != null)
            {
                quest.DetachShackles();
            }

            // Maybe override SetDefaultAction
            if (m_prisoner is MAFlowCharacter)
            {
                var building = MABuilding.FindBuilding("Tavern", true);
                m_prisoner.SetMoveToBuilding(building, PeepActions.Despawn);
            }
            else
            {
                m_prisoner.SetDefaultAction();
            }

            m_prisoner.m_nav.Unpause();
            m_prisoner = null;
            m_releaseDay = -1;
            UpdateState();
        }
    }

    public bool IsReleaseDue()
    {
        if (m_prisoner != null)
        {
            return DayNight.Me.m_day >= m_releaseDay;
        }

        return false;
    }

    public void Interact()
    {
        if(IsOccupied())
        {
            m_prisoner.TauntPrisoner();
        }
        else
        {
            TriggerOpenCloseAnimation();
        }
    }

    private void TriggerOpenCloseAnimation()
    {
        if (m_animator != null)
        {
            m_animator.SetTrigger(m_clickID);
        }
    }

    private void Update()
    {
        if(IsReleaseDue() && DayNight.Me.m_isDawn)
        {
            ReleasePrisoner();
        }

        UpdateSize();
    }

    private void UpdateSize()
    {
        if(m_sizeLerpValue < 1.0f)
        {
            m_sizeLerpValue = Mathf.Clamp01(m_sizeLerpValue + m_sizeLerpRate * Time.deltaTime);
            float smoothed = m_sizeLerpValue * m_sizeLerpValue * (3 - 2 * m_sizeLerpValue);
            m_boardsTransform.localScale = Vector3.Lerp(m_initialSize, m_targetSize, smoothed);
        }
    }

    private void Resize(Vector3 _targetSize, float _time)
    {
        m_targetSize = _targetSize;
        m_initialSize = transform.localScale;
        m_sizeLerpRate = 1.0f / _time;
        m_sizeLerpValue = 0.0f;
    }

    public override float GetDropScore(NGMovingObject _object, out SpecialHandlingAction _action, SpecialHandlingAction _restrictedAction)
    {
        _action = HasSpecialHandling(_object, _restrictedAction);

        if (_action != null && !IsOccupied())
        {
            return 1f;
        }

        return 0f;
    }

    override public SpecialHandlingAction HasSpecialHandling(NGMovingObject _o, SpecialHandlingAction _restrictedAction)
    {
        MAWorker worker = null;

        if (AllowWorkers())
        {
            worker = _o as MAWorker;
        }
        else
        {
            worker = _o as MAFlowCharacter;
        }

        if (worker != null)
        {
            return SpecialHandlingAction.AssignToStocks;
        }

        return null;
    }

    override public bool ApplySpecialDropHandling(NGMovingObject _o, SpecialHandlingAction _restrictedAction)
    {
        MAWorker worker = null;

        if (AllowWorkers())
        {
            worker = _o as MAWorker;
        }
        else
        {
            worker = _o as MAFlowCharacter;
        }

        if (worker != null)
        {
            if (_restrictedAction == SpecialHandlingAction.AssignToStocks)
            {
                SetPrisoner(worker);
                return true;
            }
        }
        return false;
    }

    override public void OnDropTargetComplete()
    {
        m_prisoner.SetState(NGMovingObject.STATE.MA_IN_PILLORY_STOCKS);
    }

    override public MADropTargetInfo GetDropTargetInfo(GameObject _source)
    {
        MADropTargetInfo dropTargetInfo = new MADropTargetInfo();

        var ba = GetComponentInChildren<BezierAnchor>();

        if (ba != null)
        {
            dropTargetInfo.m_position = transform.TransformPoint(Vector3.Scale( ba.transform.localPosition, _source.transform.localScale));
            dropTargetInfo.m_rotation = ba.transform.rotation;
        }
        else
        {
            dropTargetInfo.m_position = transform.position;
            dropTargetInfo.m_rotation = transform.rotation;
        }

        dropTargetInfo.m_landAnim = "Worker_Stocks_Idle";

        return dropTargetInfo;
    }

    public override NGDecoration SetDecorationData(GameState_Deccoration _data)
    {
        base.SetDecorationData(_data);

        if(_data.m_derrivedState == null)
        {
            _data.m_derrivedState = new GameState_PilloryStocks();
        }

        m_derrivedState = _data.m_derrivedState as GameState_PilloryStocks;

        if (m_derrivedState != null)
        {
            m_prisoner = NGManager.Me.m_MACharacterList.Find(o => o.m_ID.Equals(m_derrivedState.m_prisonerID)) as MAWorker;
            m_releaseDay = m_derrivedState.m_releaseDay;
        }

        return this;
    }

    protected override void UpdateState()
    {
        base.UpdateState();

        if (m_derrivedState != null)
        {
            m_derrivedState.m_prisonerID = m_prisoner != null ? m_prisoner.m_ID : -1;
            m_derrivedState.m_releaseDay = m_releaseDay;
        }
    }
}
