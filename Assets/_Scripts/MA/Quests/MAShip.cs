using System;
using System.Collections;
using System.Collections.Generic;
using Hairibar.Ragdoll;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class MAShip : MonoBehaviour//, IPointerClickHandler
{
    [ReadOnlyInspector][SerializeField] bool m_underControl = false;
    [ReadOnlyInspector][SerializeField] bool m_controlsActive = false;
    
    [SerializeField] 
    private GameObject m_captainContent = null;
    
    [Header("Ship Controls")]
    [SerializeField]
    protected float m_forwardForce = 1000;
    
    [SerializeField]
    protected float m_rotationForce = 720;
    
    [Header("Standard Unity forward is pos Z")]
    [SerializeField]
    protected bool m_flipForward = true;
    
    [Header("Cannon")]
    [SerializeField] 
    protected KeyControl m_activateCannon = new KeyControl(KeyCode.Space, true, false);
    
    [SerializeField] 
    protected KeyControl m_fireCannon = new KeyControl(KeyCode.Mouse0, false, false);

    [SerializeField]
    protected List<KeyCode> m_shipControlsForward = new() { KeyCode.W };
    
    [SerializeField]
    protected List<KeyCode> m_shipControlsLeft = new() { KeyCode.A, KeyCode.Q  };
    
    [SerializeField]
    protected List<KeyCode> m_shipControlsRight = new() { KeyCode.D, KeyCode.E };
    
    [SerializeField]
    protected string m_chosenCannonCamParentName = "";
    
    [SerializeField]
    protected List<CannonCameraPosition> m_cannonCameras = new();

    [SerializeField]
    protected List<GameObject> m_cannonBalls = new();
    
    [SerializeField]
    protected float m_cannonFirePower = 50000f;
    
    [SerializeField]
    protected float m_roundsPerMinute = 60;

    [SerializeField]
    protected GameObject m_cannonFireParticleSystem = null;
    
    [Header("Camera Settings")]
    [SerializeField]
    protected MAPossessionSettings m_shipPossessionSettingsOverride = null, m_shipCannonPossessionSettingsOverride = null;
    
    [SerializeField]
    protected GameObject m_waterSplashParticleEffect = null;
    
    [Header("General")]
    [SerializeField]
    protected Transform m_content = null; 

    public Save_Ship m_shipSave;
        
    protected GameObject m_gameObject = null;
    protected Transform m_transform = null;
    
    protected Rigidbody m_rigidBody = null;
    protected Collider m_collider = null;
    
    protected NavAgent m_navAgent = null;
    protected NGMovingObject m_movingObject = null, m_cannonMovingObject = null;
    
    protected Transform m_cannonBallStart = null, m_cannonBallExit = null, m_cannonBarrelHinge = null;
    protected GameObject m_cannon = null, m_cannonCamObj = null;

    protected Camera m_cannonCam = null;

    protected float m_cannonElevation = 0f;
    
    protected MATimer m_noFireTimer = new();
    private int m_impulseCount = 0;
    private float m_sec = 0;
    private Vector3 m_latestTorqueInput = Vector3.zero;
    private Vector3 m_latestAngularForceInput = Vector3.zero;


    private HashSet<Transform> m_firedProjectiles = new();
    private HashSet<GameObject> m_firedProjectilesBeingDestroyed = new();

    public bool IsPossessed => m_shipSave.m_possessState != PossessState.None;

    public enum PossessState
    {
        None,
        Ship,
        Cannon,
    }
    
    [System.Serializable]
    public class CannonCameraPosition
    {
        public Transform m_cameraTransform;
        public string m_cameraName;
    }

    [System.Serializable]
    public class Save_Ship
    {
        public Vector3 m_pos;
        public float m_rot;
        public PossessState m_possessState;
        public string m_chosenCannonCamParentName;
        public float m_cannonElevation;
    }
    
    [Serializable]
    public class KeyControl
    {
        public KeyCode m_keyCode;
        public bool m_onUp;
        public bool m_allowKeyDownRepeat;
        
        public bool UpdateKey
        {
            get
            {
                if (m_onUp) return Utility.GetKeyUp(m_keyCode);
                if (m_allowKeyDownRepeat == false) return Utility.GetKeyDown(m_keyCode);
                return Input.GetKey(m_keyCode);
            }
        }
        
        public KeyControl(KeyCode _keyCode, bool _onUp = true, bool _allowKeyDownRepeat = false)
        {
            m_keyCode = _keyCode;
            m_onUp = _onUp;
            m_allowKeyDownRepeat = _allowKeyDownRepeat;
        }
    }

    protected void Awake()
    {
        m_gameObject = gameObject;
        m_transform = transform;
        m_rigidBody = GetComponent<Rigidbody>();
        m_collider = GetComponent<Collider>();
        m_navAgent = GetComponent<NavAgent>();
        m_movingObject = GetComponent<NGMovingObject>();

        m_cannon = m_content.FindChildRecursiveByName("MA_PirateCannon").gameObject;
        m_cannonMovingObject = m_cannon.GetComponent<NGMovingObject>();
        GameState_MovingObject cannonState = new GameState_MovingObject();
        m_cannonMovingObject.SetGameStateSaveData(cannonState);
        Transform cannonTr = m_cannon.transform;
        m_cannonBallStart = cannonTr.FindChildRecursiveByName("CannonBallStart");
        m_cannonBallExit = cannonTr.FindChildRecursiveByName("CannonBallExit");
        m_cannonBarrelHinge = cannonTr.FindChildRecursiveByName("CannonBarrelHinge");
        m_cannonElevation = m_cannonBarrelHinge.localRotation.eulerAngles.x;
    }
    
    protected void Start()
    {
        GameState_MovingObject gameState = new GameState_MovingObject();
        gameState.m_health = 1f;
        m_movingObject.SetGameStateSaveData(gameState);
        m_navAgent.Initialise(m_movingObject, GlobalData.s_vehicleCosts);

        GameManager.Me.m_onPossess -= OnPossess;
        GameManager.Me.m_onPossess += OnPossess;
        
        
        if (m_cannonFireParticleSystem != null)
        {
            foreach (var particleSys in m_cannonFireParticleSystem.GetComponentsInChildren<ParticleSystem>())
            {
                particleSys.Stop();
            }
        }
        //GetComponent<NGMovingObject>().SetupWithAnimator(GetComponentInChildren<Animator>(true));
    }
    
    protected void Update()
    {
        m_underControl = false;
        
        UpdateTurret();

        if (GameManager.Me.PossessedObject == m_gameObject || GameManager.Me.PossessedObject == m_cannon)
        {
            if (m_activateCannon.UpdateKey)
            {
                if (GameManager.Me.PossessedObject == m_cannon)
                {
                    Possess();
                }
                else
                {
                    PossessCannon();
                }
                return;
            }

            
            if (m_shipSave.m_possessState == PossessState.None)
            {
                Possess();
            }
            else
            {
                m_underControl = true;
            }
        }
        else
        {
            if (IsPossessed)
            {
                UnPossess();
            }
        }

        UpdateControls();

        foreach (var firedProjectile in m_firedProjectiles)
        {
            if (firedProjectile != null)
            {
                if (firedProjectile.position.y <= GlobalData.c_seaLevel)
                {
                    if (m_waterSplashParticleEffect != null)
                    {
                        m_waterSplashParticleEffect.transform.position = firedProjectile.position.ToSeaLevel();
                        foreach (var waterSplashParticles in m_waterSplashParticleEffect
                                     .GetComponentsInChildren<ParticleSystem>())
                        {
                            waterSplashParticles.Play();
                        }
                    }

                    StartCoroutine(DestroyProjectileAfter(firedProjectile.gameObject, 0.2f));
                    break;
                }
            }
        }


        foreach (var firedProjectileBeingDestroyed in m_firedProjectilesBeingDestroyed)
        {
            if (firedProjectileBeingDestroyed == null)
            {
                m_firedProjectilesBeingDestroyed.Remove(firedProjectileBeingDestroyed);
                break;
            }
        }
    }

    protected void LateUpdate()
    {
        switch (m_shipSave.m_possessState)
        {
            case PossessState.Cannon:
                // var cam = Camera.main;
                // var camXform = cam.transform;
                // var camOffset = NGManager.Me.m_possessedTurretPos;
                // camXform.position = m_controlledVisual.position + m_controlledVisual.forward * camOffset.z + Vector3.up * camOffset.y + m_controlledVisual.right * camOffset.x;
                // camXform.forward = -m_controlledVisual.forward + Vector3.up * camOffset.w;
                break;
        }
    }

    protected void OnDestroy()
    {
        if (GameManager.Me != null)
        {
            GameManager.Me.m_onPossess -= OnPossess;

            if (GameManager.Me.PossessedObject == gameObject)
            {
                GameManager.Me.Unpossess();
            }
        }
    }


    private void FixedUpdate()
    {
        ApplyMovement();
        UpdateSeaLevel();
    }

    protected void ApplyMovement()
    {
        m_rigidBody.AddTorque(m_latestAngularForceInput, ForceMode.Impulse);
        m_latestAngularForceInput = Vector3.zero;
        m_rigidBody.AddForce(m_latestTorqueInput, ForceMode.Impulse);
        m_latestTorqueInput = Vector3.zero;
        m_impulseCount++;
    }

    protected void UpdateSeaLevel()
    {
        var p = m_rigidBody.position;
        if (Mathf.Approximately(p.y, GlobalData.c_seaLevel) == false)
        {
            p.y = GlobalData.c_seaLevel;
            m_rigidBody.MovePosition(p);
        }
    }
    
    protected void UpdateControls()
    {
        var upKey = m_shipControlsForward.FindIndex(x => Utility.GetKey(x)) > -1;
        var leftKey = m_shipControlsLeft.FindIndex(x => Utility.GetKey(x)) > -1;
        var rightKey = m_shipControlsRight.FindIndex(x => Utility.GetKey(x)) > -1;

        m_controlsActive = upKey || leftKey || rightKey;

        float z = upKey ? (m_flipForward ? -1f : 1f) : 0f;
        if (z != 0f)
        {
            m_latestTorqueInput = m_rigidBody.transform.forward * z * m_forwardForce;
        }

        m_sec += Time.deltaTime;
        if (m_sec >= 1f)
        {
            //Debug.LogError($"force input impulses per sec: {m_impulseCount}");
            m_sec = 0f;
            m_impulseCount = 0;
        }
        
        Vector3 angular = Vector3.zero;
        angular.y += (rightKey ? 1 : 0) + (leftKey ? -1 : 0);
        angular *= m_rotationForce;
        m_latestAngularForceInput = angular;
        //m_rigidBody.angularVelocity += angular;

        if (m_fireCannon.UpdateKey)
        {
            FireProjectile(m_cannonBalls.PickRandom());
        }
    }

    public void Load(Save_Ship _saveData)
    {
        Transform pTr = transform;
        m_shipSave = _saveData;
        pTr.position = m_shipSave.m_pos;
        pTr.rotation = Quaternion.AngleAxis(m_shipSave.m_rot, Vector3.up);

        m_chosenCannonCamParentName = _saveData.m_chosenCannonCamParentName;
        m_cannonElevation = _saveData.m_cannonElevation;
        m_cannonBarrelHinge.localEulerAngles = Vector3.right * m_cannonElevation;

        switch (m_shipSave.m_possessState)
        {
            case PossessState.Ship:
            case PossessState.Cannon:
                Possess();
                break;
            case PossessState.None:
                break;
        }
    }
    
    public Save_Ship Save()
    {
        Transform pTr = transform;
        if (m_shipSave == null)
        {
            m_shipSave = new();
        }

        m_shipSave.m_pos = pTr.position;
        m_shipSave.m_rot = pTr.rotation.eulerAngles.y;

        m_shipSave.m_chosenCannonCamParentName = m_chosenCannonCamParentName;
        m_shipSave.m_cannonElevation = m_cannonElevation;

        return m_shipSave;
    }

    public bool IsBeingActivelyControlled()
    {
        return m_controlsActive;
    }
    
    public bool Possess()
    {
        Debug.Log("Ship possession started");
        m_navAgent.enabled = false;
        GameManager.Me.m_possessionSettingsOverride = m_shipPossessionSettingsOverride;
        GameManager.Me.m_isCustomPossessedObjectBeingControlled = IsBeingActivelyControlled;
        if (GameManager.Me.PossessedObject != m_gameObject)
        {
            if (GameManager.Me.IsPossessing)
            {
                GameManager.Me.Unpossess();
            }
            
            GameManager.Me.PossessObject(GetComponent<NGMovingObject>(), false);
        }
        m_shipSave.m_possessState = PossessState.Ship;
        KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.PossessTurret);
        return true;
    }
    
    public bool PossessCannon()
    {
        Debug.Log("Ship Cannon possession started");
        if (GameManager.Me.PossessedObject == m_gameObject)
        {
            GameManager.Me.Unpossess();
        }
        else if(GameManager.Me.IsPossessing && GameManager.Me.PossessedObject != m_cannon)
        {
            return false;
        }

        var focusOrigin = m_cannonCameras.Find(x => x.m_cameraName == m_chosenCannonCamParentName && x.m_cameraTransform != null);
        if (focusOrigin != null)
        {
            m_cannonMovingObject.m_possessedCameraFocusOrigin = focusOrigin.m_cameraTransform;
        }
        
        GameManager.Me.PossessObject(m_cannonMovingObject, false);
        
        m_navAgent.enabled = false;
        GameManager.Me.m_possessionSettingsOverride = m_shipCannonPossessionSettingsOverride;
        KeyboardShortcutManager.Me.PopShortcuts();
        m_shipSave.m_possessState = PossessState.Cannon;
        return true;
    }
    
    public bool UnPossess()
    {
        Debug.Log("Ship possession ended");
        //m_navAgent.enabled = true;
        m_controlsActive = false;
        GameManager.Me.m_isCustomPossessedObjectBeingControlled = null;
        GameManager.Me.m_possessionSettingsOverride = null;
        m_shipSave.m_possessState = PossessState.None;
        KeyboardShortcutManager.Me.PopShortcuts();
        return true;
    }
    
    private bool UpdatePossession()
    {
        if (IsPossessed)
        {
            if (GameManager.Me.PossessedObject == m_cannon && GameManager.Me.IsFinishedBlending)
            {
                if (m_cannonCam == null)
                {
                    m_cannonCamObj = new GameObject();

                    Transform cannonParent = m_cannonCameras.Find(x =>
                    {
                        return x.m_cameraName == m_chosenCannonCamParentName && x.m_cameraTransform != null;
                    })?.m_cameraTransform ?? m_cannonBallExit;
                    
                    m_cannonCamObj.transform.SetParent(cannonParent, false);
                    m_cannonCam = m_cannonCamObj.AddComponent<Camera>();
                }
                else
                {
                    var yInputDelta = Input.GetAxis("Mouse Y");
                    var camTurnScale = GameManager.Me.CurrentPossessionSettings.m_possessedTurretSpeed;
                    int downMax = 15;
                    int upMax = -45;

                    var xDelta = Mathf.Clamp(yInputDelta * camTurnScale.x, -45f, 45f);
                    var rotX = xDelta * Time.deltaTime;
                    m_cannonElevation += rotX;
                    m_cannonElevation = Mathf.Clamp(m_cannonElevation, upMax, downMax);
                    m_cannonBarrelHinge.localEulerAngles = Vector3.right * m_cannonElevation;
                }
            }

            return true;
        }
        return false;
    }
    
    private void UpdateTurret()
    {
        if(UpdatePossession())
            return;
    }

    public void ResetCannonElevation()
    {
        if (m_cannonBarrelHinge != null)
        {
            m_cannonElevation = 0;
            m_cannonBarrelHinge.localEulerAngles = Vector3.right * m_cannonElevation;
        }
    }

    public void FireProjectile(GameObject _projectileObjTemplate)
    {
        if (m_noFireTimer.IsFinished == false)
            return;
            
        GameObject _projectileObj = null;
        
        if (_projectileObjTemplate == null)
        {
            _projectileObj = new GameObject("Ship Cannon Ball");
            _projectileObj.transform.SetParent(transform);
            var meshF = _projectileObj.AddComponent<MeshFilter>();
            var meshR = _projectileObj.AddComponent<MeshRenderer>();
            meshF.mesh = Resources.GetBuiltinResource<Mesh>("Sphere.fbx");
        }
        else
        {
            _projectileObj = Instantiate(_projectileObjTemplate, transform, false);
            _projectileObj.name = "Ship Cannon Ball";
            _projectileObj.gameObject.SetActive(true);
        }

        _projectileObj.transform.position = m_cannonBallExit.position;
        
        var rb = _projectileObj.GetComponent<Rigidbody>();
        if (rb == null)
        {
            rb = _projectileObj.AddComponent<Rigidbody>();
            rb.mass = 10;
            rb.angularDamping = 0.3f;
            rb.linearDamping = 0.3f;
        }
        
        var col = _projectileObj.GetComponent<Collider>();
        if (col == null)
        {
            col = _projectileObj.AddComponent<SphereCollider>();
        }
        
        col.enabled = false;

        CharacterCollision cannonBallCollisionEventListener = _projectileObj.AddComponent<CharacterCollision>();
        cannonBallCollisionEventListener.m_exclusiveColliderPairNames = null;
        cannonBallCollisionEventListener.m_onCollisionEnter += OnCannonBallCollisionEnter;
        
        rb.AddForce((m_cannonBallExit.position - m_cannonBallStart.position).normalized * m_cannonFirePower, ForceMode.Force);

        m_firedProjectiles.Add(_projectileObj.transform);
        
        StartCoroutine(AfterCannonFired(col));

        m_noFireTimer.Set(m_roundsPerMinute / 60f);

        if (m_cannonFireParticleSystem != null)
        {
            foreach (var particleSys in m_cannonFireParticleSystem.GetComponentsInChildren<ParticleSystem>())
            {
                particleSys.Play();
            }
        }
    }

    private IEnumerator DestroyProjectileAfter(GameObject _projectileObject, float _after)
    {
        yield return new WaitForSeconds(_after);

        foreach (var firedProjectile in m_firedProjectiles)
        {
            if (firedProjectile == null)
            {
                m_firedProjectiles.Remove(firedProjectile);
                break;
            }
        }

        if (_projectileObject != null)
        {
            m_firedProjectiles.Remove(_projectileObject.transform);
            if (m_firedProjectilesBeingDestroyed.Contains(_projectileObject) == false)
            {
                m_firedProjectilesBeingDestroyed.Add(_projectileObject);
                Destroy(_projectileObject.gameObject);
            }
        }
    }
    
    private IEnumerator AfterCannonFired(Collider _col)
    {
        yield return new WaitForSeconds(0.05f);
        _col.enabled = true;
        
        if (m_cannonFireParticleSystem != null)
        {
            foreach (var particleSys in m_cannonFireParticleSystem.GetComponentsInChildren<ParticleSystem>())
            {
                particleSys.Stop();
            }
        }
    }

    private void OnCannonBallCollisionEnter(Collision _collision)
    {
        foreach (ContactPoint collisionContact in _collision.contacts)
        {
            Debug.Log($"Cannon Ball Hit {collisionContact.otherCollider.name} at {collisionContact.point}");
        }
        foreach (var collisionContact in _collision.contacts)
        {
            CharacterCollision collisionEventListener = collisionContact.thisCollider.GetComponent<CharacterCollision>();
            if(collisionEventListener != null)
            {
                GameObject projectileObj = collisionContact.thisCollider.gameObject;
                collisionEventListener.m_onCollisionEnter -= OnCannonBallCollisionEnter;
                StartCoroutine(DestroyProjectileAfter(projectileObj, 4f));
            }
        }
    }
    
    protected void OnPossess(bool _enabled, NGLegacyBase _obj)
    {
        if (_obj == m_cannonMovingObject)
        {
            if (_enabled == false && m_cannonCamObj != null)
            {
                m_cannonCam.enabled = false;
                Destroy(m_cannonCamObj);
                m_cannonCam = null;
                m_cannonCamObj = null;
            }
            return;
        }
        
        if (_obj != m_movingObject) return;
        
        if (_enabled)
        {
            Possess();
        }
        else
        {
            UnPossess();
        }
    }
    
    // public void OnPointerClick(PointerEventData eventData)
    // {
    //     GameManager.Me.PossessObject(GetComponent<NGMovingObject>(), false);
    // }
}

#if UNITY_EDITOR
[CustomEditor(typeof(MAShip))]
public class MAShipEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        var ship = target as MAShip;
        if (GUILayout.Button("Fire Cannon Projectile"))
        {
            ship.FireProjectile(null);
        }
        if (GUILayout.Button("Reset Cannon Elevation"))
        {
            ship.ResetCannonElevation();
        }
    }
}
#endif
