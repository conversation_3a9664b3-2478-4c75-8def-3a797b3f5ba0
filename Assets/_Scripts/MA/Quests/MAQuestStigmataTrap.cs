using System;
using System.Collections;
using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using Random = UnityEngine.Random;

public class MAQuestStigmataTrap : MonoBehaviour
{
    public enum TrapState
    {
        Armed,
        Triggered,
        Trapped,
        Released,
        Disarmed,
        Last,
    }

    public Transform m_groundHole, m_ball, m_chainParent, m_handTarget, m_handCurrent, m_cameraTarget, m_cameraTargetFinal, m_cameraCurrent, m_bladeVFXParent;
    public Animator m_bladeAnim;
    public ConfigurableJoint m_chainLinkPrefab;
    public ConfigurableJoint m_ballChainAttachment;
    public Rigidbody m_groundHoldChainAttachment;
    public MAQuestStigmataTrapRelease m_trapRelease;
    public AkEventHolder m_ballSpeedAudio;
    public AkRTPCHolder m_ballSpeedRTPC;
    public AkEventHolder m_trapActivatedAudio = new AkEventHolder();
    public AkEventHolder m_trapDeactivatedAudio = new AkEventHolder();
    public AkEventHolder m_trapDeactivateFailedAudio = new AkEventHolder();
    public AkEventHolder m_trapTriggeredAudio  = new AkEventHolder();
    public AkEventHolder m_trapReleasedAudio = new AkEventHolder();
    public GameObject m_impactVFX, m_bladeVFX, m_dustVFX, m_grassVFX, m_ballEffectsHolder;
    
    [SerializeField]
    private BloodEmitter.BloodEmitterSettings m_bloodEmitterSettingsBladeStartEnterHand = new()
    {
        m_playOnStart = true,
        m_destroyOnFinish = true,
        m_destroyOnlyComponent = true,
        m_splatsPerSecond = 20,
        m_emitCountPerSplat = 3,
        m_totalBleedTime = 0.12f,
    };
    
    [SerializeField]
    private BloodEmitter.BloodEmitterSettings m_bloodEmitterSettingsBladeStartRelease = new()
    {
        m_playOnStart = true,
        m_destroyOnFinish = true,
        m_destroyOnlyComponent = true,
        m_splatsPerSecond = 1,
        m_emitCountPerSplat = 1,
        m_totalBleedTime = 0.25f,
    };
    
    [SerializeField]
    private BloodEmitter.BloodEmitterSettings m_bloodEmitterSettingsBladeEndRelease = new()
    {
        m_playOnStart = true,
        m_destroyOnFinish = true,
        m_destroyOnlyComponent = true,
        m_splatsPerSecond = 10,
        m_emitCountPerSplat = 3,
        m_totalBleedTime = 0.22f,
    };
    
    [SerializeField]
    private BloodEmitter.BloodEmitterSettings m_bloodEmitterSettingsPartialRelease = new()
    {
        m_playOnStart = true,
        m_destroyOnFinish = true,
        m_destroyOnlyComponent = true,
        m_splatsPerSecond = 4,
        m_emitCountPerSplat = 1,
        m_totalBleedTime = 0.5f,
    };
    
    [SerializeField]
    private BloodEmitter.BloodEmitterSettings m_bloodEmitterSettingsHandShake = new()
    {
        m_playOnStart = true,
        m_destroyOnFinish = true,
        m_destroyOnlyComponent = true,
        m_splatsPerSecond = 3,
        m_emitCountPerSplat = 2,
        m_totalBleedTime = 0.8f,
    };

    // variables to be made public
    private float m_maxChainLength = 35.0f;
    private float m_cameraTrackDistance = 8.0f;
    private float m_cameraTrackHeight = 2.0f;
    private float m_cameraTrackLerpRate = 2.0f;
    private float m_cameraTrackMaxTilt = 15.0f;
    private float m_maxPullSpeed = 2.25f;
    private float m_pullPeriod = 0.8f;
    private float m_pullAcceleration = 2.0f;
    private float m_pullInitialAcceleration = 0.25f;
    private float m_handToGroundTime = 0.3f;
    private float m_handRotateTime = 0.2f;
    private float m_cameraBallTime = 1.5f;
    private float m_cameraFocusTime = 0.5f;
    private float m_startCameraLerpValue = 0.45f;
    private float m_handReturnTime = 1.0f;
    private float m_triggeredCameraTargetTime = 0.5f;
    private float m_releasedCameraTargetTime = 1.0f;
    private float m_releasedCameraTargetFinalTime = 7.0f;
    private float m_trapBladeTriggerValue = 0.75f;
    private float m_playImpactVFXValue = 0.1f;
    private float m_ballRadius = 2.0f;
    private float m_chainRadius = 0.4f;
    private float m_linkLength = 0.8f;
    private float m_ballSoundStopSpeed = 0.5f;
    private float m_maxBallSoundSpeed = 70.0f; // Requested by Russ

    private PickupAndThrowBehaviour m_ballPickup;
    private ConfigurableJoint m_joint;
    private TrapState m_state = TrapState.Armed;
    private Coroutine m_triggeredCoroutine, m_releasedCoroutine;
    private int m_triggerID, m_stateID, m_partialID;
    private float m_currentChainLength = 3.0f;
    private List<ConfigurableJoint> m_chainLinks = new List<ConfigurableJoint>();
    private Transform m_targetHandBone = null;
    private bool m_isBallPickupEnabled = false;
    private bool m_isBallSoundPlaying = false;
    private int m_ballSoundID = 0;
    private Vector3 m_prevBallPos;

    public bool IsBallPickupEnabled
    {
        get => m_isBallPickupEnabled;
        set
        {
            m_isBallPickupEnabled = value;

            var inputHandler = m_ball.GetComponent<NGDecorationInputHandler>();

            if (inputHandler != null)
            {
                inputHandler.enabled = value;
            }
        }
    }

    public TrapState State
    {
        get => m_state;
        set
        {
            if (m_state != value)
            {
                m_state = value;

                switch (m_state)
                {
                    case TrapState.Triggered:
                        TriggeredSequence();
                        break;
                    case TrapState.Released:
                        ReleasedSequence();
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private void Awake()
    {
        m_joint = m_ball.GetComponent<ConfigurableJoint>();
        SetJointLimit(m_currentChainLength);
        m_triggerID = Animator.StringToHash("Trigger");
        m_stateID = Animator.StringToHash("State");
        m_partialID = Animator.StringToHash("Partial");
        m_prevBallPos = m_ball.position;

        if (m_handTarget != null)
        {
            SetTargetHandBone();
        }
    }

    private void Start()
    {
        StartCoroutine(Co_LoadNextFrame());
    }

    private void Update()
    {
        UpdateChain();
        UpdateBallSound();
        UpdateTrappedCamera();
    }

    private void UpdateChain()
    {
        switch (m_state)
        {
            case TrapState.Armed:
                {
                    if (IsBallHeld())
                    {
                        float distance = (m_ballChainAttachment.transform.position - m_groundHole.position).magnitude;

                        Debug.Log("Stigmata: " + distance + ", Ball: " + m_ballChainAttachment.transform.position + ", Hole: " + m_groundHole.position);

                        if (distance > m_currentChainLength)
                        {
                            m_currentChainLength = distance;
                            SetJointLimit(m_currentChainLength);
                        }
                    }
                    break;
                }
            case TrapState.Triggered:
                {
                    float distance = (m_ballChainAttachment.transform.position - m_groundHole.position).magnitude;

                    if(distance < m_currentChainLength)
                    {
                        m_currentChainLength = distance;
                        SetJointLimit(m_currentChainLength);
                    }

                    break;
                }
            default:
                break;
        }

        UpdateChainLinks();
    }

    private void UpdateChainLinks()
    {
        int requiredLinks = GetRequiredLinks(m_currentChainLength);

        if(requiredLinks != m_chainLinks.Count)
        {
            if (requiredLinks > m_chainLinks.Count)
            {
                Vector3 startPos = m_ballChainAttachment.transform.position;
                Vector3 endPos = m_groundHoldChainAttachment.transform.position;

                //Vector3 forward = Vector3.Cross((endPos - startPos).normalized, Vector3.up);
                Quaternion rot = Quaternion.identity;// Quaternion.LookRotation(forward);

                for (int i = 0; i < requiredLinks; i++)
                {
                    float t = ((float)(i + 1)) / ((float)(requiredLinks + 2));
                    Vector3 pos = Vector3.Lerp(startPos, endPos, t);
                    Vector3 groundPos = pos.GroundPosition(m_chainRadius);

                    if (pos.y < groundPos.y)
                    {
                        pos.y = groundPos.y;
                    }

                    if (i < m_chainLinks.Count)
                    {
                        m_chainLinks[i].GetComponent<Rigidbody>().MovePosition(Vector3.Lerp(m_chainLinks[i].transform.position, pos, 0.5f));
                    }
                    else
                    {
                        var chainLink = Instantiate(m_chainLinkPrefab, pos, rot, m_chainParent);
                        chainLink.gameObject.SetActive(true);

                        if (i % 2 == 0)
                        {
                            var meshRenderer = chainLink.GetComponentInChildren<MeshRenderer>();

                            if (meshRenderer != null)
                            {
                                meshRenderer.transform.Rotate(new Vector3(90.0f, 0.0f, 0.0f), Space.World);
                            }
                        }

                        var prevChainLink = i < 1 ? m_ballChainAttachment : m_chainLinks[i - 1];
                        prevChainLink.connectedBody = chainLink.GetComponent<Rigidbody>();
                        chainLink.connectedBody = m_groundHoldChainAttachment;
                        m_chainLinks.Add(chainLink);
                    }
                }
            }
            else
            {
                for(int i = m_chainLinks.Count - 1; i > requiredLinks - 1; i--)
                {
                    var prevChainLink = i < 1 ? m_ballChainAttachment : m_chainLinks[i - 1];
                    prevChainLink.connectedBody = m_groundHoldChainAttachment;

                    if (i >= 0) // Should always be the case
                    {
                        Destroy(m_chainLinks[i].gameObject);
                        m_chainLinks.RemoveAt(i);
                    }
                }
            }
        }
    }

    private void UpdateBallSound()
    {
        if(!m_isBallSoundPlaying)
        {
            if(IsBallHeld())
            {
                m_ballSoundID = m_ballSpeedAudio?.Play(m_ball.gameObject, _allowPreLoad: true) ?? 0;
                m_isBallSoundPlaying = true;
            }
        }

        if (m_isBallSoundPlaying)
        {
            float distance = (m_ball.position - m_prevBallPos).magnitude;
            float speed = distance / Time.deltaTime;
            float clampedSpeed = Mathf.Min(speed, m_maxBallSoundSpeed);
            m_ballSpeedRTPC?.Set(clampedSpeed, m_ball.gameObject);

            if (speed < m_ballSoundStopSpeed && !IsBallHeld())
            {
                AudioClipManager.Me.StopSound(m_ballSoundID, m_ball.gameObject);
                m_isBallSoundPlaying = false;
            }

            //Debug.Log("Ball speed: " + speed);
        }

        m_prevBallPos = m_ball.position;
    }

    private Vector2 m_trappedCameraOffset;
    private float m_trappedCameraRotation;
    private const float m_maxTrappedCameraDistance = 2.0f;
    private const float m_maxTrappedCameraRotation = 20.0f;
    private const float m_trappedCameraReturnSpeed = 5.0f;

    private void UpdateTrappedCamera()
    {
        if(m_state == TrapState.Trapped)
        {
            bool userKeyboardMove = false;
            bool didRotate = false;

            var movementInput = GetMovementInput(ref userKeyboardMove);
            var rotateInput = GetRotateInput(ref didRotate);

            if (userKeyboardMove)
            {
                m_trappedCameraOffset += movementInput * Time.deltaTime;
            }

            if(didRotate)
            {
                m_trappedCameraRotation += rotateInput * Time.deltaTime;
            }

            if(m_trappedCameraOffset.sqrMagnitude > m_maxTrappedCameraDistance * m_maxTrappedCameraDistance)
            {
                m_trappedCameraOffset = m_trappedCameraOffset.normalized * m_maxTrappedCameraDistance;
            }

            m_trappedCameraRotation = Mathf.Clamp(m_trappedCameraRotation, -m_maxTrappedCameraRotation, m_maxTrappedCameraRotation);

            if (!userKeyboardMove)
            {
                m_trappedCameraOffset = Vector2.Lerp(m_trappedCameraOffset, Vector2.zero, m_trappedCameraReturnSpeed * Time.deltaTime);      
            }

            if (!didRotate)
            {
                m_trappedCameraRotation = Mathf.Lerp(m_trappedCameraRotation, 0f, m_trappedCameraReturnSpeed * Time.deltaTime);
            }

            m_cameraCurrent.position = m_cameraTarget.position;
            m_cameraCurrent.rotation = m_cameraTarget.rotation;
            m_cameraCurrent.RotateAround(GetTrappedCameraFocus(), Vector3.up, m_trappedCameraRotation);

            Vector3 cameraPos = m_cameraCurrent.position;
            cameraPos += m_cameraCurrent.right * m_trappedCameraOffset.x;
            Vector3 cameraForward = m_cameraCurrent.forward;
            cameraForward.y = 0f;
            cameraForward.Normalize();
            cameraPos += cameraForward * m_trappedCameraOffset.y;

            m_cameraCurrent.position = cameraPos;
        }
    }

    private Vector2 m_keySmoothMove1, m_keySmoothMove2;

    private Vector2 GetMovementInput(ref bool _userKeyboardMove)
    {
        return GameManager.Me.ReadKeyStick(
            KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 1),
            KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 3),
            KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 2),
            KeyboardController.Me.GetKey(EKeyboardFunction.MoveCamera, 0),
            GameManager.c_keySpeedMove, ref m_keySmoothMove1, ref _userKeyboardMove, NGManager.Me.m_moveKeysSmoothing) +
            GameManager.Me.ReadKeyStick(KeyCode.LeftArrow, KeyCode.RightArrow, KeyCode.DownArrow, KeyCode.UpArrow,
            GameManager.c_keySpeedMove, ref m_keySmoothMove2, ref _userKeyboardMove, NGManager.Me.m_moveKeysSmoothing);
    }

    private float m_keySmoothRotate;
    private Vector3 m_rotateMouse;
    private bool m_mouseRotateStartedLegally;

    private float GetRotateInput(ref bool _didRotate)
    {
        float touchRotate = TouchManager.TouchInputAngleChange * -4f;
        if (GameManager.InputConsumingTouchGestures)
            touchRotate = 0; // if we're dragging or making don't rotate

        float mouseRotate = 0;
        if (TouchManager.TouchInputActive == false && EKeyboardFunction.RotateCameraByMouse.AnyHeld())
        {
            if (EKeyboardFunction.RotateCameraByMouse.AnyDown())
            {
                m_rotateMouse = Utility.InputPos;
                m_mouseRotateStartedLegally = true;
            }
            else if (m_mouseRotateStartedLegally)
            {
                mouseRotate += m_rotateMouse.x - Utility.InputPos.x;
                m_rotateMouse = Utility.InputPos;
                _didRotate = true;
            }
        }

        float keyRotate = GameManager.Me.ReadKeyAxis(GameManager.Me.c_cameraRotateCCW, GameManager.Me.c_cameraRotateCW, GameManager.c_keySpeedRotate, ref m_keySmoothRotate, ref _didRotate, true, NGManager.Me.m_rotateKeysSmoothing);

        return touchRotate + mouseRotate + keyRotate;
    }

    private Vector3 m_trappedCameraFocus;
    private bool m_hasCalculatedTrappedCameraFocus = false;

    private Vector3 GetTrappedCameraFocus()
    {
        if(!m_hasCalculatedTrappedCameraFocus)
        {
            var ray = new Ray(m_cameraTarget.position, m_cameraTarget.rotation.forward());
            m_trappedCameraFocus = GameManager.Me.TerrainPointFromRay(ray);
            m_hasCalculatedTrappedCameraFocus = true;
        }

        return m_trappedCameraFocus;
    }

    public bool CheckTriggered()
    {
        return m_currentChainLength >= m_maxChainLength && IsBallHeld();
    }

    public void SetLoadState(TrapState _state)
    {
        if(_state == TrapState.Triggered)
        {
            _state = TrapState.Trapped;
        }
        else if(_state == TrapState.Released)
        {
            _state = TrapState.Disarmed;
        }

        m_state = _state;
    }
    
    private void SetTargetHandBone()
    {
        Transform fingerbone = PlayerHandManager.Me.Hand.transform.FindChildRecursiveByName("Bone.Middle.Metacarpel");
        if (fingerbone == null)
        {
            Debug.LogError("SetTargetHandBone : m_targetHandBone == null");
        }
        else
        {
            GameObject handTargetBone = new GameObject("Handtarget");
            
            handTargetBone.transform.SetParent(fingerbone, false);
            
            handTargetBone.transform.localPosition = new Vector3(-0.00005f, 0.00186f, -0.00003f);
            // handTargetBone.transform.localRotation = Quaternion.Euler(0f, 90f, 0f);
            handTargetBone.transform.localScale = new Vector3(3f, 3f, 3f);

            m_targetHandBone = handTargetBone.transform;
        }
    }
    
    private void SpawnBloodFromHit()
    {
        // var normal = (transform.position.GetXZ() - m_targetHandBone.position.GetXZ()).normalized;
        // normal = Vector3.Lerp(normal, Vector3.up, 0.5f);
        //
        // MABloodControl.Me.EmitBloodAtTransform(m_targetHandBone.position, normal, m_targetHandBone, 5);
    }
    
    public void OnBladeStrikeEvent()
    {
        // Blood doesn't seem to work like thisss
        //PlayerHandManager.Me.Hand.gameObject.AddComponent<BloodEmitter>();s
        //MABloodControl.Me.EmitBlood(m_handTarget.position, Vector3.up, 5);
        // MABloodControl.Me.EmitBlood(m_handTarget.position, Vector3.up, 5);
       
        var bloodEmitter = m_targetHandBone.gameObject.AddComponent<BloodEmitter>();
        bloodEmitter.Play(m_bloodEmitterSettingsBladeStartEnterHand);

        //MABloodControl.Me.EmitBlood(m_handTarget.position, Vector3.up + Random.insideUnitCircle.GetVector3XZ() * 0.25f, 5);//, PlayerHandManager.Me.Hand, 5);

        PlayerHandManager.Me.SetScarAmount(1.0f, 1.0f);
    }

    public void OnBladeVFXEvent()
    {
        if (m_bladeVFX != null)
        {
            var bladeFX = Instantiate(m_bladeVFX, m_bladeVFXParent);
            bladeFX.transform.localPosition = Vector3.zero;
            bladeFX.transform.SetLossyScale(new Vector3(1.0f, 1.0f, 1.0f));
        }

        if (m_dustVFX != null)
        {
            var dustFX = Instantiate(m_dustVFX, m_bladeVFXParent);
            dustFX.transform.localPosition = Vector3.zero;
            dustFX.transform.SetLossyScale(new Vector3(1.0f, 1.0f, 1.0f));
        }

        if (m_grassVFX != null)
        {
            var grassFX = Instantiate(m_grassVFX, m_bladeVFXParent);
            grassFX.transform.localPosition = Vector3.zero;
            grassFX.transform.SetLossyScale(new Vector3(1.0f, 1.0f, 1.0f));
        }
    }

    public void OnBladeVFXStopEvent()
    {
        var particleSystems = m_bladeVFXParent.GetComponentsInChildren<ParticleSystem>();

        foreach(var particleSystem in particleSystems)
        {
            particleSystem.Stop();
        }
    }

    public void OnPartialRelease()
    {
        var bloodEmitter = m_targetHandBone.gameObject.AddComponent<BloodEmitter>();
        bloodEmitter.Play(m_bloodEmitterSettingsPartialRelease);
        
        m_bladeAnim.SetTrigger(m_partialID);
        m_trapDeactivateFailedAudio.Play(gameObject);
        PlayerHandManager.Me.TriggerPartialReleaseAnim();
    }

    private IEnumerator Co_LoadNextFrame()
    {
        yield return null;
        LoadState();
    }

    private void LoadState()
    {
        switch (m_state)
        {
            case TrapState.Trapped:
                m_handCurrent.position = m_handTarget.position - GetHandOffset(m_handTarget);
                m_handCurrent.rotation = m_handTarget.rotation;
                m_handCurrent.localScale = m_handTarget.localScale;
                PlayerHandManager.Me.SetTrapped(m_handCurrent, null);

                m_cameraCurrent.position = m_cameraTarget.position;
                m_cameraCurrent.rotation = m_cameraTarget.rotation;
                GameManager.Me.SetCameraOverrideTransform(m_cameraCurrent);

                m_bladeAnim.SetInteger(m_stateID, 1);

                m_ball.GetComponent<Rigidbody>().isKinematic = true;
                m_ball.position = GetBallEndPos();
                ShowBallEffects(false);

                m_trapRelease.State = MAQuestStigmataTrapRelease.TrapReleaseState.Active;

                m_currentChainLength = 0.0f;
                break;
            case TrapState.Disarmed:
            case TrapState.Last:
                m_bladeAnim.SetInteger(m_stateID, 0);

                m_ball.GetComponent<Rigidbody>().isKinematic = true;
                m_ball.position = GetBallEndPos();
                ShowBallEffects(false);

                m_trapRelease.State = MAQuestStigmataTrapRelease.TrapReleaseState.Disabled;

                m_currentChainLength = 0.0f;
                break;
            default:
                break;
        }

        // Input handler component should be set up by now
        IsBallPickupEnabled = m_isBallPickupEnabled;
    }

    private void TriggeredSequence()
    {
        if (m_triggeredCoroutine == null)
        {
            m_triggeredCoroutine = StartCoroutine(Co_TriggeredSequence());
        }
    }

    private IEnumerator Co_TriggeredSequence()
    {
        m_handCurrent.position = PlayerHandManager.Me.Hand.position;
        m_handCurrent.rotation = PlayerHandManager.Me.Hand.rotation;
        m_handCurrent.localScale = PlayerHandManager.Me.Hand.localScale;

        m_cameraCurrent.position = Camera.main.transform.position;
        m_cameraCurrent.rotation = Camera.main.transform.rotation;

        Vector3 handBallOffset = m_handCurrent.position - m_ball.position;
        Vector3 ballStartPos = m_ball.position;
        Quaternion ballStartRot = m_ball.rotation;
        Vector3 ballEndPos = m_groundHole.position + Vector3.up * m_ballRadius;

        m_ball.GetComponent<NGDecorationInputHandler>().CanCreateExplosion = false;
        PlayerHandManager.Me.SetTrapped(m_handCurrent, m_ball.gameObject);
        GameManager.Me.SetCameraOverrideTransform(m_cameraCurrent);
        KeyboardShortcutManager.Me.PopShortcuts(KeyboardShortcutManager.EShortcutType.HeldObjects);

        // Trap triggered sound and wait
        m_trapActivatedAudio.Play(gameObject);

        // Wait for a frame to set ball as kinematic
        yield return null;
        m_ball.GetComponent<Rigidbody>().isKinematic = true;
        m_ball.position = ballStartPos;
        m_ball.rotation = ballStartRot;

        // yield return new WaitForSeconds(m_trapSoundWaitTime);

        // Camera zooms to ball
        Vector3 cameraStartPos = m_cameraCurrent.position;
        Quaternion cameraStartRot = m_cameraCurrent.rotation;
        GetTrackCameraTarget(out Vector3 cameraEndPos, out Quaternion cameraEndRot);

        float cameraLerpValue = 0.0f;
        float cameraLerpRate = 1.0f / m_cameraBallTime;

        float focusLerpValue = 0.0f;
        float focusLerpRate = 1.0f / m_cameraFocusTime;

        while (cameraLerpValue < 1.0f)
        {
            UpdateLerpCamera(ref cameraLerpValue, cameraLerpRate, cameraStartPos, cameraEndPos, cameraStartRot, cameraEndRot);
            UpdateCameraFocus(ref focusLerpValue, focusLerpRate, m_ball);
            yield return null;
        }

        // Pull in ball
        float lerpValue = 0.0f;
        float lerpRate;   
        float time = 0.0f;
        cameraLerpValue = 0.0f;
        cameraLerpRate = 1.0f / m_triggeredCameraTargetTime;
        cameraEndPos = m_cameraTarget.position;
        cameraEndRot = m_cameraTarget.rotation;

        while (lerpValue < 1.0f)
        {
            float t = Mathf.Repeat(time, m_pullPeriod) / m_pullPeriod;
            float accel = Mathf.Pow(t, m_pullAcceleration);
            float pullAmount = Mathf.Lerp(m_pullInitialAcceleration, 1.0f, accel);
            
            lerpRate = pullAmount * m_maxPullSpeed;
            time += Time.deltaTime;
            lerpValue += lerpRate * Time.deltaTime;
            lerpValue = Mathf.Clamp01(lerpValue);

            Vector3 lerpPos = Vector3.Lerp(ballStartPos, ballEndPos, lerpValue);
            Vector3 groundPos = lerpPos.GroundPosition(m_ballRadius);

            if(lerpPos.y < groundPos.y)
            {
                lerpPos.y = groundPos.y;
            }

            m_ball.position = lerpPos;
            m_handCurrent.position = m_ball.position + handBallOffset;

            if (lerpValue < m_startCameraLerpValue)
            {
                UpdateTrackCamera(lerpValue);

                cameraStartPos = m_cameraCurrent.position;
                cameraStartRot = m_cameraCurrent.rotation;
            }
            else if(cameraLerpValue < 1.0f)
            {
                // Start lerp camera
                UpdateLerpCamera(ref cameraLerpValue, cameraLerpRate, cameraStartPos, cameraEndPos, cameraStartRot, cameraEndRot);
                UpdateCameraFocus(ref focusLerpValue, focusLerpRate, m_ball);
            }

            yield return null;
        }

        PlayerHandManager.Me.SetTrapped(m_handCurrent, null);

        ballStartPos = m_ball.position;
        ballEndPos = GetBallEndPos();

        Vector3 handStartPos = m_handCurrent.position;
        Quaternion handStartRot = m_handCurrent.rotation;
        Vector3 handStartScale = m_handCurrent.localScale;

        Vector3 handEndPos = m_handTarget.position - GetHandOffset(m_handTarget);
        Quaternion handEndRot = m_handTarget.rotation;
        Vector3 handEndScale = m_handTarget.localScale;

        lerpValue = 0.0f;
        lerpRate = 1.0f / m_handToGroundTime;

        float rotateLerpValue = 0.0f;
        float rotateLerpRate = 1.0f / m_handRotateTime;

        focusLerpRate = -1.0f / m_cameraFocusTime;

        m_trapTriggeredAudio.Play(gameObject);

        bool hasBladeTriggered = false;
        bool hasPlayedImpactVFX = false;
        m_currentChainLength = 0.0f;

        // Pull hand to ground
        while (lerpValue < 1.0f)
        {
            lerpValue += lerpRate * Time.deltaTime;
            lerpValue = Mathf.Clamp01(lerpValue);
            float t = EaseInOutQuad(lerpValue);

            rotateLerpValue += rotateLerpRate * Time.deltaTime;
            rotateLerpValue = Mathf.Clamp01(rotateLerpValue);
            float rotateT = EaseInOutQuad(rotateLerpValue);

            m_ball.position = Vector3.Lerp(ballStartPos, ballEndPos, t);

            if (t > 0.9f)
            {
                ShowBallEffects(false);
            }

            m_handCurrent.position = Vector3.Lerp(handStartPos, handEndPos, t);
            m_handCurrent.rotation = Quaternion.Slerp(handStartRot, handEndRot, rotateT);
            m_handCurrent.localScale = Vector3.Lerp(handStartScale, handEndScale, t);

            // Continue lerp camera if required
            if (cameraLerpValue < 1.0f || focusLerpValue > 0.0f)
            {
                UpdateLerpCamera(ref cameraLerpValue, cameraLerpRate, cameraStartPos, cameraEndPos, cameraStartRot, cameraEndRot);
                UpdateCameraFocus(ref focusLerpValue, focusLerpRate, m_ball);
            }

            if(!hasBladeTriggered && lerpValue >= m_trapBladeTriggerValue)
            {
                m_bladeAnim.SetTrigger(m_triggerID);
                m_bladeAnim.SetInteger(m_stateID, 1);
                m_trapRelease.State = MAQuestStigmataTrapRelease.TrapReleaseState.Active;
                hasBladeTriggered = true;
            }

            if(!hasPlayedImpactVFX && lerpValue >= m_playImpactVFXValue)
            {
                if(m_impactVFX != null) Instantiate(m_impactVFX, m_ball.position, Quaternion.identity, m_ball.parent);
                AudioClipManager.Me.PlaySound("PlaySound_RockImpact", gameObject);
                hasPlayedImpactVFX = true;
            }

            yield return null;          
        }

        // Finish lerp camera if required
        while (cameraLerpValue < 1.0f || focusLerpValue > 0.0f)
        {
            UpdateLerpCamera(ref cameraLerpValue, cameraLerpRate, cameraStartPos, cameraEndPos, cameraStartRot, cameraEndRot);
            UpdateCameraFocus(ref focusLerpValue, focusLerpRate, m_ball);
            yield return null;
        }

        State = TrapState.Trapped;

        m_triggeredCoroutine = null;

        yield return null;
    }

    private void ReleasedSequence()
    {
        if (m_releasedCoroutine == null)
        {
            m_releasedCoroutine = StartCoroutine(Co_ReleasedSequence());
        }
    }

    private IEnumerator Co_ReleasedSequence()
    {
        m_trapDeactivatedAudio.Play(gameObject);
        
        m_cameraCurrent.position = Camera.main.transform.position;
        m_cameraCurrent.rotation = Camera.main.transform.rotation;

        MACharacterBase character = m_trapRelease.ReleasingCharacter;
        character.m_nav.PushPause("ReleaseSequence");

        LookAtTarget(character, m_targetHandBone);
        
        yield return new WaitForSeconds(0.8f);

        GameManager.Me.Unpossess(false);
    
        //character.SetMoveToPosition(m_trapRelease.transform.position, true);

        Vector3 cameraStartPos = m_cameraCurrent.position;
        Quaternion cameraStartRot = m_cameraCurrent.rotation;

        Vector3 cameraEndPos = m_cameraTarget.position;
        Quaternion cameraEndRot = m_cameraTarget.rotation;

        float cameraLerpValue = 0.0f;
        float cameraLerpRate = 1.0f / m_releasedCameraTargetTime;

        // Move camera to target
        while (cameraLerpValue < 1.0f)
        {
            UpdateLerpCamera(ref cameraLerpValue, cameraLerpRate, cameraStartPos, cameraEndPos, cameraStartRot, cameraEndRot);
            yield return null;
        }

        m_trapReleasedAudio.Play(gameObject);

        m_bladeAnim.SetTrigger(m_triggerID);
        m_bladeAnim.SetInteger(m_stateID, 0);
        m_trapRelease.State = MAQuestStigmataTrapRelease.TrapReleaseState.Disabled;

        yield return new WaitForSeconds(0.75f);

        PlayerHandManager.Me.SetInStigmataAnim(false);

        yield return new WaitForSeconds(1.0f);
        
        //blade start to move out of hand
        var bloodEmitterBladeReleaseStart = m_targetHandBone.gameObject.AddComponent<BloodEmitter>();
        bloodEmitterBladeReleaseStart.Play(m_bloodEmitterSettingsBladeStartRelease);

        yield return new WaitForSeconds(1.4f);
        
        //blade's tip leaves hand
        var bloodEmitterBladeReleaseFinished = m_targetHandBone.gameObject.AddComponent<BloodEmitter>();
        bloodEmitterBladeReleaseFinished.Play(m_bloodEmitterSettingsBladeEndRelease);

        yield return new WaitForSeconds(0.85f);

        cameraStartPos = m_cameraCurrent.position;
        cameraStartRot = m_cameraCurrent.rotation;

        cameraEndPos = m_cameraTargetFinal.position;
        cameraEndRot = m_cameraTargetFinal.rotation;

        cameraLerpValue = 0.0f;
        cameraLerpRate = 1.0f / m_releasedCameraTargetFinalTime;

        //hand shaking
        var handShakesOffBlood = m_targetHandBone.gameObject.AddComponent<BloodEmitter>();
        handShakesOffBlood.Play(m_bloodEmitterSettingsHandShake);

        // Move camera to final target
        while (cameraLerpValue < 1.0f)
        {
            UpdateLerpCamera(ref cameraLerpValue, cameraLerpRate, cameraStartPos, cameraEndPos, cameraStartRot, cameraEndRot);
            yield return null;
        }
        
        yield return new WaitForSeconds(8.0f);

        GameManager.Me.SetCameraOverrideTransform(null);

        // return to regular hand transform
        float lerpValue = 1.0f;
        float lerpRate = 1.0f / m_handReturnTime;

        while (lerpValue > 0.0f)
        {
            lerpValue -= lerpRate * Time.deltaTime;
            PlayerHandManager.Me.m_trappedTransformWeight = lerpValue;

            yield return null;
        }

        PlayerHandManager.Me.SetTrapped(null);  

        LookAtTarget(character, null);
        character.m_nav.PopPause("ReleaseSequence");

        State = TrapState.Disarmed;

        m_releasedCoroutine = null;

        yield return null;
    }

    private float EaseInOutQuad(float x)
    {
        return x < 0.5f ? 2.0f * x * x : 1 - Mathf.Pow(-2.0f * x + 2.0f, 2.0f) / 2.0f;
    }

    private Vector3 GetHandOffset(Transform _handTransform)
    {
        Vector3 additionalOffset = new Vector3(0.15f, 0.0f, -0.15f);
        return _handTransform.rotation * Vector3.Scale(PlayerHandManager.Me.m_cursor3DOffset + additionalOffset, _handTransform.localScale);
    }

    private void LookAtTarget(MACharacterBase _character, Transform _transform)
    {
        HeadTracker ht = _character?.GetComponent<HeadTracker>();
        if (ht != null)
        {
            ht.Override(_transform);
        }
        
        if(_transform == null) return;
        
        Transform head = _character.transform.FindChildRecursiveByName("Head_Attach");
        if(head != null)
        {
            var dir = _transform.position - head.position;
            if (!ht.CanLookAt(dir))
            {
                if (_character != null && _transform != null)
                {
                    _character?.LookAt(_transform.position, 180.0f);
                }
            }
        }
    }

    private void UpdateTrackCamera(float _tiltValue)
    {
        GetTrackCameraTarget(out Vector3 targetCameraPos, out Quaternion targetCameraRot);
        float tiltAmount = Mathf.Sin(_tiltValue * Mathf.PI * 4.0f);
        Quaternion tiltRotation = Quaternion.Euler(0.0f, 0.0f, tiltAmount * m_cameraTrackMaxTilt);
        targetCameraRot *= tiltRotation;
        m_cameraCurrent.position = Vector3.Lerp(m_cameraCurrent.position, targetCameraPos, m_cameraTrackLerpRate * Time.deltaTime);
        m_cameraCurrent.rotation = Quaternion.Slerp(m_cameraCurrent.rotation, targetCameraRot, m_cameraTrackLerpRate * Time.deltaTime);     
    }

    private void UpdateLerpCamera(ref float _lerpValue, float _lerpRate, Vector3 _startPos, Vector3 _endPos, Quaternion _startRot, Quaternion _endRot)
    {
        _lerpValue += _lerpRate * Time.deltaTime;
        _lerpValue = Mathf.Clamp01(_lerpValue);
        float t = EaseInOutQuad(_lerpValue);

        m_cameraCurrent.position = Vector3.Lerp(_startPos, _endPos, t);
        m_cameraCurrent.rotation = Quaternion.Slerp(_startRot, _endRot, t);
    }

    private void UpdateCameraFocus(ref float _lerpValue, float _lerpRate, Transform _focus)
    {
        _lerpValue += _lerpRate * Time.deltaTime;
        _lerpValue = Mathf.Clamp01(_lerpValue);
        float t = EaseInOutQuad(_lerpValue);

        Quaternion focusRotation = Quaternion.LookRotation((_focus.position - m_cameraCurrent.position).normalized);
        m_cameraCurrent.rotation = Quaternion.Slerp(m_cameraCurrent.rotation, focusRotation, t);
    }

    private void GetTrackCameraTarget(out Vector3 _position, out Quaternion _rotation)
    {
        Vector3 holeToBall = (m_ball.position - m_groundHole.position).GetXZNorm();
        _position = m_ball.position + holeToBall * m_cameraTrackDistance + Vector3.up * m_cameraTrackHeight;
        _rotation = Quaternion.LookRotation((m_ball.position - _position).normalized);
    }

    private Vector3 GetBallEndPos()
    {
        return m_groundHole.position - Vector3.up * (m_ballRadius * 1.5f);
    }

    public bool IsBallHeld()
    {
        if(m_ballPickup == null)
        {
            m_ballPickup = m_ball.GetComponent<PickupAndThrowBehaviour>();
        }

        return m_ballPickup != null && m_ballPickup.Held;
    }

    private void SetJointLimit(float _limit)
    {
        var linearLimit = m_joint.linearLimit;
        linearLimit.limit = _limit;
        m_joint.linearLimit = linearLimit;
    }

    private int GetRequiredLinks(float _length)
    {
        return Mathf.CeilToInt(_length / m_linkLength);
    }

    private void ShowBallEffects(bool _show)
    {
        m_ballEffectsHolder.SetActive(_show);
    }
}
