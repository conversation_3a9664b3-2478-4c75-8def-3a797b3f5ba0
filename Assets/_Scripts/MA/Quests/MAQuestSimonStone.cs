using UnityEditor.Rendering;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;

public class MAQuestSimonStone : ReactPickupPersistent, IPointerDownHandler
{
    [Header("Quest Stone")]
    public AkEventHolder m_correctAudioEvent = new ();
    public AkEventHolder m_wrongAudioEvent = new ();
    public int m_stoneNum;
    public Animator m_animator;
    private int m_wobbleID;
    private int m_shakeID;
    public MAQuestStoneVFX m_stoneVFX;   
    public MAQuestSimonStones m_questSimonStones; 
    
    protected void Awake() 
    {
        m_wobbleID = Animator.StringToHash("Wobble");
        m_shakeID = Animator.StringToHash("Shake");
    }
    
    public enum StonesAnimState
    {
        Correct,
        Wrong,
        NoAnim,
        NoSound,
        Count
    }

    void Start()
    {
        transform.localScale = new Vector3(2.8f, 3.2f, 2.8f);
    }

    public void PlayAnim(StonesAnimState _animState)
    {
        switch (_animState)
        {
            case StonesAnimState.Correct:
                m_animator.SetTrigger(m_wobbleID);
                m_stoneVFX.Play();
                m_correctAudioEvent.Play(gameObject);
                break;
            case StonesAnimState.Wrong:
                m_animator.SetTrigger(m_shakeID);
                m_stoneVFX.PlayNoise();
                m_wrongAudioEvent.Play(gameObject);
                break;
            case StonesAnimState.NoAnim:
                m_correctAudioEvent.Play(gameObject);
                break;
            case StonesAnimState.NoSound:
                m_animator.SetTrigger(m_wobbleID);
                m_stoneVFX.Play();
                break;
        }
    }
    
    public void OnPointerDown(PointerEventData eventData)
    {
        if(GameManager.Me.CameraExceededMaxInteractionDistance()) return;
        PlayAnim(m_questSimonStones.ReceiveStoneTap(m_stoneNum));
    }
    
    // Required to maintain animation compatibility with other stones quest
    public void OnSoundAnimEvent()
    {
    }
}
