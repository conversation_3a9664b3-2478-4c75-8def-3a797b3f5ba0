using UnityEngine;
using UnityEngine.Events;
using UnityEngine.EventSystems;
using UnityEngine.Serialization;

public class MAQuestInteraction : MonoBehaviour, ICharacterObjectInteract, IPointerClickHandler
{
    public bool m_useCharacterInteract = true;
    public bool m_useHandInteract = false;

    public string m_label;
    public GameObject m_glintObject;
    public float m_highlightDistance = 4.0f;
    private NGMovingObject m_targetObject;
    private bool m_isInteractive = false;
    private bool m_shouldHideOutline = false;
    public UnityEvent m_onInteract;
    [FormerlySerializedAs("m_isCollected")] public bool m_hasInteracted = false;
    public AkEventHolder m_highlightSound = new AkEventHolder();


    public float AutoInteractTime => 0;

    private void Update()
    {
        if (m_isInteractive && m_targetObject != null && !ShouldHideOutline() &&
            (m_targetObject.gameObject.transform.position - transform.position).sqrMagnitude <= m_highlightDistance * m_highlightDistance)
        {
            if (gameObject.layer != LayerMask.NameToLayer("Outline"))
            {
                if (m_highlightSound != null)
                {
                    m_highlightSound.Play(gameObject);
                }

                gameObject.SetLayerRecursively(LayerMask.NameToLayer("Outline"));
            }
        }
        else if(gameObject.layer != LayerMask.NameToLayer("Default"))
        {
            gameObject.SetLayerRecursively(LayerMask.NameToLayer("Default"));
        }
    }

    public void SetGlintEnable(bool _enable)
    {
        if (m_glintObject != null)
        {
            m_glintObject.SetActive(_enable);
        }
    }

    public void SetInteractive(bool _isInteractive, NGMovingObject _targetObject = null)
    {
        m_isInteractive = _isInteractive;
        m_targetObject = _targetObject;
    }

    public void SetCollidersEnable(bool _enabled)
    {
        Collider[] colliders = GetComponentsInChildren<Collider>();
        foreach (Collider collider in colliders)
        {
            collider.enabled = _enabled;
        }
    }

    // ICharacterObjectInteract
    public string InteractType => null;

    public bool CanInteract(NGMovingObject _chr)
    {
        return m_useCharacterInteract && m_isInteractive && (m_targetObject == null || m_targetObject == _chr);
    }

    public string GetInteractLabel(NGMovingObject _chr) 
    {
        return m_label;
    }

    public void DoInteract(NGMovingObject _chr)
    {     
        m_shouldHideOutline = true;
        gameObject.SetLayerRecursively(LayerMask.NameToLayer("Default"));
        m_onInteract?.Invoke();
    }

    public bool RunInteractSequence(System.Collections.Generic.List<FollowChild> _chr) => false;
    public void EnableInteractionTriggers(bool _b) {}
    
    public void OnPointerClick(PointerEventData eventData)
    {
        if(m_useHandInteract && m_isInteractive)
        {  
            m_shouldHideOutline = true;
            gameObject.SetLayerRecursively(LayerMask.NameToLayer("Default"));
            m_onInteract?.Invoke();
        }
    }

    public void SetInteracted(bool _hasIteracted)
    {
        m_hasInteracted = _hasIteracted;
    }

    public bool HasInteracted()
    {
        return m_hasInteracted;
    }

    public bool IsInteractive()
    {
        return m_isInteractive;
    }

    private bool ShouldHideOutline()
    {
        return m_shouldHideOutline || m_hasInteracted;
    }

    public class SaveState
    {
        [Save] public int m_isInteractive;
        [Save] public int m_hasInteracted;
    }
    // End ICharacterObjectInteracts

    public SaveState GetSaveState()
    {
        SaveState saveState = new MAQuestInteraction.SaveState();

        saveState.m_isInteractive = IsInteractive() ? 1 : 0;
        saveState.m_hasInteracted = HasInteracted() ? 1 : 0;

        return saveState;
    }

    public void SetSaveState(SaveState _saveState)
    {
        SetInteractive(_saveState.m_isInteractive > 0);
        SetInteracted(_saveState.m_hasInteracted > 0);
    }
}
