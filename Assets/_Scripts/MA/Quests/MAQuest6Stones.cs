using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MaQuest6Stones : MAQuestBase
{
    public enum HippyQuestGiverState
    {
        Idle = 0,
        Gesture = 1,
        Positive = 2,
        Celebrate = 3,
        Negative,
        Disturb,
        Count
    }

    public enum HippyVOReactions
    {
        None,
        Locked<PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON>
    }

    [Header("MAQuest6Stones")]
    public List<MAQuestStone> m_stones = new List<MAQuestStone>();
    public List<MAQuestStonePad> m_stonePads = new List<MAQuestStonePad>();
    public bool m_showDebug = false;
    public MAQuestCutscene m_completeCutscene;
    public Animator m_tentAnim;
    public MAQuestCameraTransition m_dialogueCameraTransition;
    public AkSwitchHolder m_completeAudio = new();
    float m_playStoneDelay = 0.4f;
    Coroutine m_playStonesCoroutine;
    Coroutine m_stopGesturingCoroutine;
    Animator m_animator;
    int m_playTuneID;
    int m_playStonesID;
    int m_negativeID;
    int m_disturbID;
    int m_tapID;
    private bool m_wasSkipped = false;

    private bool m_isEnabled = false;

    public List<GameObject> m_questElements = new List<GameObject>();
    
    private HippyVOReactions m_hippyVOReation = HippyVOReactions.None;
    private bool m_isStoneCircleComplete = false;

    protected override void Awake()
    {
        base.Awake();
        m_animator = GetComponent<Animator>();
        m_playTuneID = Animator.StringToHash("PlayTune");
        m_playStonesID = Animator.StringToHash("PlayStones");      
        m_negativeID = Animator.StringToHash("Negative");
        m_disturbID = Animator.StringToHash("Disturb");
        m_tapID = Animator.StringToHash("Tap");
    }

    public void OnValidate()
    {
        foreach(var s in m_stones)
        {
            var tmp = s.GetComponentInChildren<TMP_Text>(true);
            var tmp2 = s.GetComponentInChildren<TextMeshPro>(true);
           tmp?.gameObject.SetActive(m_showDebug);
        }
        foreach(var s in m_stonePads)
        {
            var tmp = s.GetComponentInChildren<TMP_Text>(true);
            tmp?.gameObject.SetActive(m_showDebug);
        }
    }

    protected override void OnPostLoad()
    {
        base.OnPostLoad();
        if (m_questGiver != null)
        {
            m_questGiver.CharacterGameState.m_immortal = true;
        }
    }

    public void ClickedTent()
    {
        if(GameManager.Me.CameraExceededMaxInteractionDistance()) return;
        
        if (m_playStonesCoroutine == null)
        {
            m_playStonesCoroutine = StartCoroutine(Co_PlayStones());
        }

        m_tentAnim.SetTrigger(m_tapID);
    }

    IEnumerator Co_PlayStones()
    {
        int correctCount = 0;
        int stonesCount = 0;
        int decoysCount = 0;

        foreach (var p in m_stonePads)
        {
            var s = p.m_stone;

            if (s)
            {
                stonesCount++;
                if (s.IsDecoy())
                    decoysCount++;

                if (p.m_padNum == s.m_stoneNum)
                {
                    correctCount++;
                }
            }
        }

        if (correctCount >= m_stonePads.Count && !CheckQuestComplete())
        {
            MAQuestCutscene.s_isAnyActive = true; //KW: hide hud early if we know we are going to play the cutscene
        }

        yield return new WaitForSeconds(0.2f);
        SetQuestGiverState((int)HippyQuestGiverState.Disturb);
        yield return new WaitForSeconds(1.0f);

        SetQuestGiverState((int)HippyQuestGiverState.Positive);

        foreach (var p in m_stonePads)
        {
            var s = p.m_stone;

            if (s)
            {                
                s.PlayAnim(true, p.m_padNum == s.m_stoneNum);
                
                if (p.m_padNum != s.m_stoneNum)
                {
                    SetQuestGiverState((int)HippyQuestGiverState.Negative);
                }
            }

            yield return new WaitForSeconds(m_playStoneDelay);
        }

        ResetTriggers();

        if (correctCount >= m_stonePads.Count)
        {
            if(!CheckQuestComplete())
            {
                SetQuestGiverState((int)HippyQuestGiverState.Celebrate);
                m_wasSkipped = false;

                yield return m_completeCutscene.Co_Play(()=>
                {
                    m_wasSkipped = true;
                });

                m_isStoneCircleComplete = true;
            }
        }
        else
        {
            if (decoysCount > 0)
            {
                DoHippyVOReaction(HippyVOReactions.ContainsDecoy);
            }
            else if (stonesCount == m_stonePads.Count)
            {
                DoHippyVOReaction(HippyVOReactions.WrongOrder);
            }
        }

        SetQuestGiverState((int)HippyQuestGiverState.Idle);

        // uncomment to test cutscene
        //yield return m_completeCutscene.Co_Play();

        m_playStonesCoroutine = null;

        yield return null;
    }
    
    void SetStoneInteraction(QuestStatus _status)
    {
        foreach (var s in m_stones)
        {
            var pickup = s.GetComponent<Pickup>();
            if(pickup == null)
            {
                pickup = s.gameObject.AddComponent<Pickup>();
            }
            
            pickup.m_lockedInPlace = !(!s.m_isLocked && (_status == QuestStatus.InProgress || (_status == QuestStatus.Completed && s.IsDecoy())));
            pickup.m_onDragDetected = pickup.m_lockedInPlace ? OnStoneDragDetected : null;
            pickup.m_isGrabType = true;

            if(_status == QuestStatus.InProgress || DistrictManager.Me.IsWithinDistrictBounds(s.transform.position))
            {
                s.WasEverInDistrictBounds = true;
            }

            if (s.WasEverInDistrictBounds)
            {
                s.gameObject.IgnoreDistrictFilter();
                pickup.SetOverrideDistrictFilter(true);
            }

            s.m_shouldReset = _status != QuestStatus.Completed;
        }
    }

    private void OnStoneDragDetected(Pickup _pickup)
    {
        if (CheckQuestInProgress())
        {
            if (_pickup != null)
            {
                GameObject parentObject = _pickup.transform.gameObject;
                if (parentObject != null)
                {
                    MAQuestStone parentStone = parentObject.GetComponent<MAQuestStone>();
                    if (parentStone != null)
                    {
                        parentStone.PlayLockedAnim();
                        DoHippyVOReaction(HippyVOReactions.LockedStone);
                    }
                }
            }
            SetQuestGiverState((int)HippyQuestGiverState.Disturb);
        }
    }

    void PlayTune()
    {
        m_animator.SetTrigger(m_playTuneID);
    }

    public void OnNoteEvent(int index)
    {
        var stone = m_stones.Find(x => x.m_stoneNum == index);

        if (stone)
        {
            stone.PlayAnim(false, true);
        }
    }

    void OnPlayCompleteAudioEvent()
    {
        // m_completeAudio.Play(gameObject, AkEventHolder.EBus.Music);
        //KW: seems to be some weirdness with this animation event when cutscene is skipped
        if (!m_wasSkipped)
        {
            m_completeAudio.SetAsOverride();
        }
    }

    void OnCompleteEndEvent()
    {
        SetQuestGiverState((int)HippyQuestGiverState.Idle);
    }

    override public void SetQuestStatus(QuestStatus _status)
    {
        base.SetQuestStatus(_status);

        SetStoneInteraction(_status);
    }

    override public void SetQuestGiverState(int _questGiverState)
    {
        if (m_questGiver != null && m_questGiver.m_anim != null)
        {
            switch((HippyQuestGiverState)_questGiverState)
            {
                case HippyQuestGiverState.Negative:
                    m_questGiver.m_anim.SetTrigger(m_negativeID);
                    break;
                case HippyQuestGiverState.Disturb:
                    m_questGiver.m_anim.SetTrigger(m_disturbID);
                    break;
                default:
                    base.SetQuestGiverState(_questGiverState);
                    break;
            }
        }
    }

    private void ResetTriggers()
    {
        m_questGiver.m_anim.ResetTrigger(m_negativeID);
        m_questGiver.m_anim.ResetTrigger(m_disturbID);
    }

    protected override void OnActivateQuest()
    {
        base.OnActivateQuest();

        m_status = QuestStatus.Intro; 
        
        SetQuestGiverState((int)HippyQuestGiverState.Disturb);
        m_dialogueCameraTransition.Transition();
    }

    public override void OnDisplayMessage(MAMessage _message)
    {
        base.OnDisplayMessage(_message);

        if(_message.m_advisor == "Hippy" && _message.m_audioID.IsNullOrWhiteSpace() == false)
        {
            if (m_stopGesturingCoroutine != null)
            {
                StopCoroutine(m_stopGesturingCoroutine);
                m_stopGesturingCoroutine = null;
            }

            SetQuestGiverState((int)HippyQuestGiverState.Gesture);
        }
    }

    public override void OnDestroyMessage(MAMessage _message)
    {
        base.OnDestroyMessage(_message);

        //KW: probably update this now we have the message to check
        if (m_questGiver.m_anim.GetInteger(m_stateID) == (int)HippyQuestGiverState.Gesture && m_stopGesturingCoroutine == null)
        {
            m_stopGesturingCoroutine = StartCoroutine(Co_StopGesturing());
        }
    }

    IEnumerator Co_StopGesturing()
    {
        yield return new WaitForSeconds(0.3f);
        SetQuestGiverState((int)HippyQuestGiverState.Idle);
        m_stopGesturingCoroutine = null;
    }

    override protected void SetQuestGiverParent()
    {
        base.SetQuestGiverParent();

        if (m_questGiver != null)
        {
            GetComponent<MAMeshClickDetector>().AddCollider(m_questGiver.m_bodyToBodyCollider);          
        }
    }

    private void DoHippyVOReaction(HippyVOReactions _voReaction)
    {
        m_hippyVOReation = _voReaction;
        MAParserManager.Me.ExecuteSection("Quests/Quest6StonesReactions"); 
    }

    private void EnableQuestElements(bool _enable)
    {
        m_isEnabled = _enable;
        foreach (var item in m_questElements)
        {
            item.SetActive(m_isEnabled);
        }
    }
    
    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("LockedStone"))
        {
            return m_hippyVOReation == HippyVOReactions.LockedStone ? 0.0f : 1.0f;
        }

        if (_objective.Contains("ContainsDecoy"))
        {
            return m_hippyVOReation == HippyVOReactions.ContainsDecoy ? 0.0f : 1.0f;
        }

        if (_objective.Contains("WrongOrder"))
        {
            return m_hippyVOReation == HippyVOReactions.WrongOrder ? 0.0f : 1.0f;
        }
        if (_objective.Contains("CircleComplete"))
        {
            return m_isStoneCircleComplete ? 0.0f : 1.0f;
        }

        return 1.0f;
    }
    
    public override void TriggerQuestEvent(string _event)
    {
        base.TriggerQuestEvent(_event);

        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("HideQuestElements"))
        {
            EnableQuestElements(false);
        }
        else if (split[0].Contains("ShowQuestElements"))
        {
            EnableQuestElements(true);
        }
    }

    public static MaQuest6Stones Create(Transform _holder)
    {
        var prefab = Resources.Load<MaQuest6Stones>("_Prefabs/Quests/MAQuest6Stones");
        if (prefab == null)
        {
            Debug.LogError(MAParserSupport.DebugColor($"quest prefab {nameof(MaQuest6Stones)} not found in Resources/Quests "));
            return null;
        }
        var instance = Instantiate(prefab, _holder);
        return instance;
    }
    public class SaveQuest6Stones
    { 
        public SaveQuest6Stones() { }
        public SaveQuest6Stones(MAQuestStone _stone)
        {
            m_stoneNum = _stone.m_stoneNum;
            m_position = _stone.transform.position;
            m_rotation = _stone.transform.eulerAngles;
            m_onPad = (_stone.m_onPad == null) ? -1 : _stone.m_onPad.m_padNum;
            m_wasEverInDistrictBounds = (_stone.WasEverInDistrictBounds || DistrictManager.Me.IsWithinDistrictBounds(_stone.transform.position))?1:0;
        }
        [Save] public int m_stoneNum;
        [Save] public Vector3 m_position;
        [Save] public Vector3 m_rotation;
        [Save] public int m_onPad;
        [Save] public int m_wasEverInDistrictBounds;
    }
    public class SaveLoadQuest6StonesContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuest6StonesContainer() : base() { }  
        public SaveLoadQuest6StonesContainer(MAQuestBase _base) : base(_base) { }
        [Save] public List<SaveQuest6Stones> m_stones = new List<SaveQuest6Stones>();
        [Save] public int m_isEnabled;
    }
    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuest6StonesContainer(this);
        foreach (var s in m_stones)
        {
            saveContainer.m_stones.Add(new SaveQuest6Stones(s));
        }

        saveContainer.m_isEnabled = m_isEnabled  ? 1 : 0;
        return saveContainer;
    }
    
    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        var saveContainer =_l as SaveLoadQuest6StonesContainer;
        if (saveContainer?.m_stones == null) return;
        base.Load(_l);
        foreach (var s in saveContainer.m_stones)
        {
            var stone = m_stones.Find(x => x.m_stoneNum == s.m_stoneNum);
            if (stone)
            {
                stone.transform.position = s.m_position;
                stone.transform.eulerAngles = s.m_rotation;
                if (s.m_onPad != -1)
                {
                    stone.SetOnPad(m_stonePads.Find(x => x.m_padNum == s.m_onPad), false);
                }

                stone.WasEverInDistrictBounds = s.m_wasEverInDistrictBounds != 0;
            } 
        }

        EnableQuestElements(saveContainer.m_isEnabled > 0);
    }
}
