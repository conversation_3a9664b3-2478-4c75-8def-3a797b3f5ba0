using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Serialization;

public class MAPickupHolder : MonoBehaviour
{
    private class MAPickupItem
    {
        public GameObject m_item;
        public Vector3 m_startPos;
        public Quaternion m_startRot;
        public Transform m_parent;
        public float m_translateProgress = 0.0f;
        public float m_rotateProgress = 0.0f;
        public float m_rotateSpeed = 180.0f;
        public float m_rotateTime = 2.0f;
        public bool m_uncrumple = false;
        public float m_crumpleProgress = 0.0f;
        public bool m_toBeRemoved = false;
        public Action m_onComplete;
        public SkinnedMeshRenderer m_renderer;
        public Animator m_animator;
        public bool m_collectAnimPlayed = false;
        public PlayableDirector m_playableDirector;
    };
    
    private class MAFadeInItem
    {
        public GameObject m_item;
        public Transform m_parent;
        public float m_fadeInProgress = 0.0f;
        public Action m_onComplete;
        public Renderer m_renderer;
    };

    private List<MAPickupItem> m_pickupItems = new List<MAPickupItem>();
    private List<MAFadeInItem> m_fadeInItems = new List<MAFadeInItem>();

    public float m_translateTime = 0.5f;
    public float m_fadeInTime = 0.5f;
    
    public Animator m_animator;
    private bool m_animationComplete = false;
    
    private static readonly Vector3 m_possessionDistance = new Vector3(0.0f, 0.0f, 1.2f);
    private static readonly Vector3 m_godhandDistance = new Vector3(0.0f, 0.0f, 3.0f);
    
    private const float c_crumpleTime = 0.55f;
    
    public AkEventHolder m_pickupCollected = new ();
    public AkEventHolder m_notePartUncrumpled = new ();
    
    void Update()
    {
        // Adjust distance for god hand / possession mode
        // This needs to update as player may switch during collection
        if (GameManager.Me.PossessedCharacter != null)
        {
            transform.localPosition = m_possessionDistance;
        }
        else
        {
            transform.localPosition = m_godhandDistance;
        }
        
        foreach (var pickupItem in m_pickupItems)
        {
            if(pickupItem.m_translateProgress < 1.0f)
            {
                pickupItem.m_translateProgress += Time.deltaTime / m_translateTime;
                pickupItem.m_translateProgress = Mathf.Clamp01(pickupItem.m_translateProgress);
                pickupItem.m_item.transform.position = Vector3.Lerp(pickupItem.m_startPos, transform.position, pickupItem.m_translateProgress);
                pickupItem.m_item.transform.rotation = Quaternion.Lerp(pickupItem.m_startRot, transform.rotation, pickupItem.m_translateProgress);

                if (pickupItem.m_uncrumple)
                {
                    pickupItem.m_renderer.SetBlendShapeWeight(0, 100);
                }
            }
            else if (pickupItem.m_animator != null || pickupItem.m_playableDirector != null)
            {
                if (!pickupItem.m_collectAnimPlayed)
                {
                    if (pickupItem.m_animator != null)
                    {
                        pickupItem.m_animator.SetTrigger("Collect");
                    }
                    else if(pickupItem.m_playableDirector != null)
                    {
                        pickupItem.m_playableDirector.Play();
                    }
                    pickupItem.m_collectAnimPlayed = true;
                }
            }
            else
            {
                if (pickupItem.m_uncrumple && pickupItem.m_crumpleProgress <= 0.001f)
                {
                    m_notePartUncrumpled.Play(transform.gameObject);
                }
                pickupItem.m_item.transform.position = transform.position;
                pickupItem.m_rotateProgress += Time.deltaTime / pickupItem.m_rotateTime;
                pickupItem.m_crumpleProgress += Time.deltaTime / c_crumpleTime;
                pickupItem.m_rotateProgress = Mathf.Clamp01(pickupItem.m_rotateProgress);
                float rotateSpeed = pickupItem.m_rotateSpeed * Time.deltaTime;
                pickupItem.m_item.transform.Rotate(new Vector3(0.0f, pickupItem.m_rotateSpeed * Time.deltaTime, 0.0f));
                
                if (pickupItem.m_uncrumple)
                {
                    float crumpleVal = 100 * Mathf.Max(1.0f - pickupItem.m_crumpleProgress, 0.0f);
                    pickupItem.m_renderer.SetBlendShapeWeight(0, crumpleVal);
                }
            }
        }
        
        foreach (var fadeInItem in m_fadeInItems)
        {
            if(fadeInItem.m_fadeInProgress < 1.0f)
            {
                fadeInItem.m_fadeInProgress += Time.deltaTime / m_fadeInTime;
                fadeInItem.m_fadeInProgress = Mathf.Clamp01(fadeInItem.m_fadeInProgress);
                float alpha = Mathf.Lerp(0f, 1f, fadeInItem.m_fadeInProgress / m_fadeInTime);
                float easedAlpha = alpha * alpha * (3f - 2f * alpha);
                fadeInItem.m_renderer.material.SetFloat("_AlphaMultiply", easedAlpha);
            }
            else
            {
                fadeInItem.m_renderer.material.SetFloat("_AlphaMultiply", 1.0f);
            }
        }
        
        for(int i = m_pickupItems.Count - 1; i >= 0; i--)
        {
            if(m_pickupItems[i].m_rotateProgress >= 1.0f || m_animationComplete ||
                (m_pickupItems[i].m_playableDirector != null && m_pickupItems[i].m_playableDirector.time >= m_pickupItems[i].m_playableDirector.duration))
            {
                // note fragments should not blip out
                if (m_pickupItems[i].m_uncrumple || m_animationComplete)
                {
                    m_pickupItems[i].m_toBeRemoved = true;
                    RemoveExpiredPickups();
                    RemoveFadeInItems();
                }
                else
                {
                    m_pickupItems[i].m_toBeRemoved = true;
                    m_animator.SetTrigger("BlipOut");
                }
            }
        }
    }
    
    private void RemoveExpiredPickups()
    {
        for(int i = m_pickupItems.Count - 1; i >= 0; i--)
        {
            if(m_pickupItems[i].m_toBeRemoved)
            {
                m_pickupItems[i].m_item.transform.position = m_pickupItems[i].m_startPos;
                m_pickupItems[i].m_item.transform.rotation = m_pickupItems[i].m_startRot;
                m_pickupItems[i].m_item.transform.SetParent(m_pickupItems[i].m_parent, true);
                m_pickupItems[i].m_item.SetActive(false);
                m_pickupItems[i].m_onComplete?.Invoke();
                m_pickupItems.RemoveAt(i);
            }
        }
    }

    private void RemoveFadeInItems()
    {
        for(int i = m_fadeInItems.Count - 1; i >= 0; i--)
        {
            m_fadeInItems[i].m_item.transform.SetParent(m_fadeInItems[i].m_parent, true);
            m_fadeInItems[i].m_item.SetActive(false);
            m_fadeInItems[i].m_onComplete?.Invoke();
            m_fadeInItems.RemoveAt(i);
        }
    }
    
    public void Pickup(GameObject _gameObject, Action _onComplete, Animator _animator)
    {
        MAPickupItem pickupItem = CreatePickup(_gameObject, _onComplete);
        pickupItem.m_animator = _animator;
        pickupItem.m_collectAnimPlayed = false;
        m_animationComplete = false;
        m_pickupItems.Add(pickupItem);
    }

    public void Pickup(GameObject _gameObject, Action _onComplete, PlayableDirector _playableDirector)
    {
        MAPickupItem pickupItem = CreatePickup(_gameObject, _onComplete);
        pickupItem.m_playableDirector = _playableDirector;
        pickupItem.m_collectAnimPlayed = false;
        m_animationComplete = false;
        m_pickupItems.Add(pickupItem);
    }

    public void Pickup(GameObject _gameObject, Action _onComplete, float _rotateSpeed, float _rotateTime, bool _uncrumple = false)
    {
        MAPickupItem pickupItem = CreatePickup(_gameObject, _onComplete);
        pickupItem.m_rotateSpeed = _rotateSpeed;
        pickupItem.m_rotateTime = _rotateTime;
        pickupItem.m_uncrumple = _uncrumple;

        if (_uncrumple)
        {
            pickupItem.m_renderer = _gameObject.GetComponentInChildren<SkinnedMeshRenderer>();
        }
        m_pickupItems.Add(pickupItem);
    }
    
    public void Pickup(GameObject _gameObject, Action _onComplete)
    {
        MAPickupItem pickupItem = CreatePickup(_gameObject, _onComplete);
        m_pickupItems.Add(pickupItem);
    }

    private MAPickupItem CreatePickup(GameObject _gameObject, Action _onComplete)
    {
        MAPickupItem pickupItem = new MAPickupItem();
        pickupItem.m_item = _gameObject;
        pickupItem.m_onComplete = _onComplete;
        pickupItem.m_parent = pickupItem.m_item.transform.parent;
        pickupItem.m_item.transform.SetParent(transform, true);
        pickupItem.m_startPos = pickupItem.m_item.transform.position;
        pickupItem.m_startRot = pickupItem.m_item.transform.rotation;
        
        m_pickupCollected.Play(_gameObject);
        
        return pickupItem;
    }

    public void FadeIn(GameObject _gameObject, Action _onComplete)
    {
        MAFadeInItem fadeInItem = new MAFadeInItem();
        fadeInItem.m_item = _gameObject;
        fadeInItem.m_onComplete = _onComplete;
        fadeInItem.m_parent = fadeInItem.m_item.transform.parent;
        fadeInItem.m_item.transform.SetParent(transform, true);
        fadeInItem.m_item.transform.position  = transform.position;
        fadeInItem.m_item.transform.rotation = transform.rotation;
        fadeInItem.m_renderer = _gameObject.GetComponentInChildren<Renderer>();
        fadeInItem.m_renderer.material.SetFloat("_AlphaMultiply", 0.0f);
        fadeInItem.m_item.SetActive(true);
        m_fadeInItems.Add(fadeInItem);
    }

    public void AnimationCompleted()
    {
        m_animationComplete = true;
    }

    public void AddGameObject(GameObject _gameObject)
    {
        _gameObject.transform.SetParent(transform, false);
    }

}
