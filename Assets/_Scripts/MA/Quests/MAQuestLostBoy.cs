using System;
using UnityEngine;
using UnityEngine.Playables;

public class MAQuestLostBoy : MAQuestBase
{
    public const float c_DistanceStartFollowing = 15f;
    public const float c_DistanceStopFollowing = 30f;
    public const float c_DistanceBuildingArrival = 20f;
    public string m_targetBuildingType;
    private float m_unpossessCooldown = 0.0f;
    private float m_unpossessTime = 5.0f;

    public float m_followSpeedScale = 4.0f;
    
    public enum LostBoyVOReaction
    {
        Following,
        LeftBehind,
        Unpossess,
        Attacked,
        None,
        Count
    }
    private LostBoyVOReaction m_lostBoyVOReaction;
    private LostBoyVOReaction m_lastLostBoyVOReaction;
    private int LostBoyVOReactionIndex = 0;
    private float m_lastInsultTime;
    private const float c_insultCooldown = 10.0f;
    private bool m_questAccepted = false;

    private int[] m_insultCounts =
    {
        10, 8, 3, 4
    };
    private int[] m_insultIndex =
    {
        -1, -1, -1, -1
    };

    public MAQuestInteraction m_book;

    private UnityEngine.Vector3 m_lastHeroPosition;
    private const float c_minHeroMoveDistance = 15f;
    private bool m_isLostBoyReturned = false;
    private bool m_isLostBoyDead = false;
    private bool m_malmusGiftReceived = false;
    private bool m_questGiftRecieved = false;
    private float m_lastHitTime = -1.0f;
    private Vector3 m_lastLostBoyPosition;
    
    private bool m_messageDisplaying = false;

    public override void OnAcceptChallenge()
    {
        base.OnAcceptChallenge();
        m_questAccepted = true;
        // we only want to wait half the cooldown time before the first following insult
        m_lastInsultTime = Time.time - c_insultCooldown;

        UpdateLastHeroPosition();
    }

    protected override void OnPostLoad()
    {
        base.OnPostLoad();

        if(m_questGiver != null)
        {
            LipSyncManager.Me.AddLipSyncer("Lost Boy", m_questGiver, -18.0f);
        }
    }

    public void Update()
    {
        switch(m_status)
        {
            case QuestStatus.InProgress:
                UpdateLostBoyState();
                break; 
            case QuestStatus.Completed:
                CheckForDeadLostBoy();
                break;
        }
    }

    private void UpdateLostBoyState()
    {
        CheckForDeadLostBoy();
        
        if(!GameManager.Me.LoadComplete || m_isLostBoyReturned)
        {
            return;
        }
        // Check distance
        var possessed = GameManager.Me.PossessedCharacter?.gameObject?.transform;

        if(possessed == null && !m_isLostBoyDead)
        {
            if (m_unpossessCooldown > 0.0f)
            {
                m_unpossessCooldown -= Time.deltaTime;

                if (m_unpossessCooldown <= 0.0f)
                {
                    m_questGiver.SetState(NGMovingObject.STATE.MA_DECIDE_WHAT_TO_DO);
                    OnCancelChallenge();
                    return;
                }
            }
        }
        else
        {
            m_unpossessCooldown = m_unpossessTime;
        }
        
        // Lost boy has been returned, to his maker
        if (m_isLostBoyDead)
        {
            // LostBoyReaction(LostBoyVOReaction.Attacked);
            // OnFailChallenge();
            return;
        }
        else if(m_questGiver != null)
        {
            m_lastLostBoyPosition = m_questGiver.transform.position;
        }

        if(HasChildArrived())
        {
            // m_questGiver.SetMoveToPosition(MATouristManager.Me.m_touristSpawnPoint.position, false,
            //     PeepActions.Working);
            // m_questGiver.SetState(NGMovingObject.STATE.MA_LEAVING);
            // GiveRewards();
            // GameManager.Me.Unpossess();
            
            m_isLostBoyReturned = true;
            m_questGiver.SetState(NGMovingObject.STATE.IDLE);
        }
        else if(m_questGiver.m_state == NGMovingObject.STATE.MA_DECIDE_WHAT_TO_DO)
        {
            // Try and follow
            if(possessed != null)
            {
                var dist2 = (possessed.position - m_questGiver.transform.position).sqrMagnitude;
                if(dist2 <= c_DistanceStartFollowing * c_DistanceStartFollowing)
                {
                    MACharacterBase.TargetResult target = new MACharacterBase.TargetResult();
                    target.m_targetObject = 
                        TargetObject.Create(GameManager.Me.PossessedCharacter.gameObject);
                    m_questGiver.SetTargetObj(target);
                    float followSpeedMulti = 1 / (NavAgent.FinalSpeedMultiplier == 0 ? 1f : NavAgent.FinalSpeedMultiplier);
                    m_questGiver.SetSpeed(GameManager.Me.PossessedCharacter.m_possessionFwdWalkSpeed * followSpeedMulti);
                    m_questGiver.SetToChaseObject();
                    
                    if (HeroHasMoved(possessed.position))
                    {
                        LostBoyReaction(LostBoyVOReaction.Following);
                    }
                }
            }
        }
        else if(possessed == null)
        {
            LostBoyReaction(LostBoyVOReaction.Unpossess);
            m_questGiver.SetState(NGMovingObject.STATE.MA_DECIDE_WHAT_TO_DO);
        }
        else
        {
            var dist2 = (possessed.position - m_questGiver.transform.position).sqrMagnitude;
            if(dist2 > c_DistanceStopFollowing * c_DistanceStopFollowing)
            {
                LostBoyReaction(LostBoyVOReaction.LeftBehind);
                m_questGiver.SetState(NGMovingObject.STATE.MA_DECIDE_WHAT_TO_DO);
            }

            if (m_questGiver.m_state == NGMovingObject.STATE.MA_CHASE_OBJECT)
            {
                if (HeroHasMoved(possessed.position))
                {
                    LostBoyReaction(LostBoyVOReaction.Following);
                }
            }
        }
    }

    private void CheckForDeadLostBoy()
    {
        if(m_questGiver != null)
        {
            m_questGiver.m_canBeTargeted = true;
            
            if(m_questGiver.IsAnyDeadState)
            {
                m_isLostBoyDead = true;
            }
            else if(CheckDamageReactionRequired())
            {
                if (!m_questGiver.CharacterGameState.m_immortal && !m_messageDisplaying)
                {
                    LostBoyReaction(LostBoyVOReaction.Attacked);
                }
            }
        }
        else
        {      
            m_isLostBoyDead = true;   
        }
    }
    
    private MABuilding m_targetBuilding;
    
    public MABuilding GetTargetBuilding()
    {
        if (m_targetBuilding != null)
            return m_targetBuilding;
        var type = Type.GetType(m_targetBuildingType);
        var buildings = MABuildingSupport.GetBuildingsWithComponent(type);
        foreach(var b in buildings)
        {
            m_targetBuilding = b;
            break;
        }
            
        return m_targetBuilding;
    }

    private GameObject m_targetGameObject;

    public GameObject GetTargetGameObject()
    {
        if (m_targetGameObject != null)
            return m_targetGameObject;

        var foundParent = Utility.FindTransformByPath("Town/DecorationHolder");

        if(foundParent != null)
        {
            var names = foundParent.GetComponentsInChildren<MANameObject>();

            if(names != null && names.Length > 0)
            {
                var obj = names.Find(o => o.m_name.Equals("TardisCrypt"));

                if(obj != null)
                {
                    m_targetGameObject = obj.gameObject;
                }
            }
        }
        return m_targetGameObject;
    }

    public bool HasChildArrived()
    {
        if(m_questGiver == null)
            return false;

        var target = GetTargetBuilding();

        if(target == null) 
            return true;
            
        var d2 = (m_questGiver.transform.position - m_targetBuilding.gameObject.transform.position).sqrMagnitude;
        
        return d2 < (c_DistanceBuildingArrival*c_DistanceBuildingArrival);
    }

    private bool HeroHasMoved(Vector3 _heroPosition)
    {
        if ((_heroPosition - m_lastHeroPosition).xzSqrMagnitude() > c_minHeroMoveDistance * c_minHeroMoveDistance)
        {
            return true;
        }

        return false;
    }

    private void UpdateLastHeroPosition()
    {
        GameObject possessed = GameManager.Me.PossessedCharacter?.gameObject;
        if (possessed != null)
        {
            m_lastHeroPosition = possessed.transform.position;
        }
    }
    
    // The function that uses this return, QuestObjectiveComplete, treats all no zero values as false
    public override float QuestObjectiveValue(string _objective)
    {
        switch (_objective.Trim())
        {
            case "Following":
                return m_lostBoyVOReaction == LostBoyVOReaction.Following ? 0.0f : 1.0f;
            case "LeftBehind":
                return m_lostBoyVOReaction == LostBoyVOReaction.LeftBehind ? 0.0f : 1.0f;
            case "Unpossess":
                return m_lostBoyVOReaction == LostBoyVOReaction.Unpossess ? 0.0f : 1.0f;
            case "Attacked":
                return m_lostBoyVOReaction == LostBoyVOReaction.Attacked ? 0.0f : 1.0f;
            case "IsFirstAttempt":
                return !m_questAccepted ? 0.0f : 1.0f;
            case "LostBoyReturned":
                return m_isLostBoyReturned ? 0.0f : 1.0f;
            case "IsMalmusGiftReceived":
                return m_malmusGiftReceived ? 0.0f : 1.0f;
            case "IsQuestGiftReceived":
                return m_questGiftRecieved ? 0.0f : 1.0f;
        }

        if (int.TryParse(_objective, out var insultIndex))
        {
            return m_insultIndex[(int)m_lostBoyVOReaction] == insultIndex ? 0.0f : 1.0f;
        }
        return 1.0f;
    }
    
    private void LostBoyReaction(LostBoyVOReaction _VOReaction)
    {
        
        if (Time.time - c_insultCooldown > m_lastInsultTime || _VOReaction != m_lastLostBoyVOReaction || _VOReaction == LostBoyVOReaction.Attacked)
        {
            m_lostBoyVOReaction = _VOReaction;
            var index = (int)m_lostBoyVOReaction;
            m_insultIndex[index] = (m_insultIndex[index] + 1) % m_insultCounts[index];

            m_lastLostBoyVOReaction = m_lostBoyVOReaction;
            MAParserManager.Me.ExecuteSection("Quests/QuestLostBoyReactions");
            m_lastInsultTime = Time.time;
            if (m_lostBoyVOReaction == LostBoyVOReaction.Following)
            {
                UpdateLastHeroPosition();
            }
        }
    }
    
    private void PickupBook()
    {
        if(Camera.main != null)
        {
            var pickupHolder = Camera.main.transform.GetComponentInChildren<MAPickupHolder>();
            m_book.transform.position = m_questGiver != null ? m_questGiver.transform.position : m_lastLostBoyPosition;

            if(pickupHolder != null)
            {
                pickupHolder.Pickup(m_book.gameObject, OnPickupBookComplete, m_book.GetComponentInChildren<PlayableDirector>());
             
                return;
            }
        }
        OnPickupBookComplete();
    }

    private void OnPickupBookComplete()
    {
    }

    private bool IsLostBoyDead()
    {
        return m_isLostBoyDead;
    }

    private bool CheckDamageReactionRequired()
    {
        if (m_questGiver != null && GameManager.Me.PossessedCharacter != null)
        {
            float totalDamage = 0.0f;
            foreach (var damage in m_questGiver.m_receivedDamageHistory)
            {
                if (damage.m_attacker == GameManager.Me.PossessedCharacter)
                {

                    if (damage.m_time > m_lastHitTime)
                    {
                        totalDamage += damage.m_damageDone;
                        m_lastHitTime = damage.m_time;
                    }
                }
            }

            // if (totalDamage > 0.0f)
            // {
            //     Debug.Log($">>> CheckDamageReactionRequired totalDamage = {totalDamage}, m_questGiver.Health = {m_questGiver.Health}");
            // }
            
            // damage taken but not killed
            if (totalDamage > 0.0f && m_questGiver.Health > 0.0f)
            {
                return true;
            }
        }

        return false;
    }

    public override void TriggerQuestEvent(string _event)
    {
        base.TriggerQuestEvent(_event);

        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("PickupBook"))
        {
            PickupBook();
        }
        else if (split[0].Contains("MalmusGiftReceived"))
        {
            m_malmusGiftReceived = true;
        }
        else if (split[0].Contains("QuestGiftReceived"))
        {
            m_questGiftRecieved = true;
        }
        else if (split[0].Contains("LostBoyReturned"))
        {
            m_isLostBoyReturned = true;
        }
    }
    
    public override bool WaitForQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("IsLostBoyDead"))
        {
            return IsLostBoyDead();
        }
        return false;
    }
    
    public override void SetQuestStatus(QuestStatus _status)
    {
        base.SetQuestStatus(_status);

        if (m_questGiver != null)
        {
            m_questGiver.ToggleQuestScroll(_status == QuestStatus.Active);
        }
    }
    
    public override void OnDisplayMessage(MAMessage _message)
    {
        base.OnDisplayMessage(_message);

        if (_message.m_advisor == "Lost Boy")
        {
            m_messageDisplaying = true;
        }
    }

    public override void OnDestroyMessage(MAMessage _message)
    {
        base.OnDestroyMessage(_message);
        
        if (_message.m_advisor == "Lost Boy")
        {
            m_messageDisplaying = false;
        }
    }


    public class SaveLoadQuestLostBoyContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestLostBoyContainer() : base() { }
        public SaveLoadQuestLostBoyContainer(MAQuestBase _base) : base(_base) { }
        [Save] public int m_malmusGiftReceived;
        [Save] public int m_questGiftRecieved;
    }

    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestLostBoyContainer(this);

        saveContainer.m_malmusGiftReceived = m_malmusGiftReceived ? 1 : 0;
        saveContainer.m_questGiftRecieved = m_questGiftRecieved ? 1 : 0;

        return saveContainer;
    }

    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);

        var saveContainer = _l as SaveLoadQuestLostBoyContainer;
        if (saveContainer != null)
        {
            m_malmusGiftReceived = saveContainer.m_malmusGiftReceived > 0;
            m_questGiftRecieved = saveContainer.m_questGiftRecieved > 0;
        }
    }
}
