using System;
using UnityEngine;
using Unity.Mathematics;
using static Unity.Mathematics.noise;
using Random = UnityEngine.Random;

public class MAQuestWasp : MonoBehaviour
{
    public enum WaspState { Disturbed, Chasing, Attacking, Dispersing }

    [ReadOnlyInspector]
    public Animator m_animator;
    
    [ReadOnlyInspector]
    public GameObject m_waspModel;

    private WaspState m_currentState;
    private Vector3 m_spawnPoint;
    private Vector3 m_targetPosition;
    private float m_stateTimer;
    private float m_DisturbedTime;
    private float m_chasingTime;
    private float m_chasingSpeed;
    private float m_DisperseDistance;
    private float m_noiseOffset; // Unique per wasp, initialized in Initialize()
    private float m_noiseFrequency = 1.5f; // How fast the noise changes
    private MACharacterBase m_targetCharacter = null;
    private Transform m_targetBodyPart = null;

    private MAQuestWaspSwarm m_swarm;

    // Constants
    private const float c_minDisturbedDist = 1f;
    private const float c_maxDisturbedDist = 2f;
    private const float c_DisturbedMoveSpeed = 6f;
    private const float c_ChasingMoveSpeedMin = 4.5f;
    private const float c_ChasingMoveSpeedMax = 8f;
    private const float c_DisturbedNoiseMagnitude = 0.5f; 
    private const float c_ChasingNoiseMagnitude = 3.3f;
    private const float c_minDisturbedTime = 1.2f;
    private const float c_maxDisturbedTime = 3.4f;
    private const float c_minChasingTime = 4f;
    private const float c_maxChasingTime = 12f;
    private const float c_minDisperseDistance = 15f;
    private const float c_maxDisperseDistance = 18f;
    private const float c_reachedTargetDist = 1f;
    private const float c_maxAttackDist = 1.3f;
    private const float c_attackDamage = 5f;
    private const float c_dispurseScaleDist = 3.5f;
    
    private const bool c_useCnoise = false;
    
    AkEventHolder m_impactAudio = new();

    private void Awake()
    {
        m_animator = GetComponentInChildren<Animator>();
        MAAttackAnimCallback attackAnimcallback = GetComponentInChildren<MAAttackAnimCallback>();
        attackAnimcallback.m_onAttackStart -= OnAttackStart;
        attackAnimcallback.m_onAttackStart += OnAttackStart;
        attackAnimcallback.m_onAttackHit -= OnAttackHit;
        attackAnimcallback.m_onAttackHit += OnAttackHit;
        attackAnimcallback.m_onAttackEnd -= OnAttackEnd;
        attackAnimcallback.m_onAttackEnd += OnAttackEnd;
    }

    private void Start()
    {
        m_animator.ResetTrigger("Sting");
    }

    private void OnAttackStart()
    {
#if WASP_DEBUG
        Debug.Log($"OnAttackStart Wasp: {name}");
#endif
    }
        
    private void OnAttackHit()
    {
#if WASP_DEBUG
        Debug.Log($"OnAttackHit Wasp: {name}");
#endif
        if (WithinAttackDistance())
        {
            DoAttackDamage();
        }

        DisperseWasp();
    }
    
    private void OnAttackEnd()
    {
#if WASP_DEBUG
        Debug.Log($"OnAttackEnd Wasp: {name}");
#endif
    }

    public void Initialize(Vector3 spawnPoint, MACharacterBase attackTarget, MAQuestWaspSwarm swarm)
    {
        m_spawnPoint = spawnPoint;
        m_targetCharacter = attackTarget;
        m_swarm = swarm;
        m_currentState = WaspState.Disturbed;
        m_DisturbedTime = Random.Range(c_minDisturbedTime, c_maxDisturbedTime);
        m_chasingSpeed = Random.Range(c_ChasingMoveSpeedMin, c_ChasingMoveSpeedMax);
        SetNewDisturbedTarget();
        SetTargetBodyPart();
        // Unique noise offset for each wasp
        m_noiseOffset = Random.Range(0f, 1000f);
    }

    private void Update()
    {
        m_stateTimer += Time.deltaTime;

        switch (m_currentState)
        {
            case WaspState.Disturbed:
                HandleDisturbed();
                break;
            case WaspState.Chasing:
                HandleChasing();
                break;
            case WaspState.Attacking:
                HandleAttacking();
                break;
            case WaspState.Dispersing:
                HandleDispersing();
                break;
        }
    }

    private void HandleDisturbed()
    {
        MoveTowardsTarget(c_DisturbedMoveSpeed, c_DisturbedNoiseMagnitude);
        if (ReachedTarget())
        {
            SetNewDisturbedTarget();
        }

        if (m_stateTimer >= m_DisturbedTime)
        {
            m_currentState = WaspState.Chasing;
            m_chasingTime = Random.Range(c_minChasingTime, c_maxChasingTime);
            m_stateTimer = 0f;
        }
    }

    private void HandleChasing()
    {
        if (m_targetCharacter == null) return;

        m_targetPosition = m_targetBodyPart.position;
        
        MoveTowardsTarget(m_chasingSpeed, c_ChasingNoiseMagnitude);

        if (ReachedTarget())
        {
#if WASP_DEBUG
            Debug.Log($"Sting Wasp {name}");
#endif
            m_animator.SetTrigger($"Sting");
            m_currentState = WaspState.Attacking;
            m_stateTimer = 0f;
        }
        else if (m_stateTimer >= m_chasingTime)
        {
            DisperseWasp();
        }
    }

    private void HandleAttacking()
    {
        if (m_stateTimer > 4f) //fail-safe
        {
            DisperseWasp();
        }
    }

    private void HandleDispersing()
    {
        MoveTowardsTarget(m_chasingSpeed, c_ChasingNoiseMagnitude);
        
        // Scale the wasp 0 as it nears the target - so the wasp exits nicely
        float distance = Vector3.Distance(transform.position, m_targetPosition);
        if (distance < c_dispurseScaleDist)
        {
            float scale = Mathf.Max(distance / c_dispurseScaleDist, 0.0f);
            transform.localScale = new Vector3(scale, scale, scale);
        }
        
        if (ReachedTarget())
        {
            AudioClipManager.Me.PlaySound("PlaySound_WaspLoopEnd", m_animator.gameObject);
            m_swarm.RemoveWasp(this);
        }
    }

    private void DisperseWasp()
    {
#if WASP_DEBUG
        Debug.Log($"Disperse Wasp: {name}");
#endif
        m_currentState = WaspState.Dispersing;
        m_DisperseDistance = Random.Range(c_minDisperseDistance, c_maxDisperseDistance);
        SetDispersingTarget();
        m_stateTimer = 0f;
    }

    private void SetNewDisturbedTarget()
    {
        Vector3 randDir = Random.onUnitSphere;
        float dist = Random.Range(c_minDisturbedDist, c_maxDisturbedDist);
        m_targetPosition = m_spawnPoint + randDir * dist;
    }

    private void SetDispersingTarget()
    {
        Vector3 randDir = Random.onUnitSphere;
        randDir.y = Mathf.Abs(randDir.y); // Ensure non-lower direction
        m_targetPosition = transform.position + randDir.normalized * m_DisperseDistance;
    }

    private void SetTargetBodyPart()
    {
        if(m_targetCharacter == null) return;
        m_targetBodyPart = m_targetCharacter.m_ragdollController.GetRandomTargetableBodypart();
    }

    private void MoveTowardsTarget(float speed, float noiseMagnitude)
    {
        if (c_useCnoise)
        {
            MoveTowardsTargetCnoise(speed, noiseMagnitude);
        }
        else
        {
            MoveTowardsTargetPerlin(speed, noiseMagnitude);
        }
    }

    private void MoveTowardsTargetPerlin(float speed, float noiseMagnitude)
    {
        Vector3 direction = (m_targetPosition - transform.position).normalized;

        float noiseTime = Time.time * m_noiseFrequency + m_noiseOffset;
        
        float noiseX = (Mathf.PerlinNoise(noiseTime, 0.0f) - 0.5f) * noiseMagnitude;
        float noiseZ = (Mathf.PerlinNoise(noiseTime, noiseTime * 0.5f) - 0.5f) * noiseMagnitude;

        // Upward-only Y noise: clamp to zero if it would subtract from base direction's Y
        float rawNoiseY = (Mathf.PerlinNoise(0.0f, noiseTime) - 0.5f) * noiseMagnitude;
        float noiseY = rawNoiseY;

        // Only allow Y noise to increase the upward component
        if (direction.y + rawNoiseY < direction.y)
            noiseY = 0f;

        Vector3 noise = new Vector3(noiseX, noiseY, noiseZ);
        Vector3 noisyDirection = (direction + noise).normalized;

        if (noisyDirection != Vector3.zero)
        {
            transform.rotation = Quaternion.LookRotation(noisyDirection);
        }

        transform.position += noisyDirection * (speed * Time.deltaTime);
    }
    
    
    private void MoveTowardsTargetCnoise(float speed, float noiseMagnitude)
    {
        Vector3 direction = (m_targetPosition - transform.position).normalized;

        float noiseTime = Time.time * m_noiseFrequency + m_noiseOffset;

        // Use a float3 seed to differentiate axes (you can tune these values)
        float3 inputX = new float3(noiseTime, 0f, m_noiseOffset);
        float3 inputY = new float3(0f, noiseTime, m_noiseOffset + 10f);
        float3 inputZ = new float3(noiseTime, noiseTime * 0.5f, m_noiseOffset + 20f);

        float noiseX = cnoise(inputX) * noiseMagnitude;
        float rawNoiseY = cnoise(inputY) * noiseMagnitude;
        float noiseZ = cnoise(inputZ) * noiseMagnitude;

        // Clamp Y noise to only allow upward deviation
        float noiseY = (direction.y + rawNoiseY < direction.y) ? 0f : rawNoiseY;

        Vector3 noise = new Vector3(noiseX, noiseY, noiseZ);
        Vector3 noisyDirection = (direction + noise).normalized;

        if (noisyDirection != Vector3.zero)
        {
            transform.rotation = Quaternion.LookRotation(noisyDirection);
        }

        transform.position += noisyDirection * (speed * Time.deltaTime);
    }

    private bool ReachedTarget()
    {
        return Vector3.Distance(transform.position, m_targetPosition) < c_reachedTargetDist;
    }
    private bool WithinAttackDistance()
    {
        return Vector3.Distance(transform.position, m_targetPosition) <= c_maxAttackDist;
    }

    private void DoAttackDamage()
    {
#if WASP_DEBUG
        Debug.Log($"DoAttachDamage Wasp: {name}");
#endif
        m_targetCharacter.SpawnBloodFromHit(m_targetBodyPart);
        m_targetCharacter.ApplyDamageEffect(IDamageReceiver.DamageSource.AI, c_attackDamage, transform.position, null);
        
        string hitType = m_targetCharacter != null ? m_targetCharacter.GetArmourHitType() : "Wall";
        
        AudioClipManager.Me.SetSoundSwitch("CombatTarget", $"CombatTarget_{hitType}", gameObject);
        AudioClipManager.Me.PlaySound("PlaySound_CombatWaspSting", gameObject);
    }
}
