using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MADesignPriceSheet : MAGUIBase
{
    private NGDesignInterface.DesignScoreInterface m_designInterface;
    public TMP_Text m_productDescriptionText;
    public TMP_Text m_summaryText;
    public Transform m_designPriceSheetLineHolder;
    private int m_designHash;
    
    public void Update()
    {
        RefreshView();
    }

    public void Activate()
    {
        base.Activate();
        RefreshView();
    }
    public void RefreshView()
    {
        if (DesignTableManager.Me.Product == null)
        {
            m_designPriceSheetLineHolder.DestroyChildren();
            m_designHash = 0;
            m_productDescriptionText.text = "";
            return;
        }
        
        bool dsiChanged = m_designInterface != DesignTableManager.Me.DesignInterface; 
        m_designInterface = DesignTableManager.Me.DesignInterface;
        
        m_productDescriptionText.text = GetProductString(m_designInterface);
        
        if(m_designInterface.Parts.Count == 0)
        {
            m_designPriceSheetLineHolder.DestroyChildren();
            m_designHash = 0;
            m_summaryText.text = "";
            return;
        }
        
        if(m_designHash != m_designInterface.m_designHash || dsiChanged)
        {
            m_designHash = m_designInterface.m_designHash;
            
            var summary = GetSummary();
            
            if(summary.IsNullOrWhiteSpace() == false)
                summary += "\n\n";
                
            summary += GetMakingInfo();
            
            m_summaryText.text = summary;
           
            RefreshPartView();
        }
    }
    
    public static int GetOrderQuantity()
    {
        int orderQuantity = 0;
        if(DesignTableManager.Me.m_designInPlaceBuilding != null)
        {
            var order = DesignTableManager.Me.m_designInPlaceBuilding.Order;
            if(order.IsNullOrEmpty() == false)
            {
                orderQuantity = order.m_orderQuantity;
            }
            if(order.IsInfinateOrder)
                return -1;
        }
        return orderQuantity;
    }
    
    private string GetSummary()
    {
        int orderQuantity = GetOrderQuantity();
        
        if(orderQuantity < 0)
            return "";
        
        var profit = m_designInterface.GetProfit() * orderQuantity;
        
        return  $"Fulfilling this order for {orderQuantity} will make a total profit of <b>{GlobalData.CurrencySymbol}{profit:F2}</b>";
    }
    
    private string GetMakingInfo()
    {
        if(m_designInterface == null || m_designInterface.Parts == null)
            return "";
            
        float workerMakesTime = 0;
        float tapMakesTime = 0;
        Dictionary<NGBlockInfo, int> partLines = new Dictionary<NGBlockInfo, int>();
        foreach (var p in m_designInterface.Parts)
        {
            var name = p.m_block;
            if (partLines.ContainsKey(name))
            {
                partLines[name]++;
            }
            else
            {
                partLines[name] = 1;
            }
            workerMakesTime += p.m_block.m_workerTimeToMake;
            tapMakesTime += p.m_block.m_numTapsToMake;
        }
        
        return $"Worker makes 1 in <b>{workerMakesTime.ToHMSTimeString()}</b>";
    }
    
    public static string GetProductString(NGDesignInterface.DesignScoreInterface _dsi)
    {
        if(_dsi.Parts.Count == 0)
            return "No Parts";
            
        var averageRarity = _dsi.AveragePartRarity;
        var expensiveNames = new List<string>(){"Cheap", "Affordable", "Expensive", "Outrageous", "Extreme"};
        var popularNames = new List<string>(){"Popular", "Trendy", "Disliked", "Shunned", "Unwanted"};
        var productDesc = "[Gauge] [Product], [ToMake] to make, and [Popular]";
        productDesc = productDesc.Replace("[Gauge]", MADesignGuage.GetNameValue(_dsi.ProductLineName, _dsi.TotalScore, false));
        productDesc = productDesc.Replace("[Product]", _dsi.ProductLineName);
        productDesc = productDesc.Replace("[ToMake]", expensiveNames[(int)(averageRarity*expensiveNames.Count)]);
        productDesc = productDesc.Replace("[Popular]", popularNames[(int)(averageRarity*expensiveNames.Count)]);
        return productDesc;
    }
    
    public void RefreshPartView()
    {
        m_designPriceSheetLineHolder.DestroyChildren();

        foreach(var line in m_designInterface.PriceSheetData)
        {
            MADesignPriceSheetLine.Create(m_designPriceSheetLineHolder, line, true);
        }
    }
    public static MADesignPriceSheet Create(Transform _holder)
    {
        var prefab = Resources.Load<MADesignPriceSheet>("_Prefabs/Dialogs/MADesignPriceSheet");
        var instance = Instantiate(prefab, _holder);
        instance.Activate();
        return instance;
    }
}
