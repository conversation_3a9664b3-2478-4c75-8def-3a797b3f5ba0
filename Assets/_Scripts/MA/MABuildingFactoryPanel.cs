using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MABuildingFactoryPanel : NGBuildingInfoGUIComponentBase
{
    public Transform m_toggleHolder;
    List<MAToggleGUI> m_toggles = new List<MAToggleGUI>();
    public void Activate(MABuilding _building)
    {
        //base.Activate();
        m_building = _building;
        m_maBuilding = _building;
        RefreshView();
    }
    void RefreshView()
    {
        //m_title.text = m_building.GetBuildingTitle();
        m_toggleHolder.DestroyChildren();
        m_toggles.Clear();
        m_toggles.Add(MAToggleGUI.Create(m_toggleHolder, "Balanced", ClickedToggle, true));
       // var outputIs = m_maBuilding.GetOutputIsStock();
        foreach(var building in NGManager.Me.m_maBuildings)
        {
            if (building == m_maBuilding) continue;
            /*var buildingInput = building.GetInputIsStock();
            if(outputIs == buildingInput)
                m_toggles.Add(MAToggleGUI.Create(m_toggleHolder, building.GetBuildingTitle(), ClickedToggle, false));*/
        }
    }
    
    void ClickedToggle(MAToggleGUI _toggle)
    {
        foreach (var toggle in m_toggles)
        {
            if (toggle == _toggle)
                continue;
            else if(toggle.IsOn == true)
                toggle.IsOn = false;
        }
    }
    public static MABuildingFactoryPanel Create(Transform _holder, MABuilding _building)
    {
        var prefab = Resources.Load<MABuildingFactoryPanel>("_Prefabs/Dialogs/MABuildingFactoryPanel");
        var instance = Instantiate(prefab, _holder);
        instance.Activate(_building);
        return instance; 
    } 
}
