using UnityEngine;
using UnityEngine.UI;

public class MAQuickTipHandPowerButton: MAQuickTipButton
{
    public Button m_button;
    public Sprite[] m_handIcons = new Sprite[PlayerHandManager.c_allPowers.Length];
    public Image m_buttonImage;
    private void Start()
    { 
        Activate(Name);
    }
    private void Update()
    {
        if (GameManager.Me == null || GameManager.Me.LoadComplete == false) return;
        var showButton = MAUnlocks.Me.m_handPowerLightning;
        if(m_button.interactable != showButton)
        {
            transform.localScale = showButton ? Vector3.one : Vector3.zero;
            m_button.interactable = showButton;
        }
        if(showButton)
            SetPower();
        if (showButton == false)
        {
            m_highLight.SetActive(showButton);
        }
    }
    override protected void ActivateClicked()
    {
        base.ActivateClicked();
        var current = PlayerHandManager.Me.m_currentPowerType;
        for (var i = 0; i < PlayerHandManager.c_allPowers.Length; i++)
        {
            var name = PlayerHandManager.c_allPowers[i];
            if (name == current)
            {
                var next = i + 1;
            
                for (var j = 0; j < PlayerHandManager.c_allPowers.Length; j++, next++)
                {
                    if(next >= PlayerHandManager.c_allPowers.Length)
                        next = 0;
                    if(PlayerHandManager.Me.PowerUnlocked(PlayerHandManager.c_allPowers[next]))
                    {
                        PlayerHandManager.Me.ActivatePower(PlayerHandManager.c_allPowers[next]);
                    
                        SetPower();
                        return;
                    }
                }
            }
        }
        Debug.LogError("Not found hand power type");
    }

    void SetPower()
    {
        string power = PlayerHandManager.Me.m_currentPowerType;
        var index = PlayerHandManager.c_allPowers.FindIndex(o=> o == power);
        if(index >= 0 && index < m_handIcons.Length)
            m_buttonImage.sprite = m_handIcons[index];
    }
}
