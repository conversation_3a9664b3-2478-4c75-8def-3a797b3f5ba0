using System.Collections.Generic;
using UnityEngine;

public class MAEscortee : MACreatureBase
{
	//[Header("MAEscortee")]
	override public string HumanoidType => "Escortee";
	public override EDeathType DeathCountType => EDeathType.None;

	public override bool IsTimeToGoHome
	{
		get { return false; }
	}

	public override bool IsEnemy
	{
		get { return false; }
	}
	
	protected override void Awake()
	{
		base.Awake();
	}

	public override void Activate(MACreatureInfo _creatureInfo, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation, bool _addToCharacterLists = true)
	{
		base.Activate(_creatureInfo, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);
	}

	public override void DestroyedTarget()
	{
		base.DestroyedTarget();
	}

	public override void DoPossessedAction()
	{
		
	}

	public void SetupWaypoints(int id)
	{
		var path = MAWaypointPathController.GetWaypointPath(id);
		if (path == null)
			return;

		CreatureGameState.m_waypoint = 0;
		CreatureGameState.m_waypointPathId = id;

		path.AddCharacter(this);
		var waypoint = path.GetWaypointItem(CreatureGameState.m_waypoint);
		float radius = Mathf.Max(CharacterSettings.m_guardModeOverrideRadius, 0.1f);
		SetToGuard(waypoint.m_pos, radius);
	}

	public bool IsPathCompleted()
	{
		var id = CreatureGameState.m_waypointPathId;
		if (id < 0)
			return true;

		var path = MAWaypointPathController.GetWaypointPath(id);
		if (path == null)
			return true;
		
		var waypoint = path.GetWaypointItem(CreatureGameState.m_waypoint);
		waypoint.ExecuteTriggers();
		waypoint = path.GetWaypointItem(++CreatureGameState.m_waypoint);
		if (waypoint != null)
		{
			float radius = Mathf.Max(CharacterSettings.m_guardModeOverrideRadius, 0.1f);
			SetToGuard(waypoint.m_pos, radius);

			return false;
		}

		return true;
	}
}
