using System;
using System.Collections.Generic;
using UnityEngine;

public abstract class BCActionOrderBase : BCActionBase, ICardHolderSegment
{
    [KnackField] public string m_orderType = "";
    
    [Save] private OrderSlots m_slots = new();
    [Save] private OrderSequence m_sequence = new();
       
    public OrderSlots SlotManager => m_slots;
    public List<CardSlot> Slots => m_slots.Slots;
    public string OrderType => m_orderType;
    
    virtual public string SequenceType => "";
    virtual public OrderSequence Sequence => m_sequence;
    virtual protected  bool CanHoldStock => false;
    virtual public float SlotLockDuration => MAUnlocks.Me.m_orderRefresh;
    virtual public bool RequiresDelivery => false;
    virtual public int MaxCardSlots => 0;
    virtual public bool CreateDefaultSlot => false;
    virtual public bool HideEmptySlots => false;

    private static DebugConsole.Command s_debugDispatches = new ("debugdispatches", _s => 
    {
        MAUnlocks.Me.m_unlockDeliveryCart = true;
        MAParser.LoadDispatchOrders("food", "Chapter 1");
        MAParser.LoadDispatchOrders("metal", "Weapons");
        MAParser.LoadDispatchOrders("fabric", "Clothing");
    });
    
    override public void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        m_slots.RemoveInvalidOrders();
        base.Activate(_indexOfComponentType, _quantityInBuilding);
    }
    
    public virtual void OnDeliveryOrderArrvied()
    {
        AudioClipManager.Me.PlaySound("PlaySound_NewOrderPosted", Building?.gameObject);
    }
    
    protected override void Deactivate(MABuilding _previousOwner, int _quantityRemainingInBuilding, BlockDragAction _action)
    {
        switch(_action)
        {
            case BlockDragAction.ToWild:
            case BlockDragAction.Destroy:
                m_slots.ReturnAllOrdersToSequence(this, true);
                
                break;
            default:
                m_slots.ReturnAllOrders();
                break;
                
        }
        
        base.Deactivate(_previousOwner, _quantityRemainingInBuilding, _action);
    }
    
    public bool CanAcceptResource(NGCarriableResource _resource)
    {
        if(_resource == null || _resource.IsProduct == false) 
            return false;
            
        var order = _resource.GetProduct()?.GetLinkedOrder();
        if(order.IsNullOrEmpty() || order.CanDeliverTo(m_building) == false)
            return false; 
            
        return CanAcceptItemsFromOrder(order);
    }
    
    public virtual bool CanAcceptItemsFromOrder(MAOrder _order)
    {
        return _order.m_orderType.Equals(m_orderType);
    }
    
    virtual public bool OrderSlotsEnabled() => true;
    virtual public bool IsDelivering() => false;
    virtual public bool ReadyToDispatch() => true;
    
    override public bool AddResource(NGCarriableResource _resource)
    {
        if(_resource == null) return false;
        
        if(CanAcceptResource(_resource) == false)
            return false;

        if(TryImmediatelyDispatch(_resource))
        {
            OnItemAdded();
            return true;
        }
            
        if(CanHoldStock == false)
        {
            // Try add to stock in
            foreach(var stockIn in Building.StockIns)
            {
                if(stockIn.AddResource(_resource))
                {
                    OnItemAdded();
                    return true;
                }
            }
            return false;
        }
            
        // Allow 1 of each type
        if(m_stock.HasStock(_resource) == false)
        {
            m_stock.AddOrCreateStock(_resource, 1);
            OnItemAdded();
            return true;
        }
        return false;
    }
    
    protected virtual void OnItemAdded()
    {
        
    }
    
    override public NGStock GetInputStock() => m_stock;
    
    override public float GetResourceRequirement(NGCarriableResource _resource, bool _isTest = false)
    {
        if(CanHoldStock == false || CanAcceptResource(_resource) == false)
            return 0f;
        
        if(m_stock.HasStock(_resource))
            return 0f;
            
        return 1f;
    }

    virtual protected int GetTotalInputCapacity()
    {
        return 1;
    }

    public void UpdateInputStock(bool _dispatchImmediately = false)
    {
        if(CanHoldStock == false || m_stock.GetTotalStock() >= GetTotalInputCapacity()) return;
        
        if(m_building.ConsumeStockNeeded(m_stock, this))
        {
            OnItemAdded();
            if(_dispatchImmediately)
            {
                foreach(var item in m_stock.Items)
                {
                    if(item.Stock > 0 && TryImmediatelyDispatch(item.Resource))
                    {
                        item.Stock--;
                    }
                }
            }
        }
    }
    
    public NGCarriableResource ConsumeAnyStock()
    {
        m_stock.RemoveEmptyResources();
        
        foreach(var item in m_stock.Items)
        {
            if(item.Stock > 0)
            {
                item.Stock--;
                return item.Resource;
            }
        }
        return null;
    }

    virtual protected bool TryImmediatelyDispatch(NGCarriableResource _resource)
    {
        return false;
    }

    public override void UpdateInternal(BuildingComponentsState _state)
    {
        Sequence.Update();
        m_slots.Update(this);
        
        UpdateInputStock(true);
    }

    public virtual void OnOrderSequenceEmpty() { }

    protected override void SetupStockRequirements()
    {
        m_stock.RemoveEmptyStockAndClearNeededToProduce();
        
        m_stock.AddOrCreateStock(NGCarriableResource.GetInfo(NGCarriableResource.c_product), 0, 1);

        base.SetupStockRequirements();
    }
}