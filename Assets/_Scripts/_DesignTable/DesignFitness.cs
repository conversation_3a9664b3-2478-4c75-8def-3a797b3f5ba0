using System;

public static class DesignFitness {
	static GameState_DesignFitness FitnessData => GameManager.Me.m_state.m_fitnessData;
	public static float GetPartLifetime(string _id, bool _isDecoration) {
		return 20f;//TODO ResearchLabRewardsController.PartLifetime(_id, _isDecoration ? ResearchLabRewardsController.Category.PRODUCT_DECO : ResearchLabRewardsController.Category.PRODUCT_PART);
	}
	public static void ResetAgeForBlock(string _blockId) {
		if(!FitnessData.m_blockAges.TryGetValue(_blockId, out float age))
			FitnessData.m_blockAges[_blockId] = 0f;
		
		float maxAge = GetPartLifetime(_blockId, false);
		FitnessData.m_blockAges[_blockId] -= maxAge	* 1f;//TODO ResearchLabRewardsController.DuplicateAgeRecovery(_blockId, ResearchLabRewardsController.Category.PRODUCT_PART);
		if(FitnessData.m_blockAges[_blockId] < 0) FitnessData.m_blockAges[_blockId] = 0;
	}
	public static void ResetAgeForDecoration(string _blockId) {
		if(!FitnessData.m_decorationAges.TryGetValue(_blockId, out float age))
			FitnessData.m_decorationAges[_blockId] = 0f;

		float maxAge = GetPartLifetime(_blockId, true);
		FitnessData.m_decorationAges[_blockId] -= maxAge * 1f;//TODO ResearchLabRewardsController.DuplicateAgeRecovery(_blockId, ResearchLabRewardsController.Category.PRODUCT_DECO);
		if(FitnessData.m_decorationAges[_blockId] < 0) FitnessData.m_decorationAges[_blockId] = 0;
	}
	public static float GetAgeFromBlockId(string _blockId)
	{
		if(FitnessData.m_blockAges.TryGetValue(_blockId, out float age))
			return age;
		// Add new value for this block
		FitnessData.m_blockAges[_blockId] = 0f;
		return 0f;
	}
	public static float GetNormalizedAgeForDesign(GameState_Design _design) { return GetNormalizedAgeForDesign(_design.m_design); }
	public static float GetNormalizedAgeForDesign(string _design) {
		return 0.1f;
	}
}
