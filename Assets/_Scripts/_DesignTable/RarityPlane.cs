using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RarityPlane : MonoBehaviour
{
	[SerializeField] private Renderer m_renderer;
	private MaterialPropertyBlock m_propBlock;
	
	private ResearchLabRewardsController.Rarity m_rarity;
	private static readonly int HASH_TINT = Shader.PropertyToID("_Tint");

	public void Activate(ResearchLabRewardsController.Rarity _rarity){
		m_rarity = _rarity;
		m_propBlock = new MaterialPropertyBlock();
		m_renderer.GetPropertyBlock(m_propBlock);
		m_propBlock.SetColor(HASH_TINT, RarityColour(m_rarity));
		m_renderer.SetPropertyBlock(m_propBlock);
		// set the scale not to inherit from parent
		transform.localScale = new Vector3 (1f / transform.parent.localScale.x, 1f/transform.parent.localScale.y, 1f/transform.parent.transform.localScale.z);
	}
	private Color RarityColour(ResearchLabRewardsController.Rarity _rarity){
		return GlobalData.Me.m_rarityColours[(int)_rarity];
	}

}
