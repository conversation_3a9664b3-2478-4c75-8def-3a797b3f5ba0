using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.EventSystems;

using Product = GameState_Product;
using ProductDesign = GameState_Design;
public class DesignFitnessController : Mono<PERSON>ehaviour, IPointerClickHandler
{
	[System.Serializable]
	private struct LabelInfo
	{
		public string m_label;
		public float m_minValue;
	}

	[SerializeField] private GameObject m_gaugePointer;
	[SerializeField] private GameObject m_labelHolder;
	[SerializeField] private TextMeshProUGUI m_frontLabel;
	[SerializeField] private TextMeshProUGUI m_backLabel;
	[SerializeField] private TextMeshProUGUI m_gaugeLabel;
	[SerializeField] private float m_maxAngle = 90f;
	[SerializeField] private float m_minAngle = -90f;
	[SerializeField] private float m_labelAnimationSpeed = 0.5f;
	[SerializeField] private float m_pointerAnimationSpeed = 0.3f;
	[SerializeField] private List<LabelInfo> m_labelInfos = new List<LabelInfo>();

	public GameObject GaugeLabel => m_gaugeLabel.gameObject;
	
	public float m_debugValue;

	private TextMeshProUGUI m_currentLabel = null;
	private float m_currentValue = 0f;
	

	//Animation variables
	private bool m_animateLabel = false;
	private const float c_labelAngleOffset = -180f;
	private float m_labelStartXRot;
	private float m_labelTargetXRot;
	private float m_currentLabelXRot = 0f;
	private float m_labelAnimationProgess = 0f;
	

	private bool m_animatePointer = false;
	private float m_pointerAngleOffset = 0f;
	private float m_pointerStartZRot;
	private float m_pointerTargetZRot;
	private float m_currentPointerZRot = 0f;
	private float m_pointerAnimationProgess = 0f;

	public void Start()
	{
		m_currentLabelXRot = Utility.ClampAngle(m_labelHolder.transform.localEulerAngles.x);
		m_currentPointerZRot = Utility.ClampAngle(m_gaugePointer.transform.localEulerAngles.z);
	}

	private LabelInfo GetLabelInfo(float _value)
	{
		LabelInfo infoForValue = m_labelInfos[0];
		foreach(LabelInfo info in m_labelInfos)
		{		
			if(info.m_minValue > _value)
				return infoForValue;

			infoForValue = info;
		}
		return infoForValue;
	}

	public void SetFitness(float _score, string _label, bool _instant = false) {
		float targetAngle = m_minAngle + (m_maxAngle - m_minAngle) * (1 - _score);
		if (_instant) {
			SetPointerRotation(targetAngle);
		} else {
			m_pointerStartZRot = m_currentPointerZRot;
			m_pointerTargetZRot = targetAngle;
			m_pointerAngleOffset = m_pointerTargetZRot - m_pointerStartZRot;
			m_pointerAnimationProgess = 0f;
			m_animatePointer = true;
		}
		m_gaugeLabel.text = $"{_score*100:n1}%";
		m_frontLabel.text = _label;
	}

	public void ClearFitness()
	{
		m_gaugeLabel.text = "";
		m_frontLabel.text = "";
	}

	public void DisplayBuildingFitness(ProductDesign _design, bool _instant = false)
	{
		// Set Pointer
		float sum = 0;//_design.CalculateQualityValueOfDesign();
		float value = sum / (float)_design.GetNumberOfPartsInDesign();

		float targetAngle = m_minAngle + (m_maxAngle - m_minAngle) * (1 - value);

		if(_instant)
		{
			SetPointerRotation(targetAngle);
		}
		else
		{
			m_pointerStartZRot = m_currentPointerZRot;
			m_pointerTargetZRot = targetAngle;
			m_pointerAngleOffset = m_pointerTargetZRot - m_pointerStartZRot;
			m_pointerAnimationProgess = 0f;
			m_animatePointer = true;
		}	

		var quality = Mathf.RoundToInt(value * 100f);
		m_frontLabel.text = $"Quality: {quality}%";
	}

	public void Update()
	{
		if(m_animateLabel)
		{
			m_labelAnimationProgess += Time.deltaTime / m_labelAnimationSpeed;
			SetLableRotation(m_labelStartXRot + m_labelAnimationProgess * c_labelAngleOffset);

			if (m_labelAnimationProgess >= 1f) 
			{
				SetLableRotation(m_labelTargetXRot);
				
				m_animateLabel = false;
				m_labelAnimationProgess = 0f;
			}
		}

		if(m_animatePointer)
		{
			m_pointerAnimationProgess += Time.deltaTime / m_pointerAnimationSpeed;
			SetPointerRotation(m_pointerStartZRot + m_pointerAnimationProgess * m_pointerAngleOffset);

			if (m_pointerAnimationProgess >= 1f) 
			{
				SetPointerRotation(m_pointerTargetZRot);
				m_animatePointer = false;
				m_pointerAnimationProgess = 0f;
			}
		}
	}

	private void SetLableRotation(float _rotation)
	{
		m_currentLabelXRot = Utility.ClampAngle(_rotation);
		m_labelHolder.transform.localRotation = Quaternion.Euler(m_currentLabelXRot, 0f, 0f);
	}

	private void SetPointerRotation(float _rotation)
	{
		m_currentPointerZRot = Utility.ClampAngle(_rotation);
		m_gaugePointer.transform.localRotation = Quaternion.Euler(0f, 0f, m_currentPointerZRot);
	}

	public void OnPointerClick(PointerEventData eventData)
	{
	//	DesignGaugeScore.Open();
		//PDMScoreDialog.Create();
	}
}
