using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TurnTableAnimationCallbacks : MonoBehaviour  {
    protected void OnTurnTableElevated () {
        DesignTableManager.Me.TurntableAnimationComplete(false);
        //DesignTableSceneManager.Me.DeskAnimator.SetInteractionBlockerActive (false);
    }
    protected void OnTurnTableLowered () {
        DesignTableManager.Me.TurntableAnimationComplete(true);
        //OnTableLowered?.Invoke();
    }
}
