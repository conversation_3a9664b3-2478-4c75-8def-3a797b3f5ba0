using UnityEngine;

public class HoldPoint : MonoBehaviour
{
    public static Vector3 DefaultHoldOffset = Vector3.up * -2.5f;
    public static Vector3 HoldOffset(GameObject _root)
    {
        var holds = _root.GetComponentsInChildren<HoldPoint>();
        float lowestHold = 1e23f;
        Transform lowest = null;
        foreach (var hold in holds)
        {
            var holdY = Vector3.Dot(hold.transform.position, _root.transform.up);
            if (holdY < lowestHold)
            {
                lowestHold = holdY;
                lowest = hold.transform;
            }
        }
        if (lowest != null)
        {
            var lowestY = Vector3.Dot(lowest.position, _root.transform.up);
            return Vector3.up * -lowestY;
        }
        return DefaultHoldOffset;
    }
}
