using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class DTDragCat : DragBase
{
	public int m_catId;
	public string m_context;
	override public bool AcceptsClicks => true;
	override public bool SupportsLongPress => false;
	override public Camera Cam => DesignTableManager.Me.DrawerCamera;
	override public Vector3 DragPlaneNormal => -Cam.transform.forward;
	override public Vector3 DragPlaneOrigin => transform.position;

	override public void OnClick()
	{
		DesignTableManager.Me.ToggleDesignInPlaceCat(gameObject);
	}

	Vector3 m_lastPos, m_startPos;
	override public void OnDragStart()
	{
		m_lastPos = InputPosition;
		m_startPos = InputPosition;
		m_isXDrag = m_isYDrag = false;
	}
	
	bool m_isXDrag = false, m_isYDrag = false;

	override public void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag)
	{
		var newPos = InputPosition;
		if (m_isXDrag == false && m_isYDrag == false)
		{
			var totalDelta = newPos - m_startPos;
			float threshold = Screen.height * .05f;
			if (totalDelta.x * totalDelta.x > threshold * threshold)
				m_isXDrag = true;
			else if (totalDelta.y * totalDelta.y > threshold * threshold)
				m_isYDrag = true;
		}
		var dx = newPos.x - m_lastPos.x;
		var dy = newPos.y - m_lastPos.y;
		m_lastPos = newPos;
		if (m_isXDrag)
			DesignTableManager.Me.TurnDesignInPlaceCats(dx * 48 / Screen.width);
		if (m_isYDrag)
			DesignTableManager.Me.DragDesignInPlaceCats(gameObject, dy * 20 / Screen.height);
	}
}
