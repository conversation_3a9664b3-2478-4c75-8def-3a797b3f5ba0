using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public partial class DesignTableManager
{
	public Vector3 m_dragPlaneOrigin = Vector3.zero;
	private const float c_designGloballySwitchTime = 1.2f;
	private MABuilding m_designGloballySwitchCandidate = null;
	private float m_designGloballySwitchCandidateTime = 0;
	
	// This defines the drag plane for the design table
	public Vector3 PositionInFront(Ray _ray, List<GameObject> _inHand) {
        Debug.DrawRay(_ray.origin, _ray.direction * 10, Color.yellow);
        Utility.DebugClear("BlockDrag");
		float bestDistance = 1e23f, distance;
		Plane dragPlane;
		if (m_isOverHarness)
			dragPlane = new Plane(m_blockCage.transform.up, m_blockCage.transform.position);
		else
			dragPlane = new Plane(m_designCamera.transform.forward, m_turntableOrigin.transform.position);
		if (dragPlane.Raycast(_ray, out distance) && distance < bestDistance) {
			var point = _ray.GetPoint(distance);
			var heldPos = _inHand[0].transform.position;
			_inHand[0].transform.position = point;
			float minY = GetMinimumY(_inHand[0], -9999f);
			_inHand[0].transform.position = heldPos;
			if (minY < Me.LowestDragHeight) {
				var origin = m_turntableOrigin.transform.position;
				origin.y = point.y + Me.LowestDragHeight - minY;
				if (new Plane(Vector3.up, origin).Raycast(_ray, out distance) && distance < bestDistance) {
					bestDistance = distance;
					Utility.DebugAdd("BlockDrag", $"TTOrigin = {m_turntableOrigin.transform.position} {distance} 1");
				}
			} else {
				bestDistance = distance;
		        Utility.DebugAdd("BlockDrag", $"TTOrigin = {m_turntableOrigin.transform.position} {distance} 2");
			}
		}
		if (Physics.Raycast(_ray, out var hitTerrain, 1000, GameManager.c_layerTerrainBit))
			if (hitTerrain.distance < bestDistance)
			{
				Utility.DebugAdd("BlockDrag", $"Terrain@{hitTerrain.distance}");
				bestDistance = hitTerrain.distance;
			}
		var hits = Physics.RaycastAll(_ray, 1000);//.25f * CurrentBlockBuildScale);
		foreach (var hit in hits) {
			if (hit.distance < bestDistance) {
				if (hit.collider.gameObject.GetComponent<TreeHolder>() != null) continue;
				if (hit.collider.transform.IsChildOf(m_blockHolder.transform, _inHand[0].transform))
				{
					Utility.DebugAdd("BlockDrag", $"{hit.collider.name}@{hit.distance} - 1");
					bestDistance = hit.distance;
				}
				if (IsInDesignInPlace == false && hit.collider.transform.IsChildOf(_inHand[0].transform) == false && hit.collider.transform.IsChildOf(DesignHarness.Me.transform) == false)
				{
					Utility.DebugAdd("BlockDrag", $"{hit.collider.name}@{hit.distance} - 2");
					bestDistance = hit.distance;
				}
			}
		}
		if (bestDistance > 1e22f) return DesignTableManager.Me.m_turntableOrigin.transform.position;
		var camFwd = m_designCamera.transform.forward.GetXZNorm();
        Debug.DrawRay(_ray.origin, camFwd * 10, Color.red);
        var baseScale = CurrentBlockBuildScale * OverHarnessScale;
        var upDistance = m_isInDesignGlobally ? (m_isOverHarness ? 10.0f : .5f) : 0;
        var upDirection = m_isOverHarness ? m_blockCage.transform.up : Vector3.up;
		var newPos = _ray.GetPoint(bestDistance - 0.05f * baseScale) + upDirection * (upDistance * baseScale);
		if (false)//m_isInDesignGlobally)
		{
			// see if we should snap to a new building
			var bestD2 = 1e23f;
			MABuilding bestBuilding = null;
			foreach (var b in m_openBlocks)
			{
				var d2 = (b.transform.position - newPos).xzSqrMagnitude();
				if (d2 < bestD2)
				{
					var building = b.GetComponentInParent<MABuilding>();
					if (building != null)
					{
						bestD2 = d2;
						bestBuilding = building;
					}
				}
			}
			if (bestBuilding != m_designGloballyFocusBuilding)
			{
				if (bestBuilding == m_designGloballySwitchCandidate)
				{
					m_designGloballySwitchCandidateTime += Time.deltaTime;
					if (m_designGloballySwitchCandidateTime > c_designGloballySwitchTime)
					{
						SetDesignGloballyFocus(bestBuilding);
					}
				}
				else
				{
					m_designGloballySwitchCandidate = bestBuilding;
					m_designGloballySwitchCandidateTime = 0;
				}
			}
		}
		return newPos;
	}

	private float GetSnapScore(float bestD2, SnapHinge heldHinge, SnapHinge staticHinge, EDesignFitnessType blockType, Camera cam, Vector3 camFwd, Vector3 camFwdFlat, float rootModifier, float forceSnap2DDistSqrd)
	{
		if (IsInBuildingMode)
			return GetSnapScore_Building(bestD2, heldHinge, staticHinge, blockType, cam, camFwd, camFwdFlat, rootModifier, forceSnap2DDistSqrd);
		return GetSnapScore_Product(bestD2, heldHinge, staticHinge, blockType, cam, camFwd, camFwdFlat, rootModifier, forceSnap2DDistSqrd);
	}

	const int c_maxDebugSnaps = 8;
	private List<(float, string)> m_debugSnaps;
	private void RegisterDebugSnap(float _d2, string _msg)
	{
		if (m_debugSnaps == null) m_debugSnaps = new();
		for (int i = 0; i < m_debugSnaps.Count; ++i)
		{
			if (_d2 < m_debugSnaps[i].Item1)
			{
				m_debugSnaps.Insert(i, (_d2, _msg));
				if (m_debugSnaps.Count >= c_maxDebugSnaps)
					m_debugSnaps.RemoveAt(m_debugSnaps.Count - 1);
				return;
			}
		}
		if (m_debugSnaps.Count < c_maxDebugSnaps)
			m_debugSnaps.Add((_d2, _msg));
	}

	public string GetDebugSnapData()
	{
		if (DebugHingeSnaps == false || m_debugSnaps == null)
			return "";
		System.Text.StringBuilder sb = new();
		for (int i = 0; i < m_debugSnaps.Count; ++i)
		{
			sb.Append(m_debugSnaps[i].Item2);
			sb.Append("\n");
		}
		return sb.ToString();
	}

	private float GetSnapScore_Product(float bestD2, SnapHinge heldHinge, SnapHinge staticHinge, EDesignFitnessType blockType, Camera cam, Vector3 camFwd, Vector3 camFwdFlat, float rootModifier, float forceSnap2DDistSqrd)
	{
		// check for desired coupling
		float screenDirectionModifierI = 1;
		
		// in building design mode highly prioritise hinge direction
		float hingeDirectionPriorityFactor = .5f, hingeUpPriorityFactor = 0;

		// InheritVisuals have to be coupled
		float inheritVisualsModifier = 1;
		if (heldHinge.HingeCategory == SnapHinge.ECategory.InheritVisuals)
		{
			if (staticHinge.HingeCategory != SnapHinge.ECategory.InheritVisualsSource)
				return 1e23f;
			inheritVisualsModifier = .1f; // highly prefer this coupling
		}
		
		if (heldHinge.CanSnapTo(staticHinge) == false)
			return 1e23f;

		float couplingModifier = 1f;
		const float c_matchingMagnetCategoryInfluence = 3f;
		const float c_matchingMagnetCategoryInfluenceSpecial = 8f;
		if (staticHinge.HingeCategory != SnapHinge.ECategory.Default)
		{
			if (staticHinge.HingeCategory == heldHinge.HingeCategory)
			{
				if (staticHinge.HingeCategory == SnapHinge.ECategory.Special)
				{
					couplingModifier = 1f / c_matchingMagnetCategoryInfluenceSpecial;
				}
				else
				{
					couplingModifier = 1f / c_matchingMagnetCategoryInfluence;
				}
			}
			else if (staticHinge.HingeCategory == SnapHinge.ECategory.LastChoice)
				couplingModifier = 5;
		}
		// check for direction compatibility
		bool isMirrored = false;
		float directionCompatibilityModifier = .5f / hingeDirectionPriorityFactor;
		SnapHinge.EType matchingDirection;
		var heldDirection = heldHinge.HingeDirection;
		var staticDirection = staticHinge.HingeDirection;
		if (true)
		{
			if (heldDirection == SnapHinge.EType.Left || heldDirection == SnapHinge.EType.Front || heldDirection == SnapHinge.EType.Back)
				heldDirection = SnapHinge.EType.Right;
			if (staticDirection == SnapHinge.EType.Right || staticDirection == SnapHinge.EType.Front || staticDirection == SnapHinge.EType.Back)
				staticDirection = SnapHinge.EType.Left;
		}
		if (SnapHinge.OppositeHinge.TryGetValue(heldDirection, out matchingDirection))
		{
			if (staticDirection == matchingDirection)
			{
				directionCompatibilityModifier = hingeDirectionPriorityFactor;
			}
			else if (heldHinge.Mirrorable)
			{
				if (SnapHinge.OppositeHinge.TryGetValue(matchingDirection, out matchingDirection))
				{
					if (staticDirection == matchingDirection)
					{
						directionCompatibilityModifier = hingeDirectionPriorityFactor;
						isMirrored = true;
					}
				}
			}
		}
		var upMod = 1 - (staticHinge.transform.forward.y * staticHinge.transform.forward.y) * hingeUpPriorityFactor;
		directionCompatibilityModifier *= upMod;

		bool wasMirrored = ApplyMirror(heldHinge, isMirrored);
		if (isMirrored != wasMirrored)
			OffsetToCompensateForMirror(heldHinge, wasMirrored, isMirrored);

		float typeCompatibilityModifier = 1f;
		const float c_correctHingeTypeInfluence = 3f;
		if (staticHinge.RequiredAttatchTypes.Contains(blockType))
		{
			typeCompatibilityModifier = 1f / c_correctHingeTypeInfluence;
		}

		// direction modifier - prefer hinges directly in line with each other
		var heldToStatic = staticHinge.transform.position - heldHinge.transform.position;
		var directionModifier = Vector3.Dot(heldToStatic, staticHinge.transform.forward);
		const float c_maxPushFromBehindDistance = .5f;
		directionModifier = 1 + Mathf.Sign(directionModifier - c_maxPushFromBehindDistance) * .1f;
		var directionModifierMinor = Vector3.Dot(heldHinge.transform.forward, staticHinge.transform.forward);
		directionModifierMinor = 1.2f - directionModifierMinor * directionModifierMinor * .4f;
		// up direction modifier - prefer side/forward pairs and up/down pairs
		float upDirectionModifier = Mathf.Abs(heldHinge.transform.forward.y) - Mathf.Abs(staticHinge.transform.forward.y);
		upDirectionModifier = 0.5f + upDirectionModifier * upDirectionModifier;
		// screen direction modifier - prefer hinges that aren't pointing into/out of the screen
		var screenDirectionModifierJ = Vector3.Dot(staticHinge.transform.forward, camFwdFlat);
		screenDirectionModifierJ = 1 + screenDirectionModifierJ * screenDirectionModifierJ;
		// combine modifiers
		var totalModifier = directionModifier * upDirectionModifier * couplingModifier *
		                    directionCompatibilityModifier * directionModifierMinor * typeCompatibilityModifier *
		                    screenDirectionModifierI * screenDirectionModifierJ * rootModifier * inheritVisualsModifier;
		// get square distance between hinges in screen space and modify with all of the modifiers
		var screenPosI = cam.WorldToScreenPoint(heldHinge.transform.position);
		screenPosI.z = 0;
		var screenPosJ = cam.WorldToScreenPoint(staticHinge.transform.position);
		screenPosJ.z = 0;
		var rawD2 = (staticHinge.transform.position - heldHinge.transform.position).sqrMagnitude;
		var scrD2 = (screenPosI - screenPosJ).sqrMagnitude;
		var sInScreenDirection = Vector3.Dot(staticHinge.transform.forward, camFwd);
		var hInScreenDirection = Vector3.Dot(heldHinge.transform.forward, camFwd);
		if (Mathf.Abs(sInScreenDirection) < .3f && Mathf.Abs(hInScreenDirection) < .3f)
			if (scrD2 < forceSnap2DDistSqrd)
				rawD2 *= .1f;
		var d2 = rawD2 * totalModifier;
	
		if (DebugHingeSnaps)
			RegisterDebugSnap(d2, $"<color=#40ff40>S:{staticHinge.transform.parent.parent.name}.{staticHinge.transform.name}</color> <color=#ff8080>H:{heldHinge.transform.parent.parent.name}.{heldHinge.transform.name}</color> <color=#ffffff> - d2:{d2} (best:{bestD2}) - raw:{rawD2} ({scrD2} : {forceSnap2DDistSqrd} : {sInScreenDirection} : {hInScreenDirection}) [{screenPosI}..{screenPosJ}] * dm:{directionModifier} [{heldToStatic} . {staticHinge.transform.forward}] * udm:{upDirectionModifier} * cm:{couplingModifier} * dcm:{directionCompatibilityModifier} * tcm:{typeCompatibilityModifier} * sdmi:{screenDirectionModifierI} * sdmj:{screenDirectionModifierJ}</color> <color=#ffffff>IsBlocked:{CheckForBlockers(staticHinge.transform, heldHinge.transform)} {DesignInPlaceCheckValidPosition(staticHinge.transform.position)}</color>"); 
		//if (DebugHingeSnaps && (IsDebugSnap(staticHinge) || d2 < bestD2))
		//	Debug.LogError($"<color={(d2 < bestD2 ? "#40ff40" : "#ff4040")}>S:{staticHinge.transform.parent.parent.name}.{staticHinge.transform.name} H:{heldHinge.transform.parent.parent.name}.{heldHinge.transform.name} - d2:{d2} (best:{bestD2}) - raw:{rawD2} ({scrD2} : {forceSnap2DDistSqrd} : {sInScreenDirection} : {hInScreenDirection}) [{screenPosI}..{screenPosJ}] * dm:{directionModifier} [{heldToStatic} . {staticHinge.transform.forward}] * udm:{upDirectionModifier} * cm:{couplingModifier} * dcm:{directionCompatibilityModifier} * tcm:{typeCompatibilityModifier} * sdmi:{screenDirectionModifierI} * sdmj:{screenDirectionModifierJ}</color> <color=#ffffff>IsBlocked:{CheckForBlockers(staticHinge.transform, heldHinge.transform)} {DesignInPlaceCheckValidPosition(staticHinge.transform.position)}</color>", staticHinge.gameObject);
		
		ApplyMirror(heldHinge, wasMirrored);
		if (isMirrored != wasMirrored)
			OffsetToCompensateForMirror(heldHinge, isMirrored, wasMirrored);
		return d2;
	}

	private void OffsetToCompensateForMirror(SnapHinge _source, bool _wasMirrored, bool _isMirrored)
	{
		var block = _source.gameObject.GetComponentInParent<Block>();
		var offset = Vector3.zero;
		if (block?.GetFlippedVisualOffset(out offset) ?? false)
		{
			if (_wasMirrored) offset = -offset;
			block.transform.position += offset;
		}
	}

	private static bool s_oldStyleBuildingDesignMode = false;
	static DebugConsole.Command s_oldstylebuildingdesign = new ("oldstylebuildingdesign", _s => Utility.SetOrToggle(ref s_oldStyleBuildingDesignMode, _s));
	
	enum EPrimaryDirection { Side, Top, Bottom, None };
	EPrimaryDirection GetSnapPrimaryDirection(SnapHinge _sh) => _sh.HingeDirection switch
	{
		SnapHinge.EType.Top => EPrimaryDirection.Top,
		SnapHinge.EType.Bottom => EPrimaryDirection.Bottom,
		SnapHinge.EType.Left => EPrimaryDirection.Side,
		SnapHinge.EType.Right => EPrimaryDirection.Side,
		SnapHinge.EType.Front => EPrimaryDirection.Side,
		SnapHinge.EType.Back => EPrimaryDirection.Side,
		_ => EPrimaryDirection.None
	};
	private float GetSnapScore_Building(float bestD2, SnapHinge heldHinge, SnapHinge staticHinge, EDesignFitnessType blockType, Camera cam, Vector3 camFwd, Vector3 camFwdFlat, float rootModifier, float forceSnap2DDistSqrd)
	{
		if (s_oldStyleBuildingDesignMode)
			return GetSnapScore_Building_Original(bestD2, heldHinge, staticHinge, blockType, cam, camFwd, camFwdFlat, rootModifier, forceSnap2DDistSqrd);
		
		var heldHingePrimaryDirection = GetSnapPrimaryDirection(heldHinge);
		var staticHingePrimaryDirection = GetSnapPrimaryDirection(staticHinge);
		var both = (int) heldHingePrimaryDirection | (int) staticHingePrimaryDirection;
		if (both != 0 && both != 3) // not side-side, top-bottom or none-x
			return 1e23f;

		const float c_directionBiasStrength = 1000;
		var directionCompatibility = -Vector3.Dot(staticHinge.transform.forward, heldHinge.transform.forward);
		var directionBias = 1.0f + (1.0f - directionCompatibility) * c_directionBiasStrength;
		var upBias = 1.0f - staticHinge.transform.forward.y * .1f; // slight bias towards up-static so that to consistently tie-break against side-statics
		
		if (staticHinge.transform.forward.y.Nearly(0, .1f))
		{
			var heldHingeY = heldHinge.transform.position.y;
			var heldObjY = heldHinge.GetComponentInParent<Block>().transform.position.y;
			var staticHingeY = staticHinge.transform.position.y;
			var staticBuilding = staticHinge.GetComponentInParent<NGCommanderBase>();
			if (staticBuilding != null)
			{
				var heldBuildingY = staticBuilding.transform.position.y;
				var finalHeldObjY = heldObjY + staticHingeY - heldHingeY;
				if (finalHeldObjY < heldBuildingY) return 1e23f;
			}
		}

		var categoryBias = staticHinge.HingeCategory == SnapHinge.ECategory.LastChoice ? 5f : 1f;
		if (staticHinge.HingeDirection == SnapHinge.EType.Top) categoryBias *= .1f; // prefer bottom-to-top snaps over side-to-side ones
		
		var totalBias = directionBias * categoryBias * upBias;
		
		//var rawD2 = (staticHinge.transform.position - heldHinge.transform.position).sqrMagnitude;

		var screenPosI = cam.WorldToScreenPoint(heldHinge.transform.position + heldHinge.transform.forward * heldHinge.transform.lossyScale.x);
		screenPosI.z = 0;
		var screenPosJ = cam.WorldToScreenPoint(staticHinge.transform.position + staticHinge.transform.forward * staticHinge.transform.lossyScale.x);
		screenPosJ.z = 0;
		var rawD2 = (screenPosI - screenPosJ).sqrMagnitude;
		
		var d2 = rawD2 * totalBias;
		
		if (DebugHingeSnaps)
			RegisterDebugSnap(d2, $"<color=#40ff40>S:{staticHinge.transform.parent.parent.name}.{staticHinge.transform.name}</color> <color=#ff8080>H:{heldHinge.transform.parent.parent.name}.{heldHinge.transform.name}</color> <color=#ffffff> - d2:{d2} (best:{bestD2}) - raw:{rawD2} db:{directionBias} cb:{categoryBias}</color>"); 
		//if (DebugHingeSnaps && (IsDebugSnap(staticHinge) || d2 < bestD2))
		//	Debug.LogError($"<color={(d2 < bestD2 ? "#40ff40" : "#ff4040")}>S:{staticHinge.transform.parent.parent.name}.{staticHinge.transform.name} H:{heldHinge.transform.parent.parent.name}.{heldHinge.transform.name} - d2:{d2} (best:{bestD2}) - raw:{rawD2} db:{directionBias} cb:{categoryBias}</color>");
		
		return d2;
	}
	

	private float GetSnapScore_Building_Original(float bestD2, SnapHinge heldHinge, SnapHinge staticHinge, EDesignFitnessType blockType, Camera cam, Vector3 camFwd, Vector3 camFwdFlat, float rootModifier, float forceSnap2DDistSqrd)
	{
		// check for desired coupling
		float screenDirectionModifierI = 1;
		
		// in building design mode highly prioritise hinge direction
		float hingeDirectionPriorityFactor = .02f, hingeUpPriorityFactor = .5f; // how much do we prioritise 'up' hinges?

		float couplingModifier = 1f;
		const float c_matchingMagnetCategoryInfluence = 3f;
		const float c_matchingMagnetCategoryInfluenceSpecial = 8f;
		if (staticHinge.HingeCategory != SnapHinge.ECategory.Default)
		{
			if (staticHinge.HingeCategory == heldHinge.HingeCategory)
			{
				if (staticHinge.HingeCategory == SnapHinge.ECategory.Special)
				{
					couplingModifier = 1f / c_matchingMagnetCategoryInfluenceSpecial;
				}
				else
				{
					couplingModifier = 1f / c_matchingMagnetCategoryInfluence;
				}
			}
			else if (staticHinge.HingeCategory == SnapHinge.ECategory.LastChoice)
				couplingModifier = 5;
		}
		// check for direction compatibility
		bool isMirrored = false;
		float directionCompatibilityModifier = .5f / hingeDirectionPriorityFactor;
		SnapHinge.EType matchingDirection;
		var heldDirection = heldHinge.HingeDirection;
		var staticDirection = staticHinge.HingeDirection;
		if (true)
		{
			if (heldDirection == SnapHinge.EType.Left || heldDirection == SnapHinge.EType.Front || heldDirection == SnapHinge.EType.Back)
				heldDirection = SnapHinge.EType.Right;
			if (staticDirection == SnapHinge.EType.Right || staticDirection == SnapHinge.EType.Front || staticDirection == SnapHinge.EType.Back)
				staticDirection = SnapHinge.EType.Left;
		}
		if (SnapHinge.OppositeHinge.TryGetValue(heldDirection, out matchingDirection))
		{
			if (staticDirection == matchingDirection)
			{
				directionCompatibilityModifier = hingeDirectionPriorityFactor;
			}
			else if (heldHinge.Mirrorable)
			{
				if (SnapHinge.OppositeHinge.TryGetValue(matchingDirection, out matchingDirection))
				{
					if (staticDirection == matchingDirection)
					{
						directionCompatibilityModifier = hingeDirectionPriorityFactor;
						isMirrored = true;
					}
				}
			}
		}
		var upMod = 1 - (staticHinge.transform.forward.y * staticHinge.transform.forward.y) * hingeUpPriorityFactor;
		directionCompatibilityModifier *= upMod;

		bool wasMirrored = ApplyMirror(heldHinge, isMirrored);

		float typeCompatibilityModifier = 1f;
		const float c_correctHingeTypeInfluence = 3f;
		if (staticHinge.RequiredAttatchTypes.Contains(blockType))
		{
			typeCompatibilityModifier = 1f / c_correctHingeTypeInfluence;
		}

		// direction modifier - prefer hinges directly in line with each other
		var heldToStatic = staticHinge.transform.position - heldHinge.transform.position;
		var directionModifier = Vector3.Dot(heldToStatic, staticHinge.transform.forward);
		const float c_maxPushFromBehindDistance = .5f;
		directionModifier = 1 + Mathf.Sign(directionModifier - c_maxPushFromBehindDistance) * .1f;
		var directionModifierMinor = Vector3.Dot(heldHinge.transform.forward, staticHinge.transform.forward);
		directionModifierMinor = 1.2f - directionModifierMinor * directionModifierMinor * .4f;
		// up direction modifier - prefer side/forward pairs and up/down pairs
		float upDirectionModifier = Mathf.Abs(heldHinge.transform.forward.y) - Mathf.Abs(staticHinge.transform.forward.y);
		upDirectionModifier = 0.5f + upDirectionModifier * upDirectionModifier;
		// screen direction modifier - prefer hinges that aren't pointing into/out of the screen
		var screenDirectionModifierJ = Vector3.Dot(staticHinge.transform.forward, camFwdFlat);
		screenDirectionModifierJ = 1 + screenDirectionModifierJ * screenDirectionModifierJ;
		// combine modifiers
		var totalModifier = directionModifier * upDirectionModifier * couplingModifier *
		                    directionCompatibilityModifier * directionModifierMinor * typeCompatibilityModifier *
		                    screenDirectionModifierI * screenDirectionModifierJ * rootModifier;
		// get square distance between hinges in screen space and modify with all of the modifiers
		var screenPosI = cam.WorldToScreenPoint(heldHinge.transform.position);
		screenPosI.z = 0;
		var screenPosJ = cam.WorldToScreenPoint(staticHinge.transform.position);
		screenPosJ.z = 0;
		var rawD2 = (staticHinge.transform.position - heldHinge.transform.position).sqrMagnitude;
		var scrD2 = (screenPosI - screenPosJ).sqrMagnitude;
		var sInScreenDirection = Vector3.Dot(staticHinge.transform.forward, camFwd);
		var hInScreenDirection = Vector3.Dot(heldHinge.transform.forward, camFwd);
		if (Mathf.Abs(sInScreenDirection) < .3f && Mathf.Abs(hInScreenDirection) < .3f)
			if (scrD2 < forceSnap2DDistSqrd)
				rawD2 *= .1f;
		var d2 = rawD2 * totalModifier;

		if (DebugHingeSnaps && (IsDebugSnap(staticHinge) || d2 < bestD2))
			Debug.LogError($"<color={(d2 < bestD2 ? "#40ff40" : "#ff4040")}>S:{staticHinge.transform.parent.parent.name}.{staticHinge.transform.name} H:{heldHinge.transform.parent.parent.name}.{heldHinge.transform.name} - d2:{d2} (best:{bestD2}) - raw:{rawD2} ({scrD2} : {forceSnap2DDistSqrd} : {sInScreenDirection} : {hInScreenDirection}) [{screenPosI}..{screenPosJ}] * dm:{directionModifier} [{heldToStatic} . {staticHinge.transform.forward}] * udm:{upDirectionModifier} * cm:{couplingModifier} * dcm:{directionCompatibilityModifier} * tcm:{typeCompatibilityModifier} * sdmi:{screenDirectionModifierI} * sdmj:{screenDirectionModifierJ}</color> <color=#ffffff>IsBlocked:{CheckForBlockers(staticHinge.transform, heldHinge.transform)} {DesignInPlaceCheckValidPosition(staticHinge.transform.position)}</color>", staticHinge.gameObject);
		
		ApplyMirror(heldHinge, wasMirrored);
		return d2;
	}
}
