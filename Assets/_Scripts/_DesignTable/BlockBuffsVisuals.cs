using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class BlockBuffsVisuals : MonoBehaviour
{
    public Image m_icon;
    public TMPro.TextMeshProUGUI m_label;
    private GameObject m_parent;
    private float m_currentScale = 0;
    private float m_minY = -1e23f;
    private float m_baseScale = 2;
    private Camera m_camera = null;
    
    public void SetParent(GameObject _block)
    {
        m_parent = _block;
    }

    public void SetCamera(Camera _cam)
    {
        m_camera = _cam;
    }

    public void SetScale(float _scale)
    {
        m_baseScale = _scale;
    }
    
    public void ClampY(float _minY)
    {
        m_minY = _minY;
    }
    public void SetIcon(Sprite _sprite)
    {
        m_icon.sprite = _sprite;
    }

    public void SetLabel(string _s)
    {
        m_label.SetText(_s);
    }
    
    void Update()
    {
        var block = m_parent.GetComponentInChildren<Block>();
        if (block == null) return;
        Transform bestHinge = null;
        float bestFwd = -1;
        var bestPos = Vector3.zero;
        var fwd = Vector3.zero;
        bool useCylinderFromHinges = true;
        bool useBounds = true;
        if (useBounds)
        {
            var oldRot = m_parent.transform.rotation;
            m_parent.transform.rotation = Quaternion.identity;
            var bounds = ManagedBlock.GetTotalVisualBounds(block.m_toVisuals?.gameObject);
            var visScale = block.m_toVisuals.lossyScale;
            const float c_smallScale = .5f;
            if (bounds.extents.sqrMagnitude < .01f * .01f)
            {
                bounds.center = m_parent.transform.position;
                bounds.extents = Vector3.one * (c_smallScale * visScale.x);
            }
            m_parent.transform.rotation = oldRot;
            fwd = m_camera.transform.position - bounds.center;
            if (fwd.x * fwd.x > fwd.z * fwd.z) fwd = Vector3.right * Mathf.Sign(fwd.x);
            else fwd = Vector3.forward * Mathf.Sign(fwd.z);
            float dPosX = Vector3.Dot(fwd, m_parent.transform.right);
            float dPosZ = Vector3.Dot(fwd, m_parent.transform.forward);
            var worldExtent = bounds.extents / visScale.x;
            bool xSmall = worldExtent.x * worldExtent.x < c_smallScale;
            bool zSmall = worldExtent.z * worldExtent.z < c_smallScale;
            if (xSmall && !zSmall) dPosZ = 0;
            else if (zSmall && !xSmall) dPosX = 0;
            if (dPosX * dPosX > dPosZ * dPosZ)
            {
                fwd = m_parent.transform.right * Mathf.Sign(dPosX);
                bestPos = bounds.center + fwd * bounds.extents.x;
            }
            else
            {
                fwd = m_parent.transform.forward * Mathf.Sign(dPosZ);
                bestPos = bounds.center + fwd * bounds.extents.z;
            }
        }
        else if (useCylinderFromHinges)
        {
            Vector3 min = Vector3.one * 1e23f, max = Vector3.one * -1e23f;
            foreach (Transform t in block.m_toHinges)
            {
                min = Vector3.Min(min, t.position);
                max = Vector3.Max(max, t.position);
            }
            var center = block.transform.position;
            var extent = Vector3.Max(max - center, center - min);
            //var center = (max + min) * .5f;
            //var extent = (max - min) * .5f;
            var radius = extent.xzMagnitude();
            fwd = m_camera.transform.position - center;
            if (fwd.x * fwd.x > fwd.z * fwd.z) fwd = Vector3.right * Mathf.Sign(fwd.x);
            else fwd = Vector3.forward * Mathf.Sign(fwd.z);
            bestPos = center + radius * fwd;
        }
        else
        {
            foreach (Transform t in block.m_toHinges)
            {
                float fwdT = -Vector3.Dot(t.forward, m_camera.transform.forward);
                if (fwdT > bestFwd)
                {
                    bestFwd = fwdT;
                    bestHinge = t;
                }
            }
            if (bestHinge != null)
            {
                bestPos = bestHinge.position + bestHinge.forward * .1f;
                fwd = bestHinge.forward;
            }
        }
        bestPos.y = Mathf.Max(bestPos.y, m_minY);
        float targetScale = 1;
        if ((bestPos - transform.position).sqrMagnitude > .01f*.01f)
        {
            //targetScale = 0;
            //if (m_currentScale < .01f)
            {
                if (fwd.sqrMagnitude > 0)
                {
                    transform.position = bestPos;
                    transform.forward = -fwd;
                }
            }
        }
        m_currentScale = Mathf.Lerp(m_currentScale, targetScale, .5f);
        transform.localScale = Vector3.one * (m_currentScale * m_baseScale);
    }
}
