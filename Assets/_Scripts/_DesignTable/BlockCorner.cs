using System.Collections;
using System.Collections.Generic;
using UnityEngine;

//----------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------
[System.Serializable]
public class BlockCorner
{
    public enum EType
    {
        None,
        StraightA,
        StraightB,
        Convex,
        Concave,
        Internal,
    }

    // -- public --
    public EType CurrentType { get { return m_type; } }
    
    public Transform m_straightA;
    public Transform m_straightB;
    public Transform m_convex;  // Piece used to close with own adjacent wall
    public Transform m_concave; // Piece used to close two adjacent cells
    public Transform m_internal;
    
    public static EType GetType(string _name)
    {
        var type = EType.None;
        var name = _name.ToLower();

        if (name.Equals("corner_convex")) type = EType.Convex;
        else if (name.Equals("corner_concave")) type = EType.Concave;
        else if (name.Equals("corner_straighta")) type = EType.StraightA;
        else if (name.Equals("corner_straightb")) type = EType.StraightB;
        else if (name.Equals("corner_internal")) type = EType.Internal;

        return type;
    }

    //----------------------------------------------------------------------------------------
    public void Assign(EType _type, Transform _transform)
    {
        m_type = _type;
        switch(_type)
        {
            case EType.StraightA: m_straightA = _transform; break;
            case EType.StraightB: m_straightB = _transform; break;
            case EType.Convex: m_convex = _transform; break;
            case EType.Concave: m_concave = _transform; break;
            case EType.Internal: m_internal = _transform; break;
        }
    }

    public void Enable(bool _enable)
    {
        if (m_straightA != null) m_straightA.gameObject.SetActive(_enable);
        if (m_straightB != null) m_straightB.gameObject.SetActive(_enable);
        if (m_convex != null) m_convex.gameObject.SetActive(_enable);
        if (m_concave != null) m_concave.gameObject.SetActive(_enable);
        if (m_internal != null) m_internal.gameObject.SetActive(_enable);
    }

    // -- private --
    private EType m_type;
}

