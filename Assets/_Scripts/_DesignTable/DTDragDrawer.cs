using System.Collections;
using System.Collections.Generic;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.EventSystems;
using Random = UnityEngine.Random;

public class DTDragDrawer : DragBase {
	public int m_index = 0;
	public GameObject m_contents;
	public float m_fullyClosedPosition = 0;
	public float m_fullyOpenPosition = 100;
	public float m_maxOpenFraction = 1f;
	public bool m_allowClick = true;
	public string m_soundClickOpen;
	public string m_soundClickClose;
	
	public Animator m_controlledAnimator;
	public Transform m_controlledClipper;
	
	public System.Func<float, float> m_checkMaxOpen; 
	
	float m_slidePosition = 0, m_slidePositionSmoothed = 0;
	float m_posPrevious;
	Vector3 m_positionAtStart;
	bool m_havePositionAtStart = false;
    private int m_makingSound;

	override public Camera Cam => DesignTableManager.Me.DrawerCamera;
	public float Pos => m_slidePositionSmoothed;
	public bool IsMoving => !m_slidePositionSmoothed.Nearly(m_slidePosition);
	public float MaxPos => m_fullyOpenPosition * m_maxOpenFraction * transform.lossyScale.x;

	public float Fraction
	{
		get
		{
			var min = Mathf.Min(m_fullyClosedPosition, m_fullyOpenPosition * m_maxOpenFraction);
			var max = m_fullyClosedPosition + m_fullyOpenPosition * m_maxOpenFraction - min;
			var scale = transform.lossyScale.x;
			min *= scale; max *= scale;
			return Mathf.Clamp01((m_slidePosition - min) / (max - min));
		}
	}


	float OpenPos => m_isUI ? m_fullyOpenPosition : Mathf.Lerp(m_fullyClosedPosition, m_fullyOpenPosition, .05f * DesignTableManager.c_blockBaseScale);
	const float c_epsilon = .0001f;
	public bool IsClosed => Mathf.Abs(m_slidePosition - m_fullyClosedPosition) < c_epsilon;
	public bool IsClosedVisually => Mathf.Abs(m_slidePositionSmoothed - m_fullyClosedPosition) < c_epsilon;

	public void SetPos(float _pos, bool _immediate = false)
	{
		m_slidePosition = _pos;
		if (_immediate) m_slidePositionSmoothed = m_slidePosition;
	}

	public void ResetSlidePosition()
	{
		m_slidePosition = m_slidePositionSmoothed = m_fullyClosedPosition;
		if (m_havePositionAtStart) transform.position = m_positionAtStart;
		m_havePositionAtStart = false;
	}
	public void Close(bool _immediate = false) {
		m_slidePosition = m_fullyClosedPosition;
		if (_immediate) {
			m_slidePositionSmoothed = m_slidePosition;
			transform.position = m_positionAtStart;
		}
		if (m_isLocked) m_lockedPosition = m_slidePosition;
        if (!IsClosed)
        {
            if (GameManager.Me.IsOKToPlayDesignTableSound())
                AudioClipManager.Me.PlaySoundOld("PlaySound_TableCloseDrawer", transform);
        }
	}
    public void Open()
    {
        m_slidePosition = OpenPos;
        if (m_isLocked) m_lockedPosition = m_slidePosition;
        if (!IsClosed)
        {
            if (GameManager.Me.IsOKToPlayDesignTableSound())
            {
                AudioClipManager.Me.PlaySoundOld("PlaySound_TableOpenDrawer", transform);
            }
        }
		//NGTutorialManager.Me.FireOpenDrawerTrigger(m_index);
	}

	override public bool AcceptsClicks => true;

	public Vector3 ClosedWorldPos { get { return m_positionAtStart; } }
	public Vector3 OpenWorldPos { get { return ClosedWorldPos + Forward * OpenPos * .5f; } }
	
	public bool m_isUI = false;
	private Vector3 Forward => m_isUI ? transform.right : transform.forward;
	
	bool m_isLocked = false;
	float m_lockedPosition = 0;
	public void Lock(bool _lock, bool _forceClose) {
		m_isLocked = _lock;
		if (_lock) {
			m_lockedPosition = _forceClose ? m_fullyClosedPosition : m_slidePosition;
			m_slidePosition = m_lockedPosition;
		}
	}

	public void ForceClose()
	{
		m_slidePosition = m_fullyClosedPosition;
	}
		
	override public void OnClick() {
		if (m_allowClick == false) return;
        if (IsClosed)
        {
            if (!m_isLocked && !string.IsNullOrEmpty(m_soundClickOpen))
                AudioClipManager.Me.PlayUISound(m_soundClickOpen);
            m_slidePosition = OpenPos;
			//NGTutorialManager.Me.FireOpenDrawerTrigger(m_index);
		}
		else
        {
	        if (!string.IsNullOrEmpty(m_soundClickClose))
		        AudioClipManager.Me.PlayUISound(m_soundClickClose);
            m_slidePosition = m_fullyClosedPosition;
        }
	}

	public Vector3 m_sliderOffset = Vector3.zero;
	float PositionOnSlider(Ray _ray)
	{
		if (m_isUI)
			return Vector3.Dot(GameManager.InputPosition(0), Forward);
		return DragBase.PositionOnSlider(_ray, m_positionAtStart + m_sliderOffset, Forward);
	}

	public void CheckPositionAtStart() {
		if (m_havePositionAtStart) return;
		m_positionAtStart = transform.position;
		m_havePositionAtStart = true;
	}

	override public void OnPress()
	{
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.DrawerTap);
	}
	override public void OnDragStart() {
		m_posPrevious = PositionOnSlider(Cam.RayAtMouse());
		m_slidePosition = m_slidePositionSmoothed;
	}
    public override void OnDragEnd(bool _undo)
    {
		if (AudioClipManager.Me && m_makingSound != 0)
		{
			AudioClipManager.Me.ForceStopSound(m_makingSound);
			m_makingSound = 0;
		}
        base.OnDragEnd(_undo);
    }

    override public void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag) {
		var dragNow = PositionOnSlider(Cam.RayAtMouse());
		var delta = dragNow - m_posPrevious;
		m_slidePosition += delta;
		m_posPrevious = dragNow;
        if(math.abs(delta) > 0)
        {
		    if (GameManager.Me.IsOKToPlayDesignTableSound() && m_makingSound == 0 && !m_isLocked)
			    m_makingSound = AudioClipManager.Me.PlaySoundOld("PlaySound_Table_DragDrawer", transform);
        }
        else
        {
			if (AudioClipManager.Me && m_makingSound != 0)
			{
				AudioClipManager.Me.ForceStopSound(m_makingSound);
				m_makingSound = 0;
			}
		}
		//NGTutorialManager.Me.FireOpenDrawerTrigger(m_index);
	}
    private float m_lastSlidePositionChecked = -1;
    void Update() {
		m_positionAtStart = transform.position - Forward * m_slidePositionSmoothed;
		
		var min = Mathf.Min(m_fullyClosedPosition, m_fullyOpenPosition * m_maxOpenFraction);
		var max = m_fullyClosedPosition + m_fullyOpenPosition * m_maxOpenFraction - min;
		var scale = transform.lossyScale.x;
		m_slidePosition = Mathf.Clamp(m_slidePosition, min * scale, max * scale);
		if (m_slidePosition.Nearly(m_lastSlidePositionChecked) == false && m_checkMaxOpen != null)
		{
			m_slidePosition = m_checkMaxOpen(m_slidePosition);
			m_lastSlidePositionChecked = m_slidePosition;
		}
		if (m_isLocked) m_slidePosition = m_lockedPosition;
		float oldPos = m_slidePositionSmoothed;
		m_slidePositionSmoothed = Mathf.Lerp(m_slidePositionSmoothed, m_slidePosition, .25f);
		if (m_controlledAnimator != null)
		{
			var clampedScale = Mathf.Min(1, scale);
			var fraction = m_slidePositionSmoothed / (m_fullyOpenPosition * clampedScale);
			m_controlledAnimator.SetFloat("time", fraction);
			if (m_controlledClipper != null)
			{
				var clip = m_controlledClipper.GetComponent<UnityEngine.UI.RectMask2D>();
				clip.padding = new Vector4(m_slidePositionSmoothed, 0, 0, 0);
			}
		}
		else
		{
			transform.position += Forward * ((m_slidePositionSmoothed - oldPos));
		}
    }
}
