using System;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
public class DesignTableButtonData {
	public bool m_enable = true;
	public bool m_interactable = true;
	public string m_text;
	public Action m_onClick;
}
public class DesignTableButtonScript : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDown<PERSON><PERSON>ler, IPointerUpHandler {
	[SerializeField] Animator m_buttonAnimator;
	[SerializeField] TextMeshPro m_text;
	event Action OnClick;
	public bool Interactable {get; set;} = true;
	public string GetText () {
		return m_text.text;
	}
	public void SetTextColour(Color _c) {
		if (m_text.color != _c) {
			m_text.color = _c;
		}
	}

	public void SetButtonColour(Color _c)
	{
		var rnd = transform.GetChild(0).gameObject.GetComponent<Renderer>();
		Extensions.SetTintWindowColour(rnd, 0, _c, false,false,false);
	}

    public void ConfirmButtonDance()
    {
		if (GameManager.Me.IsOKToPlayUISound())
			AudioClipManager.Me.PlaySoundOld("PlaySound_ProductButton_Animated", transform);
    }
	public void SetButtonData (DesignTableButtonData _data) {
		gameObject.SetActive (_data.m_enable);
		Reset();
		if (_data.m_enable) {
			m_text.text = _data.m_text;
			OnClick += _data.m_onClick;
		}
		Interactable = _data.m_interactable;
	}

	void Update()
	{
		float targetAlpha = Interactable ? 1 : 0.2f;
		m_text.color = Color.Lerp(m_text.color, new Color(m_text.color.r, m_text.color.g, m_text.color.b, targetAlpha), 0.1f);
		
		m_buttonAnimator.SetBool("Inactive", !Interactable);
	}
	
	public void Reset () {
		m_text.text = string.Empty;
		OnClick = null;
	}

	public void OnPointerDown (PointerEventData _eventData) {
		if(!Interactable)
			return;
		//TODO if (!_eventData.IsLeftClick ())
		//	return;
		if(DesignTableManager.Me.IsTableAnimating)
			return;

		m_buttonAnimator.SetBool("CTA", false);
		m_buttonAnimator.SetTrigger ("Press");
		if (GameManager.Me.IsOKToPlayUISound())
			AudioClipManager.Me.PlaySoundOld("PlaySound_ProductButton", transform);
	}

	public void OnPointerUp (PointerEventData _eventData) {
		if(!Interactable)
			return;
		//TODO if (!_eventData.IsLeftClick ())
		//	return;
		if(DesignTableManager.Me.IsTableAnimating)
			return;

		m_buttonAnimator.SetTrigger ("Release");
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        OnClick?.Invoke ();
	}

	public void Pulse(bool _pulse)
	{
		m_buttonAnimator.SetBool("CTA", _pulse);
	}
}