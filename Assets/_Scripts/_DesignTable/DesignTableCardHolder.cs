using UnityEngine;
using System.Collections.Generic;
using TMPro;
using UnityEngine.UI;

public class DesignTableCardHolder : MonoBehaviour, INGDecisionCardHolder
{
    public enum State
    {
        OnTable,
        CloseUp,
    }
    public State m_targetState;
    public Transform m_tableTransform;

    public INGDecisionCardHolder.ECardView CardView { get { return INGDecisionCardHolder.ECardView.Detailed; } }
    public bool DragIn3D { get { return true; } }
    public bool ShowOrderBoardGUIOnCardClick { get { return true; } }
    public void ToggleHiding(bool _hide, bool _isDueToClick = false) {}
    public bool IsHidden { get; }
    public void GiftReceived(IBusinessCard _card) {}
    public Transform GiftsHolder() { return transform; }
    public Transform Root { get; }
    public bool EnableCardOnUpwardDrag() { return false; }
    public Transform ParentForDraggingCard(NGDirectionCardBase _card) { return null; }
    public Transform GetParentHolder(NGDirectionCardBase _card) { return null; }
    public void OnStartCardDrag() {}
    public void OnEndCardDrag() {}
    public GameObject m_cardHolder;
    public TMP_Text m_currentDesignSellingPrice;
    public float m_cardCamDist = 1f;
    public float m_cardMoveSpeed = 1f;
    public float m_cardScaleSpeed = 1f;
    public float m_closeUpScale = 0.3f;
    public float m_tableScale = 0.6f;
        
    private BCFactory m_factory = null;
    private MABuilding m_building = null;
    private NGOrderTile m_orderTile = null;
    private MAOrder m_currentOrder = null;

    public void OnCardClick() 
    {
        /*switch(m_targetState)
        {
            case State.CloseUp: m_targetState = State.OnTable; break;
            case State.OnTable: m_targetState = State.CloseUp; break;
        }*/
    }
    
    protected virtual BCFactory Factory
    {
        get
        {
            if(m_factory == null)
            {
                DesignTableManager parentTable = DesignTableManager.Me;
                if(parentTable == null) return null;
                if(parentTable.IsInDesignMode && parentTable.IsInProductMode &&
                   parentTable.IsInBuildingMode == false)
                {
                    m_building = DesignTableManager.Me.Building as MABuilding;
                    if(m_building == null || m_building.IsBeingConstructed) 
                        return null;
                    
                    foreach(var factory in m_building.BuildingComponents<BCFactory>())
                    {
                        m_factory = factory;
                        break;
                    }
                }
            }
            return m_factory;
        }
    }
    
    public void Start()
    {
        m_cardHolder.transform.DestroyChildren();
        var canvas = GetComponentInChildren<Canvas>();
        if(canvas)
            canvas.worldCamera = Camera.main;
    }
    
    public void Update()
    {
        if(DesignTableManager.Me.IsDesignInPlaceExiting)
        {
            Destroy(gameObject);
            return;
        }

        if(Factory == null) 
            return;

        MAOrder order = m_building.Order;
        if(order.IsValid == false)// || order.IsComplete)
        {
            Destroy(gameObject);
            return;
        }
        
        if(Mathf.Abs(Input.GetAxis("Mouse ScrollWheel")) > 0.0001f)
        {
            GetComponentInChildren<NGOrderTile>()?.SetViewOverride(INGDecisionCardHolder.ECardView.None);
            m_targetState = State.OnTable;
        }
        
        if(m_currentOrder != order)
        {
            SetupOrderCard();
        }
        
        UpdatePosition();
        
        m_currentDesignSellingPrice.text = $"{GlobalData.CurrencySymbol}{DesignTableManager.Me.DesignInterface.SellingPrice:F2}";
    }
    
    private void UpdatePosition()
    {
        
        switch(m_targetState)
        {
            case State.OnTable:
                transform.position = Vector3.Lerp(transform.position, m_tableTransform.transform.position, Time.deltaTime * m_cardMoveSpeed);
                transform.rotation = Quaternion.Lerp(transform.rotation, m_tableTransform.rotation, Time.deltaTime * m_cardMoveSpeed);
                transform.localScale = Vector3.one * Mathf.Lerp(transform.localScale.x, m_tableScale, Time.deltaTime * m_cardScaleSpeed);
                break;
            
            case State.CloseUp:
                transform.position = Vector3.Slerp(transform.position, Camera.main.transform.position + Camera.main.transform.forward * m_cardCamDist, Time.deltaTime * m_cardMoveSpeed);
                var targetRot = Quaternion.LookRotation(Camera.main.transform.forward, Camera.main.transform.up);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRot, Time.deltaTime * m_cardMoveSpeed);
                transform.localScale = Vector3.one * Mathf.Lerp(transform.localScale.x, m_closeUpScale, Time.deltaTime * m_cardScaleSpeed);
                break;
        }
    }
    
    private void SetupOrderCard()
    {
        m_currentOrder = m_building.Order;
        if(m_currentOrder.IsValid)
        {
            if(m_orderTile == null)
            {
                m_orderTile = NGOrderTile.Create(NGBusinessDecisionManager.Me.m_ngOrderTilePrefab.gameObject, m_currentOrder.TemplateBusinessGift, null, this, m_cardHolder.transform);
                AspectRatioFitter fitter = m_orderTile.gameObject.GetComponent<AspectRatioFitter>();
                if(fitter == null) fitter = m_orderTile.gameObject.AddComponent<AspectRatioFitter>();
                fitter.aspectMode = AspectRatioFitter.AspectMode.FitInParent;
                fitter.aspectRatio = 0.75f;
            }

            m_orderTile.SetupOrderUI(m_currentOrder);
        }
        else
        {
            m_currentOrder = null;
        }
    }
}