using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ShowInDesign : Mono<PERSON><PERSON><PERSON><PERSON>, IBatchWhenSame
{
    private static List<ShowInDesign> s_all = new ();
    private static void Register(ShowInDesign _sid) => s_all.Add(_sid);
    private static void Deregister(ShowInDesign _sid) => s_all.Remove(_sid);

    private static bool s_needRefresh = true;
    private static bool s_wasInDesignInPlace = false;
    public static void RefreshAll()
    {
        bool isInDesignInPlace = DesignTableManager.IsDesignInPlaceRoom;
        if (s_needRefresh == false && s_wasInDesignInPlace == isInDesignInPlace) return;
        s_needRefresh = false;
        s_wasInDesignInPlace = isInDesignInPlace;
        for (int i = 0; i < s_all.Count; ++i)
            s_all[i].Set(isInDesignInPlace);
    }

    public bool m_invert = false;
    
    void Awake()
    {
        Register(this);
        if (DesignTableManager.IsDesignInPlace == m_invert)
            Set(m_invert);
    }
    void OnDestroy() => Deregister(this);

    public void Set(bool _isInDesign)
    {
        bool show = _isInDesign != m_invert;
        Show(show);
    }
    private void Show(bool _show)
    {
        if (gameObject.activeSelf == _show) return;
        gameObject.SetActive(_show);
        var bld = gameObject.GetComponentInParent<MABuilding>();
        if (bld != null)
            bld.OnDesignTableVisibilityChanged(_show, gameObject);
    }

    public void CheckAndShow(bool _externalShow)
    {
        bool show = _externalShow && DesignTableManager.IsDesignInPlace != m_invert;
        Show(show);
    }

    public Component Component() => this;
    public bool IsApplicable(int _smi) => true;
    public int BatchHash() => m_invert ? 1 : 0;
    public void OnBatch(Component _new) { }
}
