using System;
using UnityEngine;

public class BuildHelper : MonoBehaviour
{
    private int m_buildingID, m_padID, m_height, m_side, m_wildBlockID;
    public int BuildingID => m_buildingID;
    public int PadID => m_padID;
    public int Height => m_height;
    public int Side => m_side;
    public MABuilding Building => GameManager.Me.GetMACommander<MABuilding>(m_buildingID);
    public Transform Pad {
        get {
            var building = Building;
            if (building == null) return null;
            var block = building.GetComponentInChildren<BaseBlock>();
            if (block == null) return null;
            var hinges = block.GetHinges();
            if (m_padID < 0 || m_padID >= hinges.Count) return null;
            return hinges[m_padID];
        }
    }

    public void OnDestroy()
    {
        if (Utility.IsShuttingDown || GameManager.Me == null) return;
        if(m_wildBlockID > -1)
        {
            if(Building == null || Building.HasBuildingComponent<BCBeacon>() == false)
            {
                RemoveFromSave();
            }
        }
    }
    
    private void RemoveFromSave()
    {
        for(int i = 0; i < GameManager.Me.m_state.m_buildHelpers.Count; ++i)
        {
            if(GameManager.Me.m_state.m_buildHelpers[i].m_wildBlockID == m_wildBlockID)
            {
                GameManager.Me.m_state.m_buildHelpers.RemoveAt(i);
                break;
            }
        }
    }

    public void Set(int _buildingID, int _padID, int _height, int _direction, int _wildBlockID = -1)
    {
        m_wildBlockID = _wildBlockID;
        m_buildingID = _buildingID;
        m_padID = _padID;
        m_height = _height;
        m_side = _direction;
    }
}
