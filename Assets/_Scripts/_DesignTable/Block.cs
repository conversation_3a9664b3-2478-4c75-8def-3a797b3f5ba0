using System;
using UnityEngine;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine.Rendering.Universal;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(Block))]
public class BlockEditor : Editor
{
	public override void OnInspectorGUI()
	{
		Block block = (Block)target;
		var bg = GUI.backgroundColor;
		GUILayout.BeginHorizontal();
		GUILayout.FlexibleSpace();
		GUI.backgroundColor = Color.green;
		if (GUILayout.Button("Update Object Lists", GUILayout.Width(240)))
		{
			block.SetDynamicWalls();
		}	
		GUI.backgroundColor = bg;
		GUILayout.FlexibleSpace();
		GUILayout.EndHorizontal();
		GUIStyle textStyle = new GUIStyle();
		textStyle.normal.textColor = Color.white;
		textStyle.wordWrap = true;
		textStyle.richText = true;
		GUILayout.TextArea("<b>Searches children for foundations and dynamic walls based on the following naming convention</b>\n\n" +
		                   "Objects ending <color=#c0c0ff>_Foundation</color> are <color=#ffffc0>foundations</color>\n\n" +
		                   "Objects ending <color=#c0c0ff>_NonFoundation</color> are <color=#ffffc0>non-foundations</color>\n\n" +
		                   "Objects starting <color=#c0c0ff>Wall_X</color> are walls, where X is <color=#c0ffc0>Front, Back, Left or Right</color>\n\n" +
		                   "Objects starting <color=#c0c0ff>WallCorner_XY</color> are wall corners, where X and Y are <color=#c0ffc0>Front, Back, Left or Right</color>\n\n" +
		                   "Wall pieces containing <color=#c0c0ff>Secondary</color> are non-primary pieces and will not be removed by blocks flagged as <color=#c0ffc0>Only Primary</color> in the <color=#ffffc0>Dynamic Remove Type</color> field\n\n" + 
		                   "Objects ending <color=#c0c0ff>_IsTopmost</color> are <color=#ffffc0>topmost</color> and will be removed if another block is on top of this one\n\n"+
		                   "Objects ending <color=#c0c0ff>_NotTopmost</color> are <color=#ffffc0>non-topmost</color> and will be removed if no other block is on top of this one\n\n" +
		                   "Game Objects containing <color=#c0c0ff>'door'</color> are checked for colliders. <color=#c0ffc0>Non-trigger & Enabled colliders</color> will be added to m_doorColliders array.",
			textStyle);

		base.OnInspectorGUI();
		ShowColliders(block.transform);
	}
	public static void ShowColliders(Transform _t, bool _showWarningIfNoNonTriggers = true)
	{
		GUIStyle style = new GUIStyle(GUI.skin.label);
		style.richText = true;
		GUILayout.Label("\n<color=#ffff80><b>Colliders in block:</b></color>\n<color=#ffffff>B</color>ox <color=#ffffff>M</color>esh <color=#ffffff>S</color>phere <color=#ffffff>C</color>apsule\n<color=#8080ff>Trigger</color> <color=#ff8080>Physical</color>", style);
		List<(string, int)> toOutput = new();
		int lastDepth = 0;
		List<(Transform, int)> stack = new();
		int numNonTriggers = 0;
		stack.Add((_t, 0));
		while (stack.Count > 0)
		{
			var (t, depth) = stack[^1];
			stack.RemoveAt(stack.Count - 1);
			var clls = t.gameObject.GetComponents<Collider>();
			if (clls.Length == 0)
			{
				while (toOutput.Count > 0 && depth < toOutput[^1].Item2)
					toOutput.RemoveAt(toOutput.Count - 1);
				toOutput.Add((t.name, depth));
			}
			else
			{
				for (int i = 0; i < toOutput.Count; ++i)
				{
					var (to, deptho) = toOutput[i];
					GUILayout.Label($"{new string(' ', deptho * 2)} {to}", style);
				}
				toOutput.Clear();
				var s = new string(' ', depth * 2);
				foreach (var cll in clls)
				{
					if (cll.enabled && cll.gameObject.activeInHierarchy)
					{
						if (cll.isTrigger == false) ++numNonTriggers; 
						s += cll.isTrigger ? "<color=#8080ff>" : "<color=#ff8080>";
						s += cll.GetType().Name[0];
						s += "</color>";
					}
				}
				if (GUILayout.Button($"{s} {t.name}", style)) Selection.activeGameObject = t.gameObject;
			}
			for (int i = t.childCount - 1; i >= 0; --i)
				stack.Add((t.GetChild(i), depth + 1));
		}
		if (numNonTriggers == 0 && _showWarningIfNoNonTriggers) GUILayout.Label("<color=#ff4040>Warning!</color><color=#ffffff> No non-trigger colliders found</color>", style);
	}
}
#endif

public interface IConstructable
{
	float HighestPointOfRenderer { get; }
	float LowestPointOfRenderer { get; }
	GameObject GetGameObject { get; }

	//List<Vector3> GetBlockCornerPoints(float _y = 0.0f);
	//void ToggleCollision(bool _toggle);
}

//----------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------
public class Block : MonoBehaviour, /*IStandardClickHandler, IDragHandler, IBeginDragHandler, IEndDragHandler, IBeginClickHoldHandler, IEndClickHoldHandler, */IBlockID, IConstructable, IBatchPartitioner
{
    // =========================================================================
    // Enums
    // public void SetDefenseValue(float _defensevalue)
    // {
	   //  m_defenseValueCurrent = _defensevalue;
    // }
    //
    public enum PartCategory
    {
        Cat1,
        Cat2,
        Cat3,
        Last,
    }

    // -------------------------------------------------------------------------

    public enum DynamicWallType 
    { 
        front, 
        right, 
        back, 
        left 
    }

    [System.Flags]
    public enum WallCategory
    {
	    General = 1,
	    Input = 2,
	    Output = 4,
    }

    [System.Flags]
    public enum EBodyPartInhibit
    {
	    None = 0,
	    Head = 1 << 0,
	    Chest = 1 << 1,
	    LeftArm = 1 << 2,
	    RightHand = 1 << 3,
	    LeftHand = 1 << 4,
	    Hips = 1 << 5,
	    LeftLowerLeg = 1 << 6,
	    RightLowerLeg = 1 << 7,
	    RightArm = 1 << 8,
	    LeftUpperLeg = 1 << 9,
	    LeftFoot = 1 << 10,
	    Hair = 1 << 11,
	    RightUpperLeg = 1 << 12,
	    RightFoot = 1 << 13,
	    Ears = 1 << 14,
	    Beard = 1 << 15,
    }

    // =========================================================================
    // Variables

    [Header("References")]
    public Transform m_toContents;
    public Transform m_toVisuals;
	public Transform m_toNonDestructVisuals;
    public Transform m_toHinges;
    public Transform m_toMarkers;
    public Transform m_floor;
    public Transform[] m_foundations;
    public Transform[] m_isTopmostParts;
    public Transform[] m_notTopmostParts;
    public Transform m_doorStairs;
    public Transform m_doorStairsBottomMarker;
    public Transform m_doorLadder;
    public Collider[] m_doorColliders;
    public Transform[] m_nonFoundations;
	public Collider m_collider;
	public SwitchObjects m_visualSwitcher;
	public TMP_Text m_premiumGoldText;
	public Transform PriceTagTransform => (m_premiumGoldText != null) ?m_premiumGoldText.transform.parent : null;
    public string m_blockInfoID;
    public int m_indexInDesign;
    public GameObject m_workerVisual;
    public Transform m_characterHolder;
    public GameObject[] m_inputStockVisuals;
    public GameObject[] m_outputStockVisuals;
    
    public BoxCollider m_designPresenceCollider;
    
    [HideInInspector]
    public List<GameObject> m_snapVisuals = new();
    
    public EBodyPartInhibit m_bodyPartInhibit = EBodyPartInhibit.None;
    private EBodyPartInhibit m_bodyPartInhibitFlipped = EBodyPartInhibit.None;
    private EBodyPartInhibit m_bodyPartInhibitNormalForFlipped = EBodyPartInhibit.None;
    
    public Transform m_blockOffset;
    public Transform m_blockOffsetMirrored;
    public int m_lastWildBlockID = 0;
    
    public Transform BlockOffset => m_blockOffset != null ? (m_blockOffset.gameObject.activeInHierarchy ? m_blockOffset : m_blockOffsetMirrored) : null;
    
    private bool m_isMABase = false; public bool IsMABase => m_isMABase;
    
    //private float m_defenseValueCurrent = 1f;//knack
    public float DefenseValueMax => BlockInfo.m_defence;
    //public float DefenseValueCurrent => m_defenseValueCurrent;
    
    #if UNITY_EDITOR   
       virtual public void DebugShowGUIDetails(GUIStyle _labelStyle, bool _showBase = true)
       {
         // EditorGUILayout.LabelField($"m_defenseValueCurrent: ", m_defenseValueCurrent.ToString(), _labelStyle);
          EditorGUILayout.LabelField($"m_defenseValueMax: ", DefenseValueMax.ToString(), _labelStyle);
       }
    #endif
	
	public bool DoNotAllowDeleteEver => BlockInfo.m_dontAllowDelete;
	public bool DoNotAllowDeleteAny => DoNotAllowDelete || DoNotAllowDeleteEver;
    public bool DoNotAllowDelete { get; set; } = false;
    public bool GenericFlag { get; set; } = false; // used for arbitrary short-term purposes

    public bool IsDoor { get; set; }

    private Bounds m_visualBounds;
    public Bounds VisualBounds => m_visualBounds;

    public enum EDynamicRemoveType
    {
	    None,
	    OnlyPrimary,
	    All,
    }
    public EDynamicRemoveType m_dynamicRemoveType = EDynamicRemoveType.All;
    
    [Space(10)]
    [Header("Dynamic Walls")]
    public BlockCorner m_cornerFrontLeft;
    public BlockCorner m_cornerFrontRight;
    public BlockCorner m_cornerBackLeft;
    public BlockCorner m_cornerBackRight;

    public BlockWall m_wallFront;
    public BlockWall m_wallBack;
    public BlockWall m_wallLeft;
    public BlockWall m_wallRight;
    
    public WallCategory m_wallCategory = WallCategory.General;

	[HideInInspector]
	public float m_lowestPointOfRenderer;
	[HideInInspector]
	public float m_highestPointOfRenderer;
	[HideInInspector]
	public float m_closestPointToCamera;
	public float HighestPointOfRenderer { get { return m_highestPointOfRenderer; } }
	public float LowestPointOfRenderer { get { return m_lowestPointOfRenderer; } }
	public float ClosestPointToCamera { get { return m_closestPointToCamera; } }
	public GameObject GetGameObject { get { return gameObject; } }

	public AkEventHolder m_animatedAudio;
	public AkEventHolder m_animatedBuildingAudio;
	public AkRTPCHolder m_animatedAudioSpeed;
	public float m_animatedAudioOrientationOffset = 0;
	public AkEventHolder m_createOutputAudioEvent;
	public AkSwitchHolder m_typeAudioSwitch;
	public AkEventHolder m_connectAudioEvent;
	public AkSwitchHolder m_executeAudioSwitch;
	public AkEventHolder m_executeAudioEvent;
	public AkEventHolder m_workerAssignedSound;
	public string m_handAnimation;
	public string m_buildingFrame;
	public string m_interiorVisuals;
	public bool _isFake = false;
	public int m_rarity = 0;
	public bool m_hideOnClothing = false;
	public string BlockID { get { return m_blockInfoID; } }
	public string BlockIDPlusPos => $"{m_blockInfoID}@{(int) transform.position.x}:{(int) transform.position.y}:{(int) transform.position.z}";
	public NGBlockInfo BlockInfo 
	{ 
		get 
		{ 
			if(m_blockInfo == null) m_blockInfo = NGBlockInfo.GetInfo(BlockID);
			if (m_blockInfo == null) Utility.ShowErrorOnce($"Block {BlockID} not found in NGBlockInfo - make sure the PrefabName in Knack matches the ID in the Block - things will go wrong");
			return m_blockInfo;
		}
	}
	private NGBlockInfo m_blockInfo;
	public bool m_ignoreBuildingRaise = false;
	public float m_raiseApplied = 0;

	private bool m_forceStairsAndLadderHide = false;
	public bool ForceStairsAndLadderHide
	{
		get { return m_forceStairsAndLadderHide; }
		set
		{
			m_forceStairsAndLadderHide = value;
			ShowStairs();
		}
	}

	void Start()
	{
		if (BlockInfo != null && NGManager.Me && NGManager.Me.m_useComponentSystem)
		{
	//		BlockInfo.AddBlockComponents(gameObject);
		}	
	}

	public static void GenerateComponentInfo(List<Block> _blocks, SDictionary<int, ArrayWrapper<long>> _componentInfo, bool _resetUIDs = false)
	{
		if (_componentInfo == null)
			return;

		foreach (var block in _blocks)
		{
			var components = block.GetComponents<BCBase>();
			if (components.Length == 0)
				continue;
			
			ArrayWrapper<long> ids = new(components.Length);
			_componentInfo.Add(block.m_indexInDesign, ids);
			for (int cIndex = 0; cIndex < components.Length; ++cIndex)
			{
				if(_resetUIDs)
					components[cIndex].m_uid = 0;
					
				components[cIndex].TrySetUID();
				ids.m_array[cIndex] = components[cIndex].m_uid;
			}
		}
	}
	
	public bool LoadComponents(SDictionary<long, SaveContainers.SaveMABuildingComponent> _componentData,
		SDictionary<int, ArrayWrapper<long>> _componentIds)
	{
		if (_componentData == null || _componentIds == null || GameManager.Me.PeopleLoaded == false) return false;

		bool componentsChanged = false;
		if (_componentIds.TryGetValue(m_indexInDesign, out var cmpIds))
		{
			foreach (var cmpId in cmpIds.m_array)
			{
				if(cmpId <= 0)
					continue;
					
				if (!_componentData.TryGetValue(cmpId, out var cmpSave))
				{
					componentsChanged = true;
					Debug.LogError($"{this.name}: Unable to load component save ID: {cmpId.ToString()}");
					continue;
				}

				var type = System.Type.GetType(cmpSave.m_componentType);
				if(type == null)
				{
					Debug.LogError($"Unable to load component type: {cmpSave.m_componentType}");
					continue;
				}
				
				var cmp = GetComponentInChildren(type) as BCBase;
				if (cmp != null)
					cmp.Load(cmpSave);
			}
		}
		return componentsChanged;
	}

	public void PlayExecuteAudio(string _switchValue = null)
	{
		if (m_executeAudioSwitch != null)
			m_executeAudioSwitch.Set(gameObject, _switchValue);
		if (m_executeAudioEvent != null)
			m_executeAudioEvent.Play(gameObject);
	}

	private void ShowObjects(Transform[] _objects, bool _show)
	{
		if (_objects != null)
		{
			foreach (var t in _objects)
			{
				if (t != null)
				{
					t.gameObject.SetActive(_show);
				}
			}
		}
	}

	public void ShowFoundations(bool _show, bool _forceHideLadder = false, bool _forceHideStairs = false)
	{
		ShowObjects(m_foundations, _show);
		ShowObjects(m_nonFoundations, !_show);
		if (m_doorStairs != null && m_doorLadder != null)
		{
			ShowStairs(_forceHideLadder, _forceHideStairs); // do this again next frame  in case the block isn't yet in position
			GlobalData.Me.DoNextFrame(() => { ShowStairs(_forceHideLadder, _forceHideStairs); });
		}
	}
	
	public bool m_isLadderShowing = false;
	public void ShowStairs(bool _forceHideLadder = false, bool _forceHideStairs = false)
	{
		var showLadder = m_forceStairsAndLadderHide == false && _forceHideLadder == false && m_doorStairsBottomMarker != null && m_doorStairsBottomMarker.position.IsUnderGround() == false;
		var showStairs = showLadder == false && m_forceStairsAndLadderHide == false && _forceHideStairs == false;
		if (m_doorStairs != null) m_doorStairs.gameObject.SetActive(showStairs);
		if (m_doorLadder != null)
		{
			m_doorLadder.gameObject.SetActive(showLadder);
			m_isLadderShowing = showLadder;
		}
		else
			m_isLadderShowing = false;
	}

	public void SetTopmost(bool _isTopmost)
	{
		ShowObjects(m_isTopmostParts, _isTopmost);
		ShowObjects(m_notTopmostParts, !_isTopmost);
	}

	public Block GetTopmostBlock(List<Block> _blocks)
	{
		_blocks.Remove(this);
		
		foreach (var sh in m_toHinges.GetComponentsInChildren<SnapHinge>())
		{
			if (sh != null && sh.HingeDirection == SnapHinge.EType.Top)
			{
				foreach(var block in _blocks)
				{
					foreach (Transform shOther in block.m_toHinges)
					{
						if ((shOther.position - sh.transform.position).sqrMagnitude < .1f * .1f)
						{
							return block.GetTopmostBlock(_blocks);
						}
					}
				}
				return this;
			}
		}
		return this;
	}
	
	public void CheckTopmost(Block[] _allBlocks)
	{
		// first find top hinge
		// then check all blocks for hinges at the same position
		// if none found then we're topmost
		bool isTopmost = true;
		foreach (var sh in m_toHinges.GetComponentsInChildren<SnapHinge>())
		{
			if (sh != null && sh.HingeDirection == SnapHinge.EType.Top)
			{
				for (int i = 0; i < _allBlocks.Length; ++i)
				{
					var other = _allBlocks[i];
					if (other == this || other == null) continue;
					foreach (Transform shOther in other.m_toHinges)
					{
						if ((shOther.position - sh.transform.position).sqrMagnitude < .1f * .1f)
						{
							isTopmost = false;
							i = _allBlocks.Length;
							break;
						}
					}
				}
				break;
			}
		}
		SetTopmost(isTopmost);
	}

	public void AddComponents()
	{
		BlockInfo?.AddBlockComponents(gameObject, false);
	}
	
	// public void DoDamage(float _damageDone, out float OUTdamageDone)
	// {
	// 	float defenseValueBefore = DefenseValueCurrent;
	// 	float val = defenseValueBefore - _damageDone;
	// 	m_defenseValueCurrent = 0 > val ? 0 : val;
	// 	OUTdamageDone = defenseValueBefore - m_defenseValueCurrent;
	// }

	private Transform m_flippedVisuals, m_normalVisuals;

	public bool UseFlippedVisuals(bool _useFlip, out bool _wasFlipped)
	{
		_wasFlipped = false;
		if (m_flippedVisuals == null) return false;
		_wasFlipped = m_toVisuals == m_flippedVisuals;
		m_normalVisuals.gameObject.SetActive(!_useFlip);
		m_flippedVisuals.gameObject.SetActive(_useFlip);
		m_toVisuals = _useFlip ? m_flippedVisuals : m_normalVisuals;
		return true;
	}
	public bool GetFlippedVisualOffset(out Vector3 _offset)
	{
		_offset = Vector3.zero;
		if (m_flippedVisuals == null) return false;
		if (m_blockOffset == null || m_blockOffsetMirrored == null) return false;
		_offset = m_blockOffset.position - m_blockOffsetMirrored.position;
		return true;
	}
	public bool IsFlipped => m_toVisuals == m_flippedVisuals;

	public EBodyPartInhibit BodyPartInhibit {
		get {
			if (m_flippedVisuals == null || m_flippedVisuals.gameObject.activeSelf == false) return m_bodyPartInhibit;
			if (m_bodyPartInhibitNormalForFlipped != m_bodyPartInhibit)
			{
				// flip left/right bits
				int ExchangeBits(int _value, EBodyPartInhibit _bit1, EBodyPartInhibit _bit2)
				{
					int iBit1 = (int) _bit1, iBit2 = (int) _bit2;
					var set1 = ~(((_value & iBit1) - 1) >> 31);
					var set2 = ~(((_value & iBit2) - 1) >> 31);
					return (_value & ~(iBit1 | iBit2)) | (iBit2 & set1) | (iBit1 & set2);
				}

				var flipped = (int) m_bodyPartInhibit;
				flipped = ExchangeBits(flipped, EBodyPartInhibit.LeftArm, EBodyPartInhibit.RightArm);
				flipped = ExchangeBits(flipped, EBodyPartInhibit.LeftHand, EBodyPartInhibit.RightHand);
				flipped = ExchangeBits(flipped, EBodyPartInhibit.LeftLowerLeg, EBodyPartInhibit.RightLowerLeg);
				flipped = ExchangeBits(flipped, EBodyPartInhibit.LeftUpperLeg, EBodyPartInhibit.RightUpperLeg);
				flipped = ExchangeBits(flipped, EBodyPartInhibit.LeftFoot, EBodyPartInhibit.RightFoot);
				m_bodyPartInhibitFlipped = (EBodyPartInhibit) flipped;
			}
			return m_bodyPartInhibitFlipped;
		}
	}

	const string c_blockPathRoot = "_Prefabs/_Blocks/";
	public static bool IsValidUID(string _id) {
		return NGBlockInfo.GetInfo(_id) != null;
	}

	static string[] s_buildingHingeNames1 = new string[] { "top", "bottom", "left", "right", "front", "back" };
	static string[] s_buildingHingeNames2 = new string[] { "Top0", "Bottom0", "Left0", "Right0", "Front0", "Back0" };
	static Vector3[] s_buildingHingeForwards = new Vector3[] { Vector3.up, -Vector3.up, -Vector3.right, Vector3.right, -Vector3.forward, Vector3.forward };
	static Vector3[] s_buildingHingeUps = new Vector3[] { -Vector3.forward, -Vector3.forward, Vector3.up, Vector3.up, Vector3.up, Vector3.up };
	static SnapHinge.EType[] s_buildingHingeSnapDirs = new SnapHinge.EType[] { SnapHinge.EType.Top, SnapHinge.EType.Bottom, SnapHinge.EType.Left, SnapHinge.EType.Right, SnapHinge.EType.Front, SnapHinge.EType.Back };
	static DynamicBuildingHinge.EType[] s_buildingHingeDynDirs = new DynamicBuildingHinge.EType[] { DynamicBuildingHinge.EType.None, DynamicBuildingHinge.EType.None, DynamicBuildingHinge.EType.Left, DynamicBuildingHinge.EType.Right, DynamicBuildingHinge.EType.Front, DynamicBuildingHinge.EType.Back };

	static void FixUpHinges(Transform[] _hinges) {
		for (int i = 0; i < 6; ++i) {
			if (_hinges[i] != null) {
				_hinges[i].rotation = Quaternion.LookRotation(s_buildingHingeForwards[i], s_buildingHingeUps[i]);
				var shinge = _hinges[i].gameObject.GetComponent<SnapHinge>();
				if (shinge != null) shinge.SetHingeDirectionType(s_buildingHingeSnapDirs[i]);
				var bDHinge = _hinges[i].gameObject.GetComponent<DynamicBuildingHinge>();
				if (bDHinge != null) bDHinge.m_type = s_buildingHingeDynDirs[i];
			}
		}
	}

	void ValidateHinges()
	{
		foreach (Transform t in m_toHinges)
		{
			if (t.gameObject.GetComponent<SnapHinge>() == null)
			{
				var sh = t.gameObject.AddComponent<SnapHinge>();
				var dbh = t.gameObject.GetComponent<DynamicBuildingHinge>();
				var dir = sh.HingeDirection;
				if (dbh != null)
				{
					switch (dbh.m_type)
					{
						case DynamicBuildingHinge.EType.Back:
							dir = SnapHinge.EType.Back;
							break;
						case DynamicBuildingHinge.EType.Front:
							dir = SnapHinge.EType.Front;
							break;
						case DynamicBuildingHinge.EType.Left:
							dir = SnapHinge.EType.Left;
							break;
						case DynamicBuildingHinge.EType.Right:
							dir = SnapHinge.EType.Right;
							break;
						case DynamicBuildingHinge.EType.None:
							dir = SnapHinge.EType.None;
							break;
					}
				}
				else
				{
					switch (t.name)
					{
						case "top":
							dir = SnapHinge.EType.Top;
							break;
						case "bottom":
							dir = SnapHinge.EType.Bottom;
							break;
						case "left":
							dir = SnapHinge.EType.Left;
							break;
						case "right":
							dir = SnapHinge.EType.Right;
							break;
						case "front":
							dir = SnapHinge.EType.Front;
							break;
						case "back":
							dir = SnapHinge.EType.Back;
							break;
					}
				}
				sh.SetHingeDirectionType(dir);
			}
			if (t.localScale.sqrMagnitude < .001f * .001f) t.localScale = Vector3.one;
		}
	}

	void GenerateDesignPresenceCollider()
	{
		if (m_designPresenceCollider == null)
		{
			Vector3 hTop = Vector3.one * 1e23f, hBottom = Vector3.one * 1e23f, hLeft = Vector3.one * 1e23f, hRight = Vector3.one * 1e23f, hFront = Vector3.one * 1e23f, hBack = Vector3.one * 1e23f;
			foreach (Transform t in m_toHinges)
			{
				if (t.gameObject.GetComponent<SnapHinge>() is { } sh)
				{
					switch (sh.HingeDirection)
					{
						case SnapHinge.EType.Top: hTop = t.position; break;
						case SnapHinge.EType.Bottom: hBottom = t.position; break;
						case SnapHinge.EType.Left: hLeft = t.position; break;
						case SnapHinge.EType.Right: hRight = t.position; break;
						case SnapHinge.EType.Front: hFront = t.position; break;
						case SnapHinge.EType.Back: hBack = t.position; break;
					}
				}
			}
			const float c_defaultPresenceSizeXZ = 4;
			const float c_defaultPresenceSizeY = 3;
			Vector3 center = Vector3.zero, size = Vector3.zero;
			if (hTop.x < 1e22f && hBottom.x < 1e22f)
			{
				center = (hTop + hBottom) * .5f;
				size.y = Mathf.Abs(hTop.y - hBottom.y);
			}
			else if (hBottom.x < 1e22f)
			{
				// bottom, no top
				center = hBottom + Vector3.up * (c_defaultPresenceSizeY * .5f);
				size.y = c_defaultPresenceSizeY;
			}
			else if (hTop.x < 1e22f)
			{
				// top, no bottom (unexpected!)
				center = hTop - Vector3.up * (c_defaultPresenceSizeY * .5f);
				size.y = c_defaultPresenceSizeY;
			}
			else
			{
				// no top/bottom (unlikely)
				var avg = Vector3.zero;
				var avgCount = 0;
				if (hLeft.x < 1e22f) { avg += hLeft; ++avgCount; }
				if (hRight.x < 1e22f) { avg += hRight; ++avgCount; }
				if (hFront.x < 1e22f) { avg += hFront; ++avgCount; }
				if (hBack.x < 1e22f) { avg += hBack; ++avgCount; }
				if (avgCount > 0)
				{
					avg /= avgCount;
					center = avg;
					size.y = c_defaultPresenceSizeY;
				}
			}
			if (hLeft.x < 1e22f) size.x = Mathf.Abs(hLeft.x - center.x) * 2;
			else if (hRight.x < 1e22f) size.x = Mathf.Abs(hRight.x - center.x) * 2;
			else size.x = c_defaultPresenceSizeXZ;
			if (hBack.x < 1e22f) size.z = Mathf.Abs(hBack.z - center.z) * 2;
			else if (hFront.x < 1e22f) size.z = Mathf.Abs(hFront.z - center.z) * 2;
			else size.z = c_defaultPresenceSizeXZ;

			var go = new GameObject("DesignPresenceCollider");
			go.transform.SetParent(transform, false);
			m_designPresenceCollider = go.AddComponent<BoxCollider>();
			m_designPresenceCollider.isTrigger = true;
			m_designPresenceCollider.size = size;
			m_designPresenceCollider.center = center;
		}
	}
	
	void SetDoorTypeSwitch()
	{
		var index = ((m_blockInfoID.GetHashCode() * 77717) & 0x7FFFFFFF) % 13;
		AudioClipManager.Me.SetSoundset(gameObject, index);
	}

	public void PlayCreateOutputAudio()
	{
		m_createOutputAudioEvent?.Play(gameObject);
	}

#if UNITY_EDITOR 
	const int c_dynamicPart_Foundation = 0;
	const int c_dynamicPart_NonFoundation = 1;
	const int c_dynamicPart_WallLeft = 2;
	const int c_dynamicPart_WallRight = 3;
	const int c_dynamicPart_WallFront = 4;
	const int c_dynamicPart_WallBack = 5;
	const int c_dynamicPart_Door = 6;
	const int c_dynamicPart_IsTopmost = 7;
	const int c_dynamicPart_NotTopmost = 8;
	const int c_dynamicPart_Lists = 9;
	static string[] c_dynamicPartNames = new string[] { "Foundation", "NonFoundation", "Left", "Right", "Front", "Back", "Door", "IsTopmost", "NotTopmost" };
	public void SetDynamicWalls()
	{
		// naming convention:
		// *_Foundation => foundations
		// *_NonFoundation => nonFoundations
		// Wall_X where X is Front Back Left or Right
		// WallCorner_XY where X and Y are Front Back Left or Right
		var visuals = m_toVisuals;
		var bits = new List<Transform>[c_dynamicPart_Lists];
		for (int i = 0; i < c_dynamicPart_Lists; ++i) bits[i] = new List<Transform>();
		FindBitsRecursive(visuals, bits);
		
		List<Collider> doorBlockColliders = new();
		GetComponentsInChildren<Collider>(doorBlockColliders);
		List<Collider> doors = doorBlockColliders.FindAll(x => x.isTrigger == false && x.enabled && x.name.ToLower().Contains("door"));
		foreach(Collider doorBlockCollider in doors)
		{
			if(doorBlockCollider.isTrigger == false && doorBlockCollider.enabled && bits[c_dynamicPart_Door].Contains(doorBlockCollider.transform) == false)
			{
				bits[c_dynamicPart_Door].Add(doorBlockCollider.transform);
			}
		}

		//
		// if(_t.name.ToLower().Contains("door") && _bits[c_dynamicPart_Door].Contains(_t) == false)
		// {
		// 	_bits[c_dynamicPart_Door].Add(_t);
		// }
		m_foundations = bits[c_dynamicPart_Foundation].ToArray();
		m_isTopmostParts = bits[c_dynamicPart_IsTopmost].ToArray();
		m_notTopmostParts = bits[c_dynamicPart_NotTopmost].ToArray();
		List<Collider> doorColliders = new();
		foreach(var doorPart in bits[c_dynamicPart_Door])
		{
			Collider col = doorPart.GetComponent<Collider>();
			if(col != null && col.isTrigger == false && col.enabled) doorColliders.Add(col);
		}
		if(m_collider != null && m_collider.name.ToLower().Contains("door") &&
		   m_collider.isTrigger == false && m_collider.enabled && doorColliders.Contains(m_collider) == false)
			doorColliders.Add(m_collider);
		m_doorColliders = doorColliders.ToArray();
		m_nonFoundations = bits[c_dynamicPart_NonFoundation].ToArray();
		m_wallFront.m_walls = bits[c_dynamicPart_WallFront].ToArray();
		m_wallBack.m_walls = bits[c_dynamicPart_WallBack].ToArray();
		m_wallLeft.m_walls = bits[c_dynamicPart_WallLeft].ToArray();
		m_wallRight.m_walls = bits[c_dynamicPart_WallRight].ToArray();
		bool foundNonCorner = false;
		for (int i = 0; i < m_foundations.Length; ++i)
			if (m_foundations[i].name.StartsWith("Corner_") == false)
				foundNonCorner = true;
		if (foundNonCorner == false) Debug.LogError($"No non-corner <color=#f0f0f0>Foundation</color> found - foundation parts should end <color=#80ff80>_Foundation</color>");
		for (int i = c_dynamicPart_WallLeft; i <= c_dynamicPart_WallBack; ++i)
		{
			foundNonCorner = false;
			for (int j = 0; j < bits[i].Count; ++j)
				if (bits[i][j].name.StartsWith("Wall_") == true)
					foundNonCorner = true;
			if (foundNonCorner == false) Debug.LogError($"No <color=#ff8080>Wall_{c_dynamicPartNames[i]}</color> found");
		}
		EditorUtility.SetDirty(gameObject);
	}

	private void FindBitsRecursive(Transform _t, List<Transform>[] _bits)
	{
		if (_t.name == "Foundation") _bits[c_dynamicPart_Foundation].Add(_t);
		if (_t.name.EndsWith("_Foundation")) _bits[c_dynamicPart_Foundation].Add(_t);
		if (_t.name.EndsWith("_NonFoundation")) _bits[c_dynamicPart_NonFoundation].Add(_t);
		if (_t.name.EndsWith("_IsTopmost")) _bits[c_dynamicPart_IsTopmost].Add(_t);
		if (_t.name.EndsWith("_NotTopmost")) _bits[c_dynamicPart_NotTopmost].Add(_t);
		if (_t.name.StartsWith("Wall_"))
		{
			for (int i = c_dynamicPart_WallLeft; i <= c_dynamicPart_WallBack; ++i)
				if (_t.name.Contains(c_dynamicPartNames[i]))
					_bits[i].Add(_t);
		}
		if (_t.name.StartsWith("WallCorner_"))
		{
			for (int i = c_dynamicPart_WallLeft; i <= c_dynamicPart_WallBack; ++i)
				if (_t.name.Contains(c_dynamicPartNames[i]))
					_bits[i].Add(_t);
		}
		foreach (Transform child in _t)
			FindBitsRecursive(child, _bits);
	}
#endif

	void CheckColliders()
	{
#if UNITY_EDITOR
		var clls = GetComponentsInChildren<MeshCollider>();
		foreach (var cll in clls)
		{
			if (cll.convex == false) Utility.ShowErrorOnce($"Block mesh collider error! <color=#ffffff>{name} - {cll.name}</color> has a non-convex mesh collider", cll.gameObject);
		}
#endif
	}

	void ApplyFallbackFoundation()
	{
		if (m_foundations == null || m_foundations.Length == 0)
		{
			var fallback = GlobalData.Me.m_fallbackFoundationVisual;
			if (fallback != null)
			{
				m_foundations = new Transform[1];
				m_foundations[0] = Instantiate(fallback).transform;
				m_foundations[0].SetParent(m_toVisuals);
			}
		}
	}
	
#if UNITY_EDITOR || DEVELOPMENT_BUILD
	public static string s_overrideBlock = null;
	private static DebugConsole.Command s_debugBlockOvrride = new DebugConsole.Command("blockoverride", _s => s_overrideBlock = _s);
#endif

	private static Dictionary<string, Bounds> s_boundsCache = new Dictionary<string, Bounds>();
	public static void Create(string _id, Transform _parent, Vector3? _eulerAngles, Action<GameObject> _onComplete, bool _loadComponents = true) {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		if(s_overrideBlock.IsNullOrWhiteSpace() == false && _id.Contains("MABase") == false)
		{
			_id = s_overrideBlock;
		}
#endif

		var entry = NGBlockInfo.GetInfo(_id);
		if (entry == null) {
			Debug.LogError($"#!# Couldn't find resource {_id} in block data");
			_onComplete(null);
			return;
		}
		var path = c_blockPathRoot + entry.m_prefabPath;
		ResManager.LoadAsync<GameObject>(path, (_prefab) => {
			if (_prefab == null) {
				Debug.LogError($"#!# Couldn't load resource {_id} at path {path}");
				_onComplete(null);
			} else {
				var go = Instantiate(_prefab);
				var block = go.GetComponent<Block>();
				if (block.m_toVisuals == null) Debug.LogError($"Error: block {_id} Visuals null", go);
				else if (block.m_toVisuals.name != "Visuals") Debug.LogError($"Error: block {_id} Visuals points to name {block.m_toVisuals.name} - should be Visuals", go);

				block.ApplyFallbackFoundation();
				block.m_isMABase = _id.StartsWith("MABase");
				block.IsDoor = false;
				foreach (var it in go.GetComponentsInChildren<InteractTransform>())
				{
					if (it.m_type == InteractTransform.Type.Door)
					{
						block.IsDoor = true;
						block.SetDoorTypeSwitch(); 
						break;
					}
				}
				if (s_boundsCache.TryGetValue(_id, out var bounds) == false)
				{
					bounds = ManagedBlock.GetTotalVisualBounds(go);
					s_boundsCache[_id] = bounds;
				}
				block.m_visualBounds = bounds;
				
				block.CheckColliders();
				
				block.m_normalVisuals = block.m_toVisuals;
				block.m_flippedVisuals = block.m_toVisuals.parent.Find("VisualsFlip");
				//block.m_flippedVisuals = block.transform.FindRecursiveByName<Transform>("VisualsFlip");
				//if (block.m_flippedVisuals != block.transform.FindRecursiveByName<Transform>("VisualsFlip"))
				//	Debug.LogError($"Error: block {_id} VisualsFlip not found in expected location", go);
				if (block.m_flippedVisuals != null)
				{
					//foreach (Transform t in block.m_flippedVisuals)
					//	t.localScale = new Vector3(-t.localScale.x, t.localScale.y, t.localScale.z);
					block.m_flippedVisuals.gameObject.SetActive(false);
				}

				// we're now assuming all information about hinges -except- position and (mostly) name are unreliable
				// therefore we regenerate hinge data based on name, then use position to check whether pairs of hinges are the wrong way round  
				var hinges = new Transform[6]; 
				bool hasNonBuilding = false;
				foreach (Transform t in block.m_toHinges) {
					int index = System.Array.IndexOf(s_buildingHingeNames1, t.name);
					if (index == -1) index = System.Array.IndexOf(s_buildingHingeNames2, t.name);
					if (index == -1) hasNonBuilding = true;
					else hinges[index] = t;
				}
				if (!hasNonBuilding) {
					FixUpHinges(hinges);
					bool refix = false;
					for (int i = 0; i < 6; i += 2) {
						bool changed = false;
						if (hinges[i] != null && hinges[i+1] != null) {
							var thisToNext = hinges[i+1].position - hinges[i].position;
							if (Vector3.Dot(thisToNext, hinges[i].forward) > 0) {
								// hinges are positioned in the wrong order, reverse them
								hinges[i].name = $"v{s_buildingHingeNames1[i+1]}";
								hinges[i+1].name = $"v{s_buildingHingeNames1[i]}";
								changed = true;
							}
						} else if (hinges[i] != null || hinges[i+1] != null) {
							int present = (hinges[i] != null) ? i : i+1;
							var thisToNext = hinges[present].position - bounds.center;
							if (Vector3.Dot(thisToNext, hinges[present].forward) < 0) {
								hinges[present].name = $"v{s_buildingHingeNames1[i+i+1-present]}";
								changed = true;
							}
						}
						if (changed) {
							(hinges[i], hinges[i+1]) = (hinges[i+1], hinges[i]);
							refix = true;
						}
					}
					if (refix) FixUpHinges(hinges);
				}
				
				block.ValidateHinges();
				block.GenerateDesignPresenceCollider();

				block.ShowFoundations(false, true);

				if (_parent != null) {
					go.transform.SetParent(_parent, false);
					go.transform.localPosition = Vector3.zero;
					if (_eulerAngles != null) {
						var euler = _eulerAngles.Value;
						euler.x = go.transform.eulerAngles.x;
						euler.z = go.transform.eulerAngles.z;
						go.transform.eulerAngles = euler;
					}
				}
				foreach (var renderer in go.GetComponentsInChildren<Renderer>(true))
				{
					renderer.renderingLayerMask = 1 << 0;
				}

				if (_loadComponents && block.BlockInfo != null && NGManager.Me && NGManager.Me.m_useComponentSystem)
				{
					block.BlockInfo.AddBlockComponents(go, false);
				}
				_onComplete(go);
			}
		});
	}
	public SnapHinge GetHinge(SnapHinge.EType _hinge)
	{
		var hinges = m_toHinges.GetComponentsInChildren<SnapHinge>();
		return hinges.Find(o => o.HingeDirection.Equals(_hinge));
	}
	public static void Load(string _id, Action<GameObject> _onComplete, bool _loadComponents = true) { Create(_id, null, null, _onComplete, _loadComponents); }


	public Component Component() => this;
	public List<List<Transform>> GetExcludedTransforms()
	{
		var foundations = new List<Transform>(m_foundations ?? Array.Empty<Transform>());
		var nonFoundations = new List<Transform>(m_nonFoundations ?? Array.Empty<Transform>());
		var tops = new List<Transform>(m_isTopmostParts ?? Array.Empty<Transform>());
		var nonTops = new List<Transform>(m_notTopmostParts ?? Array.Empty<Transform>());
		
		var wallF = new List<Transform>(m_wallFront.m_walls ?? Array.Empty<Transform>());
		var wallB = new List<Transform>(m_wallBack.m_walls ?? Array.Empty<Transform>());
		var wallL = new List<Transform>(m_wallLeft.m_walls ?? Array.Empty<Transform>());
		var wallR = new List<Transform>(m_wallRight.m_walls ?? Array.Empty<Transform>());
		var cornerFL = GatherFrom(m_cornerFrontLeft);
		var cornerFR = GatherFrom(m_cornerFrontRight);
		var cornerBL = GatherFrom(m_cornerBackLeft);
		var cornerBR = GatherFrom(m_cornerBackRight);
		wallF.AddRange(cornerFL);
		wallF.AddRange(cornerFR);
		wallB.AddRange(cornerBL);
		wallB.AddRange(cornerBR);
		wallL.AddRange(cornerFL);
		wallL.AddRange(cornerBL);
		wallR.AddRange(cornerFR);
		wallR.AddRange(cornerBR);
		
		var secondaries = transform.FindChildrenRecursiveByNameContains("Secondary");
		
		var res = new List<List<Transform>>{foundations, nonFoundations, tops, nonTops, wallF, wallB, wallL, wallR, secondaries};
		if (m_doorStairs != null) res.Add(new List<Transform> {m_doorStairs});
		if (m_doorLadder != null) res.Add(new List<Transform> {m_doorLadder});
		return res;
	}

	private List<Transform> GatherFrom(BlockCorner _corner)
	{
		var list = new List<Transform>();
		if (_corner.m_concave != null) 
			list.Add(_corner.m_concave);
		if (_corner.m_convex != null) 
			list.Add(_corner.m_convex);
		if (_corner.m_straightA != null) 
			list.Add(_corner.m_straightA);
		if (_corner.m_straightB != null) 
			list.Add(_corner.m_straightB);
		if (_corner.m_internal != null) 
			list.Add(_corner.m_internal);
		return list;
	}

	public void OnBatch(Dictionary<MeshRenderer, MeshRenderer> _replaced) { }
}
