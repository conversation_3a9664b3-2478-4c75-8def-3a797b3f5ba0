#if CHOICES
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class DesignCompetitionSubmitConfirm : MonoBehaviour {
    public GameObject m_resourceEntry;
    public Button m_buyButton;
    public Button m_confirmButton;
    public Button m_closeButton;
    private System.Action m_onCancel;

    public void Activate(Dictionary<NGCarriableResource, float> _required, System.Action _okCb, System.Action<Dictionary<NGCarriableResource, float>> _buyCb, System.Action _cancelCb)
    {
        /*m_onCancel = _cancelCb;
        var toBuy = new Dictionary<NGCarriableResource, float>();
        bool isFirst = true;
        foreach (var kvp in _required) {
            var sprite = kvp.Key.SpriteImage();
            float required = kvp.Value;
            float owned = NGManager.Me.GetOwnedResource(kvp.Key);
            var res = m_resourceEntry;
            if (!isFirst) res = Instantiate(res, res.transform.parent);
            var image = res.GetComponentInChildren<Image>();
            var texts = res.GetComponentsInChildren<TMPro.TextMeshProUGUI>();
            image.sprite = sprite;
            texts[0].text = $"{required} / {owned}";
            texts[1].text = kvp.Key.m_title;
            if (required > owned) {
                toBuy[kvp.Key] = required - owned;
            }
            isFirst = false;
        }
        m_confirmButton.onClick.AddListener(() => {
            Close();
            _okCb();
        });
        m_buyButton.onClick.AddListener(() => {
            Close();
            _buyCb(toBuy);
        });
        m_closeButton.onClick.AddListener(() => Close(true));
        if (toBuy.Count == 0) m_buyButton.interactable = false;
        else m_confirmButton.interactable = false;
        NGTutorialManager.Me.m_buyMoreResourcesTrigger = true;*/
        
    }

    public void Close(bool _cancel = false) {
        Destroy(gameObject);
        if (_cancel && m_onCancel != null) m_onCancel();
    }

    public static DesignCompetitionSubmitConfirm Create(GameObject _prefab, Transform _parent, Dictionary<NGCarriableResource, float> _required, 
        System.Action _okCb, System.Action<Dictionary<NGCarriableResource, float>> _buyCb, System.Action _cancelCb) {
        var go = Instantiate(_prefab, _parent);
        var dcsc = go.GetComponent<DesignCompetitionSubmitConfirm>();
        dcsc.Activate(_required, _okCb, _buyCb, _cancelCb);
        return dcsc;
    }
}
#endif