using System.Collections.Generic;
using UnityEngine;

public class BuildHelperConnectionVisuals : MonoBehaviour
{
    Dictionary<int, LineRenderer> m_connections = new(); 
    void Update()
    {
        List<int> alive = new();
        var building = GetComponentInParent<MABuilding>();
        HashSet<int> unusedConnections = new();
        foreach (var connection in m_connections)
            unusedConnections.Add(connection.Key);
        foreach (var bh in GameManager.Me.m_state.m_buildHelpers)
        {
            if (bh.m_buildingID == building.m_linkUID)
            {
                var wb = GameState_WildBlock.Find(bh.m_wildBlockID);
                if (wb != null && wb.Obj != null)
                {
                    if (m_connections.TryGetValue(bh.m_wildBlockID, out var lr) == false)
                    {
                        var go = Instantiate(Resources.Load<GameObject>("PowerEffects/BuildHelperConnection"));
                        go.transform.SetParent(transform);
                        lr = go.GetComponent<LineRenderer>();
                        m_connections[bh.m_wildBlockID] = lr;
                    }
                    
                    MAPowerEffectLightning.FillLineWithLightning(building.transform.position, wb.Obj.transform.position, lr, wb.Obj.GetInstanceID(), .666f, .8f, .4f, .4f, 1);
                    unusedConnections.Remove(bh.m_wildBlockID);
                }
            }
        }
        foreach (var remove in unusedConnections)
        {
            var lr = m_connections[remove];
            Destroy(lr.gameObject);
            m_connections.Remove(remove);
        }
    }
}
