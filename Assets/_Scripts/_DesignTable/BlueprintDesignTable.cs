using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BlueprintDesignTable : MonoBehaviour
{
    public enum Type{
		TEDDY = 0,
		CAKE,
		COUNT
	}
	[SerializeField] private GameObject m_teddy;
	[SerializeField] private GameObject m_cake;
	[SerializeField] private GameObject[] m_blueprints;

	public void SetBlueprint(Type _type){
		for(int i = 0; i < m_blueprints.Length; i++){
			m_blueprints[i].SetActive(i == (int)_type);
		}
	}

}
