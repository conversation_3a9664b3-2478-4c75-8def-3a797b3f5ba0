using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class DTDragPalette : DragBase, IManagedBlockFinishedLoading {
	public int m_paletteId;
	public string m_context;
	void Start() {
		var body = gameObject.GetComponent<Rigidbody>();
		if (body == null) body = gameObject.AddComponent<Rigidbody>();
		body.isKinematic = true;
	}

	void FixDecalProjectors()
	{
		foreach (var dp in GetComponentsInChildren<UnityEngine.Rendering.HighDefinition.DecalProjector>())
			dp.transform.localScale = new Vector3(dp.transform.localScale.x, dp.transform.localScale.y, dp.transform.localScale.z * 10);
		foreach (var dp in GetComponentsInChildren<UnityEngine.Rendering.Universal.DecalProjector>())
			dp.transform.localScale = new Vector3(dp.transform.localScale.x, dp.transform.localScale.y, dp.transform.localScale.z * 10);
	}

	private Block m_block;
	public void OnFinishedLoading()
	{
		DesignTableManager.SetWorldTriPlanar(gameObject, false);
		FixDecalProjectors();
		DisableLights();
		foreach (var st in GetComponentsInChildren<SoundTrigger>())
			st.enabled = false;

		m_block = GetComponentInChildren<Block>();
		if (m_block != null && m_block.m_designPresenceCollider != null) m_block.m_designPresenceCollider.enabled = false;
	}
	
	override public bool AcceptsClicks => true;
	override public bool SupportsLongPress => false;
	override public Camera Cam => DesignTableManager.Me.m_designCamera;
	override public Vector3 DragPlaneNormal => -Cam.transform.forward;
	override public Vector3 DragPlaneOrigin => transform.position;
	override public void OnClick() {
		string pname = null, description = null, rarity = null;
		float materialCount = 0;
		//NGBuffSet buffs = null;
		List<NGCarriableResource> materials = null;
		Sprite icon = null;
		var managedBlock = GetComponent<ManagedBlock>(); // normal drawer item
		if (managedBlock != null) {
			if(IsRightButton)
			{
				DesignUtilities.ShowBlockInfo(gameObject, managedBlock.m_blockID); 
			}
		} else if (name.StartsWith("Product_")) {
		} else {
			// paint/pattern/sticker
			GameObject drag;
			//buffs = new NGBuffSet();
			int id;
			if (int.TryParse(m_context.Substring(1), out id))
			{
				icon = DesignTableManager.IdToSprite(m_context);
				switch (m_context[0]) {
					case DesignTableManager.c_paintId:
						BlockInfoPanelV2.Create(PaintPotData.s_entries[id], DesignTableManager.Me.IsInBuildingMode, icon, gameObject);
						break;
					case DesignTableManager.c_patternId:
						BlockInfoPanelV2.Create(PatternData.s_entries[id], DesignTableManager.Me.IsInBuildingMode, icon, gameObject);
						break;
					case DesignTableManager.c_stickerId:
						BlockInfoPanelV2.Create(StickerData.s_entries[id], DesignTableManager.Me.IsInBuildingMode, icon, gameObject);
						break;
					case DesignTableManager.c_eraserId:
						BlockInfoPanelV2.CreateEraser(DesignTableManager.Me.IsInBuildingMode, icon, gameObject);
						break;
				}
			}
		}
	}

	void Update()
	{
		var mb = GetComponent<ManagedBlock>();
		if (mb == null) return;
		var scale = DesignTableManager.Me.GetPaletteVisibility(this);
		if (mb.CompleteStarted == false) scale = 1;
		
		var xzScale = Mathf.Min(1, scale * 20);
		var yScale = scale;
		transform.localScale = new Vector3(xzScale, yScale, xzScale);
	}

	public string GetName()
	{
		var managedBlock = GetComponent<ManagedBlock>(); // normal drawer item
		if (managedBlock != null)
		{
			var info = NGBlockInfo.GetInfo(managedBlock.m_blockID);
			if(info != null)
			{
				return info.m_displayName;
			}
		}
		else if (name.StartsWith("Product_"))
		{
		}
		else
		{
			// paint/pattern/sticker
			int id;
			if (int.TryParse(m_context.Substring(1), out id))
			{
				switch (m_context[0])
				{
					case DesignTableManager.c_paintId:
						return PaintPotData.s_entries[id].m_name;
					case DesignTableManager.c_patternId:
						return PatternData.s_entries[id].m_name;
					case DesignTableManager.c_stickerId:
						return StickerData.s_entries[id].m_name;
				}
			}
		}
		return null;
	}
	
	string GetUnlockName()
	{
		var managedBlock = GetComponent<ManagedBlock>(); // normal drawer item
		if (managedBlock != null)
		{
			return managedBlock.m_blockID;
		}
		else if (name.StartsWith("Product_"))
		{
		}
		else
		{
			// paint/pattern/sticker
			int id;
			if (int.TryParse(m_context.Substring(1), out id))
			{
				switch (m_context[0])
				{
					case DesignTableManager.c_paintId:
						return PaintPotData.s_entries[id].m_name;
					case DesignTableManager.c_patternId:
						return PatternData.s_entries[id].m_name;
					case DesignTableManager.c_stickerId:
						return StickerData.s_entries[id].m_name;
				}
			}
		}
		return null;
	}

	override public void OnDragStart()
	{
		if (!DesignTableManager.Me.DEBUG_IsAllUnlocked && !DesignTableManager.Me.ContextTestUnlock(GetUnlockName(), true))
			EndDrag();
		Block block = m_block;
		if (block != null && MAGameInterface.CheckSpecialPickupBlock(block))
			EndDrag();
	}
	
	DTDragDrawer m_ownerDrawer = null;
	Vector3 m_dragStartPos = new Vector3(1e23f, 0, 0);
	void SetBlockHeight(float _f)
	{
		if (_f < 0) _f = 0;
		var block = m_block?.transform;
		if (block == null)
		{
			block = transform;
			_f *= .01f;
		}
		if (m_dragStartPos.x > 1e22f)
			m_dragStartPos = block.localPosition;
		block.localPosition = m_dragStartPos + Vector3.up * _f;
	}

	void CheckStartOwnerDrag()
	{
		if (m_ownerDrawer == null)
		{
			m_ownerDrawer = GetComponentInParent<DTDragDrawer>();
			m_ownerDrawer.OnDragStart();
		}
	}
	void FinishOwnerDrag()
	{
		SetBlockHeight(0);
		if (m_ownerDrawer != null)
		{
			m_ownerDrawer.OnDragEnd(false);
			m_ownerDrawer = null;
		}
	}
	

	override public void OnDragEnd(bool _undo)
	{
		FinishOwnerDrag();
	}
	override public void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag) {
		float dragHeightFactor = (.01f * 1024) / Screen.height;
		SetBlockHeight(_totalScreenDrag.y * dragHeightFactor);
		if (InputPosition.y < Screen.height * .25f)
		{
			CheckStartOwnerDrag();
			m_ownerDrawer.OnDragUpdate(_dragPoint, _totalScreenDrag);
			return;
		}
		FinishOwnerDrag();
		if (true) {//if (_totalScreenDrag.sqrMagnitude > ScreenThresholdSqrd(GameManager.Me.m_dragThreshold)) {
			EndDrag();
			var managedBlock = GetComponent<ManagedBlock>(); // normal drawer item
			if (managedBlock != null) {
				//DesignTableManager.Me.TrackSpend(NGBlockInfo.GetInfo(managedBlock.m_blockID).m_price, "DTBuyBlock", managedBlock.m_blockID);
				DTDragBlock.Create((int)InputId, managedBlock.m_blockID, _dragPoint, (_g) => {
					_g.transform.rotation = DesignTableManager.Me.TurntableVisual.transform.rotation;
					_g.GetComponent<DTDragBlock>()?.ClearLastSnapped();
				}, true, null, DesignTableManager.Me.IsInDesignInPlace || DesignTableManager.Me.m_isInDesignGlobally ? 0 : 31, 1.0f, true);
			} else if (name.StartsWith("Product_")) {
				var drag = Instantiate(gameObject);
				drag.transform.localScale = transform.lossyScale;
				Destroy(drag.GetComponent<DTDragPalette>());
				var dragLabel = drag.AddComponent<DTDragLabel>();
				if (name.StartsWith("Product_Price")) {
					drag.transform.localEulerAngles = new Vector3(90f, transform.eulerAngles.y, 0);
					dragLabel.m_dragPlane = new Plane(Vector3.up, DesignTableManager.Me.m_turntable.transform.position + Vector3.up * .25f);
					dragLabel.m_snapType = EProductItemType.Product_Price;
				} else {
					drag.transform.localEulerAngles = new Vector3(0f, transform.eulerAngles.y, 0);
					dragLabel.m_dragPlane = new Plane(-Cam.transform.forward.GetXZ().normalized, DesignTableManager.Me.m_turntable.transform.position);
					if (name.StartsWith("Product_Tag"))
						dragLabel.m_snapType = EProductItemType.Product_Tag;
					else
						dragLabel.m_snapType = EProductItemType.Product_Line;
				}
			} else {
				// paint/pattern/sticker
				GameObject drag;
				if (m_context[0] == DesignTableManager.c_paintId) {
					drag = Instantiate(DesignTableManager.Me.m_paintbrushCursor);
					drag.GetComponent<TintSprites>().Tint(DesignTableManager.ContextToColour(m_context));
					if (GameManager.Me.IsOKToPlayDesignTableSound())
						AudioClipManager.Me.PlaySoundOld("PlaySound_Table_SelectPaint", transform);
				} else if (m_context[0] == DesignTableManager.c_eraserId) {
					drag = Instantiate(DesignTableManager.Me.m_eraserCursor);
					if (GameManager.Me.IsOKToPlayDesignTableSound())
						AudioClipManager.Me.PlaySoundOld("PlaySound_Table_SelectRubber", transform);
					//drag.GetComponent<TintSprites>().Tint(DesignTableManager.ContextToColour(m_context)); // TODO - pick up colour from target
				} else {
					drag = Instantiate(DesignTableManager.Me.m_textureCursor);
					Texture2D icon = null;
					if (m_context[0] != DesignTableManager.c_eraserId) {
						var rnd = GetComponentInChildren<MeshRenderer>();
						icon = rnd.material.GetTexture("_BaseMap") as Texture2D;
					}
					drag.GetComponent<TintSprites>().Replace(icon);
					if (GameManager.Me.IsOKToPlayDesignTableSound())
						AudioClipManager.Me.PlaySoundOld("PlaySound_Table_SelectSticker", transform);
				}
				drag.transform.parent = DesignTableManager.Me.m_designTableUI;
				drag.transform.position = InputPosition;
				drag.GetComponent<DTDragSprite>().m_context = m_context;
			}
		}
	}
}

public class DTDragLabel : MonoBehaviour {
	public EProductItemType m_snapType;
	public Plane m_dragPlane;
	void Update() {
		if (!GameManager.GetMouseButton(0)) {
			DesignTableManager.Me.SnapSpecial(gameObject, m_snapType, true);
			DesignTableManager.Me.RemoveSnapLine();
			enabled = false;
		} else {
			var cam = DesignTableManager.Me.m_designCamera;
			var ray = cam.RayAtMouse(true);
			float t;
			if (m_dragPlane.Raycast(ray, out t)) {
				transform.position = ray.GetPoint(t);
				DesignTableManager.Me.SnapSpecial(gameObject, m_snapType, false);
			}
		}
	}
}
