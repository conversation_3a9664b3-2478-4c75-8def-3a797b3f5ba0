using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Assertions.Must;

public class Decoration : MonoBehaviour {
	[Serializable]
	public class DecorationGroup {
		public int m_meshId;
		public List<DecorationEntry> m_entries = new List<DecorationEntry>();
	}
	[Serializable]
	public class DecorationEntry {
		public int m_window;
		public string m_data;
		public List<string> m_stickers = new List<string>();
	}
	public List<DecorationGroup> m_groups = new List<DecorationGroup>();
	void Awake() { UpdateMaterials(); }

	public const string c_baseMapProperty = "_BaseMap";
	public const string  c_normalMapProperty = "_BumpMap";
	public const string c_maskMapProperty = "_MaskMap";
	public const string c_tint1ColourProperty = "_R";
	public const string c_tint1AmountProperty = "_R_ON_OFF";
	public const string c_tint2ColourProperty = "_G";
	public const string c_tint2AmountProperty = "_G_Blend";
	public const string c_tint3ColourProperty = "_B";
	public const string c_tint3AmountProperty = "_B_Blend";
	public const string c_tint4ColourProperty = "_A";
	public const string c_tint4AmountProperty = "_A_Blend";

	public static (string, string) GetTintPropertyNames(int _index) {
		switch (_index) {
			case 0: return (c_tint1ColourProperty, c_tint1AmountProperty);
			case 1: return (c_tint2ColourProperty, c_tint2AmountProperty);
			case 2: return (c_tint3ColourProperty, c_tint3AmountProperty);
			case 3: return (c_tint4ColourProperty, c_tint4AmountProperty);
		}
		return (null, null);
	}

	public static void SetTintProperty(Material _mat, int _index, Color _tint)
	{
		var (colourProperty, amountProperty) = GetTintPropertyNames(_index);
		if (colourProperty != null)
		{
			_mat.SetColor(colourProperty, _tint);
			_mat.SetFloat(amountProperty, _tint.a);
		}
	}

	public static Color GetTintProperty(Material _mat, int _index)
	{
		var (colourProperty, amountProperty) = GetTintPropertyNames(_index);
		if (colourProperty != null)
		{
			var c = _mat.GetColor(colourProperty);
			c.a = _mat.GetFloat(amountProperty);
			return c;
		}
		return Color.white;
	}
	
	static int ByteDataW(byte[] _data) { return (int)_data[0] + (((int)_data[1])<<8); }
	static int ByteDataH(byte[] _data) { return (int)_data[2] + (((int)_data[3])<<8); }
	public void UpdateMaterials() {
		for (int i = 0; i < m_groups.Count; ++i) {
			var group = m_groups[i];
			var patterns = new Dictionary<string, Vector4>();
			var patternIndices = new int[group.m_entries.Count];
			int nextPattern = 0;
			for (int j = 0; j < group.m_entries.Count; ++j) {
				var entry = group.m_entries[j];
				var data = entry.m_data;
				if (!string.IsNullOrEmpty(data) && data[0] == DesignTableManager.c_patternId) {
					patterns[data] = Vector4.zero;
					patternIndices[nextPattern++] = j;
				}
			}
			Texture2D atlasTexture = null;
			Texture2D normalTexture = null;
			Texture2D maskTexture = null;
			var obj = transform.GetTransformFromTopologicalID(group.m_meshId);
			if (obj == null) continue;
			StickerInstance.ClearStickers(obj);
			var dh = obj.GetComponent<DecorationHolder>();
			var channel = (int)(dh?.m_channel ?? 0);
			var rnds = new HashSet<Renderer>();
			if (dh?.m_externalMeshRenderers != null)
			{
				foreach (var rnd in dh.m_externalMeshRenderers)
					rnds.Add(rnd);
			}
			foreach (var rnd in obj.GetComponentsInChildren<Renderer>())
				rnds.Add(rnd);
			if (rnds.Count == 0 && dh != null)
			{
				var parentRnd = dh.transform.parent.GetComponentInChildren<Renderer>();
				if (parentRnd != null) rnds.Add(parentRnd);
			}
			
			foreach (var rnd in rnds)
			{
				var mat = rnd.material;
				if (patterns.Count > 1)
				{
					Debug.LogError($"Error: model {obj.name} has more than one pattern assigned");
				}
				else if (patterns.Count == 1)
				{
					foreach (var kvp in patterns)
					{
						atlasTexture = DesignTableManager.ContextToTexture(kvp.Key);
						normalTexture = DesignTableManager.ContextToTexture(kvp.Key, 1);
						maskTexture = DesignTableManager.ContextToTexture(kvp.Key, 2);
						break;
					}
				}
				int haveSet = 0;
				for (int j = 0; j < group.m_entries.Count; ++j)
				{
					var entry = group.m_entries[j];
					int windowIndex = entry.m_window - 1;
					var data = entry.m_data;
					if (!string.IsNullOrEmpty(data))
					{
						switch (data[0])
						{
							case DesignTableManager.c_paintId:
								TintHelper.SetTintProperties(rnd.gameObject, mat, DesignTableManager.ContextToColour(data), 1, null, null, null, channel);
								haveSet |= 1 << windowIndex;
								break;
							
							case DesignTableManager.c_patternId:
								TintHelper.SetTintProperties(rnd.gameObject, mat, Color.black, 0, atlasTexture, normalTexture, maskTexture, channel);
								haveSet |= 1 << windowIndex;
								break;
						}
					}
					for (int s = 0; s < entry.m_stickers.Count; ++s)
					{
						ApplySticker(obj, entry.m_stickers[s], s);
					}
				}
				if (haveSet == 0)
					TintHelper.SetTintProperties(rnd.gameObject, mat, Color.black, 0, null, null, null, channel);
			}
		}
	}

	public static Vector3 StringsToVector3(string[] _bits, int _firstIndex) {
		Vector3 v;
		v.x = float.Parse(_bits[_firstIndex+0]);
		v.y = float.Parse(_bits[_firstIndex+1]);
		v.z = float.Parse(_bits[_firstIndex+2]);
		return v;
	}
	void ApplySticker(Transform _obj, string _stickerData, int _index) {
		var bits = _stickerData.Split(',');
		int stickerId = int.Parse(bits[0].Substring(1));
		Vector3 rayOrigin = StringsToVector3(bits, 1) / DesignTableManager.c_stickerQuantise, rayDirection = StringsToVector3(bits, 4) / DesignTableManager.c_stickerQuantise;
		float scale = 1, rotate = 0;
		if (bits.Length >= 9) {
			scale = float.Parse(bits[7]) * .01f;
			rotate = float.Parse(bits[8]);
		}
		StickerInstance.AddSticker(_obj, stickerId, rayOrigin, rayDirection, scale, rotate, _index, _stickerData);
	}

	public void RemoveStickerData(string _id) {
		for (int i = 0; i < m_groups.Count; ++i) {
			for (int j = 0; j < m_groups[i].m_entries.Count; ++j) {
				for (int k = 0; k < m_groups[i].m_entries[j].m_stickers.Count; ++k) {
					if (_id.CompareTo(m_groups[i].m_entries[j].m_stickers[k]) == 0) {
						m_groups[i].m_entries[j].m_stickers.RemoveAt(k);
						UpdateMaterials();
						return;
					}
				}
			}
		}
	}

	const char c_separator = '^';
	public static string[] GetDecorationEntries(string _decorationData) {
		return _decorationData.Split(c_separator);
	}
	public string ExportData() {
		string s = "";
		bool haveGroups = false;
		for (int i = 0; i < m_groups.Count; ++i) {
			if (m_groups[i].m_entries.Count > 0) {
				haveGroups = true;
				break;
			}
		}
		if (!haveGroups) return s;
		s += $"{m_groups.Count}{c_separator}";
		for (int i = 0; i < m_groups.Count; ++i) {
			var group = m_groups[i];
			s += $"{group.m_meshId}{c_separator}{group.m_entries.Count}{c_separator}";
			for (int j = 0; j < group.m_entries.Count; ++j) {
				var entry = group.m_entries[j];
				s += $"{entry.m_window}={entry.m_data};";
				for (int k = 0; k < entry.m_stickers.Count; ++k) {
					s += $"{entry.m_stickers[k]};";
				}
				s += $"{c_separator}";
			}
		}
		return s;
	}
	public static float GetDecorationCost(GameObject _o)
	{
		float totalCost = 0;
		var dec = _o.GetComponent<Decoration>();
		if (dec == null)
			return totalCost;
		
		foreach(var group in dec.m_groups)
		{
			foreach(var entry in group.m_entries)
			{
				if(!string.IsNullOrEmpty(entry.m_data))
				{
					switch(entry.m_data[0])
					{
						case DesignTableManager.c_paintId:
							int paintIndex = int.Parse(entry.m_data.Substring(1));
							totalCost += PaintPotData.s_entries[paintIndex].m_cost;
						break;
						
						case DesignTableManager.c_patternId:
							int patternIndex = int.Parse(entry.m_data.Substring(1));
							totalCost += PatternData.s_entries[patternIndex].m_cost;
						break;
					}
				}
				
				foreach(var sticker in entry.m_stickers)
				{
					var bits = sticker.Split(',');
					int stickerId = int.Parse(bits[0].Substring(1));
					totalCost += StickerData.s_entries[stickerId].m_cost;
				}
			}
		}
		return totalCost;
	}
	
	public static string Export(GameObject _o) {
		var dec = _o.GetComponent<Decoration>();
		if (dec == null) return "";
		return dec.ExportData();
	}

	public static void Import(GameObject _o, string _s) {
		if (string.IsNullOrEmpty(_s)) return;
		var dec = _o.GetComponent<Decoration>();
		if (dec == null) dec = _o.AddComponent<Decoration>();

		var bits = GetDecorationEntries(_s);
		int next = 0;
		int groupCount = int.Parse(bits[next++]);
		dec.m_groups.Clear();
		for (int i = 0; i < groupCount; ++i) {
			int meshId = int.Parse(bits[next++]);
			int entryCount = int.Parse(bits[next++]);
			dec.m_groups.Add(new DecorationGroup() { m_meshId = meshId });
			var group = dec.m_groups[i];
			for (int j = 0; j < entryCount; ++j) {
				var bits2 = bits[next++].Split('=');
				int window = int.Parse(bits2[0]);
				var dataSplit = bits2[1].Split(';');
				List<string> stickers = null;
				if (dataSplit.Length > 1) {
					stickers = new List<string>();
					for (int k = 1; k < dataSplit.Length; ++k) {
						if (!string.IsNullOrEmpty(dataSplit[k])) {
							stickers.Add(dataSplit[k]);
						}
					}
				}
				group.m_entries.Add(new DecorationEntry() { m_window = window, m_data = dataSplit[0], m_stickers = stickers });
			}
		}
		dec.UpdateMaterials();
	}

	static DecorationGroup GetWindowEntry(Transform _root, GameObject _o, int _window, bool _canAdd, out int _index) {
		_index = -1;
		var dec = _root.gameObject.GetComponent<Decoration>();
		if (dec == null) {
			if (!_canAdd) return null;
			dec = _root.gameObject.AddComponent<Decoration>();
		}
		int meshId = _o.transform.GetTopologicalIDFromTransform(_root);
		DecorationGroup foundGroup = null;
		int foundIndex = -1;
		for (int i = 0; i < dec.m_groups.Count; ++i) {
			if (dec.m_groups[i].m_meshId == meshId) {
				foundGroup = dec.m_groups[i];
				for (int j = 0; j < dec.m_groups[i].m_entries.Count; ++j) {
					if (dec.m_groups[i].m_entries[j].m_window == _window) {
						foundIndex = j;
						break;
					}
				}
				break;
			}
		}
		if (foundGroup == null) {
			if (!_canAdd) return null;
			foundGroup = new DecorationGroup() { m_meshId = meshId };
			dec.m_groups.Add(foundGroup);
		}
		if (foundIndex == -1) {
			if (!_canAdd) return null;
			foundIndex = foundGroup.m_entries.Count;
			foundGroup.m_entries.Add(new DecorationEntry() { m_window = _window });
		}
		_index = foundIndex;
		return foundGroup;
	}
	public static string GetWindowData(Transform _root, GameObject _o, int _window) {
		var foundGroup = GetWindowEntry(_root, _o, _window, false, out int foundIndex);
		if (foundGroup == null) return null;
		return foundGroup.m_entries[foundIndex].m_data;
	}
	public static void SetWindowData(Transform _root, GameObject _o, int _window, string _data) {
		bool canAdd = !string.IsNullOrEmpty(_data) && _data[0] != DesignTableManager.c_eraserId;
		var foundGroup = GetWindowEntry(_root, _o, _window, canAdd, out int foundIndex);
		if (foundGroup == null) return;
		if (canAdd) foundGroup.m_entries[foundIndex].m_data = _data;
		else if (foundGroup.m_entries[foundIndex].m_stickers.Count > 0) foundGroup.m_entries[foundIndex].m_data = null;
		else foundGroup.m_entries.RemoveAt(foundIndex);
		_root.gameObject.GetComponent<Decoration>().UpdateMaterials();
	}

	public static void SetStickerData(Transform _root, GameObject _o, int _window, string _data, bool _add) {
		var foundGroup = GetWindowEntry(_root, _o, _window, _add, out int foundIndex);
		if (foundGroup == null) return;
		if (_add) {
			if (foundGroup.m_entries[foundIndex].m_stickers == null) {
				foundGroup.m_entries[foundIndex].m_stickers = new List<string>();
			}
			foundGroup.m_entries[foundIndex].m_stickers.Add(_data);
		} else {
			if (foundGroup.m_entries[foundIndex].m_stickers == null) {
				return;
			}
			foundGroup.m_entries[foundIndex].m_stickers.Remove(_data);
		}
		_root.gameObject.GetComponent<Decoration>().UpdateMaterials();
	}

#if UNITY_EDITOR
	void OnDrawGizmos() {
		foreach (var g in m_groups) {
			var obj = transform.GetTransformFromTopologicalID(g.m_meshId);
			foreach (var e in g.m_entries) {
				if (e.m_stickers != null) {
					foreach (var s in e.m_stickers) {
						var bits = s.Split(',');
						Vector3 origin, forward;
						origin.x = (float)int.Parse(bits[1]) * .01f; origin.y = (float)int.Parse(bits[2]) * .01f; origin.z = (float)int.Parse(bits[3]) * .01f;
						forward.x = (float)int.Parse(bits[4]) * .01f; forward.y = (float)int.Parse(bits[5]) * .01f; forward.z = (float)int.Parse(bits[6]) * .01f;
						origin = obj.transform.TransformPoint(origin);
						forward = obj.transform.TransformDirection(forward);
						Gizmos.color = Color.blue;
						Gizmos.DrawLine(origin, origin + forward * 4f);
					}
				}
			}
		}
	}
#endif
}
