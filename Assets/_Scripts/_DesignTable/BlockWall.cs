using System.Collections;
using System.Collections.Generic;
using UnityEngine;

//----------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------
[System.Serializable]
public class BlockWall
{
    public enum EType
    {
		None,
		Default,
		Open,
		Window,
		Door,
		DoorWindow,
		Partition,
   }

    // -- public --
    public EType CurrentType { get { return m_type; } }
    public EType m_lockedType;
    
    public Transform[] m_walls;
    public Transform m_default;
    //public Transform m_open;
    //public Transform m_window;
    //public Transform m_door;
    //public Transform m_doorWindow;
    //public Transform m_partition;
    
    //----------------------------------------------------------------------------------------
    public static EType GetType(string _name)
    {
	    var type = EType.None;

	    var name = _name.ToLower();
	    if (name.Equals("wall")) type = EType.Default;
	    else if (name.Equals("wall_open")) type = EType.Open;
	    else if (name.Equals("wall_window")) type = EType.Window;
	    else if (name.Equals("wall_door")) type = EType.Door;
	    else if (name.Equals("wall_door_window")) type = EType.DoorWindow;
	    else if (name.Equals("wall_partition")) type = EType.Partition;

	    return type;
    }

    //----------------------------------------------------------------------------------------
    public void Assign(EType _type, Transform _transform)
    {
	    m_type = _type;
	    /*switch (_type)
	    {
		    case EType.Default: m_default = _transform; break;
		    case EType.Open: m_open = _transform; break;
		    case EType.Window: m_window = _transform; break;
		    case EType.Door: m_door = _transform; break;
		    case EType.DoorWindow: m_doorWindow = _transform; break;
		    case EType.Partition: m_partition = _transform; break;
	    }*/
    }

    private static bool ShouldAffect(Transform _wall, bool _onlyAffectPrimary)
	{
	    if (_onlyAffectPrimary == false) return true;
	    return !_wall.name.Contains("Secondary");
	}
    public void Enable(bool _enable, bool _onlyAffectPrimary)
	{
	    if (m_walls != null && m_walls.Length > 0)
	    {
		    foreach (var wall in m_walls)
			    if (wall != null && ShouldAffect(wall, _onlyAffectPrimary))
				    wall.gameObject.SetActive(_enable);
	    }
	    else if (m_default != null) m_default.gameObject.SetActive(_enable);
    }

    public bool IsEnabled
    {
	    get
	    {
		    if (m_walls != null && m_walls.Length > 0)
			    return m_walls[0] != null && m_walls[0].gameObject.activeSelf;
		    return m_default == null || m_default.gameObject.activeSelf;
	    }
    }
    // -- private --
    private EType m_type;
}
