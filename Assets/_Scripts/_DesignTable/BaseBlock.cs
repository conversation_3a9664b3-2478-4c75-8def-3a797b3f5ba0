using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class BaseBlock : MonoBehaviour
{
    public float m_baseWidth = 1, m_baseDepth = 1;
    public const float c_blockSize = 4;
    public GameObject m_emptyPlotSign;
    public TMP_Text m_plotSign;

    void Start()
    {
        gameObject.SetActive(NGManager.Me.m_showBaseBlock);
    }
    
    public void ShowEmptyPlotSign(NGCommanderBase _base, bool _show)
    {
        if (m_emptyPlotSign != null && m_emptyPlotSign.activeSelf != _show)
            m_emptyPlotSign.SetActive(_show);
        
        if(m_plotSign != null)
            m_plotSign.text = _base.IsLocked ? "Locked" : "Empty Plot";
    }
    
    public bool IsPointInside(Vector3 _point, float _margin = 0)
    {
        var origin = transform.position - (transform.right * .5f + transform.forward * .5f) * c_blockSize;
        var offsetInPlot = _point - origin;
        var dRight = Vector3.Dot(offsetInPlot, transform.right);
        var dFwd = Vector3.Dot(offsetInPlot, transform.forward);
        return dRight >= -_margin && dRight <= m_baseWidth * c_blockSize + _margin && dFwd >= -_margin && dFwd <= m_baseDepth * c_blockSize + _margin; 
    }
    
    public bool IsValidDoorDirection(Vector3 _localPos, Vector3 _forward)
    {
        var lx = Mathf.RoundToInt(_localPos.x / c_blockSize);
        var lz = Mathf.RoundToInt(_localPos.z / c_blockSize);
        var allowLeft = lx == 0;
        var allowRight = lx == (int)m_baseWidth - 1;
        var allowFront = lz == 0;
        var allowBack = lz == (int)m_baseDepth - 1;
        var dotFwd = Vector3.Dot(transform.forward, _forward);
        var dotSide = Vector3.Dot(transform.right, _forward);
        var isValid = dotFwd * dotFwd > dotSide * dotSide ? (dotFwd > 0 ? allowFront : allowBack) : (dotSide > 0 ? allowLeft : allowRight);
        return isValid;
    }

    public List<Transform> GetHinges()
    {
        var toHinges = GetComponent<Block>().m_toHinges;
        var hinges = new List<Transform>();
        foreach (Transform child in toHinges)
        {
            if (child.name.Contains("Top"))
                hinges.Add(child);
        }
        return hinges;
    }
    void OnDrawGizmos()
    {
        var origin = transform.position - (transform.right * .5f + transform.forward * .5f) * c_blockSize;
        var ext1 = transform.right * (m_baseWidth * c_blockSize);
        var ext2 = transform.forward * (m_baseDepth * c_blockSize);
        Gizmos.color = Color.white;
        Gizmos.DrawLine(origin, origin + ext1);
        Gizmos.DrawLine(origin + ext1, origin + ext1 + ext2);
        Gizmos.DrawLine(origin + ext1 + ext2, origin + ext2);
        Gizmos.DrawLine(origin + ext2, origin);
    }
}
