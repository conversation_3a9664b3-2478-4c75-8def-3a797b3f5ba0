using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using TMPro;

using Product = GameState_Product;
public enum EProductItemType {
		Product_Tag,
		Product_Line,
		Product_Price
}

public class ProductInfoItem : <PERSON>o<PERSON><PERSON><PERSON>our, IBeginDrag<PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON>ler, IEndDragHandler
{
	[SerializeField] TextMeshPro m_text;

	public string UID { get; private set; }
	public EProductItemType Type { get; private set; }
	public string Text { get; private set; }

	public void Setup(EProductItemType _type, string _text, string _uid) {
		UID = _uid;
		Type = _type;
		Text = _text;
		m_text.text = _text;
	}

	public void SetPriceText(string _value){
		m_text.text = _value;
	}

	public void SetPriceText(Product _product)
    {

    }

	void IBeginDragHandler.OnBeginDrag(PointerEventData _eventData) {
		if (_eventData.button == PointerEventData.InputButton.Left)
			TryPickupItem(_eventData);
	}

	void IDragHandler.OnDrag(PointerEventData _eventData) {

	}

	void IEndDragHandler.OnEndDrag(PointerEventData _eventData) {
		if (_eventData.button == PointerEventData.InputButton.Left)
			TryDropItem();
	}

	private void TryPickupItem(PointerEventData _eventData) {
		/*TODO var plane = new Plane(Vector3.back, _eventData.pointerCurrentRaycast.worldPosition.NewZ(transform.parent.position.z));
		Ray raycast = Camera.main.ScreenPointToRay(Input.mousePosition);
		float rayDist;
		plane.Raycast(raycast, out rayDist);
		if (rayDist > 0) {
			Vector3 planePoint = raycast.GetPoint(rayDist);
			var clampedPos = planePoint.NewZ(transform.parent.position.z);
			var clonedItem = ProductInfoItemHandler.Me.CloneDrawerItem(this, clampedPos);
			ProductInfoItemHandler.Me.StartDrag(clonedItem, plane);
		}*/
	}

	private void TryDropItem() {
		//TODO ProductInfoItemHandler.Me.EndDrag();
	}
}
