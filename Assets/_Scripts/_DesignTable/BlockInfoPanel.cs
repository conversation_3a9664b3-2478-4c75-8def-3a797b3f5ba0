using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class BlockInfoPanel : MonoBehaviour
{
    public TMPro.TextMeshProUGUI m_title;
    public TMPro.TextMeshProUGUI m_description;
    public Image m_icon;
    public GameObject m_entryHolder;
    private GameObject m_owner;
    private enum InfoEntries
    {
        Rarity,
        WorkerTimeToMake,
        TapsToMake,
        Materials,
        PriceEffect,
        MarketEffect,
        TimesUsed,
        MaxUses,
        Buff,
        Debug,
        NumEntries,
    };
    private string[] m_infoEntryLabels = {
        "Rarity", "Worker Time To Make", "Taps To Make", "Materials", "Price Effect", "Market Effect", "Times Used", "Usage Cap", "Buff", "Debug"
    };

    public void Activate(NGBlockInfo _info, bool _isBuildingMode, Sprite _sprite, GameObject _owner) {
        
        /*var partArray = new DesignUtilities.PartDescription[1] { new DesignUtilities.PartDescription(){m_blockID = _info.m_prefabName}};
        var designInterface = NGDesignInterface.Get(partArray, DesignTableManager.Me.DefaultPartSet);
        m_title.text = _info.m_displayName;
        m_description.text = _info.m_description;
        SetSprite(_sprite);

        (var buff, var buffValue) = _info.GetBuffInfo();
        bool hasBuff = !string.IsNullOrEmpty(buff);
        
        var materials = _isBuildingMode ? _info.BuildingMaterials : _info.GetProductMaterials(designInterface);
        string matString = "";
        foreach (var mat in materials) matString += $"{_info.m_materialCost} x {mat.m_title} ";
        var values = new string[(int) InfoEntries.NumEntries];
        values[(int) InfoEntries.Rarity] = _info.Rarity.ToString();
        values[(int) InfoEntries.WorkerTimeToMake] = _info.m_workerTimeToMake.ToShortTimeString(_showFraction: true);
        values[(int) InfoEntries.TapsToMake] = _info.m_numTapsToMake.ToString("F1");
        values[(int) InfoEntries.Materials] = matString;
        values[(int) InfoEntries.PriceEffect] = _info.m_priceModifier.ToString("F4");
        values[(int) InfoEntries.MarketEffect] = _info.m_marketModifier.ToString("F4");
        values[(int) InfoEntries.MaxUses] = _info.m_usageCap.ToString("n0");
        
        #if UNITY_EDITOR
        values[(int) InfoEntries.Debug] = $"Bo={designInterface.Parts[0].BoredomPercent}, Pr={designInterface.SellingPrice:F4}, Sc={designInterface.TotalScore}";
        #endif
        
        if (hasBuff)
        {
            if (m_infoEntryLabels.Length < (int)InfoEntries.NumEntries) Array.Resize(ref m_infoEntryLabels, (int)InfoEntries.NumEntries);
            m_infoEntryLabels[(int) InfoEntries.Buff] = $"{buff} Buff";
            values[(int) InfoEntries.Buff] = buffValue;
        }
        
        SetData(values, _owner);*/
    }

    public void Activate(string _title, string _description, Sprite _sprite, string _rarity, string _materials, GameObject _owner)
    {
        m_title.text = _title;
        m_description.text = _description;
        SetSprite(_sprite);

        var values = new string[(int) InfoEntries.NumEntries];
        values[(int) InfoEntries.Rarity] = _rarity;
        values[(int) InfoEntries.Materials] = _materials;
        values[(int) InfoEntries.PriceEffect] = "Positive";
        values[(int) InfoEntries.MarketEffect] = "Positive";
        /*(var type, var value) = _buffs.GetInfoStrings();
        if (type != null)
        {
            m_infoEntryLabels[(int)InfoEntries.Buff] = $"{type} Buff";
            values[(int) InfoEntries.Buff] = value;
        }*/

        SetData(values, _owner);
    }

    void SetSprite(Sprite _sprite)
    {
        if (_sprite != null)
            m_icon.sprite = _sprite;
        else
            m_icon.transform.parent.gameObject.SetActive(false);
    }
    
    void SetData(string[] _values, GameObject _owner) {
        m_owner = _owner;
        var entryTemplate = m_entryHolder.transform.GetChild(0).gameObject;
        var entryLine = m_entryHolder.transform.GetChild(1).gameObject;
        GameObject lineGO = null;
        for (int i = 0; i < (int)InfoEntries.NumEntries; ++i)
        {
            if (_values[i] == null) continue;
            var go = entryTemplate;
            if (i != 0) go = Instantiate(go, m_entryHolder.transform);
            var bits = go.GetComponentsInChildren<TMPro.TextMeshProUGUI>();
            bits[0].text = m_infoEntryLabels[i];
            bits[1].text = _values[i];
            if (i != 0) lineGO = Instantiate(entryLine, m_entryHolder.transform);
        }
        Destroy(lineGO); // don't need the last line
    }

    public float m_bezierRaise = 0;
    void Update() {
        if(m_owner == null)
        {
            Close();
            return;
        }
        
        var bez = gameObject.GetComponentInChildren<BezierLine>();
        var uiPos = GetComponent<RectTransform>().WorldPosition();
        bez.SetControlPoints(uiPos, m_owner.transform.position + Vector3.up * m_bezierRaise);
        DragBase.AnimateBezier(bez.gameObject.GetComponent<LineRenderer>());
    }

    public void Close()
    {
        Destroy(gameObject);
    }

    private static BlockInfoPanel s_currentPanel = null;

    public static void DestroyPreviousInstance()
    {
        if (s_currentPanel == null) return;
        Destroy(s_currentPanel.gameObject);
        s_currentPanel = null;
    }
    public static BlockInfoPanel Create(GameObject _prefab, Transform _parent, NGBlockInfo _info, bool _isBuildingMode, Sprite _icon, GameObject _owner)
    {
        DestroyPreviousInstance();
        var go = Instantiate(_prefab, _parent);
        var bip = go.GetComponent<BlockInfoPanel>();
        bip.Activate(_info, _isBuildingMode, _icon, _owner);
        s_currentPanel = bip;
        return bip;
    }
    public static BlockInfoPanel Create(GameObject _prefab, Transform _parent, string _title, string _description, Sprite _icon, string _rarity, string _materials, GameObject _owner)
    {
        DestroyPreviousInstance();
        var go = Instantiate(_prefab, _parent);
        var bip = go.GetComponent<BlockInfoPanel>();
        bip.Activate(_title, _description, _icon, _rarity, _materials, _owner);
        s_currentPanel = bip;
        return bip;
    }
}
