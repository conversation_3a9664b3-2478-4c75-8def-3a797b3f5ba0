using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Globalization;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class BlockBalance {
	public enum Ints {
		Rarity,
		Premium,
		//
		Count,
	}
	public enum Floats
	{
		CostInBuilding,
		CostInProduct,
		MaterialCost,
		ProductionTime,
		Capacity,
		RadiusIncrease,
		BuildTimeModifier,
		BuildingHPModifier,
		BuildingFireStartModifier,
		BAMValue,
		WorkforceValue,
		QualityValue,
		SafetyModifier,
		HealthModifier,
		EnvironmentModifier,
		SocialModifier,
		GoldCost,
		//
		Count,
	}
	public enum Strings
	{
		//
		Count,
	}

	public BlockBalance(string _id) {
		var bits = _id.Split(':');
		m_id = bits.Length > 0 ? bits[0] : _id;
		m_floatValues = new float[(int)Floats.Count];
		m_intValues = new int[(int)Ints.Count];
		m_stringValues = new string[(int)Strings.Count];
		BlockBalanceManager.Register(this);
	}
	public string m_id;
	public float[] m_floatValues;
	public int[] m_intValues;
	public string[] m_stringValues;

	//
	public int Premium { get { return m_intValues[(int)Ints.Premium]; } set { m_intValues[(int)Ints.Premium] = value; } }
	public float CostInBuilding { get { return m_floatValues[(int)Floats.CostInBuilding]; } set { m_floatValues[(int)Floats.CostInBuilding] = value; } }
	public float CostInProduct { get { return m_floatValues[(int)Floats.CostInProduct]; } set { m_floatValues[(int)Floats.CostInProduct] = value; } }
	public float MaterialCost { get { return m_floatValues[(int)Floats.MaterialCost]; } set { m_floatValues[(int)Floats.MaterialCost] = value; } }
	public float ProductionTime { get { return m_floatValues[(int)Floats.ProductionTime]; } set { m_floatValues[(int)Floats.ProductionTime] = value; } }
	public float Capacity { get { return m_floatValues[(int)Floats.Capacity]; } set { m_floatValues[(int)Floats.Capacity] = value; } }
	public float RadiusIncrease { get { return m_floatValues[(int)Floats.RadiusIncrease]; } set { m_floatValues[(int)Floats.RadiusIncrease] = value; } }
	public float BuildTimeModifier { get { return m_floatValues[(int)Floats.BuildTimeModifier]; } set { m_floatValues[(int)Floats.BuildTimeModifier] = value; } }
	public float BuildingHPModifier { get { return m_floatValues[(int)Floats.BuildingHPModifier]; } set { m_floatValues[(int)Floats.BuildingHPModifier] = value; } }
	public float BuildingFireStartModifier { get { return m_floatValues[(int)Floats.BuildingFireStartModifier]; } set { m_floatValues[(int)Floats.BuildingFireStartModifier] = value; } }
	public float SafetyModifier { get { return m_floatValues[(int)Floats.SafetyModifier]; } set { m_floatValues[(int)Floats.SafetyModifier] = value; } }
	public float HealthModifier { get { return m_floatValues[(int)Floats.HealthModifier]; } set { m_floatValues[(int)Floats.HealthModifier] = value; } }
	public float EnvironmentModifier { get { return m_floatValues[(int)Floats.EnvironmentModifier]; } set { m_floatValues[(int)Floats.EnvironmentModifier] = value; } }
	public float SocialModifier { get { return m_floatValues[(int)Floats.SocialModifier]; } set { m_floatValues[(int)Floats.SocialModifier] = value; } }
	//
	public bool FromString(Ints _which, string _value) {
		return int.TryParse(_value, out m_intValues[(int)_which]);
	}
	public bool FromString(Ints _which, string _value, bool _isBool) {
		
		bool.TryParse(_value, out _isBool);
		m_intValues[(int)_which] = 0;
		if(_isBool)
		{
			m_intValues[(int)_which] = 1;
		}
		return true;
	}
	public bool FromString(Ints _which, string _value, string[] _matches, int _default = -999) {
		_value = _value.ToLower();
		for (int i = 0; i < _matches.Length; i ++) {
			if (_matches[i] == _value) {
				m_intValues[(int)_which] = i;
				return true;
			}
		}
		if (_default != -999) {
			m_intValues[(int)_which] = _default;
		}
		return false;
	}
	public bool FromString(Floats _which, string _value) {
		return float.TryParse(_value, NumberStyles.Float, CultureInfo.InvariantCulture, out m_floatValues[(int)_which]);
	}
	public bool FromString(Strings _which, string _value) {
		m_stringValues[(int)_which] = _value;
		return true;
	}

}

// general interface for anything that can return a BlockID
public interface IBlockID {
	string BlockID { get; }
}

public class BlockBalanceManager : MonoSingleton<BlockBalanceManager> {
	public static void SetBlockData() {
#if UNITY_EDITOR
		if (!Application.isPlaying) return;
#endif
		ReadBlockBalance();
	}

	public string m_DEBUG_SnapshotEntries;

	/// TEMP /// Replace with proper CSV reader
	public static string[] ReadCSVLines(string _name) {
		return Resources.Load<TextAsset>(_name)?.text?.Split('\n');
	}
	public static List<string> SplitCSVEntries(string _line) {
		var bits = new List<string>();
		string bit = "";
		bool inQuote = false;
		for (int i = 0; i <= _line.Length; ++i) {
			char c = i < _line.Length ? _line[i] : (char)0;
			if (c == '"') {
				inQuote = !inQuote;
			} else if ((c == ',' && !inQuote) || c == 0) {
				bits.Add(bit);
				bit = "";
			} else {
				bit += c;
			}
		}
		return bits;
	}

	public const bool c_giveAllProductStarterPacksOnStart = false;
	
	public static void ReadBlockBalance() {
		var unlocks = new Dictionary<string, int>();

		// 0:ID, 1;Obsolete, 2:Catagory, 3:Type, 4:ProductLineID, 5:Rarity, 6:UnlockYear, 7:CostBuilding, 8:CostDesignTable
		// 9:MaterialCost, 10:ProductionTimeSeconds, 11:Capacity, 12:RadiusIncrease, 13:BuildTimeModifier, 
		// 14:BuildingHpModifier, 15:BuildingFireStartModifier, 16:BAMValue, 17:WorkforceValue, 18:QualityValue
		// 19:SafetyModifier, 20:HealthModifier, 21:EnvironmentModifier, 22:SocialModifier, 23:Premium, 24:GoldCost,
		foreach (var block in NGBlockInfo.s_allBlocks)
		{
			var entry = new BlockBalance(block.Value.m_prefabName);
			//entry.CostInBuilding = block.Value.m_buildingCost;
			//entry.CostInProduct = block.Value.m_designTableCost;
			Register(entry);

			var blockInfo = NGBlockInfo.GetInfo(entry.m_id);// GetSnapshotData(entry.m_id);
			if (blockInfo != null)
			{
				if(blockInfo.m_starterPack)
				{
					unlocks[entry.m_id] = -1;
				}
				/*bool starterPack = (GameManager.c_buildingPartsNonConsumable || GameManager.HasLoadedFromSeed) && block.Value.m_starterPack;
				var buyItem = new ReactItemInfo.BlockInfo(entry.m_id, blockInfo.m_prefabName);
				// If an item has a rarity of 5 it is a premium block therefore always unlocked
				if((buyItem.IsBuildingBlock() || c_giveAllProductStarterPacksOnStart) && starterPack) {
					buyItem.Activate(true);
					int initialValue = (GameManager.c_buildingPartsNonConsumable == false && starterPack) ? 1 : -1;
					unlocks[entry.m_id] = initialValue;
				}
				ItemDetailsHelper.AddItem(buyItem);*/
			}
		}

		// unlock them all at once, so that it uses 1 server call instead of dozens from inside the loop.
		GameManager.AddUnlocks(unlocks);
	}
	// Avatar temp fix
	public static void ReadPartTypeData() {
	}

	static Dictionary<string, BlockBalance> s_items = new Dictionary<string, BlockBalance>();
	public static void Clear() {
		s_items.Clear();
	}
	public static void Register(BlockBalance _this) {
		s_items[_this.m_id] = _this;
	}
	public static BlockBalance GetItem(string _id) {
		BlockBalance ret = null;
		if (_id != null)
			s_items.TryGetValue(_id, out ret);
		else
			Debug.LogErrorFormat("BlockBalance.GetItem can't accept a null id");
		return ret;
	}

	//==== Abstract getters
	public static int GetValueFromID(BlockBalance.Ints _which, string _id, int _default = 0) {
		var bb = GetItem(_id);
		return (bb != null) ? bb.m_intValues[(int)_which] : _default;
	}
	public static float GetValueFromID(BlockBalance.Floats _which, string _id, float _default = 0) {
		var bb = GetItem(_id);
		return (bb != null) ? bb.m_floatValues[(int)_which] : _default;
	}
	public static string GetValueFromID(BlockBalance.Strings _which, string _id, string _default = null) {
		var bb = GetItem(_id);
		return (bb != null) ? bb.m_stringValues[(int)_which] : _default;
	}
	//

	// fire-once error logging; ensures we don't spam error logs multiple times for an individual case
	private static HashSet<string> s_errorLogged = new HashSet<string>();
	private static void LogError(string _category, string _id, string _fmt, params object[] _args) {
		var finalID = _category + "." + _id;
		if (s_errorLogged.Contains(finalID)) return;
		Debug.LogErrorFormat(_fmt, _args);
		s_errorLogged.Add(finalID);
	}

	//==== Getters
	public static float GetBuildCostFromID(string _id) { var cost = GetValueFromID(BlockBalance.Floats.CostInBuilding, _id, -1f); if (cost < 0) { LogError("PCost", _id, "No cost for block ID {0}", _id); return 0f; } return cost; }
	public static float GetProductCostFromID(string _id) { var cost = GetValueFromID(BlockBalance.Floats.CostInProduct, _id, -1f); if (cost < 0) { LogError("BCost", _id, "No cost for block ID {0}", _id); return 0f; } return cost; }
	public static float MaterialCost(string _id) { return GetValueFromID(BlockBalance.Floats.MaterialCost, _id); }
	public static float ProductionTime(string _id) { return GetValueFromID(BlockBalance.Floats.ProductionTime, _id); }
	public static float Capacity(string _id) { return GetValueFromID(BlockBalance.Floats.Capacity, _id); }
	public static float RadiusIncrease(string _id) { return GetValueFromID(BlockBalance.Floats.RadiusIncrease, _id); }
	public static float BuildTimeModifier(string _id) { return GetValueFromID(BlockBalance.Floats.BuildTimeModifier, _id); }
	public static float BuildingHPModifier(string _id) { return GetValueFromID(BlockBalance.Floats.BuildingHPModifier, _id); }
	public static float BuildingFireStartModifier(string _id) { return GetValueFromID(BlockBalance.Floats.BuildingFireStartModifier, _id); }
	public static float BAMValue(string _id) { return GetValueFromID(BlockBalance.Floats.BAMValue, _id); }
	public static float WorkforceValue(string _id) { return GetValueFromID(BlockBalance.Floats.WorkforceValue, _id); }
	public static float QualityValue(string _id) { return GetValueFromID(BlockBalance.Floats.QualityValue, _id); }

	public static int Rarity(string _id) { return GetValueFromID(BlockBalance.Ints.Rarity, _id, 0); }
	public static float SafetyModifier(string _id) { return GetValueFromID(BlockBalance.Floats.SafetyModifier, _id); }
	public static float HealthModifier(string _id) { return GetValueFromID(BlockBalance.Floats.HealthModifier, _id); }
	public static float EnvironmentModifier(string _id) { return GetValueFromID(BlockBalance.Floats.EnvironmentModifier, _id); }
	public static float SocialModifier(string _id) { return GetValueFromID(BlockBalance.Floats.SocialModifier, _id); }
	public static int Premium(string _id) { return GetValueFromID(BlockBalance.Ints.Premium, _id, 0); }
	public static float GoldCost(string _id) { return GetValueFromID(BlockBalance.Floats.GoldCost, _id); }

	//

	//==== Abstract group getters
	static int SumInts(BlockBalance.Ints _which, IEnumerable<IBlockID> _blocks) { int v = 0; foreach (var b in _blocks) v += GetValueFromID(_which, b.BlockID); return v; }
	static int SumInts(BlockBalance.Ints _which, IEnumerable<string> _blocks) { int v = 0; foreach (var b in _blocks) v += GetValueFromID(_which, b); return v; }
	//
	static float SumFloats(BlockBalance.Floats _which, IEnumerable<IBlockID> _blocks) { float v = 0; foreach (var b in _blocks) v += GetValueFromID(_which, b.BlockID); return v; }
	static float SumFloats(BlockBalance.Floats _which, IEnumerable<string> _blocks) { float v = 0; foreach (var b in _blocks) v += GetValueFromID(_which, b); return v; }
	static float ProductFloats(BlockBalance.Floats _which, IEnumerable<IBlockID> _blocks, float _base = 0f) { float v = 1f; foreach (var b in _blocks) v *= _base + GetValueFromID(_which, b.BlockID); return v; }
	static float ProductFloats(BlockBalance.Floats _which, IEnumerable<string> _blocks, float _base = 0f) { float v = 1f; foreach (var b in _blocks) v *= _base + GetValueFromID(_which, b); return v; }
	//

	//==== Group getters
	public static int TotalBuildCost(IEnumerable<IBlockID> _blocks) { return (int)SumFloats(BlockBalance.Floats.CostInBuilding, _blocks); }
	public static int TotalBuildCost(IEnumerable<string> _blocks) { return (int)SumFloats(BlockBalance.Floats.CostInBuilding, _blocks); }
	//
	public static int TotalProductCost(IEnumerable<IBlockID> _blocks) { return (int)SumFloats(BlockBalance.Floats.CostInProduct, _blocks); }
	public static int TotalProductCost(IEnumerable<string> _blocks) { return (int)SumFloats(BlockBalance.Floats.CostInProduct, _blocks); }
	//
	public static int TotalCapacity(IEnumerable<IBlockID> _blocks) { return (int)SumFloats(BlockBalance.Floats.Capacity, _blocks); }
	public static int TotalCapacity(IEnumerable<string> _blocks) { return (int)SumFloats(BlockBalance.Floats.Capacity, _blocks); }
	//
	public static float TotalRadiusMultiplier(IEnumerable<IBlockID> _blocks) { return 1f + SumFloats(BlockBalance.Floats.RadiusIncrease, _blocks); } // ProductFloats(BlockBalance.Floats.Capacity, _blocks, 1f);
	public static float TotalRadiusMultiplier(IEnumerable<string> _blocks) { return 1f + SumFloats(BlockBalance.Floats.RadiusIncrease, _blocks); }
	//
	public static float TotalBuildingHPMultiplier(IEnumerable<IBlockID> _blocks) { return 1f + SumFloats(BlockBalance.Floats.BuildingHPModifier, _blocks); }
	public static float TotalBuildingHPMultiplier(IEnumerable<string> _blocks) { return 1f + SumFloats(BlockBalance.Floats.BuildingHPModifier, _blocks); }
	//
	public static float TotalBuildingFireStartModifier(IEnumerable<IBlockID> _blocks) { return 1f + SumFloats(BlockBalance.Floats.BuildingFireStartModifier, _blocks); }
	public static float TotalBuildingFireStartModifier(IEnumerable<string> _blocks) { return 1f + SumFloats(BlockBalance.Floats.BuildingFireStartModifier, _blocks); }
	//
	public static float TotalProductionTime(IEnumerable<IBlockID> _blocks) { return SumFloats(BlockBalance.Floats.ProductionTime, _blocks); }
	public static float TotalProductionTime(IEnumerable<string> _blocks) { return SumFloats(BlockBalance.Floats.ProductionTime, _blocks); }
	//====

	//==== Debug/dev features
#if TODO
#if UNITY_EDITOR
	[MenuItem("22Cans/Design/Check Block Balance Consistency")]
#endif
	public static void DEBUG_CheckConsistency() {
		if (!Application.isPlaying) {
			Debug.LogErrorFormat("Consistency check only runs in Play mode");
			return;
		}

		string errors = "";
		var typesWithSpaces = new HashSet<string>();
		foreach (var kvp in BlockDataSnapshot.Snapshot) {
			if (kvp.Value.TypeIDs != null)
				foreach (var t in kvp.Value.TypeIDs)
					if (t.Contains(" "))
						if (!typesWithSpaces.Contains(t))
							typesWithSpaces.Add(t);

			if (kvp.Key == kvp.Value.UID && GetItem(kvp.Key) == null) {
				bool foundInDeprecated = false;
				if (kvp.Value.DeprecatedIDs != null) {
					foreach (var did in kvp.Value.DeprecatedIDs) {
						if (GetItem(did) != null) {
							errors += string.Format("Key {0} in snapshot has upgraded from {1}, not in BlockBalance\n", kvp.Key, did);
							foundInDeprecated = true;
							break;
						}
					}
				}
				if (!foundInDeprecated)
					errors += string.Format("Key {0} exists in Snapshot but not in BlockBalance\n", kvp.Key);
			} else if (kvp.Value.Obsolete) {
				errors += string.Format("Key {0} exists in BlockBalance but is marked Obsolete in Snapshot\n", kvp.Key);
			}
		}
		foreach (var kvp in s_items) {
			if (!BlockDataSnapshot.Snapshot.ContainsKey(kvp.Key)) {
				errors += string.Format("Key {0} exists in BlockBalance but not in Snapshot\n", kvp.Key);
			}
		}
		foreach (var t in typesWithSpaces)
			errors += string.Format("Found block Part Type with spaces: {0}\n", t);
		if (errors.Length > 0) Debug.LogErrorFormat("{0}", errors);
		else Debug.LogErrorFormat("Consistency check complete, no errors found");
	}
#endif
}
