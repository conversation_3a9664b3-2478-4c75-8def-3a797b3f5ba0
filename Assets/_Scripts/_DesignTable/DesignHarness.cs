using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class DesignHarness : Mono<PERSON>ingleton<DesignHarness>
{
    public Light m_mainDesignLamp;
    public float m_interiorLightNits = 48f;
    public float m_exteriorLightNits = 12f;
    public void SetupLights(bool _isInterior)
    {
        m_mainDesignLamp.lightUnit = UnityEngine.Rendering.LightUnit.Nits;
        m_mainDesignLamp.intensity = _isInterior ? m_interiorLightNits : m_exteriorLightNits;
    }
}
