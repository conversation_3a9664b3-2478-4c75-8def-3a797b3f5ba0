using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class StickerInstance : MonoBeh<PERSON>our {
	GameObject m_stickerHolder;


	public class StickerItem
	{
		public int m_id;
		public string m_label;
		public Vector3 m_position;
	}
	public List<StickerItem> m_stickers = new ();

	public class EdgeInfo {
		public int m_triangle;
		public int m_v0, m_v1, m_nv0, m_nv1;
		public Vector3 m_texSide, m_texUp;
		public static long EdgeId(int _v1, int _v2) {
			int mask = (_v1 - _v2) >> 31;
			int min = (_v1 & mask) | (_v2 & ~mask);
			int max = _v1 + _v2 - min;
			return ((long)min) | (((long)max) << 32);
		}
	}

	public static bool TriangleAABBIntersects(Vector2 _v1, Vector2 _v2, Vector2 _v3, Vector2 _aabbMin, Vector2 _aabbMax) {
		// check for a point (any) from one shape in the other (for contained) plus all edge intersections
		if (_v1.x >= _aabbMin.x && _v1.x < _aabbMax.x && _v1.y >= _aabbMin.y && _v1.y < _aabbMax.y) return true;
		if (GeometryUtilities.IsPointInTriangle(_aabbMin, _v1, _v2, _v3)) return true;
		//
		var aabbNP = new Vector2(_aabbMin.x, _aabbMax.y);
		var aabbPN = new Vector2(_aabbMax.x, _aabbMin.y);
		if (GeometryUtilities.LineSegmentIntersect2D(_v1, _v2, _aabbMin, aabbNP)) return true;
		if (GeometryUtilities.LineSegmentIntersect2D(_v1, _v2, aabbNP, _aabbMax)) return true;
		if (GeometryUtilities.LineSegmentIntersect2D(_v1, _v2, _aabbMax, aabbPN)) return true;
		if (GeometryUtilities.LineSegmentIntersect2D(_v1, _v2, aabbPN, _aabbMin)) return true;
		if (GeometryUtilities.LineSegmentIntersect2D(_v2, _v3, _aabbMin, aabbNP)) return true;
		if (GeometryUtilities.LineSegmentIntersect2D(_v2, _v3, aabbNP, _aabbMax)) return true;
		if (GeometryUtilities.LineSegmentIntersect2D(_v2, _v3, _aabbMax, aabbPN)) return true;
		if (GeometryUtilities.LineSegmentIntersect2D(_v2, _v3, aabbPN, _aabbMin)) return true;
		if (GeometryUtilities.LineSegmentIntersect2D(_v3, _v1, _aabbMin, aabbNP)) return true;
		if (GeometryUtilities.LineSegmentIntersect2D(_v3, _v1, aabbNP, _aabbMax)) return true;
		if (GeometryUtilities.LineSegmentIntersect2D(_v3, _v1, _aabbMax, aabbPN)) return true;
		if (GeometryUtilities.LineSegmentIntersect2D(_v3, _v1, aabbPN, _aabbMin)) return true;
		return false;
	}

	void Awake() {
		if (m_stickerHolder != null) return;
		m_stickerHolder = transform.Find("Stickers")?.gameObject;
		if (m_stickerHolder != null) return;
		m_stickerHolder = new GameObject("Stickers");
		m_stickerHolder.transform.SetParent(transform);
		m_stickerHolder.transform.localPosition = Vector3.zero;
		m_stickerHolder.transform.localRotation = Quaternion.identity;
		m_stickerHolder.transform.localScale = Vector3.one;
		m_stickerHolder.layer = transform.gameObject.layer;
	}
	public void Clear() {
		Awake(); // in case the instance was disabled on creation
		if (m_stickerHolder != null) m_stickerHolder.transform.DestroyChildren();
		ClearRuneEffects();
		m_stickers.Clear();
	}

	private void ClearRuneEffects()
	{
		var runeEffects = GetComponentsInChildren<RuneEffect>();
		for (int i = 0; i < runeEffects.Length; ++i)
			Destroy(runeEffects[i].gameObject);
	}

	private void AddRuneEffect(int _id)
	{
		var data = StickerData.s_entries[_id];
		string runeEffect = data.m_path == "Planes_Fire_Ball" ? "Flame" : null; // TODO - look up rune effect from data properly
		if (runeEffect != null)
		{
			var effectPrefab = Resources.Load<RuneEffect>($"RuneEffects/{runeEffect}RuneEffect");
			if (effectPrefab != null)
				Instantiate(effectPrefab, transform, false);
		}
	}

	public void AddSticker(int _id, Ray _ray, float _scale, float _rotate, int _index, bool _addColliders, string _originalId) {
		Awake(); // in case the instance was disabled on creation
		//AddRuneEffect(_id);
		var mesh = GetComponent<MeshFilter>().sharedMesh; // TODO LODGroups
		var verts = mesh.vertices;
		var nrms = mesh.normals;
		float bestDistance = 1e23f;
		int bestTriangle = -1;
		Vector3 bestHit = Vector3.zero;
		Vector2 bestBarycentric;
		for (int i = 0; i < mesh.subMeshCount; ++i) {
			var indices = mesh.GetIndices(i);
			for (int j = 0; j < indices.Length; j += 3) {
				Vector3 hit; Vector2 barycentric;
				var t = RaycastTriangle(_ray, verts[indices[j+0]], verts[indices[j+1]], verts[indices[j+2]], out hit, out barycentric);
				if (t > 0 && t < bestDistance) {
					bestDistance = t;
					bestTriangle = (i << 24) | j;
					bestHit = hit; bestBarycentric = barycentric;
				}
			}
		}
		if (bestTriangle == -1) return; // error case

		m_stickers.Add(new StickerItem() {m_id = _id, m_label = _originalId, m_position = bestHit});
		
		// got a triangle, walk the mesh
		const float c_extrude = .002f;
		const float c_extrudePerLayer = .0005f;
		float extrude = c_extrude + _index * c_extrudePerLayer;
		var sMesh = new Mesh();
		var sVerts = new List<Vector3>();
		var sNrms = new List<Vector3>();
		var sUVs = new List<Vector2>();
		var sInds = new List<int>();
		var inds = mesh.GetIndices(bestTriangle>>24);
		var firstIndex = bestTriangle & 0xFFFFFF;

		// generate texture basis vectors
		var v0 = verts[inds[firstIndex+0]];
		var v1 = verts[inds[firstIndex+1]];
		var v2 = verts[inds[firstIndex+2]];
		var n0 = nrms[inds[firstIndex+0]];
		var n1 = nrms[inds[firstIndex+1]];
		var n2 = nrms[inds[firstIndex+2]];
		Vector3 e01 = v1 - v0, e02 = v2 - v0;
		var nrm = Vector3.Cross(e01, e02).normalized;
		var texUp = Vector3.up;
		if (Mathf.Abs(Vector3.Dot(texUp, nrm)) > .6f) {
			texUp = Vector3.forward;
		}
		var texSide = Vector3.Cross(texUp, nrm).normalized; // texture basis is texSide, texUp, nrm
		texUp = Vector3.Cross(nrm, texSide);
		texSide = texSide.RotateAbout(nrm, Mathf.Deg2Rad * _rotate);
		texUp = texUp.RotateAbout(nrm, Mathf.Deg2Rad * _rotate);
		const float c_uvBaseScale = 2.5f * .5f;
		float uvScale = c_uvBaseScale / _scale;
		var uvOrigin = Vector2.one * .5f;
		var uv0 = new Vector2(Vector3.Dot(v0 - bestHit, texSide), Vector3.Dot(v0 - bestHit, texUp)) * uvScale + uvOrigin;
		var uv1 = new Vector2(Vector3.Dot(v1 - bestHit, texSide), Vector3.Dot(v1 - bestHit, texUp)) * uvScale + uvOrigin;
		var uv2 = new Vector2(Vector3.Dot(v2 - bestHit, texSide), Vector3.Dot(v2 - bestHit, texUp)) * uvScale + uvOrigin;

		var vertMap = new Dictionary<int, int>();
		vertMap[inds[firstIndex+0]] = (sVerts.Count << 8) | 1;
		sVerts.Add(v0 + n0 * extrude); sNrms.Add(n0); sUVs.Add(uv0);
		vertMap[inds[firstIndex+1]] = (sVerts.Count << 8) | 1;
		sVerts.Add(v1 + n1 * extrude); sNrms.Add(n1); sUVs.Add(uv1);
		vertMap[inds[firstIndex+2]] = (sVerts.Count << 8) | 1;
		sVerts.Add(v2 + n2 * extrude); sNrms.Add(n2); sUVs.Add(uv2);
		sInds.Add(0); sInds.Add(1); sInds.Add(2);

		var checkEdges = new List<EdgeInfo>();
		checkEdges.Add(new EdgeInfo() { m_triangle = firstIndex, m_texSide = texSide, m_texUp = texUp, m_v0 = inds[firstIndex+0], m_v1 = inds[firstIndex+1], m_nv0 = 0, m_nv1 = 1 });
		checkEdges.Add(new EdgeInfo() { m_triangle = firstIndex, m_texSide = texSide, m_texUp = texUp, m_v0 = inds[firstIndex+1], m_v1 = inds[firstIndex+2], m_nv0 = 1, m_nv1 = 2 });
		checkEdges.Add(new EdgeInfo() { m_triangle = firstIndex, m_texSide = texSide, m_texUp = texUp, m_v0 = inds[firstIndex+2], m_v1 = inds[firstIndex+0], m_nv0 = 2, m_nv1 = 0 });

		var edgeUsed = new HashSet<long>();
		edgeUsed.Add(EdgeInfo.EdgeId(firstIndex+0, firstIndex+1));
		edgeUsed.Add(EdgeInfo.EdgeId(firstIndex+1, firstIndex+2));
		edgeUsed.Add(EdgeInfo.EdgeId(firstIndex+2, firstIndex+0));
		for (int i = 0; i < checkEdges.Count; ++i) {
			// find neighbour to edge checkEdges[i] == triangle with [i] that -isn't- m_triangle
			int c0 = checkEdges[i].m_v0, c1 = checkEdges[i].m_v1;
			for (int j = 0; j < inds.Length; j += 3) {
				if (j == checkEdges[i].m_triangle) continue;
				int match2, i0 = inds[j+0], i1 = inds[j+1], i2 = inds[j+2];
				if (i0 == c0 && i1 == c1) match2 = i2;
				else if (i1 == c0 && i0 == c1) match2 = i2;
				else if (i0 == c0 && i2 == c1) match2 = i1;
				else if (i2 == c0 && i0 == c1) match2 = i1;
				else if (i1 == c0 && i2 == c1) match2 = i0;
				else if (i2 == c0 && i1 == c1) match2 = i0;
				else continue;
				// found neighbour, reproject texture
				int ni0 = checkEdges[i].m_nv0, ni1 = checkEdges[i].m_nv1;
				var nv2 = verts[match2];
				var nn2 = nrms[match2];
				var e01n = verts[c1] - verts[c0];
				var e02n = nv2 - verts[c0];
				var nnrm = Vector3.Cross(e01n, e02n).normalized;
				var ntexSide = checkEdges[i].m_texSide;
				var ntexUp = checkEdges[i].m_texUp;
				// TODO - what if this is 90 degrees?
				ntexSide = (ntexSide - nnrm * Vector3.Dot(ntexSide, nnrm)).normalized;
				ntexUp = (ntexUp - nnrm * Vector3.Dot(ntexUp, nnrm)).normalized;
				var nuv2 = new Vector2(Vector3.Dot(e02n, ntexSide), Vector3.Dot(e02n, ntexUp)) * uvScale + sUVs[ni0];

				// check that we're not completely out of the UV space
				if (!TriangleAABBIntersects(sUVs[ni0], sUVs[ni1], nuv2, Vector2.zero, Vector2.one)) continue;

				int lookup;
				if (!vertMap.TryGetValue(match2, out lookup)) {
					lookup = (sVerts.Count << 8) | 0;
					sVerts.Add(nv2 + nn2 * extrude); sNrms.Add(nn2); sUVs.Add(Vector2.zero);
				}
				lookup ++;
				vertMap[match2] = lookup;
				int ni2 = lookup >> 8;
				float uvCount = (float)(lookup & 0xFF);
				sUVs[ni2] = (sUVs[ni2] * (uvCount-1) + nuv2) / uvCount;

				sInds.Add(ni1); sInds.Add(ni0); sInds.Add(ni2);
				// add each edge to checkEdges except any already used
				long e0 = EdgeInfo.EdgeId(c0, match2), e1 = EdgeInfo.EdgeId(match2, c1);
				if (!edgeUsed.Contains(e0)) {
					edgeUsed.Add(e0);
					checkEdges.Add(new EdgeInfo() { m_triangle = j, m_texSide = ntexSide, m_texUp = ntexUp, m_v0 = c0, m_v1 = match2, m_nv0 = ni0, m_nv1 = ni2 });
				}
				if (!edgeUsed.Contains(e1)) {
					edgeUsed.Add(e1);
					checkEdges.Add(new EdgeInfo() { m_triangle = j, m_texSide = ntexSide, m_texUp = ntexUp, m_v0 = match2, m_v1 = c1, m_nv0 = ni2, m_nv1 = ni1 });
				}
			}
		}

		// finally build the mesh
		sMesh.SetVertices(sVerts);
		sMesh.SetNormals(sNrms);
		sMesh.SetUVs(0, sUVs);
		sMesh.SetIndices(sInds, MeshTopology.Triangles, 0, true);
		sMesh.UploadMeshData(false);
		var go = new GameObject(_originalId);
		go.transform.parent = m_stickerHolder.transform;
		go.transform.localPosition = Vector3.zero; go.transform.localRotation = Quaternion.identity; go.transform.localScale = Vector3.one;
		var mf = go.AddComponent<MeshFilter>();
		var mr = go.AddComponent<MeshRenderer>();
		mr.sharedMaterial = DesignTableManager.StickerIdToMaterial(_id);
		mf.sharedMesh = sMesh;
		go.layer = m_stickerHolder.layer;
		if (_addColliders) {
			var stk = go.AddComponent<Sticker>();
			stk.m_id = _originalId;
			var mc = go.AddComponent<MeshCollider>();
			mc.sharedMesh = sMesh;
		}
	}
	//=====
	public float RaycastTriangle(Ray _ray, Vector3 _v0, Vector3 _v1, Vector3 _v2, out Vector3 _hit, out Vector2 _barycentric) {
		_hit = Vector3.zero; _barycentric = Vector2.zero;
		Vector3 edge1 = _v1 - _v0, edge2 = _v2 - _v0;
		var h = Vector3.Cross(_ray.direction, edge2);
		var a = Vector3.Dot(edge1, h);
		if (a * a < Mathf.Epsilon*Mathf.Epsilon) return 0; // ray is parallel to triangle
		var f = 1f / a;
		var s = _ray.origin - _v0;
		var u = f * Vector3.Dot(s, h);
		if (u < 0.0f || u > 1.0f) return 0;
		var q = Vector3.Cross(s, edge1);
		var v = f * Vector3.Dot(_ray.direction, q);
		if (v < 0.0f || u + v > 1.0f) return 0;
		var t = f * Vector3.Dot(edge2, q);
		_barycentric.x = u; _barycentric.y = v;
		_hit = _ray.origin + _ray.direction * t;
		return t;
	}
	//=====
	public static void ClearStickers(Transform _object) {
		var si = _object.gameObject.GetComponent<StickerInstance>();
		if (si != null) si.Clear();
	}
	public static StickerInstance AddSticker(Transform _object, int _stickerId, Vector3 _rayOrigin, Vector3 _rayDirection, float _scale, float _rotate, int _index, string _originalId) {
		var si = _object.gameObject.GetComponent<StickerInstance>();
		if (si == null) {
			si = _object.gameObject.AddComponent<StickerInstance>();
		}
		si.AddSticker(_stickerId, new Ray(_rayOrigin, _rayDirection), _scale, _rotate, _index, true, _originalId);
		return si;
	}
}
