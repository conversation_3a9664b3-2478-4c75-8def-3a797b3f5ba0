using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public enum EDesignFitnessType
{
	None,
	Jewellery_Acc,
	Jewellery_Base,
	Jewellery_Bracelet,
	Jewellery_Ring,
	Jewellery_Jewel,
	Jewellery_Necklace,
	Cake_Base,
	Cake_Decoration,
	Cake_Filling,
	<PERSON><PERSON>_Topping,
	Doll_Accessories,
	<PERSON>_<PERSON>,
	<PERSON>_<PERSON>,
	<PERSON>_<PERSON>gs,
	<PERSON><PERSON>,
	<PERSON><PERSON>ream_Topping,
	Ice<PERSON>ream_Base,
	IceCream_Filling,
	IceCream_Icecream,
	Jack<PERSON>_Sleeve,
	Jack<PERSON>_Mannequin,
	Jackets_Body,
	Jackets_Neck,
	Jackets_Pocket,
	Jackets_Accessories,
	ToyPlane_Wings,
	ToyPlane_Accessories,
	ToyPlane_Fuselage,
	ToyPlane_Tail,
	ToyPlane_Stand,
	ToyPlane_Trim,
	ToyPlane_Propellor,
	<PERSON>_<PERSON>,
	<PERSON>_<PERSON>,
	<PERSON>_Decoration,
	<PERSON>_<PERSON>,
	<PERSON>_Leg,
	Bicycles_Decoration,
	Bicycles_Frame,
	Bicycles_Handles,
	Bicycles_Pedal,
	Bicycles_Seat,
	Bicycles_Wheel
}

public class DesignPartType : MonoBehaviour
{
	[SerializeField] private EDesignFitnessType m_type;
	public EDesignFitnessType DesignFitnessType => m_type;
}
