using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class DTDragSprite : MonoBehaviour {
	public string m_context;
	void Start() {
		DesignTableManager.Me.FreezeTableContents(true);
		PlayerHandManager.Me.ForceHideCursor(true);
	}
	void Update() {
		if (!GameManager.GetMouseButton(0)) {
			// end of drag, destroy self and apply if appropriate
			DesignTableManager.Me.UpdateSpriteDrag(m_context, transform.position, true);
			DesignTableManager.Me.FreezeTableContents(false);
			PlayerHandManager.Me.ForceHideCursor(false);
			Destroy(gameObject);
		} else {
			transform.position = Utility.mousePosition.ApplyFingerOffset(true);
			DesignTableManager.Me.UpdateSpriteDrag(m_context, transform.position, false);
		}
	}
}
