using System;
using System.Collections.Generic;
using UnityEngine.UI;
using UnityEngine;
using TMPro;
using Random = UnityEngine.Random;

public class OtherPlayerDetailGUI : MonoBehaviour
{
    public static OtherPlayerDetailGUI s_playerDetailGUI;

    public GameObject m_object;
    public GameObject m_trophy;
    public TMP_Text m_playerName;
	public TMP_Text m_description;
	public Button m_visitBtn;
	public Image m_avatar;
    public Transform m_trophyCabinet;

    private string[] m_activityLevel = { "inactive", "active", "very active", "addicted to work" };
    private string[] m_satisfaction = { "a tyrant", "a bit grumpy", "quite quiet", "a jolly guy", "a fun boss"};
    private string m_playerID;
    private int m_sound;

    void Start()
    {
    }

    public void Activate(string id, string name, string avatar, string companyName, string startDate, int activityLevel, float satisfaction, string awards)
    {
        m_playerID = id;
        m_playerName.text = name;

        int s = (int)(satisfaction * 2.5f);
        if (s > 4)
            s = 4;

        var substrings = name.Split(' ');
        string firstName = substrings[0];
        string details = firstName + ", the CEO of " + companyName + " since " + startDate + ", is " + m_activityLevel[activityLevel] + ". His town thinks he is " + m_satisfaction[s] + ".";

        GameManager.Me.GetDesignSpriteAsync(avatar, CaptureObjectImage.Use.Avatar, _s =>
        {
            if (m_avatar != null) // image has been destroyed since requesting
                m_avatar.sprite = _s;
        });

        m_description.text = details;

        if(GameManager.Me.IsOKToPlayUISound())
            m_sound = AudioClipManager.Me.PlaySoundOld("PlaySound_WindowBubble_Small01", transform);

        FillTrophyCabinet(awards);

        if (string.IsNullOrEmpty(id) || id == GameManager.UserId || id.StartsWith("Fake"))
        {
            m_visitBtn.gameObject.SetActive(false);
        }
        else
        {
            m_visitBtn.gameObject.SetActive(true);
        }
    }

    private string RemoveDuplicateAwards(string awards)
    {
        List<string> awardList = new List<string>();
        string decoded = "";

        var split = awards.Split('|');
        foreach (var item in split)
        {
            if (awardList.Contains(item) == false)
            {
                awardList.Add(item);
                if (decoded == "")
                    decoded += item;
                else
                    decoded += "|" + item;
            }
        }

        return decoded;
    }

    private int CountAwards(string awards, string type)
    {
        int c = 0;

        var trophies = awards.Split('|');
        foreach (var trophy in trophies)
        {
            if (trophy == type)
                c++;
        }

        return c;
    }

    private void FillTrophyCabinet(string awards)
    {
        if(awards != null)
        {
            var decoded = RemoveDuplicateAwards(awards);
            var trophies = decoded.Split('|');
            foreach(var trophy in trophies)
            {
                int awarded = CountAwards(awards, trophy);
                if (trophy != "")
                {
                }
            }
        }
    }

    public void OnPressVisitTown()
    {
        GameManager.Me.VisitPlayerWorld(m_playerID);
        Destroy(gameObject);
    }

    public void Deactivate()
    {
        s_playerDetailGUI = null;
        Destroy(gameObject);
    }

    public static OtherPlayerDetailGUI Create(string name, string id, string avatar, string awards, int activity, float satisfaction)
    {
        if (s_playerDetailGUI != null)
            s_playerDetailGUI.Deactivate();
        var go = Instantiate(NGManager.Me.m_otherPlayerGUIPrefab.gameObject, GameManager.Me.CurrentCanvas);
     //   go.transform.SetSiblingIndex(0);
        var tag = go.GetComponent<OtherPlayerDetailGUI>();

        tag.Activate(id, name, avatar, "The Company", DateTime.Now.ToShortDateString(), activity, satisfaction, awards);
        s_playerDetailGUI = tag;
        return tag;
    }
}
