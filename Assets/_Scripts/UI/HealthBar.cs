using System;
using System.Collections.Generic;
using BehaviourDesignComponents;
using TMPro;
using Unity.Mathematics;
using UnityEditor;
using UnityEngine;
using UnityEngine.UI;

public interface IHealthBar
{
	public float MaxHealth { get; }
	public float MaxArmour { get; }
	public float NormalizedHealth { get; }
	public float NormalizedArmour { get; }
	public bool ShowHealthBar { get; }
	public int Level { get; }
	public float HealthBarHeight { get; }
}

public class HealthBar : MonoBehaviour
{
	public Canvas m_canvas;
	
	[SerializeField]
    private GameObject m_healthBarVisualHolder = null;

    [SerializeField]
    private TMPro.TextMeshProUGUI m_levelText = null;
    
    private IHealthBar m_info;
    private MACharacterBase m_characterBase;
    private Transform m_targetTransform;
    
    private Vector3 m_targetOffset = Vector3.zero;

    public HealthBarElement m_healthBar;
	public HealthBarElement m_manaBar;

	public RuneBarUI runeBarUI = null;

	[SerializeField]
	private GameObject healingIcon = null;

	[SerializeField]
	private bool isExternal = false;
	
	private int m_lastLevelValue = -999;
	
    private void Awake()
    {
		if(m_info == null)
			m_info = GetComponentInParent<IHealthBar>();
		//AssertRefOrDisableSelf(m_info, "m_info");
    }
    
    private void Start()
    {
		SetupBars();
	}

	private void SetupBars()
	{
		if (m_info == null)
			return;
			
		m_characterBase = m_info as MACharacterBase;
		m_targetTransform = null;
		
		if(isExternal == false)
		{
			if(m_characterBase != null) m_targetTransform = m_characterBase.GetHeadTransform();
			if(m_targetTransform == null) m_targetTransform = transform.parent;
		}

		/*//KW: set Height if it doesn't seem to have been set up manually
		if(transform.localPosition.y <= 0.0f)
        {
			Vector3 pos = transform.localPosition;
			pos.y = m_info.HealthBarHeight;
			transform.localPosition = pos;
			
			
		}*/
		
		m_targetOffset = Vector3.up * m_info.HealthBarHeight;
		
		if (isExternal == false)
	    {
			if (AssertRefOrDisableSelf(m_canvas, "m_canvas"))
				m_canvas.worldCamera = Camera.main;
		}

	    if (AssertRefOrDisableSelf(m_healthBar, "m_healthBar")) m_healthBar.Init(m_info);
	    if (AssertRefOrDisableSelf(m_manaBar, "m_manaBar")) m_manaBar.Init(m_info);
        
        SetBarEnabled(m_healthBar, true);
        
        UpdateShowBar();
	}

	private void Update()
	{
        SetLevel();
        UpdateShowBar();
	}

	public void SetupInfo(IHealthBar info)
	{
		m_info = info;

		if (AssertRefOrDisableSelf(m_info, "m_info"))
		{
			gameObject.SetActive(true);
			SetupBars();
		}
	}

	public void SetLevel()
    {
		if (m_info == null)
			return;
		if (m_info.Level.CheckUnchanged(ref m_lastLevelValue))
			return;
		
#if UNITY_EDITOR
	    if (AssertRefOrDisableSelf(m_levelText, "m_levelText") == false) return;
#endif
	    m_levelText.text = $"{(m_info.Level < 0 ? "" : m_info.Level)}";
    }

	private void SetBarEnabled(HealthBarElement _bar, bool _value)
	{
#if UNITY_EDITOR
	    if (AssertRefOrDisableSelf(_bar, "_bar") == false) return;
#endif
		if (_bar.gameObject.activeSelf != _value)
			_bar.gameObject.SetActive(_value);
	}
	
	private void UpdateShowBar()
    {
		if (m_info == null)
		{
			if (m_healthBarVisualHolder.activeSelf)
				m_healthBarVisualHolder.SetActive(false);
			
			return;
		}
		
		bool isPossessed = GameManager.Me.IsPossessed(m_info as NGMovingObject);
	    SetBarEnabled(m_manaBar, isPossessed);

		bool showHealingIcon = false;
		if (isPossessed && m_characterBase != null)
		{
			float rate = m_characterBase.GetHealthRecoveryRate();
			showHealingIcon = (rate > 0f) && ((m_info.NormalizedHealth < 1f) || ((m_characterBase.Armour != null) && (m_info.NormalizedArmour < 1f)));
		}
		if (healingIcon != null && healingIcon.activeSelf != showHealingIcon)
			healingIcon.SetActive(showHealingIcon);
		
	    runeBarUI.ToggleUI(isPossessed && GameManager.BranchingCombosEnabled);

        var show = m_info.ShowHealthBar || (MADemoDialog.Me != null && MADemoDialog.Me.alwaysShowingHealthBars);
		if (isPossessed)
			show = isExternal;

        if (m_healthBarVisualHolder == null)
        {
	        gameObject.SetActive(false);
        }
        else if(m_healthBarVisualHolder.gameObject.activeSelf != show)
        {
			UpdatePosition(true);
	        m_healthBarVisualHolder.gameObject.SetActive(show);
	    }
    }
	
	public const float c_smoothSpeed = 8f;
	private Vector3 m_lastPos;
	private float m_minY;
	
	private void UpdatePosition(bool _immediate = false)
	{
		if(m_targetTransform == null) return;
		
		var targetPos = m_targetTransform.position + m_targetOffset;
		float height = targetPos.y - transform.parent.position.y;
			
		if(height < m_minY)
			targetPos.y += m_minY - height;
		else
			m_minY = height;
			
		if(_immediate)
			m_lastPos = targetPos;
		else
			m_lastPos = Vector3.Lerp(m_lastPos, targetPos, Time.deltaTime * c_smoothSpeed); 
		transform.position = m_lastPos;
	}
	
	private void LateUpdate()
	{
		if(m_healthBarVisualHolder.gameObject.activeSelf)
		{
			UpdatePosition();
		}
	}
	
	private bool AssertRefOrDisableSelf(object _ref, string _name)
	{
		if (_ref == null)
		{
			if (!isExternal)
				Debug.LogError($"{GetType().Name} - {transform.Path()} - NUllReference exception '{_name}'");
			gameObject.SetActive(false);
			return false;
		}
		return true;
	}
}