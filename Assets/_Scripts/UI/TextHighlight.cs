using DG.Tweening;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UIElements;
using static UnityEngine.Rendering.DebugUI;

public class TextHighlight : <PERSON>o<PERSON><PERSON><PERSON>our, IPointerEnterHandler, IPointerExitHandler
{

    [Header("Text Highlighting")]
    [Range(0, 1f)]
    [SerializeField] private float m_highlightDuration = 0.5f;
    [Range(0f, 1f)]
    [SerializeField] private float m_glowOpacity = 1f;
    [SerializeField] private Color m_glowColour = Color.green;
    private Color m_hideGlow;
    private TMP_Text m_text;
    private Material m_matInstance;
    private void Start()
    {
        m_text = GetComponent<TMP_Text>();
        m_hideGlow = m_glowColour;
        m_hideGlow.a = 0f;
        m_glowColour.a = m_glowOpacity;

        //m_matInstance = new Material(m_text.fontMaterials[0]);
        //m_text.fontMaterial = m_matInstance;

    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        StartCoroutine(HighlightText(m_glowColour));
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        StartCoroutine(HighlightText(m_hideGlow));
    }


    public IEnumerator HighlightText(Color endValue)
    {
        float elapsedTime = 0;
        Color startValue = m_text.fontMaterial.GetColor(ShaderUtilities.ID_GlowColor);
        while (elapsedTime < m_highlightDuration)
        {
            elapsedTime += Time.deltaTime;
            Color32 lerpAlpha = Color.Lerp(startValue, endValue, elapsedTime / m_highlightDuration);
            m_text.fontMaterial.SetColor(ShaderUtilities.ID_GlowColor, lerpAlpha);
            yield return null;
        }
        m_text.fontMaterial.SetColor(ShaderUtilities.ID_GlowColor, endValue);
    }
}
