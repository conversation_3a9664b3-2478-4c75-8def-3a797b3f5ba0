using System.Collections.Generic;
using UnityEngine;

public class RadialPopup : MonoBehaviour
{
    public int m_segments = 9;
    public Gradient m_gradient;
    public Transform m_visuals;
    public float m_fadeInLength = .4f;
    public Color m_disabledImageColour = new Color(.5f, .5f, .5f, .5f);
    
    public Sprite m_earlyAccessSprite;
    
    private float m_fade = 0, m_fadeTarget = 0;
    private int m_segmentsConfigured = 0;

    public enum ELockState
    {
        Unlocked,
        Locked,
        EarlyAccess,
        Active,
    };
    private Sprite[] m_sprites;
    private System.Func<bool> m_closePopup;
    private System.Action<int> m_onButtonPress;
    private System.Func<int, ELockState> m_isButtonActive;
    private System.Func<int, KeyCode> m_getShortcut;
    private bool m_closeOnSelect, m_forceClose;
    private KeyCode m_shortcutKey = KeyCode.None;
    private System.Action m_shortcutCb = null;
    
    private CanvasGroup m_canvasGroup;
    private float m_alphaTarget = 1, m_alphaCurrent = 0;

    private void Activate(Vector3 _screenPos, Sprite[] _sprites, bool _closeOnSelect, System.Func<bool> _closePopup, System.Action<int> _onButtonPress, System.Func<int, ELockState> _isButtonActive,
        System.Func<int, KeyCode> _buttonShortcut, float _animationTime, KeyCode _shortcutKey = KeyCode.None, System.Action _shortcutCb = null)
    {
        m_segments = _sprites.Length;
        m_sprites = _sprites;
        m_closePopup = _closePopup;
        m_onButtonPress = _onButtonPress;
        m_isButtonActive = _isButtonActive;
        m_closeOnSelect = _closeOnSelect;
        m_forceClose = false;
        m_visuals.position = _screenPos;
        m_getShortcut = _buttonShortcut;
        m_shortcutKey = _shortcutKey;
        m_shortcutCb = _shortcutCb;
        if (_animationTime >= 0) m_fadeInLength = Mathf.Max(.001f, _animationTime);
        Setup();
    }

    void Setup()
    {
        m_canvasGroup = GetComponent<CanvasGroup>();
        m_canvasGroup.alpha = 0;
        m_alphaCurrent = 0;
        m_segmentsConfigured = m_segments;
        m_visuals.DestroyChildren(true, 1);
        var offClickButton = GetComponent<UnityEngine.UI.Button>();
        offClickButton.onClick.RemoveAllListeners();
        offClickButton.onClick.AddListener(() => ForceClose());
        
        var firstChild = m_visuals.GetChild(0);
        firstChild.localScale = Vector3.one;
        var firstImage = firstChild.GetComponent<UnityEngine.UI.Image>();
        firstImage.fillAmount = 1.0f / m_segments;
        var firstImageRT = firstImage.transform as RectTransform;
        var arcHalfAngle = 360.0f * .5f / m_segments;
        var radius = firstImageRT.rect.width * .5f;
        var sin = Mathf.Sin(arcHalfAngle * Mathf.Deg2Rad);
        var iconRadius = radius * sin / (1 + sin);
        var iconY = radius - iconRadius;
        var iconRotate = Quaternion.Euler(0, 0, -arcHalfAngle);
        var iconPosition = (iconRotate * Vector3.down) * iconY;
        var subIconPosition = iconPosition * .5f;
        var iconSizeDelta = Vector2.one * (iconRadius * 2 * .717f);
        for (int i = 0; i < m_segments; ++i)
        {
            var child = i == 0 ? firstChild : Instantiate(firstChild, m_visuals);
            child.rotation = Quaternion.identity;
            child.DestroyChildren(true, 1);
            var image = child.GetComponent<UnityEngine.UI.Image>();
            var button = child.GetComponent<UnityEngine.UI.Button>();
            var subImage = child.GetChild(0).GetComponent<UnityEngine.UI.Image>();
            var unrotate = arcHalfAngle * (i * -2);
            int buttonIndex = i;
            button.onClick.RemoveAllListeners();
            button.onClick.AddListener(() => OnButton(buttonIndex));
            var lockState = m_isButtonActive != null ? m_isButtonActive(i) : ELockState.Unlocked;
            var isUnlocked = lockState == ELockState.Unlocked || lockState == ELockState.Active;
            button.interactable = isUnlocked;
            var buttonClr = m_gradient.Evaluate((float) i / m_segments);
            if (lockState == ELockState.Active) buttonClr = Color.Lerp(buttonClr, Color.yellow, .5f);
            image.color = buttonClr;
            if (lockState == ELockState.Locked) LockItem(button, unrotate, subIconPosition);
            else if (isUnlocked && m_getShortcut != null) ShowShortcut(button, m_getShortcut(i), unrotate, subIconPosition);
            var icon = subImage.transform;
            icon.rotation = Quaternion.Euler(0, 0, unrotate);
            icon.localPosition = iconPosition;
            (icon as RectTransform).sizeDelta = iconSizeDelta;
            var iconImage = icon.GetComponent<UnityEngine.UI.Image>();
            iconImage.sprite = lockState == ELockState.EarlyAccess ? m_earlyAccessSprite : m_sprites[i];
            iconImage.color = isUnlocked ? Color.white : m_disabledImageColour;
            if (i > 0) child.localScale = Vector3.zero;
        }
        firstChild.localScale = Vector3.zero;
        m_fadeTarget = 1;
    }

    void LockItem(UnityEngine.UI.Button _button, float _angle, Vector3 _pos)
    {
        var lockPrefab = Resources.Load<UnityEngine.UI.Image>("_GUI/Padlock");
        var lockImage = Instantiate(lockPrefab, _button.transform);
        lockImage.transform.localRotation = Quaternion.Euler(0, 0, _angle);
        lockImage.rectTransform.anchoredPosition = _pos;
    }
    
    private List<(KeyCode, UnityEngine.UI.Button)> m_shortcuts = new (); 
    
    void ShowShortcut(UnityEngine.UI.Button _button, KeyCode _key, float _angle, Vector3 _pos)
    {
        if (_key == KeyCode.None) return;
        m_shortcuts.Add((_key, _button));
        var go = KeyboardShortcutManager.Me.MakeShortcutVisual(_key, _button.transform);
        var rt = go.transform as RectTransform;
        rt.localRotation = Quaternion.Euler(0, 0, _angle);
        rt.anchorMin = rt.anchorMax = Vector2.one * .5f;
        rt.anchoredPosition = _pos;
        rt.sizeDelta = Vector2.one * 40;
    }
    
    void RefreshButtonStates()
    {
        for (int i = 0; i < m_segments; ++i)
        {
            var child = m_visuals.GetChild(i);
            var image = child.GetComponent<UnityEngine.UI.Image>();
            var button = child.GetComponent<UnityEngine.UI.Button>();
            var subImage = child.GetChild(0).GetComponent<UnityEngine.UI.Image>();
            var lockState = m_isButtonActive != null ? m_isButtonActive(i) : ELockState.Unlocked;
            var isUnlocked = lockState == ELockState.Unlocked || lockState == ELockState.Active;
            button.interactable = isUnlocked;
            var buttonClr = m_gradient.Evaluate((float) i / m_segments);
            if (lockState == ELockState.Active) buttonClr = Color.Lerp(buttonClr, Color.yellow, .5f);
            image.color = buttonClr;
            var icon = subImage.transform;
            var iconImage = icon.GetComponent<UnityEngine.UI.Image>();
            iconImage.color = isUnlocked ? Color.white : m_disabledImageColour;
        }        
    }

    void Update()
    {
        if (m_segmentsConfigured != m_segments) Setup();
        
        if (m_shortcutKey != KeyCode.None)
        {
            if (Input.GetKeyDown(m_shortcutKey))
            {
                m_shortcutCb?.Invoke();
                RefreshButtonStates();
            }
            else if (Input.GetKey(m_shortcutKey) == false)
            {
                if (m_fadeTarget > .5f && m_fade < .75f)
                {
                    m_shortcutCb?.Invoke();
                    m_fadeTarget = 0;
                    m_forceClose = true;
                }
            }
        }

        if (EKeyboardFunction.Cancel.IsClicked())
        {
            m_fadeTarget = 0;
            m_forceClose = true;
        }
        
        foreach (var (key, button) in m_shortcuts)
        {
            if (Input.GetKeyDown(key))
            {
                button.onClick.Invoke();
                break;
            }
        }
        
        if (m_fade.Nearly(m_fadeTarget) == false)
        {
            var df = Mathf.Sign(m_fadeTarget - m_fade);
            m_fade += df * Time.deltaTime / m_fadeInLength;
            m_fade = Mathf.Clamp01(m_fade);
            var fadeProgress = m_fade * m_segments;
            var fadeIndex = (int)fadeProgress;
            var fadeFraction = fadeProgress - fadeIndex;
            for (int i = 0; i < m_segments; ++i)
            {
                var child = m_visuals.GetChild(i);
                var scale = i < fadeIndex ? 1 : (i > fadeIndex ? 0 : fadeFraction);
                child.localScale = Vector3.one * scale;
            }
            float segmentAngle = 360.0f / m_segments;
            for (int i = 0; i < m_segments; ++i)
            {
                var child = m_visuals.GetChild(i);
                var rot = (i < fadeIndex ? i : fadeIndex - 1 + fadeFraction) * segmentAngle;
                child.rotation = Quaternion.Euler(0, 0, rot);
            }
        }
        else
        {
            if (m_fadeTarget < .0001f)
            {
                m_onButtonPress(-1);
                Destroy(gameObject);
            }
        }
        if (m_closePopup()) ForceClose();
        m_fadeTarget = m_forceClose == false ? 1 : 0;
        m_alphaCurrent = Mathf.Lerp(m_alphaCurrent, m_alphaTarget, .1f);
        if (m_canvasGroup.alpha.Nearly(m_alphaCurrent) == false)
            m_canvasGroup.alpha = Mathf.Max(0, m_alphaCurrent * 1.5f - .5f);
    }

    private void OnButton(int _index)
    {
        m_onButtonPress(_index);
        if (m_closeOnSelect)
            ForceClose();
    }
    void ForceClose()
    {
        foreach (var button in m_visuals.GetComponentsInChildren<UnityEngine.UI.Button>())
            button.interactable = false;
        foreach (var img in GetComponentsInChildren<UnityEngine.UI.Image>())
            img.raycastTarget = false;
        m_alphaTarget = .3f;
        m_forceClose = true;
    }

    public static void Create(Vector3 _screenPos, Sprite[] _sprites, bool _closeOnSelect, System.Func<bool> _closePopup, System.Action<int> _onButtonPress,
        System.Func<int, ELockState> _buttonLockState = null, System.Func<int, KeyCode> _buttonShortcut = null,
        float _animationTime = -1, Transform _parent = null, KeyCode _shortcutKey = KeyCode.None, System.Action _shortcutCb = null)
    {
        if (_closeOnSelect && _buttonLockState != null)
        {
            // if there are only 2 options, we can just toggle
            int unlockedCount = 0, activeIndex = -1;
            for (int i = 0; i < _sprites.Length; ++i)
            {
                var state = _buttonLockState(i);
                if (state == ELockState.Unlocked || state == ELockState.Active)
                    ++unlockedCount;
                if (state == ELockState.Active)
                    activeIndex = i;
            }
            if (unlockedCount <= 1)
            {
                _onButtonPress(-1);
                return;
            }
            if (unlockedCount == 2)
            {
                for (int i = 0; i < _sprites.Length; ++i)
                {
                    var state = _buttonLockState(i);
                    if ((state == ELockState.Unlocked || state == ELockState.Active) && activeIndex != i)
                    {
                        _onButtonPress(i);
                        _onButtonPress(-1);
                        return;
                    }
                }
            }
        }
        if (_parent == null) _parent = GameManager.Me.CurrentCanvas;
        var prefab = Resources.Load<RadialPopup>("_GUI/RadialPopup");
        var popup = Instantiate(prefab, _parent);
        popup.Activate(_screenPos, _sprites, _closeOnSelect, _closePopup, _onButtonPress, _buttonLockState, _buttonShortcut, _animationTime, _shortcutKey, _shortcutCb);
    }
}
