using UnityEngine;
using UnityEngine.UI;

public class KeyAssignController : MonoBehaviour
{
    void OnEnable() => Refresh();
    
    public void Refresh()
    {
        var scroller = GetComponent<ScrollRect>();
        var content = scroller.content;
        content.DestroyChildren(true, 1);
        var template = content.GetChild(0).gameObject.GetComponent<KeyAssignRow>();

        var rows = KeyboardController.Me.m_keyAssignRows;
        bool even = true;
        for (int i = 1; i < rows.Length; ++i) // skip first (dummy) row
        {
            var inst = i == 1 ? template : Instantiate(template, content);
            inst.Set(rows[i], this, ref even);
        }
        content.sizeDelta = new Vector2(content.sizeDelta.x, (rows.Length - 1) * template.GetComponent<RectTransform>().sizeDelta.y);
    }

    public void ResetToDefaults()
    {
        KeyboardController.Me.ResetToDefaults();
        Refresh();
    }
}
