using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class RuneBarUI : MonoBehaviour
{
	[SerializeField]
	private Image runeFire = null;
	[SerializeField]
	private Image runeLightning = null;

	private string activeRuneType = null;
	private float glowElapsedTime = 0f;
	private float glowDuration = 0.5f;

	private Dictionary<string, Image> runeUIs = null;
	private Dictionary<string, Image> RuneUIs
	{
		get
		{
			if (runeUIs == null)
			{
				runeUIs = new Dictionary<string, Image>
                {
                    { "Flame", runeFire },
                    { "Lightning", runeLightning }
                };
			}

			return runeUIs;
		}
	}

    public void Start()
    {
	    ResetRuneUIs();
	}

	public void Update()
	{
		if (activeRuneType != null)
		{
			float halfDuration = glowDuration * 0.5f;
			float t = glowElapsedTime / halfDuration;
			if (glowElapsedTime >= halfDuration)
				t = 1f - ((glowElapsedTime - halfDuration) / halfDuration);
			t = Mathf.Clamp01(t);
			var color = Color.Lerp(Color.white, Color.gray, t);
			float scale = Mathf.Lerp(1.5f, 1f, t);
			SetRuneUIColorAndScale(activeRuneType, color, scale);
			
			glowElapsedTime += Time.deltaTime;
			if (glowElapsedTime >= glowDuration)
				glowElapsedTime = glowElapsedTime - glowDuration;
		}
	}

	private void SetRuneUIColorAndScale(string runeType, Color color, float scale)
	{
		if (!RuneUIs.TryGetValue(runeType, out Image runeUI))
			return;
		
		runeUI.color = color;
		runeUI.transform.localScale = Vector3.one * scale;
		var icon = runeUI.transform.GetChild(0).GetComponent<Image>();
		icon.color = color;
	}

	public void ResetRuneUIs()
	{
		activeRuneType = null;
		foreach (var key in RuneUIs.Keys)
		{
			SetRuneUIColorAndScale(key, Color.gray, 1f);
		}
	}

	public void ActivateRuneUI(string runeType)
	{
		activeRuneType = runeType;
		glowElapsedTime = 0f;

		foreach (var key in RuneUIs.Keys)
		{
			bool active = key == runeType;
			var color = active ? Color.white : Color.gray;
			float scale = active ? 1.5f : 1f;
			SetRuneUIColorAndScale(key, color, scale);
		}
	}

	public void ToggleUI(bool enable)
	{
		if (gameObject.activeSelf != enable)
		{
			ResetRuneUIs();
			gameObject.SetActive(enable);
		}
	}
}
