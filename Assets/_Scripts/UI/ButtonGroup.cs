using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class ButtonGroup : MonoBehaviour
{
    public Color m_highlightedColour = new Color(1, 1, 1f, .5f);
    public Color m_pressedColour = new Color(1, 1, 1, .35f);
    public Color m_selectedColour = new Color(1, 1, 1, 1);
    public Color m_unselectedColour = new Color(1, 1, 1, .25f);
    private ColorBlock m_colorsUnselected;
    private ColorBlock m_colorsSelected;
    
    private void CreateColourBlocks()
    {
        m_colorsUnselected =
            new()
            {
                normalColor = m_unselectedColour,
                highlightedColor = m_highlightedColour,
                selectedColor = m_unselectedColour,
                pressedColor = m_pressedColour,
                colorMultiplier = 1,
            };
        m_colorsSelected =
            new()
            {
                normalColor = m_selectedColour,
                highlightedColor = m_highlightedColour,
                selectedColor = m_selectedColour,
                pressedColor = m_pressedColour,
                colorMultiplier = 1,
            };
    }

    public void Select(int _index)
    {
        CreateColourBlocks();
        var buttons = GetComponentsInChildren<Button>(true);
        int liveIndex = -1;
        for (int i = 0; i < buttons.Length; ++i)
        {
            //if (buttons[i].gameObject.activeSelf)
            {
                ++liveIndex;
                buttons[i].colors = liveIndex == _index ? m_colorsSelected : m_colorsUnselected;
            }
        }
    }
}
