using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class TownManagementModeButton : MonoBehaviour
{
    public Action<ETownManagementMode> m_onClick;
    public ETownManagementMode m_mode;
    public TextMeshProUGUI m_text;

    public void OnClick()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        m_onClick?.Invoke(m_mode);
    }

    public void SetCurrent(bool _isCurrent)
    {
        m_text.color = _isCurrent ? Color.yellow : Color.white;
    }

}
