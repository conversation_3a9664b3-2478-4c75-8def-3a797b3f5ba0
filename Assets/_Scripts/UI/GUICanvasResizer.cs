using UnityEngine;

public class GUICanvasResizer : MonoBehaviour {
	private RectTransform m_rectTransform;
	private Vector2 m_originalSize;

	[SerializeField]private Vector2 m_anchorMin;
	[SerializeField]private Vector2 m_anchorMax;
	
	void Start()
	{
		m_rectTransform = GetComponent<RectTransform>();
		m_originalSize = m_rectTransform.rect.size;
		Refresh();
	}

	void Update()
	{
		Refresh();
	}
	
	void Refresh()
	{
		var oldMin = m_anchorMin;
		var oldMax = m_anchorMax;
		
		m_anchorMin = new Vector2 (Screen.safeArea.xMin / Screen.width, Screen.safeArea.yMin / Screen.height);
		m_anchorMax = new Vector2 (Screen.safeArea.xMax / Screen.width, Screen.safeArea.yMax / Screen.height);

		if (TouchManager.Orientation == ScreenOrientation.LandscapeRight)
		{
			m_anchorMin.x = 0;
		}
		else
		{
			m_anchorMax.x = 1;
		}

		if ((oldMin - m_anchorMin).sqrMagnitude > 0 || (oldMax - m_anchorMax).sqrMagnitude > 0)
		{
			m_rectTransform.anchorMin = m_anchorMin;
			m_rectTransform.anchorMax = m_anchorMax;
		}
	}
	
	public Vector2 CalculateRelaxation(float _leftFraction, float _rightFraction, float _topFraction, float _bottomFraction) {
		//_leftFraction *= .5f; _rightFraction *= .5f; _topFraction *= .5f; _bottomFraction *= .5f;
		var v = Vector2.zero;
		float leftMargin = m_anchorMin.x * m_originalSize.x, rightMargin = (1f - m_anchorMax.x) * m_originalSize.x;
		float bottomMargin = m_anchorMin.y * m_originalSize.y, topMargin = (1f - m_anchorMax.y) * m_originalSize.y;
		v.x = rightMargin * _rightFraction - leftMargin * _leftFraction;
        v.y = topMargin * _topFraction - bottomMargin * _bottomFraction;
        return v;
	}
}
