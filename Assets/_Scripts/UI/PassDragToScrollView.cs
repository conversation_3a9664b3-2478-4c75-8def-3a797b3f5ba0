using UnityEngine;
using UnityEngine.UI;

public class PassDragToScrollView : <PERSON>o<PERSON><PERSON><PERSON>our, UnityEngine.EventSystems.IBeginDragHandler, UnityEngine.EventSystems.IDragHandler, UnityEngine.EventSystems.IEndDragHandler, UnityEngine.EventSystems.IScrollHandler
{
    public float m_scrollMultiplier = 5;
    private ScrollRect m_parentScroller;
    void Start() => m_parentScroller = GetComponentInParent<ScrollRect>();
    void UnityEngine.EventSystems.IBeginDragHandler.OnBeginDrag(UnityEngine.EventSystems.PointerEventData eventData) => m_parentScroller.OnBeginDrag(eventData);
    void UnityEngine.EventSystems.IDragHandler.OnDrag(UnityEngine.EventSystems.PointerEventData eventData) => m_parentScroller.OnDrag(eventData);
    void UnityEngine.EventSystems.IEndDragHandler.OnEndDrag(UnityEngine.EventSystems.PointerEventData eventData) => m_parentScroller.OnEndDrag(eventData);

    void UnityEngine.EventSystems.IScrollHandler.OnScroll(UnityEngine.EventSystems.PointerEventData data)
    {
        data.scrollDelta *= m_scrollMultiplier;
        m_parentScroller.OnScroll(data);
    }
}
