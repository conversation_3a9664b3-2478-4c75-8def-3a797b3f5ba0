using UnityEngine;
using UnityEngine.EventSystems;

public class DragSimple : DragBase {
    private GameObject m_bestTarget = null;
    override public bool AcceptsClicks => true;
    override public bool UpdatesDuringClick => true;
    PointerEventData m_eventData = new PointerEventData(null);
    override public void OnDragStart() {
        Vector2 input = m_eventData.pressPosition = InputPosition;
        m_eventData.button = InputId;
        m_eventData.delta = new Vector2(input.x, input.y) - m_eventData.position;
        m_eventData.position = input;
        
        GetComponentInChildren<IBeginDragHandler>()?.OnBeginDrag(m_eventData);
    }
	
    override public void OnDragEnd(bool _undo) {
		
        Vector3 input = InputPosition;
        m_eventData.delta = new Vector2(input.x, input.y) - m_eventData.position;
        m_eventData.position = input;
        GetComponentInChildren<IEndDragHandler>()?.OnEndDrag(m_eventData);
    }

    override public void OnDragUpdate(Vector3 _dragPoint, Vector3 _totalScreenDrag) {
        if (CouldBeClick && TimeSinceClick > c_longClickTime) {
            EndDrag();
        }

        Vector3 input = InputPosition;
        m_eventData.delta = new Vector2(input.x, input.y) - m_eventData.position;
        m_eventData.position = input;
		
        if(!CouldBeClick)
            GetComponentInChildren<IDragHandler>()?.OnDrag(m_eventData);
    }
    const float c_longClickTime = .75f;
    override public void OnClick() {
        //GetComponentInChildren<IDragCard>()?.OnClick(TimeSinceClick > c_longClickTime);
    }
}